import { createContext, useContext, useMemo, useState } from 'react';
import {
  CommentaryPlayer,
  CricketCommentaryContextType,
} from '../../../../../types/commentry';
import { useQuery } from '@tanstack/react-query';
import {
  fetchMatchCommentary,
  fetchMatchCommentaryByMatchId,
  getGameStats,
  getLiveCricketCommentary,
  getMatchDetails,
  getMatchLineUp,
  getSmartPlayCricketStats,
} from '@/helpers/fetchers/commentry';
import { quyerKeys } from '@/lib/queryKeys';
import { MatchDetailUI } from '../../../../../db/commentary/indext';
import { ReactSelectOptionType } from '../../../../../types';
import moment from 'moment';
import { getFooterText } from '@/lib/utils';
import { usePathname, useSearchParams } from 'next/navigation';
import { useCompetition } from '../../competitionContext';

const cricketCommentaryContext = createContext<
  CricketCommentaryContextType | undefined
>(undefined);

function extractPlayers(data: any[]): CommentaryPlayer[] {
  const players: CommentaryPlayer[] = [];

  data.forEach((item) => {
    // Extract batsman information
    if (item.batsmanStriker.batName) {
      players.push({
        name: item.batsmanStriker.batName,
        role: 'batsman',
        label: item.batsmanStriker.batName,
        value: item.batsmanStriker.batName,
      });
    }

    // Extract bowler information
    if (item.bowlerStriker.bowlName) {
      players.push({
        name: item.bowlerStriker.bowlName,
        role: 'bowler',
        label: item.batsmanStriker.batName,
        value: item.batsmanStriker.batName,
      });
    }
  });

  // Remove duplicates by name
  const uniquePlayers = Array.from(
    new Map(players.map((player) => [player.name, player])).values(),
  );

  return uniquePlayers;
}

const CricketCommentaryProvider = ({
  children,
}: Readonly<{ children: React.ReactNode }>) => {
  const [teamId, setTeamId] = useState<number | undefined>(undefined);
  const [matchId, setMatchId] = useState<number | undefined>(undefined);
  const [eventFilter, setEventFilter] = useState<string | undefined>(undefined);
  const [selectedOption, setSelectedOption] = useState<ReactSelectOptionType>({
    value: 'preview',
    label: 'Preview',
  });

  const searchParams = useSearchParams();
  const sport_id = searchParams.get('sport_id');

  const pathname = usePathname();

  const [showPlayerDetailsTable, setShowPlayerDetailsTable] =
    useState<boolean>(false);
  const { data } = useQuery({
    queryFn: () => fetchMatchCommentary(102072, teamId),
    queryKey: [quyerKeys.getMatchCommentary, teamId],
    enabled: false,
  });

  const { eventDetailsResponse } = useCompetition();

  const { data: commentaryList } = useQuery({
    queryFn: () =>
      fetchMatchCommentaryByMatchId(
        eventDetailsResponse?.result?.eventDetails?.id ?? 0,
        teamId,
        selectedOption?.value === 'preview',
      ),

    queryKey: [quyerKeys.getCommentaryByMatchId, teamId, matchId],
    enabled:
      pathname.includes('competitions') &&
      eventDetailsResponse?.result?.eventDetails?.SportId === 4 &&
      eventDetailsResponse.result.eventDetails.status !== 'upcoming',
  });

  const { data: liveCommentary, isLoading: isLiveCommentaryLoading } = useQuery(
    {
      queryFn: () => getLiveCricketCommentary({ matchId, teamId, eventFilter }),
      queryKey: [
        quyerKeys.getLiveCricketCommentary,
        matchId,
        teamId,
        eventFilter,
      ],
      enabled: !!matchId,
    },
  );

  const { data: matchDetailsData } = useQuery({
    queryFn: () =>
      getMatchDetails(eventDetailsResponse?.result?.eventDetails?.id ?? 0),
    queryKey: [quyerKeys.getMatchDetails],
    enabled:
      pathname.includes('competitions') &&
      eventDetailsResponse?.result?.eventDetails?.SportId === 4,
  });

  const { data: gameStats } = useQuery({
    queryFn: () =>
      getGameStats(eventDetailsResponse?.result?.eventDetails?.id ?? 0),
    queryKey: [quyerKeys.getGameStats],
    enabled:
      pathname.includes('competitions') &&
      eventDetailsResponse?.result?.eventDetails?.SportId === 4,
  });

  const { data: smartPlayCricketStats } = useQuery({
    queryFn: () =>
      getSmartPlayCricketStats({
        tournamentId:
          eventDetailsResponse?.result?.eventDetails?.CricketTournamentId ?? 0,
        seasonId:
          eventDetailsResponse?.result?.eventDetails?.CricketSeasonId ?? 0,
        eventId: eventDetailsResponse?.result?.eventDetails?.id ?? 0,
        sportId: eventDetailsResponse?.result?.eventDetails?.SportId ?? 4,
      }),
    queryKey: [quyerKeys.getSmartPlayCricketStats],
    enabled:
      pathname.includes('competitions') &&
      eventDetailsResponse?.result?.eventDetails?.SportId === 4,
  });

  const { data: matchLineUp } = useQuery({
    queryKey: [quyerKeys.getMatchLineUp],
    queryFn: () =>
      getMatchLineUp(eventDetailsResponse?.result?.eventDetails?.id ?? 0),
    enabled: false,
  });

  const matchDetails: MatchDetailUI[] = [
    {
      label: 'Series',
      value: matchDetailsData?.result?.CricketSeason?.name ?? '-',
    },
    {
      label: 'Season',
      value: matchDetailsData?.result?.CricketSeason?.year ?? '-',
    },
    {
      label: 'Match',
      value: matchDetailsData?.result?.eventName ?? '-',
    },
    {
      label: 'Date',
      value:
        moment(matchDetailsData?.result?.startTime).format('D MMMM YYYY') ?? '',
    },
    {
      label: 'Time',
      value: moment(matchDetailsData?.result?.startTime).format('h:mm A') ?? '',
    },
    {
      label: 'Toss',
      value: getFooterText(
        matchDetailsData?.result,
        matchDetailsData?.result?.ScoreBoard?.TPa,
        matchDetailsData?.result?.ScoreBoard?.TCho,
      ),
    },
    {
      label: 'Venue',
      value: matchDetailsData?.result?.CricketStadium?.name ?? 0,
    },
  ];

  const options: ReactSelectOptionType[] = [
    { value: 'preview', label: 'Preview' },
    {
      value: matchDetailsData?.result?.homeTeam?.id.toString() ?? '',
      label: matchDetailsData?.result?.homeTeam?.name ?? '',
    },
    {
      value: matchDetailsData?.result?.awayTeam?.id.toString() ?? '',
      label: matchDetailsData?.result?.awayTeam?.name ?? '',
    },
  ];

  const players = extractPlayers([]);
  const teamPlayerOptions: CommentaryPlayer[] = players;

  const HIGHLIGHTOPTIONS: ReactSelectOptionType[] = [
    { label: 'Full Commentary', value: 'fullCommentary' },
    {
      label: 'Four',
      value: 'four',
    },
    {
      label: 'Six',
      value: 'six',
    },
    {
      label: 'Wicket',
      value: 'wicket',
    },
    {
      label: 'Fifty',
      value: 'fifty',
    },
    {
      label: 'Century',
      value: 'century',
    },
    {
      label: 'Dropped Catches',
      value: 'droppedCatch',
    },
    {
      label: 'UDRS',
      value: 'udrs',
    },
    {
      label: 'Other',
      value: 'other',
    },
  ];

  const selectedTeamPlayers = matchLineUp?.result?.result
    ?.map((player) =>
      player?.teamId === Number(selectedOption?.value) &&
      selectedOption.value !== 'preview'
        ? player
        : null,
    )
    ?.filter((player) => player !== null);

  const awayTeamPlayers = matchLineUp?.result?.result
    ?.map((player) =>
      player?.teamId !== Number(selectedOption?.value) &&
      selectedOption.value !== 'preview'
        ? player
        : null,
    )
    ?.filter((player) => player !== null);

  const awayTeamBowlerPlayers: ReactSelectOptionType[] | undefined =
    awayTeamPlayers
      ?.filter((player) => {
        switch (player.role) {
          case 'Bowler':
          case 'Bowling Allrounder':
            return player;
          default:
            return null;
        }
      })
      .filter((player) => player !== null)
      .map((player) => ({
        label: player?.CricketPlayer?.name,
        value: player?.id.toString(),
      }));

  const selectedBatterPlayers: ReactSelectOptionType[] | undefined =
    selectedTeamPlayers
      ?.filter((player) => {
        switch (player.role) {
          case 'Batsman':
          case 'Batting Allrounder':
          case 'WK-Batsman':
          case 'Bowling Allrounder':
          case 'bet1':
            return player;
          default:
            return null;
        }
      })
      .filter((player) => player !== null)
      .map((player) => ({
        label: player?.CricketPlayer?.name,
        value: player?.id.toString(),
      }));

  // Game Stats

  const homeTeam = gameStats?.result?.CricketMatchInnings[0];
  const awayTeam = gameStats?.result?.CricketMatchInnings[1];
  const battingTeam = homeTeam?.CricketBatInnings?.map((player) => ({
    ...player,
    isExpanded: false,
  }));
  const bowlingTeam = awayTeam?.CricketBowlInnings?.map((player) => ({
    ...player,
    expand: false,
  }));

  const cricketContextValue = useMemo(
    (): CricketCommentaryContextType => ({
      state: {
        matchDetails: matchDetails,
        commentary: [],
        option: options,
        HIGHLIGHTOPTIONS,
        teamPlayerOptions,
        matchDetailsData,
        commentaryList,
        selectedBatterPlayers,
        awayTeamBowlerPlayers,
        battingTeam,
        bowlingTeam,
        gameStats,
        matchId,
        liveCommentary,
        isLiveCommentaryLoading,
        smartPlayCricketStats,
      },
      selectedOption,
      setSelectedOption,
      setTeamId,
      setShowPlayerDetailsTable,
      showPlayerDetailsTable,
      setMatchId,
      setEventFilter,
    }),
    [
      data,
      commentaryList,
      matchDetailsData,
      selectedOption,
      HIGHLIGHTOPTIONS,
      setSelectedOption,
      setShowPlayerDetailsTable,
      showPlayerDetailsTable,
      setTeamId,
      teamId,
      selectedBatterPlayers,
      awayTeamBowlerPlayers,
      battingTeam,
      bowlingTeam,
      gameStats,
      matchId,
      liveCommentary,
      isLiveCommentaryLoading,
      smartPlayCricketStats,
      setMatchId,
      setEventFilter,
    ],
  );

  return (
    <cricketCommentaryContext.Provider value={cricketContextValue}>
      {children}
    </cricketCommentaryContext.Provider>
  );
};

export const useCricketCommentary = () => {
  const cricketContext = useContext(cricketCommentaryContext);
  if (!cricketContext) {
    throw new Error(
      'useCricketCommentary must be used within a CricketCommentaryProvider',
    );
  }
  return cricketContext;
};

export default CricketCommentaryProvider;
