import React from 'react';

const LightSmart = () => {
  return (
    <svg
      width="48"
      height="18"
      viewBox="0 0 48 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        id="mask0_4_64"
        style={{ maskType: 'luminance' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="10"
        height="18"
      >
        <path
          d="M5.17354 0.446808C4.07294 0.461649 3.01402 0.869551 2.18841 1.59669C1.3628 2.32384 0.825053 3.32218 0.672493 4.41104C0.582037 5.12466 0.69218 5.84935 0.990595 6.50399C1.2534 7.15317 1.55752 7.78488 1.90109 8.39526C2.87515 10.1195 3.95667 11.781 5.13926 13.3698C5.58689 12.799 6.04118 12.1902 6.46215 11.6032C6.80468 11.8566 7.06705 12.203 7.21799 12.6012C7.36893 12.9994 7.40207 13.4326 7.31346 13.8491C7.22485 14.2655 7.01824 14.6478 6.71824 14.9503C6.41823 15.2527 6.03749 15.4627 5.62139 15.555C5.20529 15.6474 4.77137 15.6183 4.37135 15.4713C3.97132 15.3242 3.62206 15.0654 3.36519 14.7256C3.10831 14.3858 2.95466 13.9794 2.92253 13.5549C2.8904 13.1303 2.98115 12.7054 3.18398 12.331L0.758209 12.311C0.45885 13.4224 0.600074 14.6063 1.15249 15.6164C1.70491 16.6264 2.62597 17.3848 3.72399 17.7336C4.42402 17.9596 5.16823 18.0138 5.89367 17.8917C6.61911 17.7696 7.30444 17.4747 7.8917 17.0321C8.47895 16.5895 8.95085 16.0121 9.26749 15.3488C9.58412 14.6855 9.73618 13.9558 9.71079 13.2214C9.45019 11.9762 8.96792 10.7879 8.28695 9.71287C7.35107 8.09401 6.32037 6.53173 5.20021 5.03417C4.76592 5.6202 4.3021 6.20718 3.88399 6.80652C3.56788 6.57264 3.31953 6.25919 3.16429 5.89815C3.00904 5.5371 2.95244 5.14138 3.00028 4.75135C3.04811 4.36133 3.19866 3.99096 3.43657 3.67802C3.67448 3.36509 3.99123 3.12079 4.35451 2.97005C4.71779 2.81931 5.1146 2.76752 5.50446 2.81996C5.89432 2.87241 6.26328 3.02722 6.57369 3.26859C6.8841 3.50997 7.12486 3.82927 7.27141 4.19392C7.41796 4.55858 7.46505 4.95554 7.40788 5.34431C7.3668 5.60104 7.28345 5.84921 7.16121 6.07875C7.52884 6.60389 8.25457 7.73979 8.58981 8.29156C9.26844 7.26078 9.67714 6.07631 9.77841 4.84676C9.73405 3.80362 9.33529 2.80669 8.64782 2.02021C7.96036 1.23374 7.0252 0.704629 5.99642 0.520061C5.7426 0.472843 5.48506 0.448328 5.22688 0.446808H5.17354Z"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_4_64)">
        <path
          d="M9.80385 -0.310927L-1.39137 0.556213L0.0779382 19.4834L11.2732 18.6163L9.80385 -0.310927Z"
          fill="url(#paint0_radial_4_64)"
        />
      </g>
      <path
        d="M17.0429 17.6851H15.201L13.9772 14.8586C13.948 14.78 13.8914 14.6351 13.8076 14.4239C13.7238 14.2127 13.6305 13.9723 13.5276 13.7027C13.4241 13.4332 13.3184 13.156 13.2105 12.8713C13.1025 12.5865 13.0041 12.3265 12.9152 12.0912V13.9939C12.9152 14.3376 12.9127 14.6614 12.9076 14.9652C12.9025 15.269 12.8952 15.5093 12.8857 15.6863L12.8124 17.6841H10.5733L11.1628 7.71691H13.2581L15.5572 13.4583C15.6258 13.6346 15.7166 13.8822 15.8296 14.2012C15.9426 14.5203 16.0537 14.8681 16.1629 15.2449C16.2709 14.8821 16.3813 14.5412 16.4944 14.2222C16.6074 13.9032 16.7026 13.6495 16.7801 13.4611L19.0792 7.71976H21.1278L21.7173 17.687H19.4316L19.343 15.6891C19.3328 15.5027 19.3255 15.2572 19.3211 14.9528C19.3166 14.6484 19.3141 14.3268 19.3135 13.9881C19.3135 13.6495 19.316 13.3108 19.3211 12.9721C19.3262 12.6334 19.3287 12.3414 19.3287 12.0959C19.25 12.3021 19.1639 12.5351 19.0706 12.7952C18.9773 13.0552 18.882 13.3089 18.7849 13.5562C18.6865 13.8017 18.5931 14.03 18.5049 14.2412C18.4166 14.4524 18.3477 14.6167 18.2982 14.734L17.0429 17.6851Z"
        fill="white"
      />
      <path
        d="M26.2546 7.71786H28.4232L32.0785 17.6851H29.6165L28.8794 15.2858H25.6507L24.8888 17.6851H22.4669L26.2546 7.71786ZM28.3327 13.4887C28.2343 13.2039 28.1337 12.9071 28.0308 12.5982C27.9279 12.2894 27.8295 11.9875 27.7356 11.6926C27.6422 11.3976 27.5562 11.1205 27.4775 10.8611C27.3987 10.6017 27.3298 10.3832 27.2708 10.2056C27.2117 10.3921 27.1428 10.6141 27.0641 10.8716C26.9854 11.1291 26.8993 11.4062 26.806 11.703C26.7127 11.9973 26.6146 12.2967 26.5117 12.6011C26.4089 12.9055 26.3082 13.2001 26.2098 13.4849H28.3318"
        fill="white"
      />
      <path
        d="M32.9614 7.71691C33.0497 7.71691 33.2265 7.71437 33.4919 7.7093C33.7573 7.70422 34.0519 7.70169 34.3757 7.70169C34.7002 7.70169 35.0148 7.69915 35.3195 7.69408C35.6243 7.689 35.8602 7.68647 36.0272 7.68647C37.0236 7.61494 38.0164 7.86795 38.8568 8.40758C39.3607 8.86585 39.686 9.48749 39.7751 10.1624C39.8642 10.8373 39.7113 11.5219 39.3434 12.095C39.0224 12.516 38.5951 12.8443 38.1053 13.0463C38.4046 13.2182 38.6694 13.444 38.8863 13.7123C39.1348 14.0215 39.352 14.3545 39.5349 14.7064C39.7412 15.0946 39.9501 15.5385 40.1616 16.0383C40.373 16.5381 40.6063 17.0876 40.8616 17.687H38.2529C37.9355 17.0001 37.6701 16.3941 37.4567 15.8689C37.2434 15.3438 37.0418 14.9021 36.8519 14.5437C36.7088 14.2481 36.5226 13.9753 36.2996 13.7341C36.2146 13.6465 36.1125 13.5771 35.9996 13.5303C35.8868 13.4835 35.7655 13.4603 35.6434 13.4621H35.3929V17.6879H32.9614V7.71691ZM35.3929 11.9418H36.0119C36.3338 11.9499 36.6469 11.8368 36.8891 11.625C37.018 11.4955 37.1171 11.3394 37.1795 11.1677C37.2418 10.996 37.2659 10.8128 37.2501 10.6309C37.2513 10.4546 37.2289 10.2789 37.1834 10.1086C37.1425 9.95302 37.0699 9.80758 36.9701 9.68143C36.8614 9.55129 36.7219 9.45025 36.5643 9.38746C36.365 9.30881 36.1519 9.27127 35.9377 9.27711H35.3929V11.9418Z"
        fill="white"
      />
      <path
        d="M47.4246 7.71786L47.4189 9.49972H45.1207V17.6813H42.6921V9.49972H40.3111V7.71786H47.4246Z"
        fill="white"
      />
      <defs>
        <radialGradient
          id="paint0_radial_4_64"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(-4.86556 7.3464) rotate(85.5611) scale(9.96015 28.5719)"
        >
          <stop stop-color="#E84B23" />
          <stop offset="0.151" stop-color="#E84A23" />
          <stop offset="0.257" stop-color="#E74823" />
          <stop offset="0.35" stop-color="#EC5130" />
          <stop offset="0.435" stop-color="#F36648" />
          <stop offset="0.514" stop-color="#FF806C" />
          <stop offset="0.59" stop-color="#FF9487" />
          <stop offset="0.663" stop-color="#FFC3BC" />
          <stop offset="0.731" stop-color="#FFE4E0" />
          <stop offset="0.797" stop-color="white" />
          <stop offset="1" stop-color="white" />
        </radialGradient>
      </defs>
    </svg>
  );
};

export default LightSmart;
