import { Meta, StoryObj } from '@storybook/react';
import EventConfirmModal from './index';

const meta: Meta<typeof EventConfirmModal> = {
  title: 'Components/EventConfirmModal',
  component: EventConfirmModal,
};

export default meta;

export const Default: StoryObj<typeof EventConfirmModal> = {
  args: {
    isOpen: true,
    onClose: () => { },
    user: { coins: 100, holdCoins: 0, bonusCoins: 100 },
    eventConfigurationData: { entryCoin: 100 },
    isCreatingTeamPending: false,
    acceptTerms: false,
    setAcceptTerms: () => { },
    handleTeamSubmitConfirmation: () => { },

  },
};