import Image from 'next/image';
import React, { SyntheticEvent } from 'react';

import { Config } from '@/helpers/context/config';

import {
  getDefaultProfileImage,
  getDefaultTeamImage,
  getDefaultUserImage,
} from '../../../../db/db';

type PlayerAvatarProps = {
  avatarUrl: string;
  className?: string;
  isTeam?: boolean;
  isUser?: boolean;
};

const PlayerAvatar: React.FC<PlayerAvatarProps> = ({
  avatarUrl,
  className = 'flex items-center justify-center  md:w-[36px] md:h-[36px] md:min-w-[36px] w-[30px] h-[30px] min-w-[30px]',
  isTeam,
  isUser,
}) => {
  let userAvatar = '';

  if (avatarUrl) {
    if (avatarUrl.includes('uploads')) {
      const path = avatarUrl.startsWith('/') ? avatarUrl : '/' + avatarUrl;
      const baseUrl = Config?.mediaURL?.endsWith('/')
        ? Config.mediaURL
        : Config.mediaURL + '/';
      userAvatar = baseUrl + path.replace(/^\/+/, '');
    } else {
      userAvatar = avatarUrl;
    }
  }

  const handleImageError = (event: SyntheticEvent<HTMLImageElement>) => {
    (event.target as HTMLImageElement).src = isTeam
      ? getDefaultTeamImage()
      : isUser
        ? getDefaultUserImage()
        : getDefaultProfileImage(); // Change the src to the default image
  };

  return (
    <div
      className={`flex items-center justify-center  md:w-[36px] md:h-[36px] md:min-w-[36px] w-[30px] h-[30px] min-w-[30px] ${className}`}
    >
      {userAvatar && (
        <Image
          src={userAvatar}
          alt="Player Avatar"
          width={100}
          height={100}
          onError={handleImageError}
          style={{ width: '100%', height: '100%' }}
          className="rounded-full object-contain"
          unoptimized={true}
        />
      )}
    </div>
  );
};

export default PlayerAvatar;
