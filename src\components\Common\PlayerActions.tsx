'use client';
// PlayerActions component
const PlayerActions: React.FC<{
  showSubstituteButton: boolean;
  showRemoveButton: boolean;
  dreamTeamId: string | null;
  addMore: boolean;
  onSubstitute: () => void;
  onSetCaptain: () => void;
  onSetViceCaptain: () => void;
  onRemove: () => void;
  isReserveType?: boolean;
  add_more?: boolean;
}> = (
  {
    showSubstituteButton,
    showRemoveButton,
    addMore,
    onSubstitute,
    onSetCaptain,
    onSetViceCaptain,
    onRemove,
    isReserveType = false,
    add_more = false,
  }, // Continuing from the PlayerActions component...
) => (
    <div className="hidden md:block">
      <div
        className="hidden absolute -bottom-3 items-center justify-center left-1/2 -translate-x-1/2 group-hover:flex z-10"
        style={{
          width: 'max-content',
        }}
      >
        <div className="flex text-white text-base font-normal gap-x-1 justify-center">
          {!isReserveType && (
            <>
              {showSubstituteButton && (
                <button
                  className="bg-secondary-100 px-2 rounded-md cursor-pointer"
                  style={{ width: 'max-content' }}
                  onClick={onSubstitute}
                >
                  Substitute
                </button>
              )}
              <button
                className="bg-secondary-100 px-2 rounded-md cursor-pointer disabled:cursor-not-allowed"
                style={{ width: 'max-content' }}
                disabled={!addMore}
                onClick={onSetCaptain}
              >
                Captain
              </button>
              <button
                className="bg-secondary-100 max-w-fit px-2 rounded-md cursor-pointer disabled:cursor-not-allowed"
                style={{ width: 'max-content' }}
                disabled={!addMore}
                onClick={onSetViceCaptain}
              >
                Vice-captain
              </button>
              {showRemoveButton && (
                <button
                  className="bg-negative-200 max-w-fit px-2 rounded-md cursor-pointer disabled:cursor-not-allowed disabled:bg-[#C9C9C9]"
                  style={{ width: 'max-content' }}
                  onClick={onRemove}
                >
                  Remove
                </button>
              )}
            </>
          )}

          {isReserveType && !add_more && (
            <button
              className="bg-negative-200 px-2 rounded-md cursor-pointer disabled:cursor-not-allowed disabled:bg-[#C9C9C9]"
              style={{ width: 'max-content' }}
              onClick={onRemove}
            >
              Substitute
            </button>
          )}
        </div>
      </div>
    </div>
  );

export default PlayerActions;
