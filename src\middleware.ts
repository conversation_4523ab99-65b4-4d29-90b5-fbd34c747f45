import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const protectedRoutes = ['/dashboard', '/my-competitions', '/settings'];
const authRoutes = ['/login', '/register'];

export function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;
  const authToken = request.cookies.get('auth_token')?.value;
  const platform = request.nextUrl.searchParams.get('platform');

  const isProtectedRoute = protectedRoutes.some((route) =>
    path.startsWith(route),
  );
  const isAuthRoute = authRoutes.some((route) => path.startsWith(route));

  const fullUrl = request.nextUrl.toString();

  // Always create a response object to store the current URL
  let res: NextResponse;

  // Case 1: User is not authenticated (and not on mobile)
  if (!authToken && platform !== 'mobile') {
    // Do not store redirect for auth routes (like /login or /register)
    if (!isAuthRoute) {
      res = isProtectedRoute
        ? NextResponse.redirect(new URL('/login', request.url))
        : NextResponse.next();

      // Store the attempted URL in a cookie for redirect after login
      res.cookies.set('redirect_url', fullUrl, {
        path: '/',
        maxAge: 300, // 5 minutes (adjust as needed)
      });
    } else {
      res = NextResponse.next(); // allow auth pages
    }
  }
  // Case 2: User is authenticated but visiting login/register
  else if (authToken && isAuthRoute) {
    res = NextResponse.redirect(new URL('/', request.url));
  }
  // Case 3: User is authenticated, allow access
  else {
    res = NextResponse.next();
  }

  // Store the current URL in a cookie for all requests
  res.cookies.set('current_url', fullUrl, {
    path: '/',
  });

  return res;
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico|public).*)'],
};
