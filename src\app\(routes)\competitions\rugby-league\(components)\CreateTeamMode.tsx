'use client';
import RugbyLeagueFantsyUI from '../(components)/RugbyLeagueFantsyUI';
import RugbyLeaguePlayerSelectionUI from '../(components)/RugbyLeaguePlayerSelectionUI';
import { cn, LocalStorage } from '@/lib/utils';
import { But<PERSON> } from '@/components/UI/button';
import CompetitionDetailsHeader from '@/components/Common/Competition/CompetitionDetailsHeader';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Token } from '../../../../../../db/db';
import { setApiMessage } from '@/helpers/commonFunctions';
import {
  LastEntryType,
  RugbyLeaguePlayersByRole,
  RugbyLeaguePlayersByRoleLimit,
  RugbyPlayer,
} from '../../../../../../types/rugby-league';
import { ALL_RUGBY_ROLES } from '@/helpers/constants/index';
import { useQueryClient } from '@tanstack/react-query';
import { quyerKeys } from '@/lib/queryKeys';
import { useSearchParams } from 'next/navigation';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { CheckedState } from '@radix-ui/react-checkbox';
import { useAuthContext } from '@/helpers/context/authContext';
import { useUserProfileContext } from '@/helpers/context/userContext';
import { Config } from '@/helpers/context/config';
import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';
import DreamTeamLoader from '@/components/Loading/DreamTeamLoader';

type CreateTeamModeProps = {
  handleTeamSubmitConfirmation: ({
    coins,
    bonusCoins,
  }: {
    coins: number;
    bonusCoins: number;
  }) => void;
  coins: number;
  bonusCoins: number;
  teamSubmitConfirmation: boolean;
  setTeamSubmitConfirmation: Dispatch<SetStateAction<boolean>>;
  activeTabPlayer: keyof RugbyLeaguePlayersByRole;
  setActiveTabPlayer: Dispatch<SetStateAction<keyof RugbyLeaguePlayersByRole>>;
  rugbyPlayersByRole: RugbyLeaguePlayersByRole;
  playerByRoleLimit: RugbyLeaguePlayersByRoleLimit;
  remainingBudget: number;
  lastEntry: LastEntryType;
  clearTeam: () => void;
  removePlayer: (
    playerId: number,
    role: keyof RugbyLeaguePlayersByRole,
  ) => void;
  showPlayerTabel: boolean;
};

const CreateTeamMode = ({
  handleTeamSubmitConfirmation,
  setTeamSubmitConfirmation,
  teamSubmitConfirmation,
  activeTabPlayer,
  setActiveTabPlayer,
  rugbyPlayersByRole,
  playerByRoleLimit,
  remainingBudget,
  lastEntry,
  clearTeam,
  removePlayer,
  showPlayerTabel,
}: CreateTeamModeProps) => {
  const { BAC, BR, FRF, HAL, IC } = rugbyPlayersByRole || {};
  const {
    BAC: BACLIMIT,
    BR: BRLIMIT,
    FRF: FRFLIMIT,
    HAL: HALLIMIT,
    IC: ICLIMIT,
  } = playerByRoleLimit || {};
  const queryClient = useQueryClient();
  const [acceptTerms, setAcceptTerms] = useState<CheckedState>(false);
  const [submitedSuccess, setSubmitedSuccess] = useState(false);

  const { setLoginPopUp } = useAuthContext();
  const { openReserveModal, setOpenReserveModal, createReservePlayerPayload, state: { reserveState: { reservePlayers = [null] } = {} } = {} } = useRugbyLeagueContext();

  const {
    rugbyLeaguePlayersByRole,
    eventDetailsResponse,
    refetchDreamTeam,
    isDreamTeamResponseLoading,
  } = useCompetition();
  const searchParams = useSearchParams();
  const dreamTeamId = searchParams.get('dreamTeamId');
  const event_id = searchParams.get('event_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const add_more = searchParams.get('add_more');
  const headerStats = {
    selectedPlayer: `Player ${BAC?.length + BR?.length + HAL?.length + FRF?.length + IC?.length}/${BACLIMIT + BRLIMIT + HALLIMIT + FRFLIMIT + ICLIMIT
      }`,
    remainingSalary: remainingBudget,
    totalScore: 200,
  };


  const localEventId = LocalStorage.getItem('event_id');
  const isSameEvent = localEventId === event_id;
  const localEventType = LocalStorage.getItem('event_type');
  const isSameEventType = localEventType === eventDetailsResponse?.result?.eventConfiguration.eventType;
  const playerId = searchParams.get('playerId');

  const values = useUserProfileContext();

  const testUserProfile = {
    nickName: values?.user?.nickName,
    profilePicUrl: Config.mediaURL! + values?.user?.Media?.filePath,
  };

  const testTeamData = {
    favouriteTeam: {
      players: ['Player 1', 'Player 2', 'Player 3'],
    },
    expertTeam: {
      players: ['Player 4', 'Player 5', 'Player 6'],
    },
  };

  const testEventDetails = {
    salaryCap: 5000000,
    eventName: 'Champions League Final',
  };

  const testCompetitionData = {
    currentRank: 10,
    totalRank: 100,
    totalScore: 300,
    totalLivePoints: 150,
  };

  const clearLastEntery = () => {
    const lastPlayer = lastEntry.players[lastEntry.players.length - 1];

    if (lastEntry?.mode === 'MANUAL' && lastPlayer) {
      removePlayer(lastPlayer?.player?.playerId, lastPlayer?.tabSection);
    }
    if (
      lastEntry.mode === 'EXPERT_FAVORITE' ||
      lastEntry.mode === 'FAVORITE' ||
      lastEntry.mode === 'FEELING_LUCKY'
    ) {
      clearTeam();
    }
  };

  const playersByRole = {
    BAC,
    BR,
    FRF,
    HAL,
    IC,
  };

  const playerLimits = {
    BACLIMIT,
    BRLIMIT,
    FRFLIMIT,
    HALLIMIT,
    ICLIMIT,
  };

  let captain: RugbyPlayer | undefined = undefined;
  let viceCaptain: RugbyPlayer | undefined = undefined;
  ALL_RUGBY_ROLES.forEach((role) => {
    playersByRole[role]?.forEach((player) => {
      if (player?.isCaiptain) {
        captain = player;
      }
      if (player?.isViceCaiptain) {
        viceCaptain = player;
      }
    });
  });

  const handelTeamCreation = () => {
    if (Token) {
      if (!captain) {
        setApiMessage('error', 'Please Select Captain');
      }

      if (!viceCaptain) {
        setApiMessage('error', 'Please Select Vice Captain');
      }
      if (captain && viceCaptain) {
        queryClient.refetchQueries({
          queryKey: [quyerKeys.getFantasyUser],
          exact: true,
        });

        if (
          eventDetailsResponse?.result?.eventConfiguration?.eventType ===
          'free' ||
          dreamTeamId
        ) {
          const reservePlayerPayload = reservePlayers.filter((player) => player !== null).map((player, index) => ({
            playerId: player?.playerId ?? 0,
            playerValue: player?.scoreData?.playerCurrentSalary ?? 0,
            reserve: true,
            reserveRank: player?.reserveRank ?? 0,
          }));
          createReservePlayerPayload(reservePlayerPayload);
          handleTeamSubmitConfirmation({
            coins: 0,
            bonusCoins: 0,
          });
        } else {
          // setOpenReserveModal(true);
          setTeamSubmitConfirmation(true);
        }
      }
    } else {
      // setOpenReserveModal(true);
      setLoginPopUp(true);
    }
  };



  useEffect(() => {
    if (!isSameEvent || !isSameEventType) {
      LocalStorage.removeItem('rugby_league_reserve_players');
      clearTeam();
    }
  }, [isSameEvent, event_id, isSameEventType, eventDetailsResponse]);




  const validTeamSelection =
    Object.values(playerLimits).reduce((a, b) => a + b, 0) ===
    Object.values(playersByRole).reduce((a, b) => a + b.length, 0);



  return (
    <div className="bg-off-white-200 md:px-[32px] md:py-[16px] p-0 mt-2">
      <CompetitionDetailsHeader
        stats={headerStats}
        status="team-creation"
        activeTab="Preview"
        userProfile={testUserProfile}
        teamData={testTeamData}
        eventDetails={testEventDetails}
        competitionData={testCompetitionData}
        playerLimits={{
          BAC: BACLIMIT,
          BR: BRLIMIT,
          FRF: FRFLIMIT,
          HAL: HALLIMIT,
          IC: ICLIMIT,
        }}
        playersByRole={{
          BAC,
          BR,
          FRF,
          HAL,
          IC,
        }}
        sportType="rugby"
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 h-full gap-x-2 gap-y-3 mt-2 max">
        <div className="w-full min-h-screen h-full">
          {isDreamTeamResponseLoading && dreamTeamId ? <DreamTeamLoader /> : <RugbyLeagueFantsyUI />}
        </div>
        <div className="h-full">
          <RugbyLeaguePlayerSelectionUI
            activeTab={activeTabPlayer}
            setActiveTab={setActiveTabPlayer}
            playerByRole={rugbyLeaguePlayersByRole}
            stats={headerStats}
          />
        </div>
      </div>
      {/* Bottom Action Buttons */}
      {!showPlayerTabel && !openReserveModal && !teamSubmitConfirmation && (
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center bg-white w-full">
          <div
            className={cn(
              'flex w-full space-x-2 pb-4 md:space-x-0  md:mt-0 justify-around items-center',
            )}
          >
            <div className="space-x-2 mt-5 flex flex-wrap gap-2 justify-center">
              <Button
                size="sm"
                variant="ghost"
                className=" !bg-[#335F83] text-white w-40"
                onClick={clearLastEntery}
              >
                Clear last entry
              </Button>
              <Button
                size="sm"
                className="w-40 "
                onClick={handelTeamCreation}
                disabled={!validTeamSelection}
              >
                {dreamTeamId ? 'Update Team' : 'Submit Team'}
              </Button>
              <Button
                size="sm"
                variant="link"
                className=" text-secondary-100 w-40 border-secondary-100 border"
                onClick={() => {
                  LocalStorage.removeItem('rugby_league_dream_team');
                  clearTeam();
                }}
              >
                Clear All
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateTeamMode;
