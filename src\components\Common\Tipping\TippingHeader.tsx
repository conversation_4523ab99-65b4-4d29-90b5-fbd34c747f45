export default function TippingHeader() {
  return (
    <div className="w-full max-w-[1350px] mx-auto">
      {/* Main content area */}
      <div className="grid grid-cols-3 place-items-center py-2 lg:max-h-[92px] max-h-[150px] bg-white rounded-t-md">
        {/* Adelaide Strikers */}
        <div className="flex items-center lg:flex-row flex-col lg:gap-4 gap-0">
          <div className="text-right hidden lg:block">
            <h2 className="text-[31.36px] font-apotekCompRegular font-bold text-black tracking-wide">
              ADELAIDE STRIKERS
            </h2>
          </div>
          <div className="w-16 h-16 relative">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
              <div className="text-white font-bold text-lg">AS</div>
            </div>
          </div>
          <div className="lg:hidden text-black-100 text-lg font-bold text-center">
            AD
          </div>
        </div>

        {/* Stats Circle */}
        <div className="flex items-center justify-center">
          <div className="relative w-[66px] h-[66px]">
            {/* Outer circle with segments */}
            <svg
              className="w-[66px] h-[66px] transform -rotate-90"
              viewBox="0 0 120 120"
            >
              {/* Background circle */}
              <circle
                cx="60"
                cy="60"
                r="54"
                fill="none"
                stroke="#e5e7eb"
                strokeWidth="12"
              />
              {/* Blue segment (77%) */}
              <circle
                cx="60"
                cy="60"
                r="54"
                fill="none"
                stroke="#3b82f6"
                strokeWidth="12"
                strokeDasharray={`${77 * 3.39} 339.29`}
                strokeLinecap="round"
              />
              {/* Pink segment (23%) */}
              <circle
                cx="60"
                cy="60"
                r="54"
                fill="none"
                stroke="#ec4899"
                strokeWidth="12"
                strokeDasharray={`${23 * 3.39} 339.29`}
                strokeDashoffset={`-${77 * 3.39}`}
                strokeLinecap="round"
              />
            </svg>

            {/* Center content */}
            <div className="absolute inset-0 flex flex-col items-center justify-center leading-none">
              <div className="text-blue-600 text-[10px] font-bold">77%</div>
              <div className="text-gray-600 text-[8px] font-medium">STATS</div>
              <div className="text-pink-500 text-[10px] font-bold">23%</div>
            </div>
          </div>
        </div>

        {/* Sydney Sixers */}
        <div className="flex items-center lg:flex-row flex-col lg:gap-4 gap-0">
          <div className="w-[65px] h-[65px] relative">
            <div className="w-[65px] h-[65px] bg-pink-500 rounded-full flex items-center justify-center">
              <div className="text-white font-bold text-lg">SS</div>
            </div>
          </div>
          <div className="text-left hidden lg:block">
            <h2 className="text-[31.36px] font-apotekCompRegular font-bold text-black tracking-wide">
              SYDNEY SIXERS
            </h2>
          </div>
          <div className="lg:hidden text-black-100 text-lg font-bold text-center">
            SY
          </div>
        </div>
      </div>

      {/* Bottom section */}
      <div className="bg-tipping-gradient h-[54px] rounded-b-md">
        <div className="text-center flex items-center justify-center h-full">
          <h3 className="text-white text-xl font-bold tracking-wider">
            ENTER YOUR TIP
          </h3>
        </div>
      </div>
    </div>
  );
}
