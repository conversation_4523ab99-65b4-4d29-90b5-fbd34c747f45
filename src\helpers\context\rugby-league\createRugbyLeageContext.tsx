import {
  createContext,
  useContext,
  useMemo,
  useReducer,
  useState,
} from 'react';
import {
  LuckyCreateTeamPayload,
  RugbyDreamTeamPlayer,
  RugbyFavoriteTeam,
  RugbyLeagueContextType,
  RugbyLeaguePlayersByRole,
  RugbyPlayer,
  RugbyTeamState,
  ReservePlayerPayload,
} from '../../../../types/rugby-league';
import { rugbyLeagueReducer } from '@/helpers/reducers/rugby-league/rugbyLeagueReduer';
import { setApiMessage } from '@/helpers/commonFunctions';
import { FantasyTeamResponse } from '../../../../types/competitions';
import { TeamFilterState } from '../../../../types';

const rugbyLeagueContext = createContext<RugbyLeagueContextType | undefined>(
  undefined,
);

const initialState: RugbyTeamState = {
  playerByRoleLimit: {
    BAC: 5,
    BR: 3,
    HAL: 2,
    FRF: 3,
    IC: 4,
  },
  playersByRole: {
    BAC: [],
    BR: [],
    FRF: [],
    HAL: [],
    IC: [],
  },
  lastEntry: {
    mode: 'MANUAL',
    players: [],
  },
  remainingBudget: 0,
  totalBudget: 0,
  reserveState: {
    reservePlayers: [null],
    reservePlayersLimit: 1,
  },
};

// Helper functions
const getTotalPlayers = (playersByRole: RugbyLeaguePlayersByRole): number => {
  return Object.values(playersByRole).reduce(
    (total, players) => total + players.length,
    0,
  );
};

const RugbyLeagueContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [state, dispatch] = useReducer(rugbyLeagueReducer, initialState);
  const [activeTabPlayer, setActiveTabPlayer] =
    useState<keyof RugbyLeaguePlayersByRole>('BAC');
  const [team, setTeam] = useState<TeamFilterState>({
    home: false,
    away: false,
  });

  const [showPlayerTabel, setShowPlayerTabel] = useState<boolean>(false);

  // Reserve modal and position state
  const [openReserveModal, setOpenReserveModal] = useState<boolean>(false);
  const [activePlayerPosition, setActivePlayerPosition] = useState<number>(0);
  const canAddReservePlayer = (player: RugbyPlayer) => {
    const isPlayerSelected = Object.values(state.playersByRole)
      .flat()
      .some((p) => p.playerId === player.playerId);

    if (isPlayerSelected) {
      return { canAdd: false, reason: 'Player already selected in team' };
    }

    // withing budget
    const price = player.scoreData?.playerCurrentSalary;
    if (price > state.remainingBudget) {
      return { canAdd: false, reason: 'Insufficient budget' };
    }

    return { canAdd: true };

  };

  const canAddPlayer = (
    player: RugbyPlayer,
    role: keyof RugbyLeaguePlayersByRole,
  ) => {
    // Check if player is already selected in any role
    const isPlayerSelected = Object.values(state.playersByRole)
      .flat()
      .some((p) => p.playerId === player.playerId);

    if (isPlayerSelected) {
      return { canAdd: false, reason: 'Player already selected in team' };
    }

    // Check total players limit
    if (getTotalPlayers(state.playersByRole) >= 17) {
      return { canAdd: false, reason: 'Team is full with 17 players' };
    }

    // // Check role-specific limit
    if (state.playersByRole[role].length >= state.playerByRoleLimit[role]) {
      return {
        canAdd: false,
        reason: `Maximum ${state.playerByRoleLimit[role]} ${role} player${state.playerByRoleLimit[role] > 1 ? 's' : ''
          } allowed`,
      };
    }

    // // Check budget
    const price = player.scoreData?.playerCurrentSalary;
    if (price > state.remainingBudget) {
      setApiMessage('error', 'Insufficient budget');
      return { canAdd: false, reason: 'Insufficient budget' };
    }

    return { canAdd: true };
  };

  const addPlayer = (
    player: RugbyPlayer,
    role: keyof RugbyLeaguePlayersByRole,
  ) => {
    const { canAdd, reason } = canAddPlayer(player, role);
    if (!canAdd) {
      setApiMessage('error', reason ?? '');
      return;
    }
    dispatch({ type: 'ADD_PLAYER', payload: { player, role } });
  };

  const removePlayer = (
    playerId: number,
    role: keyof RugbyLeaguePlayersByRole,
  ) => {
    dispatch({ type: 'REMOVE_PLAYER', payload: { playerId, role } });
  };

  const setPlayerRoleToCaptain = (
    playerId: number,
    role: keyof RugbyLeaguePlayersByRole,
  ) => {
    dispatch({ type: 'SET_CAPTAIN', payload: { playerId, role } });
  };

  const setPlayerRoleToViceCaiptain = (
    playerId: number,
    role: keyof RugbyLeaguePlayersByRole,
  ) => {
    dispatch({ type: 'SET_VICE_CAPTAIN', payload: { playerId, role } });
  };

  const createDreamTeam = (payload: {
    sportId: string | null;
    eventId: string | null;
    tournamentId: string | null;
    eventName: string | null;
    competitionId: string | null;
    name: string;
  }) => {
    dispatch({ type: 'CREATE_DREAM_TEAM', payload });
  };

  const getDreamTeam = (
    fantasyTeamResponse: FantasyTeamResponse,
    playerId?: number,
    role?: keyof RugbyLeaguePlayersByRole,
  ) => {
    dispatch({
      type: 'GET_DREAM_TEAM',
      payload: { fantasyTeamResponse, playerId, role },
    });
  };

  const clearTeam = () => {
    dispatch({ type: 'CLEAR_TEAM' });
  };

  const createLuckyTeam = (payload: LuckyCreateTeamPayload) => {
    dispatch({ type: 'CREATE_LUCKY_TEAM', payload });
  };

  const setBudget = (amount: number) => {
    dispatch({ type: 'SET_TOTAL_BALANCE', payload: { amount } });
  };

  const createFavouriteTeam = (payload: {
    favoriteTeam: RugbyFavoriteTeam;
    playersByRole: RugbyLeaguePlayersByRole;
  }) => {
    dispatch({
      type: 'CREATE_FAVOURITE_TEAM',
      payload,
    });
  };

  const createExpertTeam = (
    payload: RugbyDreamTeamPlayer[],
    playerByRole: RugbyLeaguePlayersByRole,
  ) => {
    dispatch({
      type: 'CREATE_EXPERT_TEAM',
      payload: { dreamPlayers: payload, playerByRole },
    });
  };

  // Reserve player logic
  const addReservePlayer = (player: RugbyPlayer, position: number) => {
    const { canAdd, reason } = canAddReservePlayer(player);
    if (!canAdd) {
      setApiMessage('error', reason ?? '');
      return;
    }
    dispatch({ type: 'ADD_RESERVE_PLAYER', payload: { player, position } });
  };
  const removeReservePlayer = (playerId: number, position: number) => {
    dispatch({ type: 'REMOVE_RESERVE_PLAYER', payload: { playerId, position } });
    setActivePlayerPosition(position);
  };
  const createReservePlayerPayload = (reservePlayerPayload: ReservePlayerPayload[]) => {
    dispatch({ type: 'CREATE_RESERVE_PLAYER_PAYLOAD', payload: { reservePlayerPayload } });
  };
  const clearReservePlayers = () => {
    dispatch({ type: 'CLEAR_RESERVE_PLAYERS' });
  };

  const rugbyLeagueContextValue = useMemo(
    (): RugbyLeagueContextType => ({
      state,
      activeTabPlayer,
      setActiveTabPlayer,
      addPlayer,
      removePlayer,
      showPlayerTabel,
      setShowPlayerTabel,
      clearTeam,
      setPlayerRoleToCaptain,
      setPlayerRoleToViceCaiptain,
      createDreamTeam,
      getDreamTeam,
      createLuckyTeam,
      setBudget,
      createFavouriteTeam,
      createExpertTeam,
      setTeam,
      team,
      // Reserve player API
      openReserveModal,
      setOpenReserveModal,
      activePlayerPosition,
      setActivePlayerPosition,
      addReservePlayer,
      removeReservePlayer,
      createReservePlayerPayload,
      clearReservePlayers,
    }),
    [
      state,
      activeTabPlayer,
      setActiveTabPlayer,
      addPlayer,
      removePlayer,
      showPlayerTabel,
      setShowPlayerTabel,
      clearTeam,
      setPlayerRoleToCaptain,
      setPlayerRoleToViceCaiptain,
      createDreamTeam,
      getDreamTeam,
      createLuckyTeam,
      setBudget,
      createFavouriteTeam,
      createExpertTeam,
      setTeam,
      team,
      openReserveModal,
      setOpenReserveModal,
      activePlayerPosition,
      setActivePlayerPosition,
      addReservePlayer,
      removeReservePlayer,
      createReservePlayerPayload,
      clearReservePlayers,
    ],
  );

  return (
    <rugbyLeagueContext.Provider value={rugbyLeagueContextValue}>
      {children}
    </rugbyLeagueContext.Provider>
  );
};

export const useRugbyLeagueContext = () => {
  const context = useContext(rugbyLeagueContext);
  if (context === undefined) {
    throw new Error(
      'useRugbyLeagueContext must be used within a RugbyLeagueContextProvider',
    );
  }
  return context;
};

export default RugbyLeagueContextProvider;
