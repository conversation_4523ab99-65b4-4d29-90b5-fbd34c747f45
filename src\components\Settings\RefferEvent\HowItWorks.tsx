import React from 'react';
import { Link } from 'lucide-react';
import UserPlusIcon from './UserPlusIcon';
import CoinStack from './CoinStack';
import RefferalLink from '@/components/Icons/Share/RefferalLink';
import RefferalFriend from '@/components/Icons/Share/RefferalFriend';
import Image from 'next/image';
import <PERSON>arn<PERSON><PERSON><PERSON><PERSON>oin from '../../../../public/images/earn-reffer-coin.png';

const HowItWorks = () => (
  <div className="bg-[#F3F4F5] rounded-xl p-6">
    <div className='mb-2 flex items-center justify-center'>
      <h2 className="text-[22.4px] text-center w-full">How it works</h2>
      <hr className='border border-black-400 w-full' />
    </div>
    <div className="mb-6 flex">
      <div className="mr-4 text-yellow-500">
        <RefferalLink />
      </div>
      <div>
        <h4 className="text-base">Invite your friends</h4>
        <p className="text-gray-600 text-xs">
          Share your unique referral link to invite friends to join the
          SmartPlay Fantasy Sports app.
        </p>
      </div>
    </div>
    <div className="mb-6 flex">
      <div className="mr-4 text-yellow-500">
        <RefferalFriend />
      </div>
      <div>
        <h4 className="text-base">Friend Purchases Smartcoins</h4>
        <p className="text-gray-600 text-xs">
          Your friend signs up using your link and buys SmartCoins to
          enter a competition.
        </p>
      </div>
    </div>
    <div className="flex">
      <div className="mr-4">
        <Image src={EarnRefferCoin} alt="Earn Reffer Coin" />
      </div>
      <div>
        <h4 className="text-base">Earn Bonus Coins</h4>
        <p className="text-gray-600 text-xs">
          You earn Bonus Coins equal to 10% of the total SmartCoins
          purchased by your friends.
        </p>
      </div>
    </div>
  </div>
);

export default HowItWorks; 