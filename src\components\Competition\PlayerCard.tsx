import { Tooltip } from '@material-tailwind/react';
import { Plus } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { type Dispatch, type SetStateAction } from 'react';

import {
  Menubar,
  MenubarContent,
  MenubarMenu,
  MenubarTrigger,
} from '@/components/UI/menubar';
import { useCompetition } from '@/helpers/context/competitionContext';
import {
  type PlayersByRole,
  useTeam,
} from '@/helpers/context/createTeamContext';
import useScreen from '@/hooks/useScreen';
import {
  cn,
  formatNumberWithCommas,
  getPlayerRoleType,
  getShortName,
} from '@/lib/utils';

import { getDefaultProfileImage } from '../../../db/db';
import type { Player } from '../../../types/competitions';
import { Badge } from '../UI/badge';
import { Card } from '../UI/card';
import PlayerLockIcon from '../UI/Icons/PlayerLockIcon';
import PlayerAvatar from '../UI/PlayerAvatar/indext';
import PlayerValueChange from './PlayerValueChange';
import { CompetitionStatusProps } from '../../../types';
import { getPlayerRole } from '../Common/Player/PlayerCardContent';
import { PUBLIC_IMAGES } from '@/lib/constants/constants';
import Image from 'next/image';

// PlayerScore.tsx
const PlayerScore: React.FC<{
  score: number;
  isPlayerLocked: boolean;
  player: Player;
  setPlayerRoleToCaptain: (playerId: number, role: keyof PlayersByRole) => void;
  setPlayerRoleToViceCaiptain: (
    playerId: number,
    role: keyof PlayersByRole,
  ) => void;
  activeTab: keyof PlayersByRole;
}> = ({
  score,
  isPlayerLocked,
  player,
  setPlayerRoleToCaptain,
  setPlayerRoleToViceCaiptain,
  activeTab,
}) => (
    <div className="md:text-xs text-[12px] absolute top-1 right-1 flex justify-end flex-col items-end">
      {score}
      {player.isCaiptain && (
        <button
          className="w-5 h-5 bg-[#FC4714] flex flex-col justify-center items-center text-white rounded-full"
          onClick={() => setPlayerRoleToCaptain(player.playerId, activeTab)}
          disabled={isPlayerLocked}
        >
          C
        </button>
      )}
      {player.isViceCaiptain && (
        <button
          className="w-5 h-5 bg-[#003764] flex flex-col justify-center items-center text-white rounded-full"
          onClick={() => setPlayerRoleToViceCaiptain(player.playerId, activeTab)}
          disabled={isPlayerLocked}
        >
          VC
        </button>
      )}
    </div>
  );

// MobileMenuContent.tsx
const MobileMenuContent: React.FC<{
  player: Player;
  dreamTeamId: string | null;
  add_more: string | null;
  onEditTeam: () => void;
  onSetCaptain: () => void;
  onSetViceCaptain: () => void;
  onRemove: () => void;
  isReserveType?: boolean;
}> = ({
  player,
  dreamTeamId,
  add_more,
  onEditTeam,
  onSetCaptain,
  onSetViceCaptain,
  onRemove,
  isReserveType,
}) => (
    <MenubarContent
      align="center"
      className="bg-transparent block md:hidden"
      sideOffset={-30}
    >
      <div className="bg-[#1e2c3a] rounded-lg p-3 flex gap-1 relative">
        <div className="flex justify-between items-start w-full">
          <div className="flex flex-col">
            <span className="text-white text-xs font-semibold">
              {player.name}
            </span>
            <span className="text-gray-400 text-[9px]">{player.teamName}</span>
            <span className="text-gray-400 text-[9px]">
              {getPlayerRole(player.role) === 'WKP'
                ? 'WKP/BAT'
                : getPlayerRole(player.role)}
            </span>
          </div>
        </div>
        <div className="w-full space-y-1">
          {!isReserveType && (
            <>

              <div className="flex justify-end space-x-1 w-full">
                <Badge
                  className={cn(
                    'text-white text-[9px] p-0 px-1 cursor-pointer',
                    dreamTeamId && add_more !== 'true'
                      ? 'bg-gray-200'
                      : 'bg-secondary-100',
                  )}
                  onClick={() => {
                    if (add_more === 'true') {
                      onSetCaptain();
                    }
                  }}
                >
                  Captain
                </Badge>
              </div>
              <div className="flex justify-end">
                <Badge
                  className={cn(
                    'text-white text-[9px] p-0 px-1 cursor-pointer',
                    dreamTeamId && add_more !== 'true'
                      ? 'bg-gray-200'
                      : 'bg-secondary-100',
                  )}
                  onClick={() => {
                    if (add_more === 'true') {
                      onSetViceCaptain();
                    }
                  }}
                >
                  Vice Captain
                </Badge>
              </div>
              <div className="flex justify-end">
                {dreamTeamId && add_more !== 'true' ? (
                  <Badge
                    className="text-white bg-secondary-100 text-[9px] p-0 px-1 cursor-pointer"
                    onClick={onEditTeam}
                  >
                    Substitute
                  </Badge>
                ) : (
                  <Badge
                    className="text-white bg-orange-400 text-[9px] p-0 px-1 cursor-pointer"
                    onClick={onRemove}
                  >
                    Remove
                  </Badge>
                )}
              </div>
            </>
          )}

          {isReserveType && (
            <div className="flex justify-end space-x-1 w-full">
              <Badge
                className="text-white bg-orange-400 text-[9px] p-0 px-1 cursor-pointer"
                onClick={onRemove}
              >
                Substitute
              </Badge>
            </div>
          )}
        </div>
      </div>
    </MenubarContent>
  );

// PlayerCardContent.tsx
const PlayerCardContent: React.FC<{
  player: Player;
  isPlayerLocked: boolean;
  playerCurrentSalary: number;
  playerLastSalary: number;
}> = ({ player, isPlayerLocked, playerCurrentSalary, playerLastSalary }) => (
  <div className="bg-white rounded-lg shadow p-3 md:w-40 w-[150px] border border-gray-100 h-[112px]">
    <div className="flex items-center gap-2 mt-2">
      <div className="w-full">
        <h3 className="font-semibold text-left md:text-sm text-xs truncate w-[95%]">
          <Tooltip
            content={player?.name}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            {getShortName(player.name)}
          </Tooltip>
        </h3>
        <p className="text-xs text-left text-gray-600 truncate">
          {player.teamName}
        </p>
        <div className="flex justify-between items-center flex-wrap mt-1">
          <span className="md:text-xs text-[9px] text-gray-500">
            {getPlayerRole(player.role) === 'WKP'
              ? 'WKP/BAT'
              : getPlayerRole(player.role)}
          </span>
          {!isPlayerLocked && (
            <PlayerValueChange
              formatToCustomStyle={formatNumberWithCommas}
              playerCurrentSalary={playerCurrentSalary}
              playerLastSalary={playerLastSalary}
            />
          )}
        </div>
      </div>
    </div>
  </div>
);

// Utility functions
const calculateScore = (
  player: Player,
  isPlayerLocked: boolean,
  eventStatus: CompetitionStatusProps | undefined,
): number => {
  let score = player?.scoreData?.livePoint ?? 0;

  if (isPlayerLocked) {
    return score;
  }

  if (eventStatus === 'finished') {
    score = player?.scoreData?.lastScore ?? 0;
    if (player?.isCaiptain) return score * 2;
    if (player?.isViceCaiptain) return score * 1.5;
  }

  return player?.scoreData?.lastScore ?? 0;
};

const calculateSalary = (player: Player, addMore: string | null) => {
  if (addMore === 'true') {
    return {
      current: player?.scoreData?.playerCurrentSalary ?? 0,
      last: player?.scoreData?.playerLastSalary ?? 0,
    };
  }
  return {
    current: player?.playerValue ?? 0,
    last: player?.scoreData?.playerCurrentSalary ?? 0,
  };
};

// Main PlayerCard component
const PlayerCard: React.FC<{
  player?: Player;
  tabSection: keyof PlayersByRole;
  setActiveTab: Dispatch<SetStateAction<keyof PlayersByRole>>;
  setShowPlayerTabel: Dispatch<SetStateAction<boolean>>;
  isPlayerLocked?: boolean;
  isActive?: boolean;
  setActivePlayerPosition?: Dispatch<SetStateAction<number>>;
  activePlayerPosition?: number;
  isReserveType?: boolean;
  setOpenReserveModal?: Dispatch<SetStateAction<boolean>>;
  playerIndex?: number;
}> = ({
  player,
  tabSection,
  setActiveTab,
  setShowPlayerTabel,
  isPlayerLocked = false,
  isActive,
  setActivePlayerPosition,
  activePlayerPosition,
  isReserveType = false,
  setOpenReserveModal,
  playerIndex,
}) => {
    const {
      removePlayer,
      activeTab,
      setPlayerRoleToCaptain,
      setPlayerRoleToViceCaiptain,
      removeReservePlayer,
    } = useTeam();
    const { eventDetailsResponse, refetchDreamTeam } = useCompetition();
    const searchParams = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();
    const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;


    if (!player) {
      return (
        <EmptyPlayerCard
          tabSection={tabSection}
          setActiveTab={setActiveTab}
          setShowPlayerTabel={setShowPlayerTabel}
          isActive={isActive}
          setActivePlayerPosition={setActivePlayerPosition}
          activePlayerPosition={activePlayerPosition}
          isReserveType={isReserveType}
          setOpenReserveModal={setOpenReserveModal}
          playerIndex={playerIndex}
          eventStatus={eventStatus}
        />
      );
    }

    const dreamTeamId = searchParams.get('dreamTeamId');
    const add_more = searchParams.get('add_more');
    const readOnly =
      (eventStatus === 'finished' ||
        eventDetailsResponse?.result?.dreamTeams?.length! > 0) &&
      add_more !== 'true';

    const score = calculateScore(player, isPlayerLocked, eventStatus);
    const salary = calculateSalary(player, add_more);

    const handleEditTeam = () => {
      const query = {
        event_id: searchParams.get('event_id'),
        sport_id: searchParams.get('sport_id'),
        tournament_id: searchParams.get('tournament_id'),
        dreamTeamId,
        competition_id: searchParams.get('competition_id'),
        add_more: 'true',
        seasonId: searchParams.get('seasonId'),
        playerId: `${player.playerId}`,
        role: tabSection,
      };
      refetchDreamTeam();
      // @ts-expect-error
      router.push(`${pathname}?${new URLSearchParams(query).toString()}`);
    };

    return (
      <Menubar className="border-0 bg-transparent" asChild>
        <MenubarMenu>
          <MenubarTrigger
            disabled={isPlayerLocked || (readOnly && eventStatus !== 'upcoming')}
          >
            <AnimatePresence>
              <motion.div
                className="box relative group"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
              >
                <div className="md:text-xs text-[12px] absolute top-1 left-1">
                  {isPlayerLocked ? (
                    <PlayerLockIcon />
                  ) : (
                    player?.scoreData?.avg?.toFixed(2) || 0
                  )}
                </div>

                <PlayerScore
                  score={score}
                  isPlayerLocked={isPlayerLocked}
                  player={player}
                  setPlayerRoleToCaptain={setPlayerRoleToCaptain}
                  setPlayerRoleToViceCaiptain={setPlayerRoleToViceCaiptain}
                  activeTab={activeTab}
                />

                <div className="absolute top-[-20px] left-1/2 -translate-x-1/2">
                  <div className="relative flex">
                    <PlayerAvatar
                      avatarUrl={player?.image ?? getDefaultProfileImage()}
                    />

                  </div>
                </div>

                <PlayerCardContent
                  player={player}
                  isPlayerLocked={isPlayerLocked}
                  playerCurrentSalary={salary.current}
                  playerLastSalary={salary.last}
                />

                <div className="absolute top-[-20px] left-1/2 -translate-x-1/2">
                  <div className="relative flex">
                    <PlayerAvatar
                      avatarUrl={player?.image ?? getDefaultProfileImage()}
                    />
                    <MobileMenuContent
                      player={player}
                      dreamTeamId={dreamTeamId}
                      add_more={add_more}
                      onEditTeam={handleEditTeam}
                      onSetCaptain={() => {
                        setPlayerRoleToCaptain(player.playerId, activeTab);
                      }}
                      onSetViceCaptain={() =>
                        setPlayerRoleToViceCaiptain(
                          player.playerId,
                          activeTab,
                        )
                      }
                      onRemove={() => {
                        if (isReserveType) {
                          removeReservePlayer(player.playerId, playerIndex ?? 0);
                          console.log(playerIndex, "playerIndex")
                        } else {
                          removePlayer(player.playerId, activeTab);
                        }
                      }}
                      isReserveType={isReserveType}
                    />
                  </div>
                </div>

                {eventStatus === 'upcoming' && (
                  <PlayerActions
                    showSubstituteButton={!!dreamTeamId && add_more !== 'true'}
                    showRemoveButton={
                      !eventDetailsResponse?.result?.dreamTeams ||
                      add_more === 'true'
                    }
                    dreamTeamId={dreamTeamId}
                    addMore={add_more === 'true'}
                    onSubstitute={handleEditTeam}
                    onSetCaptain={() =>
                      setPlayerRoleToCaptain(player.playerId, activeTab)
                    }
                    onSetViceCaptain={() =>
                      setPlayerRoleToViceCaiptain(player.playerId, activeTab)
                    }
                    onRemove={() => {
                      if (isReserveType) {
                        removeReservePlayer(player.playerId, playerIndex ?? 0);
                        console.log(playerIndex, "playerIndex")
                      } else {
                        removePlayer(player.playerId, activeTab);
                      }
                    }}
                    isReserveType={isReserveType}
                    add_more={add_more === 'true'}
                  />
                )}
              </motion.div>
            </AnimatePresence>
          </MenubarTrigger>
        </MenubarMenu>
      </Menubar>
    );
  };

// EmptyPlayerCard component
const EmptyPlayerCard: React.FC<{
  tabSection: keyof PlayersByRole;
  setActiveTab: Dispatch<SetStateAction<keyof PlayersByRole>>;
  setShowPlayerTabel: Dispatch<SetStateAction<boolean>>;
  isActive?: boolean;
  setActivePlayerPosition?: Dispatch<SetStateAction<number>>;
  activePlayerPosition?: number;
  isReserveType?: boolean;
  setOpenReserveModal?: Dispatch<SetStateAction<boolean>>;
  playerIndex?: number;
  eventStatus?: CompetitionStatusProps;
}> = ({ tabSection, setActiveTab, setShowPlayerTabel, isActive, setActivePlayerPosition, activePlayerPosition, isReserveType, setOpenReserveModal, playerIndex, eventStatus }) => {
  const { width } = useScreen();

  return (
    <AnimatePresence>
      <motion.div
        key="modal"
        initial={{ opacity: 1, scale: 1 }}
        animate={{ opacity: 1, scale: 1 }}
      >

        <Card className="p-4 bg-white shadow-sm border border-gray-100 rounded-lg md:w-40 w-[150px] relative h-20">
          <div className="flex justify-center items-centershadow-md rounded-full absolute top-[-15px] left-1/2 transform -translate-x-1/2">
            <Image
              src={PUBLIC_IMAGES.DEFAULT_PLAYER_IMAGE}
              alt="default player image"

            />
          </div>
          <div className="rounded-md absolute bottom-[5px] left-1/2 transform -translate-x-1/2 p-2 py-1">
            <button
              className="flex justify-center items-center space-x-1 mt-2"
              onClick={() => {
                if (isReserveType && eventStatus === 'upcoming') {
                  setOpenReserveModal?.(true);
                  return;
                }
                if (width <= 700) {
                  setShowPlayerTabel(true);
                }
                setActiveTab(tabSection);

              }}
            >
              <div
                className={cn(
                  'text-white rounded-md',
                  isActive ? 'bg-[#1C9A6C]' : 'bg-[#C9C9C9]',
                )}
              >
                <Plus size={30} />
              </div>
              {/* <span
                className={cn(isActive ? 'text-[#1C9A6C]' : 'text-[#636363]')}
              >
                Add
              </span> */}
            </button>
          </div>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};

// PlayerActions component
const PlayerActions: React.FC<{
  showSubstituteButton: boolean;
  showRemoveButton: boolean;
  dreamTeamId: string | null;
  addMore: boolean;
  onSubstitute: () => void;
  onSetCaptain: () => void;
  onSetViceCaptain: () => void;
  onRemove: () => void;
  isReserveType?: boolean;
  add_more: boolean;
}> = (
  {
    showSubstituteButton,
    showRemoveButton,
    addMore,
    onSubstitute,
    onSetCaptain,
    onSetViceCaptain,
    onRemove,
    isReserveType,
    add_more,
  },
) => (
    <div className="hidden md:block">
      <div
        className="hidden absolute -bottom-3 items-center justify-center left-1/2 -translate-x-1/2 group-hover:flex z-10"
        style={{
          width: 'max-content',
        }}
      >
        <div className="flex text-white text-base font-normal gap-x-1 justify-center">
          {
            !isReserveType && (
              <>
                {showSubstituteButton && (
                  <button
                    className="bg-secondary-100 px-2 rounded-md cursor-pointer"
                    style={{ width: 'max-content' }}
                    onClick={onSubstitute}
                  >
                    Substitute
                  </button>
                )}
                <button
                  className="bg-secondary-100 px-2 rounded-md cursor-pointer disabled:cursor-not-allowed"
                  style={{ width: 'max-content' }}
                  disabled={!addMore}
                  onClick={onSetCaptain}
                >
                  Captain
                </button>
                <button
                  className="bg-secondary-100 max-w-fit px-2 rounded-md cursor-pointer disabled:cursor-not-allowed"
                  style={{ width: 'max-content' }}
                  disabled={!addMore}
                  onClick={onSetViceCaptain}
                >
                  Vice-captain
                </button>
                {showRemoveButton && (
                  <button
                    className="bg-negative-200 max-w-fit px-2 rounded-md cursor-pointer disabled:cursor-not-allowed disabled:bg-[#C9C9C9]"
                    style={{ width: 'max-content' }}
                    onClick={onRemove}
                  >
                    Remove
                  </button>
                )}
              </>
            )
          }

          {
            isReserveType && !add_more && (
              <button
                className="bg-negative-200 px-2 rounded-md cursor-pointer disabled:cursor-not-allowed disabled:bg-[#C9C9C9]"
                style={{ width: 'max-content' }}
                onClick={onRemove}
              >
                Substitute
              </button>
            )
          }
        </div>
      </div>
    </div>
  );

export default PlayerCard;
