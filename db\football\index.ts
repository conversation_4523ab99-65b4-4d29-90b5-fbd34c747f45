import {
  FootballTournamentHeaderProps,
  PlayerArray,
} from '../../types/football';

export const mockFootballTournamentHeaderProps: FootballTournamentHeaderProps =
  {
    competitionStatus: 'upcoming',
    tournamentDetails: {
      prizePoolAmount: 5000, // Example prize pool amount
      tournamentType: 'paid', // Example tournament type
      entryCoin: '100', // Example entry coin
      minUserEntry: '1', // Minimum user entry
      userEntryCount: 25, // Example user entry count
      currentRank: '1', // Example current rank
      tournamentName: 'Champions League', // Example tournament name
      winningPrize: '2000', // Example winning prize
      drawPoolAvailable: true, // Example draw pool availability
      startTime: '2025-01-21T08:30:00.000Z', // Example start time in ISO format
    },
    isTournamentDetailsLoading: false, // Example loading state
  };

export const mockLeaderboardData = [
  {
    userId: 7,
    smartbUserId: 4,
    firstName: 'Mayur',
    lastName: 'Vekariya',
    nickName: 'MV',
    profileImage: 'uploads/1733990049442.jpg',
    id: 2208,
    competitionId: 175,
    name: 'Team 1',
    teamValue: 1959827,
    liveScore: 0,
    totalScore: 0,
    totalCurrentSalary: '1959827',
    rank: 1,
    winningPrice: 50,
    winnerPercentage: 50,
  },
];

export const footballTournamentMockData = {
  competitionStatus: 'ongoing', // or 'finished'
  tournamentDetails: {
    prizePoolAmount: 1000, // Total prize pool in coins
    tournamentType: 'paid', // 'free' or 'paid'
    userEntryCount: 25, // Number of teams entered
    currentRank: 5, // Current rank of the user
    tournamentName: 'Champions League', // Name of the tournament
    winningPrize: 500, // Prize for the winner
    drawPoolAvailable: true, // Whether a draw pool is available
    startTime: '2023-10-15T14:00:00Z', // Start time in UTC
  },
};
