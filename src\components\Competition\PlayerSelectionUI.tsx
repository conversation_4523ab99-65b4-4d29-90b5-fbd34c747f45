import { Icon<PERSON>utton } from '@material-tailwind/react';
import type { SortingState } from '@tanstack/react-table';
import { Search, SlidersHorizontal } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import { useRef, useState } from 'react';
import Select, { components } from 'react-select';
import { useClickAway } from 'react-use';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { Card } from '@/components/UI/card';
import { Input } from '@/components/UI/input';
import { useCompetition } from '@/helpers/context/competitionContext';
import { useTeam } from '@/helpers/context/createTeamContext';
import { useDataTable } from '@/hooks/useDataTable';
import useScreen from '@/hooks/useScreen';
import { cn, formatNumberWithCommas } from '@/lib/utils';

import type { PlayersByRoleType } from '../../../types/competitions';
import DataTable from '../UI/DataTabel';
import { Drawer, DrawerContent } from '../UI/drawer';
import SimpleTabs from '../UI/SimpleTabs';
import type { ProfileStats } from './CompetitionDetailsHeader';
import type { FiltersState } from './CricketPlayerFilter';
import CricketPlayerFilter from './CricketPlayerFilter';
import CricketPlayerFilterMobile from './CricketPlayerFilterMobile';
import {
  cricketCloumns,
  cricketReservedPlayerColumn,
} from '../UI/DataTabel/columns/createTeamColumn';
import ReservePlayerModal from '../Players/Reserve/ReservePlayerModal';
import { useSearchParams } from 'next/navigation';

export const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

type PlayerSelectionUI = {
  activeTab: keyof PlayersByRoleType;
  setActiveTab: Dispatch<SetStateAction<keyof PlayersByRoleType>>;
  stats: ProfileStats;
  playerByRole: PlayersByRoleType;
};

const PlayerSelectionUI = ({
  activeTab,
  setActiveTab,
  stats,
  playerByRole,
}: PlayerSelectionUI) => {
  const [showMobileFilter, setShowMobileFilter] = useState(false);
  const playerData: PlayersByRoleType = playerByRole;
  const {
    state: {
      playersByRole: { ALL, BAT, BOW, WKP },
    },
    showPlayerTabel,
    setShowPlayerTabel,
    showFilter,
    setShowFilter,
  } = useTeam();
  const { eventDetailsResponse, isPlayerListResponseLoading } = useCompetition();
  const { state } = useTeam();
  const filterRef = useRef(null);


  const getPlayerSelectionInstruction = (role: keyof PlayersByRoleType): string => {
    switch (role) {
      case 'ALL':
        return 'Select 1 All Rounder';
      case 'BAT':
        return 'Select 5 Batsman';
      case 'BOW':
        return 'Select 4 Bowlers';
      case 'WKP':
        return 'Select 1 Wicketkeeper';
    }
  };

  const isDreamTeam = eventDetailsResponse?.result?.dreamTeams?.length ?? 0;
  const searchParams = useSearchParams();
  const add_more = searchParams.get('add_more') === 'true';
  const [filters, setFilters] = useState<FiltersState>({
    matchesPlayed: false,
    priceRange: [0, 100],
    dualPosition: false,
    teams: {
      awayTeam: false,
      homeTeam: false,
    },
    breakeven: [0, 100],
    projectedScore: [0, 100],
    projectedValueChange: [6300, 9300],
    selectionPercentage: [0, 100],
  });

  const [selectedTeam, setSelectedTeam] = useState<string>('all');

  interface TabItem {
    id: keyof PlayersByRoleType;
    name: string;
    count: number;
    maxCount: number;
    seq: number;
  }

  const tabData =
    eventDetailsResponse?.result?.sportRule
      ?.map((rule) => {
        let tabId: keyof PlayersByRoleType;
        let seq: number;

        switch (rule.positionType) {
          case 'batsman':
            tabId = 'BAT';
            seq = 2;
            break;
          case 'allRounder':
            tabId = 'ALL';
            seq = 3;
            break;
          case 'bowler':
            tabId = 'BOW';
            seq = 4;
            break;
          case 'wicketKeeper':
            tabId = 'WKP';
            seq = 1;
        }

        const tabItemData: TabItem = {
          id: tabId,
          maxCount: rule.minPlayer,
          count: state?.playersByRole?.[tabId]?.length || 0,
          name: tabId,
          seq,
        };

        return tabItemData;
      })
      .sort((a, b) => a.seq - b.seq) || [];

  const sortOptions = [
    { value: 'player_asc', label: 'Sort Players A to Z' },
    { value: 'player_desc', label: 'Sort Players Z to A' },
    { value: 'team_asc', label: 'Sort Teams A to Z' },
    { value: 'team_desc', label: 'Sort Teams Z to A' },
  ];
  const { width } = useScreen();

  const { table } = useDataTable({
    columns: cricketCloumns,
    data: playerData[activeTab] || [],
  });

  const handleSortChange = (option: string): void => {
    const sorting: SortingState = [];
    switch (option) {
      case 'player_asc':
        sorting.push({ id: 'name', desc: false });
        break;
      case 'player_desc':
        sorting.push({ id: 'name', desc: true });
        break;
      case 'team_asc':
        sorting.push({ id: 'teamName', desc: false });
        break;
      case 'team_desc':
        sorting.push({ id: 'teamName', desc: true });
        break;
      default:
        break;
    }
    // Update sorting on the table and log it
    table.setSorting(sorting);
  };

  const sortedData = table.getSortedRowModel().rows.map((row) => row.original);

  const validTeamSelection =
    ALL?.length + BAT?.length + BOW?.length + WKP?.length === 11;

  // useClickAway(filterRef, () => {
  //   setFilters({
  //     matchesPlayed: false,
  //     priceRange: [0, 100],
  //     dualPosition: false,
  //     teams: {
  //       homeTeam: false,
  //       awayTeam: false,
  //     },
  //     breakeven: [0, 100],
  //     projectedScore: [0, 100],
  //     projectedValueChange: [6300, 9300],
  //     selectionPercentage: [0, 100],
  //   });

  //   setShowFilter(false);
  //   // table.resetColumnFilters();
  // });

  return (
    <div
      className={cn(
        'w-full max-w-4xl mx-auto relative hidden md:block !bg-none',
      )}
    >
      <div className="p-2 space-y-2">
        <div className="flex justify-between items-center space-x-2">
          <div className="relative w-[40%] hidden md:block">
            <Search
              className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 "
              size={16}
            />
            <Input
              placeholder="Search"
              className=" border-gray-100 pl-10"
              onChange={(e) => {
                table.getColumn('name')?.setFilterValue(e.target.value);
              }}
            />
          </div>
          <div className="flex items-center gap-2  w-[60%] md:w-fit justify-between md:justify-start sort-container">
            {/* <CommonSelect
              placeholder="Sort"
              options={sortOptions}
              onChange={handleSortChange}
            /> */}
            <Select
              className="min-w-[321px] md:min-w-[300px]"
              onChange={(e) => e?.value && handleSortChange(e?.value)}
              options={sortOptions}
              classNamePrefix="select"
              placeholder="Sort"
              isSearchable={false}
              styles={{
                control: (base) => ({
                  ...base,

                  boxShadow: 'none',
                  backgroundColor: 'transparent',
                  borderRadius: '10px',
                }),
                menu: (base) => ({
                  ...base,
                  borderRadius: '10px',
                }),
              }}
            />
            <div>
              <IconButton
                {...({} as any)}
                className="bg-secondary-100 hidden md:block"
                onClick={() => setShowFilter(!showFilter)}
              >
                <SlidersHorizontal />
              </IconButton>
              <IconButton
                {...({} as any)}
                className="bg-secondary-100 md:hidden block"
                onClick={() => setShowMobileFilter(!showMobileFilter)}
              >
                <SlidersHorizontal />
              </IconButton>
            </div>
          </div>
        </div>

        <div className="w-full">
          <SimpleTabs
            tabs={tabData}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
          />
        </div>

        {add_more && (
          <div className="text-sm text-slate-500 my-2 text-base">
            {getPlayerSelectionInstruction(activeTab)}
          </div>
        )}

        <div>
          <DataTable
            columns={cricketCloumns}
            data={sortedData}
            isLoading={isPlayerListResponseLoading}
            stickyColumns={width > 390 ? [] : [0]}
            initialColumnVisibility={{
              name: false,
              team: false,
              teamName: false,
            }}
          />
        </div>
      </div>


      <Drawer open={showPlayerTabel} onOpenChange={setShowPlayerTabel}>
        <DrawerContent className="bg-white">
          <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md">
            SELECT PlAYERS
          </div>
          <div className="p-2 space-y-2 mt-10  h-full ">
            <div className="flex justify-between items-center space-x-2">
              <Card className="p-3 shadow-sm w-1/2 bg-dark-card-gradient text-white">
                <div className="text-center">
                  <div className="text-xs text-[#BFCCD8] mb-1">
                    Remaining Salary:
                  </div>
                  <div className="font-semibold text-white">
                    ${formatNumberWithCommas(stats?.remainingSalary)}
                  </div>
                </div>
              </Card>

              {isDreamTeam > 0 ? (
                <Card className="p-3 shadow-sm w-1/2 bg-dark-card-gradient text-white">
                  <div className="text-center">
                    <div className="text-xs text-[#BFCCD8] mb-1">
                      Team Value:
                    </div>
                    <div className="font-semibold text-white">
                      ${formatNumberWithCommas(stats?.remainingSalary * 2)}
                    </div>
                  </div>
                </Card>
              ) : (
                <Card
                  className={cn(
                    'p-3 shadow-sm  text-white w-1/2',
                    validTeamSelection
                      ? 'bg-[#D1E7DF] border border-[#B4DBCD]'
                      : 'bg-dark-card-gradient',
                  )}
                >
                  <div className="text-center">
                    <div
                      className={cn(
                        'text-sm  mb-1',
                        validTeamSelection
                          ? 'text-black-700'
                          : 'text-slate-500',
                      )}
                    >
                      Selected Player:
                    </div>
                    <div
                      className={cn(
                        'font-semibold',
                        validTeamSelection
                          ? 'text-[#1C9A6C]'
                          : 'text-slate-900',
                      )}
                    >
                      {stats.selectedPlayer}
                    </div>
                  </div>
                </Card>
              )}
            </div>
            <div className="grid md:grid-cols-[60%_40%] grid-cols-1 gap-x-1 place-items-center">
              <div className="relative w-full">
                <Search
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 "
                  size={16}
                />
                <Input
                  placeholder="Search"
                  className="pl-8 border-gray-100 !py-4"
                  onChange={(e) => {
                    table.getColumn('name')?.setFilterValue(e.target.value);
                  }}
                />
              </div>
              <div className="flex items-center gap-2  w-full md:w-fit px-1 md:px-0 justify-between mt-2 md:mt-0 md:justify-start sort-container">
                <Select
                  className="min-w-[321px] md:min-w-[300px]"
                  onChange={(e) => e?.value && handleSortChange(e?.value)}
                  options={sortOptions}
                  classNamePrefix="select"
                  placeholder="Sort"
                  isSearchable={false}
                  styles={{
                    control: (base) => ({
                      ...base,
                      border: 'none',
                      boxShadow: 'none',
                      backgroundColor: 'transparent !important',
                      borderRadius: '10px',
                    }),
                    menu: (base) => ({
                      ...base,
                      borderRadius: '10px',
                    }),
                  }}
                />
                <div>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 hidden md:block"
                    onClick={() => setShowFilter(!showFilter)}
                  >
                    <SlidersHorizontal />
                  </IconButton>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 md:hidden block"
                    onClick={() => setShowMobileFilter(!showMobileFilter)}
                  >
                    <SlidersHorizontal />
                  </IconButton>
                </div>
              </div>
            </div>

            <div className="w-full">
              <SimpleTabs
                tabs={tabData}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                size="small"
              />
            </div>

            <div>
              <DataTable
                columns={cricketCloumns}
                data={sortedData}
                stickyColumns={[0]}
                initialColumnVisibility={{ name: false, teamName: false }}
                isLoading={isPlayerListResponseLoading}
              />
            </div>
          </div>
        </DrawerContent>
      </Drawer>
      {/* <div ref={filterRef}> */}
      {showFilter && (
        <div className="absolute top-14 right-0 w-full z-40 ">
          <CricketPlayerFilter
            playerTable={table}
            filters={filters}
            setFilters={setFilters}
            setShowFilter={setShowFilter}
            selectedTeam={selectedTeam}
            setSelectedTeam={setSelectedTeam}
            homeTeam={
              eventDetailsResponse?.result?.eventDetails?.homeTeam?.name
            }
            awayTeam={
              eventDetailsResponse?.result?.eventDetails?.awayTeam?.name
            }
          />
        </div>
      )}
      {/* </div> */}

      {showMobileFilter && (
        <div>
          <CricketPlayerFilterMobile
            open={showMobileFilter}
            setOpen={setShowMobileFilter}
            selectedTeam={selectedTeam}
            setSelectedTeam={setSelectedTeam}
            homeTeam={
              eventDetailsResponse?.result?.eventDetails?.homeTeam?.name
            }
            awayTeam={
              eventDetailsResponse?.result?.eventDetails?.awayTeam?.name
            }
          />
        </div>
      )}
    </div>
  );
};

export default PlayerSelectionUI;
