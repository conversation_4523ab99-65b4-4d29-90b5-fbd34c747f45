import { X, RotateCcw } from 'lucide-react';
import { Button } from '@/components/UI/button'
import SaveIcon from '@/components/Icons/Actions/SaveIcon'

type BottomActionProps = {
  onClearAll: () => void
  onClearLastEntry: () => void
  onSave: () => void
  onSubmitTeam: () => void
  disabledClearAll: boolean
  disabledClearLastEntry: boolean
  disabledSave: boolean
  disabledSubmitTeam: boolean
}

export default function BottomAction({ onClearAll, onClearLastEntry, onSave, onSubmitTeam, disabledClearAll, disabledClearLastEntry, disabledSave, disabledSubmitTeam }: BottomActionProps) {
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 flex w-full justify-center gap-2 bg-black-300 p-2 lg:p-4 max-h-[66px]">
      <Button
        variant="outline"
        className="flex h-[35px] w-[35px] min-w-[35px] items-center justify-center gap-2 bg-transparent p-0 lg:h-[44px] lg:w-[195px] lg:min-w-[195px] lg:p-2"
        onClick={onClearAll}
        disabled={disabledClearAll}
      >
        <X className="h-5 w-5" />
        <span className="hidden lg:inline">Clear All</span>
      </Button>
      <Button
        className="flex h-[35px] w-[35px] min-w-[35px] items-center justify-center gap-2 bg-transparent bg-primary-400 p-0 lg:h-[44px] lg:w-[195px] lg:min-w-[195px] lg:p-2"
        onClick={onClearLastEntry}
        disabled={disabledClearLastEntry}
      >
        <RotateCcw className="h-5 w-5" />
        <span className="hidden lg:inline">Clear Last Entry</span>
      </Button>
      <Button
        className="flex h-[35px] w-[35px] min-w-[35px] items-center justify-center gap-2 bg-transparent bg-success-100 p-0 lg:h-[44px] lg:w-[195px] lg:min-w-[195px] lg:p-2"
        onClick={onSave}
        disabled={disabledSave}
      >
        <SaveIcon />
        <span className="hidden lg:inline">Save</span>
      </Button>
      <Button
        className="flex h-[35px] w-[100px] min-w-[100px] items-center justify-center gap-2 bg-transparent bg-secondary-100 px-3 lg:h-[44px] lg:w-[195px] lg:min-w-[195px] lg:p-2"
        onClick={onSubmitTeam}
        disabled={disabledSubmitTeam}
      >
        <span className="inline">Submit team</span>
      </Button>
    </div>
  )
}
