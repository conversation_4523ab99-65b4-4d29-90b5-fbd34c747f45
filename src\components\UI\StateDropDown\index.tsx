'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import type { Control } from 'react-hook-form';
import { Controller } from 'react-hook-form';
import AsyncSelect from 'react-select/async';
import { useDebounce } from 'use-debounce';

import { Config } from '@/helpers/context/config';

interface State {
  id: string | number;
  state: string;
}

interface Option {
  id: string | number;
  label: string;
  value?: string | number;
}
const fetchCountries = async (
  offset: number,
  search: string,
  countryId: number | null,
): Promise<Option[]> => {
  if (!countryId) {
    // If countryId is not available, return an empty array or handle as needed
    return [];
  }

  try {
    const res = await fetch(
      Config.baseURL +
        `public/state/country/${countryId}?limit=20&offset=${offset}&search=${search}`,
    );
    if (!res.ok) {
      throw new Error('Network response was not ok');
    }
    const data = await res.json();

    return (
      data?.result?.rows?.map((state: State) => ({
        id: state.id,
        label: state.state,
        value: state.id,
      })) || []
    );
  } catch (error) {
    // Handle the error
    console.error('Error fetching countries:', error);
    throw error;
  }
};

const StateDropDown = ({
  name,
  control,
  countryId,
  placeholder,
  className,
  value,
  setValue,
}: {
  name: string;
  control: Control<any>;
  countryId: number;
  placeholder: string;
  className?: string;
  value: Option | undefined;
  setValue: Dispatch<SetStateAction<Option | undefined>>;
}) => {
  const [inputValue, setInputValue] = useState('');
  const [debouncedSearch] = useDebounce(inputValue, 300);
  const [offset, setOffset] = useState(0);
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isPending } =
    useInfiniteQuery({
      queryKey: ['async-countries', debouncedSearch, countryId],
      queryFn: ({ pageParam = 0 }) =>
        fetchCountries(pageParam, debouncedSearch, countryId),
      initialPageParam: 0,
      getNextPageParam: (lastPage, allPages) => {
        if (!lastPage || lastPage.length < 20) return undefined;
        return allPages.length * 20;
      },
    });

  useEffect(() => {
    setOffset(0); // Reset offset when search changes
  }, [debouncedSearch]);

  // Function to load options for AsyncSelect
  const loadOptions = async (inputValue: string): Promise<Option[]> => {
    const result = await fetchCountries(0, inputValue, countryId);
    return result;
  };

  return (
    <div className="w-full">
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <AsyncSelect
            isDisabled={!countryId}
            classNamePrefix="select"
            className={className}
            isLoading={isFetchingNextPage || isPending}
            {...field}
            styles={{
              container: (provided) => ({
                ...provided,
              }),
              valueContainer: (provided) => ({
                ...provided,
                overflow: 'visible',
              }),
              // @ts-expect-error
              placeholder: (provided, state) => ({
                ...provided,
                position: 'absolute',
                top:
                  state.hasValue ||
                  state.selectProps.inputValue ||
                  state.selectProps.menuIsOpen
                    ? -14
                    : 'auto',
                backgroundColor:
                  state.hasValue ||
                  state.selectProps.inputValue ||
                  state.selectProps.menuIsOpen
                    ? 'white'
                    : 'transparent',
                transition: 'top 0.2s, font-size 0.1s !important',
                fontSize:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '12px !important',
                color: state.selectProps.menuIsOpen ? '#4455c7' : '#a4a4a4',
                padding:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '0px 3px',
                paddingLeft:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '1px !important',
                marginLeft:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '7px !important',
                lineHeight:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '8px !important',
              }),
            }}
            placeholder={placeholder}
            onInputChange={(value) => setInputValue(value)}
            loadOptions={loadOptions}
            defaultOptions={(countryId && data?.pages?.flat()) || []}
            onChange={(country) => {
              field.onChange(country?.id);
              setValue(country || undefined);
            }}
            value={value}
            onMenuScrollToBottom={() => {
              if (hasNextPage && !isFetchingNextPage) {
                fetchNextPage();
              }
            }}
          />
        )}
      />
    </div>
  );
};

export default StateDropDown;
