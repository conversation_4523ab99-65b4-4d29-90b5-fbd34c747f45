'use client';
import './transactions.scss';

import { <PERSON><PERSON>, IconButton } from '@material-tailwind/react';
import { ArrowLeftIcon, ArrowRightIcon } from 'lucide-react';
import moment from 'moment-timezone';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import type { DateRange } from 'react-day-picker';
import Select, { components } from 'react-select';
import Arrow from '@/assets/images/icons/blueUparrow.svg';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import SmartBCoins from '@/assets/images/settings/smartBCoins.png';
import TransactionsFilter from '@/assets/images/settings/TransactionsFilter.svg';
import Loader from '@/components/Loader';
import TransactionFilterModalPage from '@/components/Settings/Transactions/TransactionsFilterModal';
import CustomDialog from '@/components/UI/CustomDialog';
import MasterCardIcon from '@/components/UI/Icons/MasterCardIcon';
import VisaIcon from '@/components/UI/Icons/VisaIcon';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { Config } from '@/helpers/context/config';
import { DatePickerWithRange } from './DatePickerWithRange';
import { cn, generateUniqueId } from '@/lib/utils';

const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const monthOption = [
  { value: 'All', label: 'All' },
  { value: '3 months', label: 'Last 3 months' },
  { value: '6 months', label: 'Last 6 months' },
  { value: '1 year', label: 'Last one year' },
];

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const TransactionsPage = () => {
  const [activeTab, setActiveTab] = useState<string>('SmartCoins Activity');
  const [openRows, setOpenRows] = useState<number[]>([]); // Track multiple open rows by ID
  const [screenWidth, setScreenWidth] = useState(0);
  const [selectedMonth, setSelectedMonth] = useState('All');
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [historyData, setHistoryData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [historyCount, setHistoryCount] = useState(0);
  const [offset, setOffset] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [dateFrom, setDateFrom] = useState<string | null>('');
  const [dateTo, setDateTo] = useState<string | null>(null);
  const [date, setDate] = React.useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  });
  const rowPerPage = 20;

  const renderPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5; // Number of pages to display at a time
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(historyCount, startPage + maxPagesToShow - 1);

    if (startPage > 1) {
      pageNumbers.push(
        <IconButton
          key={1}
          {...getItemProps(1)}
          className={`${currentPage === 1 ? 'bg-secondary-100 rounded-full' : ''}`}
        >
          1
        </IconButton>,
      );
      if (startPage > 2) {
        pageNumbers.push(<span key="start-ellipsis">...</span>);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(
        <IconButton
          key={i}
          {...getItemProps(i)}
          className={`${currentPage === i ? 'bg-secondary-100 rounded-full' : ''}`}
        >
          {i}
        </IconButton>,
      );
    }

    if (endPage < historyCount) {
      if (endPage < historyCount - 1) {
        pageNumbers.push(<span key="end-ellipsis">...</span>);
      }
      pageNumbers.push(
        <IconButton
          key={historyCount}
          {...getItemProps(historyCount)}
          className={`${currentPage === historyCount ? 'bg-secondary-100 rounded-full' : ''}`}
        >
          {historyCount}
        </IconButton>,
      );
    }

    return pageNumbers;
  };

  const handleOpenDialog = () => {
    setIsDialogOpen(true);
    setDateFrom(null);
    setDateTo(null);
    setSelectedMonth('All');
  };
  const handleCloseDialog = () => setIsDialogOpen(false);

  useEffect(() => {
    // This will only run on the client, not on the server
    setScreenWidth(window.innerWidth);

    // Optional: Add event listener to handle screen resize
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup the event listener on component unmount
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const tabs = ['SmartCoins Activity', 'Withdrawals'];

  const toggleCollapse = (id: number) => {
    if (openRows?.includes(id)) {
      // If row is already open, close it by removing its ID from the array
      setOpenRows(openRows?.filter((rowId) => rowId !== id));
    } else {
      // Otherwise, open it by adding its ID to the array
      setOpenRows([...openRows, id]);
    }
  };

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
    setDateFrom(null);
    setDateTo(null);
    setSelectedMonth('All');
    setDate({
      // @ts-expect-error
      from: null,
      // @ts-expect-error
      to: null,
    });
  };

  useEffect(() => {
    fetchHistoryData(
      0,
      activeTab === 'SmartCoins Activity' ? 'fantasy' : 'fantasyWithdraw',
      null,
      null,
    );
  }, [activeTab]);

  const handleSelectMonthChanges = (selectValue: any) => {
    setSelectedMonth(selectValue);
  };

  useEffect(() => {
    let months: number | null;
    switch (selectedMonth) {
      case '3 months':
        months = 3;
        break;
      case '6 months':
        months = 6;
        break;
      case '1 year':
        months = 12;
        break;
      default:
        months = null;
    }

    const toDate = selectedMonth === 'All' ? null : new Date(); // Today's date
    const fromDate =
      selectedMonth === 'All'
        ? null
        : new Date()?.setMonth(new Date()?.getMonth() - months!); // Subtract specified months

    setDateFrom(fromDate ? moment(fromDate).format('YYYY-MM-DD') : null);
    setDateTo(toDate ? moment(toDate).format('YYYY-MM-DD') : null);
    setDate({
      // @ts-expect-error
      from: selectedMonth === 'All' ? null : fromDate,
      // @ts-expect-error
      to: selectedMonth === 'All' ? null : toDate,
    });
  }, [selectedMonth]);

  const fetchHistoryData = async (
    offset: any,
    paymentBase: any,
    startDate: any,
    endDate: any,
  ) => {
    setIsLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${Config.baseURL}user/payment/history?limit=${rowPerPage}&offset=${offset}&paymentBase=${paymentBase ?? ''
        }&paymentType=&timeZone=${timezone}&startDate=${startDate ?? ''}&endDate=${endDate ?? ''}`,
      );

      if (status === 200) {
        const resultArray = processArray(data?.result);
        setHistoryData(resultArray);
        setHistoryCount(Math.ceil(data?.count / rowPerPage));
      }
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const processArray = (dataArray: any) => {
    return dataArray?.map((obj: any) => {
      const updatedObj = { ...obj }; // Create a shallow copy of the object

      // Parse `paymentDescription` if it's a string
      if (typeof updatedObj?.paymentDescription === 'string') {
        try {
          updatedObj.paymentDescription = JSON.parse(
            updatedObj.paymentDescription,
          );
        } catch (e) {
          console.error('Error parsing paymentDescription:', e); // Log the error
        }
      }

      return updatedObj;
    });
  };

  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  });

  const getItemProps = (index: any) =>
    ({
      variant: currentPage === index ? 'filled' : 'text',
      color: 'gray',
      onClick: () => {
        setCurrentPage(index);
        fetchHistoryData(
          (Number(index) - 1) * rowPerPage,
          activeTab === 'SmartCoins Activity' ? 'fantasy' : 'fantasyWithdraw',
          dateFrom,
          dateTo,
        );
        setOffset((Number(index) - 1) * rowPerPage);
      },
      className: 'rounded-full',
    }) as any;

  const next = () => {
    if (currentPage === 5) return;
    setCurrentPage(currentPage + 1);
    fetchHistoryData(
      (Number(currentPage + 1) - 1) * rowPerPage,
      activeTab === 'SmartCoins Activity' ? 'fantasy' : 'fantasyWithdraw',
      dateFrom,
      dateTo,
    );
    setOffset((Number(currentPage + 1) - 1) * rowPerPage);
  };

  const prev = () => {
    if (currentPage === 1) return;

    setCurrentPage(currentPage - 1);

    fetchHistoryData(
      (Number(currentPage - 1) - 1) * rowPerPage,
      activeTab === 'SmartCoins Activity' ? 'fantasy' : 'fantasyWithdraw',
      dateFrom,
      dateTo,
    );

    setOffset((Number(currentPage - 1) - 1) * rowPerPage);
  };

  return (
    <div>
      <h4 className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[16px] font-inter font-semibold text-black-100 capitalize">
        Transactions
      </h4>
      <div className="mt-[28px] flex items-center justify-between flex-wrap gap-y-1.5">
        <div>
          <div className="flex items-center gap-[3px] rounded-lg bg-black-300 p-[3px]">
            {tabs?.map((tab, index) => (
              <button
                key={generateUniqueId()}
                className={`px-[9px] py-[6px] cursor-pointer ${activeTab === tab ? 'bg-primary-200 rounded-[6px]' : ''
                  }`}
                onClick={() => handleTabClick(tab)}
              >
                <p
                  className={`text-[16px] leading-[19px] font-inter font-medium ${activeTab === tab ? 'text-white' : 'text-primary-200'
                    }`}
                >
                  {tab}
                </p>
              </button>
            ))}
          </div>
        </div>
        {screenWidth > 799 ? (
          <div className="flex items-center gap-1.5">
            <div className="min-w-[129px] max-w-[129px]">
              <Select
                className="React desktop-odds-select"
                value={monthOption?.find((item: any) => {
                  return item?.value === selectedMonth;
                })}
                onChange={(e: any) => handleSelectMonthChanges(e?.value)}
                options={monthOption}
                classNamePrefix="select"
                placeholder="Odds"
                isSearchable={false}
                components={{ DropdownIndicator }}
              />
            </div>
            <div>
              <DatePickerWithRange
                setDateFrom={setDateFrom}
                setDateTo={setDateTo}
                setDate={setDate}
                date={date}
                activeTab={activeTab}
                fetchHistoryData={fetchHistoryData}
              />
            </div>
            {/* <div className="w-[192px]">
                <SearchAutocomplete
                  query={query}
                  suggestions={suggestions}
                  placeholder="Search transaction"
                  onQueryChange={handleQueryChange}
                  onSuggestionClick={handleSuggestionClick}
                  onClearSearch={handleClearSearch}
                />
              </div> */}
          </div>
        ) : (
          <button className="cursor-pointer" onClick={() => handleOpenDialog()}>
            <TransactionsFilter />
          </button>
        )}
      </div>

      {isLoading && (
        <div className="mt-1.5 ">
          <Loader />
        </div>
      )}

      {historyData?.length > 0 && screenWidth > 799 && (
        <div className="table-container mt-[9px]">
          <div className="overflow-x-auto rounded-lg shadow-[0px_1px_3px_0px_#0000002b]">
            <table className="min-w-full ">
              <thead>
                <tr className="bg-black-300 text-left">
                  <th className="py-[12px] px-[27px] text-[16px] leading-[19px] font-semibold font-inter text-primary-200">
                    <div className="flex items-center gap-1 ">Date </div>
                  </th>
                  <th className="py-[12px] px-[12px] text-[16px] leading-[19px] font-semibold font-inter text-primary-200">
                    Invoice
                  </th>
                  <th className="py-[12px] px-[12px] text-[16px] leading-[19px] font-semibold font-inter text-primary-200">
                    SmartCoins
                  </th>
                  <th className="py-[12px] px-[12px] text-[16px] leading-[19px] font-semibold font-inter text-primary-200">
                    {activeTab === 'Withdrawals' ? 'Debit' : 'Credit/Debit'}
                  </th>
                  <th className="py-[12px] px-[12px] text-[16px] leading-[19px] font-semibold font-inter text-primary-200"></th>
                </tr>
              </thead>
              <tbody>
                {historyData?.map((row: any, index) => {
                  const renderCoins = () => {
                    if (activeTab === 'Withdrawals') {
                      if (row?.coins) {
                        return (
                          <div className="flex items-center gap-1">
                            <Image
                              src={SmartBCoins}
                              alt="coins"
                              unoptimized={true}
                            />
                            {row?.coins}
                          </div>
                        );
                      }
                      return null;
                    }

                    if (row?.SubscriptionPurchased?.SubscriptionPlan?.coins) {
                      return (
                        <div className="flex items-center gap-1">
                          <Image
                            src={SmartBCoins}
                            alt="coins"
                            unoptimized={true}
                          />
                          {row?.SubscriptionPurchased?.SubscriptionPlan?.coins}
                        </div>
                      );
                    }

                    if (row?.coins) {
                      return (
                        <div className="flex items-center gap-1">
                          <Image
                            src={SmartBCoins}
                            alt="coins"
                            unoptimized={true}
                          />
                          {row?.coins}
                        </div>
                      );
                    }

                    return null;
                  };

                  const getTransactionType = () => {
                    if (activeTab === 'Withdrawals') {
                      return 'Debit';
                    }

                    if (row?.paymentDescription?.status === 'credited') {
                      return 'Credit';
                    }

                    if (row?.status === 'refunded') {
                      return 'Refunded';
                    }

                    if (
                      row?.status === 'success' &&
                      row?.paymentType === null &&
                      row?.paymentDescription === null
                    ) {
                      return 'Credit';
                    }

                    if (
                      row?.status === 'success' &&
                      row?.paymentType !== null
                    ) {
                      return 'Credit';
                    }

                    return 'Debit';
                  };

                  const renderStatus = () => {
                    if (!row?.status) {
                      return null;
                    }

                    let statusClass = '';
                    let textClass = '';
                    let statusText = '';

                    switch (row.status) {
                      case 'pending':
                        statusClass = 'bg-negative-200';
                        textClass = 'text-negative-200';
                        statusText = 'Processing';
                        break;
                      case 'rejected':
                        statusClass = 'bg-negative-200';
                        textClass = 'text-negative-200';
                        statusText = 'Rejected';
                        break;
                      default:
                        statusClass = 'bg-success-100';
                        textClass = 'text-success-100';
                        statusText = 'Successful';
                        break;
                    }

                    return (
                      <div className="flex items-center gap-1">
                        <div
                          className={`${statusClass} w-[8px] h-[8px] rounded-full`}
                        ></div>
                        <span className={textClass}>{statusText}</span>
                      </div>
                    );
                  };

                  const renderPaymentDescription = () => {
                    if (row?.paymentDescription?.eventName) {
                      // For winning entries
                      if (row?.paymentDescription?.status === 'credited') {
                        return (
                          <>
                            <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                              <span className="font-semibold">
                                Competition:
                              </span>{' '}
                              {row?.paymentDescription?.eventName}
                            </div>
                            <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                              <span className="font-semibold">Position:</span>{' '}
                              {row?.paymentDescription?.rank &&
                                row?.paymentDescription?.teamName
                                ? '#' +
                                row?.paymentDescription?.rank +
                                ' ' +
                                `(${row?.paymentDescription?.teamName})`
                                : ''}
                            </div>
                            <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                              <span className="font-semibold">Winnings:</span>{' '}
                              {row?.coins ? (
                                <div className="flex items-center gap-1">
                                  <Image
                                    src={SmartBCoins}
                                    alt="coins"
                                    unoptimized={true}
                                  />
                                  {row?.coins}
                                </div>
                              ) : (
                                ''
                              )}
                            </div>
                          </>
                        );
                      }

                      // For entry details
                      return (
                        <>
                          <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                            <span className="font-semibold">Competition:</span>{' '}
                            {row?.paymentDescription?.eventName}
                          </div>
                          <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                            {row?.paymentDescription?.status !== 'refunded' && (
                              <span className="font-semibold">
                                Entry coins:
                              </span>
                            )}{' '}
                            {row?.paymentDescription?.entryCoin ? (
                              <div className="flex items-center gap-1">
                                <Image
                                  src={SmartBCoins}
                                  alt="coins"
                                  unoptimized={true}
                                />
                                {row?.paymentDescription?.entryCoin}
                              </div>
                            ) : (
                              ''
                            )}
                          </div>
                          {Number(row?.paymentDescription?.bonusCoins) > 0 && (
                            <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                              <span className="font-semibold">Bonus coins Paid:</span>{' '}
                              <div className="flex items-center gap-1">
                                <span className="text-negative-200">
                                  -
                                </span>
                                <Image
                                  src={SmartBCoins}
                                  alt="coins"
                                  unoptimized={true}
                                />
                                <span className={cn('text-negative-200')}>
                                  {row?.paymentDescription?.bonusCoins}
                                </span>
                              </div>
                            </div>
                          )}
                          <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                            <span className="font-semibold">
                              {row?.paymentDescription?.status === 'refunded'
                                ? 'SmartCoins Refunded:'
                                : 'SmartCoins Paid:'}
                            </span>{' '}
                            <div className={`${row?.paymentDescription?.status === 'debited'
                                ? 'text-negative-200'
                                : 'text-success-100'
                              } flex items-center gap-1 capitalize`}>
                              {row?.paymentDescription?.status === 'debited' && (
                                <span className="text-negative-200">-</span>
                              )}
                              <Image
                                src={SmartBCoins}
                                alt="coins"
                                unoptimized={true}
                              />
                              {row?.paymentDescription?.coins}
                            </div>
                          </div>
                        </>
                      );
                    }
                    return null;
                  };

                  const renderAmountAndCoins = () => {
                    if (row?.SubscriptionPurchased === null && row?.amount) {
                      return (
                        <>
                          <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                            <span className="font-semibold">Amount paid:</span>{' '}
                            {row?.amount ? formatter.format(row?.amount) : ''}
                          </div>
                          <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                            <span className="font-semibold">
                              SmartCoins received:
                            </span>{' '}
                            {row?.coins ? (
                              <div className="flex items-center gap-1">
                                <Image
                                  src={SmartBCoins}
                                  alt="coins"
                                  unoptimized={true}
                                />
                                {row?.coins}
                              </div>
                            ) : (
                              ''
                            )}
                          </div>
                        </>
                      );
                    }
                    return null;
                  };

                  const renderMembershipDetails = () => {
                    if (
                      row?.SubscriptionPurchased &&
                      !row?.paymentDescription?.eventName
                    ) {
                      return (
                        <>
                          <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                            <span className="font-semibold">Membership:</span>{' '}
                            {row?.SubscriptionPurchased?.SubscriptionPlan?.name}
                          </div>
                          <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                            <span className="font-semibold">Amount paid:</span>{' '}
                            {row?.SubscriptionPurchased
                              ? formatter.format(
                                row?.SubscriptionPurchased?.SubscriptionPlan
                                  ?.amount,
                              )
                              : ''}
                          </div>
                          <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                            <span className="font-semibold">
                              SmartCoins received:
                            </span>{' '}
                            {row?.SubscriptionPurchased?.SubscriptionPlan
                              ?.coins ? (
                              <div className="flex items-center gap-1">
                                <Image
                                  src={SmartBCoins}
                                  alt="coins"
                                  unoptimized={true}
                                />
                                {
                                  row?.SubscriptionPurchased?.SubscriptionPlan
                                    ?.coins
                                }
                              </div>
                            ) : (
                              ''
                            )}
                          </div>
                          <div className="text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                            <span className="font-semibold">
                              Payment Method:
                            </span>{' '}
                            {row?.Card ? (
                              <>
                                <span className="Card-img">
                                  {row?.Card?.brand === 'VISA' ? (
                                    <VisaIcon />
                                  ) : (
                                    <MasterCardIcon />
                                  )}
                                </span>
                                <span className="Card-name">
                                  {row?.Card?.brand} Card ending in{' '}
                                  {row?.Card?.last4}
                                </span>
                              </>
                            ) : (
                              '-'
                            )}
                          </div>
                        </>
                      );
                    }
                    return null;
                  };

                  return (
                    <>
                      <tr
                        key={generateUniqueId()}
                        className="border-t border-black-300"
                      >
                        <td className="py-[12px] px-[27px] text-[16px] leading-[19px] font-normal font-inter text-black-100">
                          {row?.createdAt
                            ? moment(row?.createdAt).format(
                              'ddd DD/MM/YYYY hh:mma',
                            )
                            : ''}
                        </td>
                        <td className="py-[12px] px-[12px] text-[16px] leading-[19px] font-normal font-inter text-black-100">
                          {row?.id}
                        </td>
                        <td className="py-[12px] px-[12px] text-[16px] leading-[19px] font-normal font-inter text-black-100">
                          {renderCoins()}
                        </td>
                        <td className="py-[12px] px-[12px] text-[16px] leading-[19px] font-normal font-inter text-black-100">
                          {getTransactionType()}
                        </td>
                        <td className="py-[12px] px-[12px] text-[16px] leading-[19px] font-normal font-inter text-black-100">
                          <button
                            className="text-blue-500 flex items-center gap-[3px]"
                            onClick={() => toggleCollapse(row?.id)}
                          >
                            Details{' '}
                            <Arrow
                              className={
                                openRows?.includes(row?.id)
                                  ? 'up-arrow'
                                  : 'down-arrow origin-center rotate-180'
                              }
                            />
                          </button>
                        </td>
                      </tr>
                      {openRows?.includes(row?.id) && (
                        <tr>
                          <td colSpan={5} className="pl-[27px] pr-3 pt-0 pb-3">
                            {renderPaymentDescription()}
                            {renderAmountAndCoins()}
                            {renderMembershipDetails()}
                          </td>
                        </tr>
                      )}
                    </>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {historyData?.length > 0 && screenWidth < 799 && (
        <div className="mt-3">
          {historyData?.map((row: any, index) => {
            const getTransactionType = () => {
              if (activeTab === 'Withdrawals') {
                return 'Debit';
              }

              if (row?.paymentDescription?.status === 'credited') {
                return 'Credit';
              }

              if (row?.status === 'refunded') {
                return 'Refunded';
              }

              if (
                row?.status === 'success' &&
                row?.paymentType === null &&
                row?.paymentDescription === null
              ) {
                return 'Credit';
              }

              if (row?.status === 'success' && row?.paymentType !== null) {
                return 'Credit';
              }

              return 'Debit';
            };

            const renderCoins = () => {
              if (activeTab === 'Withdrawals') {
                if (row?.coins) {
                  return (
                    <p className="flex items-center gap-1">
                      <Image src={SmartBCoins} alt="coins" unoptimized={true} />
                      <span className="text-[14px] leading-4 font-semibold font-inter text-black-100">
                        {row?.coins}
                      </span>
                    </p>
                  );
                }
                return <p className="flex items-center gap-1"></p>;
              }

              if (row?.SubscriptionPurchased?.SubscriptionPlan?.coins) {
                return (
                  <p className="flex items-center gap-1">
                    <Image src={SmartBCoins} alt="coins" unoptimized={true} />
                    <span className="text-[14px] leading-4 font-semibold font-inter text-black-100">
                      {row?.SubscriptionPurchased?.SubscriptionPlan?.coins}
                    </span>
                  </p>
                );
              }

              if (row?.coins) {
                return (
                  <p className="flex items-center gap-1">
                    <Image src={SmartBCoins} alt="coins" unoptimized={true} />
                    <span className="text-[14px] leading-4 font-semibold font-inter text-black-100">
                      {row?.coins}
                    </span>
                  </p>
                );
              }

              return <p className="flex items-center gap-1"></p>;
            };

            // Function to render status
            const renderStatus = () => {
              if (!row?.status) return null;

              let statusClass = '';
              let statusText = '';

              // Set classes and text based on the status
              if (row?.status === 'pending' || row?.status === 'rejected') {
                statusClass = 'bg-negative-200 text-negative-200';
                statusText = 'Rejected'; // Assuming 'rejected' should be shown for both pending and rejected
              } else {
                statusClass = 'bg-success-100 text-success-100';
                statusText = 'Successful';
              }

              // Return the JSX for status
              return (
                <div className="flex items-center gap-1">
                  <div
                    className={`${statusClass} w-[8px] h-[8px] rounded-full`}
                  ></div>
                  <span className={`${statusClass}`}>{statusText}</span>
                </div>
              );
            };

            // Function to handle the case when SubscriptionPurchased is null and row has amount
            const renderAmountAndCoins = () => {
              if (row?.SubscriptionPurchased === null && row?.amount) {
                return (
                  <>
                    <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                      <span className="font-semibold">Amount paid:</span>{' '}
                      {row?.amount ? formatter.format(row?.amount) : ''}
                    </div>
                    <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                      <span className="font-semibold">
                        SmartCoins received:
                      </span>{' '}
                      {row?.coins ? (
                        <div className="flex items-center gap-1">
                          <Image
                            src={SmartBCoins}
                            alt="coins"
                            unoptimized={true}
                          />
                          {row?.coins}
                        </div>
                      ) : (
                        ''
                      )}
                    </div>
                  </>
                );
              }
              return null;
            };

            // Function to handle the case when there is a payment description
            const renderPaymentDescription = () => {
              if (row?.paymentDescription?.eventName) {
                // For winning entries
                if (row?.paymentDescription?.status === 'credited') {
                  return (
                    <>
                      <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                        <span className="font-semibold">Competition:</span>{' '}
                        {row?.paymentDescription?.eventName}
                      </div>
                      <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                        <span className="font-semibold">Position:</span>{' '}
                        {row?.paymentDescription?.rank &&
                          row?.paymentDescription?.teamName
                          ? '#' +
                          row?.paymentDescription?.rank +
                          ' ' +
                          `(${row?.paymentDescription?.teamName})`
                          : ''}
                      </div>
                      <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                        <span className="font-semibold">Winnings:</span>{' '}
                        {row?.coins ? (
                          <div className="flex items-center gap-1">
                            <Image
                              src={SmartBCoins}
                              alt="coins"
                              unoptimized={true}
                            />
                            {row?.coins}
                          </div>
                        ) : (
                          ''
                        )}
                      </div>
                    </>
                  );
                }

                // For entry details
                return (
                  <>
                    <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                      <span className="font-semibold">Competition:</span>{' '}
                      {row?.paymentDescription?.eventName}
                    </div>
                    <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                      {row?.paymentDescription?.status !== 'refunded' && (
                        <span className="font-semibold">Entry coins:</span>
                      )}{' '}
                      {row?.paymentDescription?.entryCoin ? (
                        <div className="flex items-center gap-1">
                          <Image
                            src={SmartBCoins}
                            alt="coins"
                            unoptimized={true}
                          />
                          {row?.paymentDescription?.entryCoin}
                        </div>
                      ) : (
                        ''
                      )}
                    </div>
                    {Number(row?.paymentDescription?.bonusCoins) > 0 && (
                      <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                        <span className="font-semibold">Bonus coins Paid:</span>{' '}
                        <div className="flex items-center gap-1">
                          <span className="text-negative-200">
                            -
                          </span>
                          <Image
                            src={SmartBCoins}
                            alt="coins"
                            unoptimized={true}
                          />
                          <span className={cn('text-negative-200')}>
                            {row?.paymentDescription?.bonusCoins}
                          </span>
                        </div>
                      </div>
                    )}
                    <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                      <span className="font-semibold">
                        {row?.paymentDescription?.status === 'refunded'
                          ? 'SmartCoins Refunded:'
                          : 'SmartCoins Paid:'}
                      </span>{' '}
                      <div className={`${row?.paymentDescription?.status === 'debited'
                        ? 'text-negative-200'
                        : 'text-success-100'
                        } flex items-center gap-1 capitalize`}>
                        {row?.paymentDescription?.status === 'debited' && (
                          <span className="text-negative-200">-</span>
                        )}
                        <Image
                          src={SmartBCoins}
                          alt="coins"
                          unoptimized={true}
                        />
                        {row?.paymentDescription?.coins}
                      </div>
                    </div>
                  </>
                );
              }
              return null;
            };

            // Function to handle the case when there is no payment description and showing membership details
            const renderMembershipDetails = () => {
              if (!row?.paymentDescription?.eventName) {
                return (
                  <>
                    <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                      <span className="font-semibold">Membership:</span>{' '}
                      {row?.SubscriptionPurchased?.SubscriptionPlan?.name}
                    </div>
                    <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                      <span className="font-semibold">Amount paid:</span>{' '}
                      {row?.SubscriptionPurchased
                        ? formatter.format(
                          row?.SubscriptionPurchased?.SubscriptionPlan
                            ?.amount,
                        )
                        : ''}
                    </div>
                    <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                      <span className="font-semibold">
                        SmartCoins received:
                      </span>{' '}
                      {row?.SubscriptionPurchased?.SubscriptionPlan?.coins ? (
                        <div className="flex items-center gap-1">
                          <Image
                            src={SmartBCoins}
                            alt="coins"
                            unoptimized={true}
                          />
                          {row?.SubscriptionPurchased?.SubscriptionPlan?.coins}
                        </div>
                      ) : (
                        ''
                      )}
                    </div>
                    <div className="text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                      <span className="font-semibold">Payment Method:</span>{' '}
                      {row?.Card ? (
                        <>
                          <span className="Card-img">
                            {row?.Card?.brand === 'VISA' ? (
                              <VisaIcon />
                            ) : (
                              <MasterCardIcon />
                            )}
                          </span>
                          <span className="Card-name">
                            {row?.Card?.brand + ' '} Card ending in{' '}
                            {' ' + row?.Card?.last4}
                          </span>
                        </>
                      ) : (
                        '-'
                      )}
                    </div>
                  </>
                );
              }
              return null;
            };

            return (
              <div
                key={generateUniqueId()}
                className="p-3 rounded-lg bg-white mb-2 shadow-[0px_1px_3px_0px_#0000002b]"
              >
                <p className="text-[11.42px] leading-[14px] font-normal font-inter text-black-600 mb-1.5">
                  {getTransactionType()}
                </p>
                <div className="flex items-center justify-between">
                  {renderCoins()}

                  <p>
                    <button
                      className="text-sm text-blue-500 flex items-center gap-[3px]"
                      onClick={() => toggleCollapse(row?.id)}
                    >
                      Details{' '}
                      <Arrow
                        className={
                          openRows?.includes(row?.id)
                            ? 'up-arrow'
                            : 'down-arrow origin-center rotate-180'
                        }
                      />
                    </button>
                  </p>
                </div>
                <div className="mt-[9px] grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-[11.42px] leading-[14px] font-normal font-inter text-black-600 mb-1.5">
                      Date/time
                    </p>
                    <p className="text-[11.42px] leading-[14px] font-semibold font-inter text-black-100 mb-1.5">
                      {row?.createdAt
                        ? moment(row?.createdAt).format('ddd DD/MM/YYYY hh:mma')
                        : ''}
                    </p>
                  </div>
                  <div>
                    <p className="text-[11.42px] leading-[14px] font-normal font-inter text-black-600 mb-1.5">
                      Invoice
                    </p>
                    <p className="text-[11.42px] leading-[14px] font-semibold font-inter text-black-100 mb-1.5">
                      {row?.id}
                    </p>
                  </div>
                </div>
                {openRows?.includes(row?.id) &&
                  (activeTab === 'Withdrawals' ? (
                    <div className="mt-4">
                      <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                        <span className="font-semibold">Details:</span>
                      </div>
                      <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100  flex items-center gap-2">
                        <span className="font-semibold">SmartCoins:</span>{' '}
                        {row?.coins ? (
                          <div className="flex items-center gap-1">
                            <Image
                              src={SmartBCoins}
                              alt="coins"
                              unoptimized={true}
                            />
                            {row?.coins}
                          </div>
                        ) : (
                          ''
                        )}
                      </div>
                      <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                        <span className="font-semibold">
                          Withdrawal amount:
                        </span>{' '}
                        {'$' + (row?.coins * 0.05).toFixed(2)}
                      </div>
                      <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-start gap-2 flex-col">
                        <span className="font-semibold">Account details:</span>{' '}
                        {row?.paymentDescription ? (
                          <>
                            <div>
                              Account name -
                              {row?.paymentDescription?.accountHolderName ?? ''}
                            </div>
                            <div>
                              BSB - {row?.paymentDescription?.bsb ?? ''}
                            </div>
                            <div>
                              Account number -
                              {row?.paymentDescription?.accountNumber ?? ''}
                            </div>
                          </>
                        ) : (
                          ''
                        )}
                      </div>
                      <div className="text-[14px] leading-[16px] font-normal font-inter text-black-100 flex items-center gap-2">
                        <span className="font-semibold">Status:</span>{' '}
                        {renderStatus()}
                      </div>
                    </div>
                  ) : (
                    <div className="mt-4">
                      <div className="mb-2 text-[14px] leading-[16px] font-normal font-inter text-black-100">
                        <span className="font-semibold">Details:</span>
                      </div>
                      {renderPaymentDescription()}
                      {renderAmountAndCoins()}
                      {renderMembershipDetails()}
                    </div>
                  ))}
              </div>
            );
          })}
        </div>
      )}

      {!isLoading && !historyData && (
        <div className="mt-2 p-2 text-center">
          <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter text-black-100">
            No Data Available
          </p>
        </div>
      )}

      {!isLoading && historyData?.length > 0 ? (
        <div className="flex items-center justify-center mt-3 p-2 gap-4">
          <Button
            variant="text"
            className="flex items-center gap-2 rounded-full w-[40px] h-[40px] p-0 justify-center"
            onClick={prev}
            disabled={currentPage === 1}
            {...({} as any)}
          >
            <ArrowLeftIcon strokeWidth={2} className="h-4 w-4" />
          </Button>

          <div className="flex items-center gap-2">{renderPageNumbers()}</div>

          <Button
            {...({} as any)}
            variant="text"
            className="flex items-center gap-2 rounded-full w-[40px] h-[40px] p-0 justify-center"
            onClick={next}
            disabled={currentPage === historyCount}
          >
            <ArrowRightIcon strokeWidth={2} className="h-4 w-4" />
          </Button>
        </div>
      ) : null}

      <CustomDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        title="filters"
        maxWidth={390}
        className="transction-filter-modal"
        outerClickClose={true}
      >
        <div>
          <TransactionFilterModalPage
            activeTab={activeTab}
            handleClose={handleCloseDialog}
            setDateFrom={setDateFrom}
            setDateTo={setDateTo}
            dateFrom={dateFrom}
            dateTo={dateTo}
            setSelectedMonth={setSelectedMonth}
            selectedMonth={selectedMonth}
            fetchHistoryData={fetchHistoryData}
          />
        </div>
      </CustomDialog>
    </div>
  );
};

export default TransactionsPage;
