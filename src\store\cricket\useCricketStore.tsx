import { calculateRemainingBudget } from '@/lib/cricketUtils';
import {
  CricketPlayer,
  CricketTeamActions,
  CricketTeamState,
} from '@/lib/types/cricket';
import { create } from 'zustand';
import { combine, devtools } from 'zustand/middleware';

const initialCricketTeamState: CricketTeamState = {
  reserveState: {
    totalBudget: 1200000,
    remainingBudget: 1200000,
    reservePlayers: [],
    reservePlayersLimit: 4,
  },
};

const createCricketTeamActions = (
  set: (fn: (state: CricketTeamState) => Partial<CricketTeamState>) => void,
  get: () => CricketTeamState,
): CricketTeamActions => {
  return {
    addReservePlayer: (player: CricketPlayer) => {
      set((state) => {
        const updatedPlayers = [...state.reserveState.reservePlayers, player];
        return {
          reserveState: {
            ...state.reserveState,
            reservePlayers: updatedPlayers,
            remainingBudget: calculateRemainingBudget(
              state.reserveState.totalBudget,
              updatedPlayers,
            ),
          },
        };
      });
    },
    removeReservePlayer: (player: CricketPlayer) => {
      set((state) => {
        const updatedPlayers = state.reserveState.reservePlayers.filter(
          (p) => p.playerId !== player.playerId,
        );
        return {
          reserveState: {
            ...state.reserveState,
            reservePlayers: updatedPlayers,
            remainingBudget: calculateRemainingBudget(
              state.reserveState.totalBudget,
              updatedPlayers,
            ),
          },
        };
      });
    },
    setReservedRemainingBudget: (budget: number) => {
      set((state) => ({
        reserveState: {
          ...state.reserveState,
          remainingBudget: budget,
        },
      }));
    },
    setReservedTotalBudget: (budget: number) => {
      set((state) => ({
        reserveState: {
          ...state.reserveState,
          totalBudget: budget,
        },
      }));
    },
  };
};

export const useCricketStore = create(
  devtools(
    combine<CricketTeamState, CricketTeamActions>(
      initialCricketTeamState,
      createCricketTeamActions,
    ),
    {
      name: 'cricket-team',
    },
  ),
);
