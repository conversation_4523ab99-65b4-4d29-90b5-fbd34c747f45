'use client';

const VisaIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="30"
      height="20"
      viewBox="0 0 30 20"
    >
      <defs>
        <clipPath id="clip-path">
          <rect
            id="Rectangle_9132"
            data-name="Rectangle 9132"
            width="23.778"
            height="7.704"
            fill="none"
          />
        </clipPath>
      </defs>
      <g
        id="Group_32660"
        data-name="Group 32660"
        transform="translate(-0.007 0)"
      >
        <g
          id="Rectangle_9131"
          data-name="Rectangle 9131"
          transform="translate(0.007 0)"
          fill="#fff"
          stroke="#c3c3c3"
          strokeWidth="0.2"
        >
          <rect width="30" height="20" rx="1" stroke="none" />
          <rect
            x="0.1"
            y="0.1"
            width="29.8"
            height="19.8"
            rx="0.9"
            fill="none"
          />
        </g>
        <g
          id="Group_32655"
          data-name="Group 32655"
          transform="translate(2.923 6.25)"
        >
          <g
            id="Group_32654"
            data-name="Group 32654"
            clipPath="url(#clip-path)"
          >
            <path
              id="Path_29453"
              data-name="Path 29453"
              d="M7.009,4.864,5,9.938l-.819-4.3a.885.885,0,0,0-.921-.773H.041L0,5.1a7.939,7.939,0,0,1,1.35.389c.824.3.882.47,1.021,1.008l1.512,5.827H5.909L9.031,4.864Z"
              transform="translate(0 -4.745)"
              fill="#1434cb"
            />
            <path
              id="Path_29454"
              data-name="Path 29454"
              d="M339.346,4.864l-1.586,7.463h1.928l1.58-7.463Z"
              transform="translate(-329.502 -4.745)"
              fill="#1434cb"
            />
            <path
              id="Path_29455"
              data-name="Path 29455"
              d="M695.758,4.865h-1.625a.91.91,0,0,0-.892.684l-2.824,6.779h2.022l.391-1.13h2.463l.238,1.13h1.784Zm-2.368,4.817,1.006-2.8.6,2.8Z"
              transform="translate(-673.536 -4.746)"
              fill="#1434cb"
            />
            <path
              id="Path_29456"
              data-name="Path 29456"
              d="M633.452,0Z"
              transform="translate(-617.964)"
              fill="#1434cb"
            />
            <path
              id="Path_29457"
              data-name="Path 29457"
              d="M473.029,2.194c0-.307.369-.644,1.135-.644a3.856,3.856,0,0,1,1.568.357l.341-1.576A5.1,5.1,0,0,0,474.225,0c-1.688,0-3.2.875-3.2,2.492,0,1.854,2.676,1.982,2.676,2.914,0,.392-.449.743-1.217.743a4.041,4.041,0,0,1-1.9-.491l-.348,1.632a5.942,5.942,0,0,0,2.183.414c1.846,0,3.3-.918,3.3-2.563,0-1.959-2.687-2.084-2.687-2.948"
              transform="translate(-458.737)"
              fill="#1434cb"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};

export default VisaIcon;
