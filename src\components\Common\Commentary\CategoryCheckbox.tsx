'use client';

import { Check, Minus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CategoryCheckboxProps {
  id: string;
  label: string;
  checked: boolean;
  indeterminate?: boolean;
  onChange: (checked: boolean, indeterminate?: boolean) => void;
  isParent?: boolean;
  // Add new prop for controlling children
  onSelectAll?: (checked: boolean) => void;
}

export default function CategoryCheckbox({
  id,
  label,
  checked,
  indeterminate = false,
  onChange,
  isParent = false,
  onSelectAll,
}: CategoryCheckboxProps) {
  // Handle icon click for indeterminate state and select all functionality
  const handleIconClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent the checkbox input's onChange from firing

    // Determine the new state
    let newChecked = false;
    let newIndeterminate = false;

    if (indeterminate) {
      // If currently indeterminate, set to checked (select all)
      newChecked = true;
      newIndeterminate = false;
    } else if (checked) {
      // If currently checked, uncheck all
      newChecked = false;
      newIndeterminate = false;
    } else {
      // If currently unchecked, check all
      newChecked = true;
      newIndeterminate = false;
    }

    // Update this checkbox
    onChange(newChecked, newIndeterminate);

    // If this is a parent checkbox, propagate to children
    if (isParent && onSelectAll) {
      onSelectAll(newChecked);
    }
  };

  // Handle regular checkbox changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newChecked = e.target.checked;

    // Update this checkbox
    onChange(newChecked, false);

    // If this is a parent checkbox, propagate to children
    if (isParent && onSelectAll) {
      onSelectAll(newChecked);
    }
  };

  return (
    <div
      className={cn('flex items-center', !isParent && 'rounded-md')}
      style={{
        backgroundColor: checked && !isParent ? '#78C2A7' : 'transparent',
        padding: isParent && checked ? '4px' : '0',
        borderRadius: '6px',
      }}
    >
      <div className="relative flex items-center justify-center min-w-6 h-6 ml-2">
        <input
          type="checkbox"
          id={id}
          checked={checked}
          onChange={handleChange}
          className="peer sr-only"
          aria-checked={indeterminate ? 'mixed' : checked}
        />
        <div
          className="flex items-center justify-center w-6 h-6 rounded border cursor-pointer"
          style={{
            backgroundColor: checked || indeterminate ? '#78C2A7' : 'white',
            borderColor: checked || indeterminate ? '#78C2A7' : '#d1d5db',
          }}
          onClick={handleIconClick}
        >
          {indeterminate ? (
            <Minus className="h-4 w-4 text-white" />
          ) : checked ? (
            <Check className="h-4 w-4 text-white" />
          ) : null}
        </div>
      </div>
      <label
        htmlFor={id}
        className={cn(
          'flex-1 cursor-pointer py-2 px-4 text-xs',
          isParent ? 'font-bold' : '',
          checked && !isParent ? 'text-white' : '',
        )}
      >
        {label}
      </label>
    </div>
  );
}
