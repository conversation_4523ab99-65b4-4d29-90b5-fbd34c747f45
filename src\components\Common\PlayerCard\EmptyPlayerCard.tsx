import { Card } from '@/components/UI/card';
import { AnimatePresence, motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import Plus from '@/components/Icons/Plus/Plus';
import useScreen from '@/hooks/useScreen';
import DefaultPlayer from '@/components/Icons/DefaultPlayer/DefaultPlayer';

type EmptyPlayerCardProps<TRole extends string = string> = {
  tabSection: string;
  setActiveTab: (role: TRole) => void;
  setShowPlayerTabel: (show: boolean) => void;
  isActive?: boolean;
};

const EmptyPlayerCard = <TRole extends string = string>({
  tabSection,
  setActiveTab,
  setShowPlayerTabel,
  isActive,
}: EmptyPlayerCardProps<TRole>) => {
  const { width } = useScreen();

  const handleClick = () => {
    setActiveTab(tabSection as TRole);
    if (width <= 959) {
      setShowPlayerTabel(true);
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        key="empty-player-card"
        initial={{ opacity: 1, scale: 1 }}
        animate={{ opacity: 1, scale: 1 }}
      >
        <div className="flex md:w-[165px] w-[150px] justify-center mb-[-15px] z-10 relative">
          <DefaultPlayer />
        </div>

        <Card className="p-4 bg-white shadow-sm border border-gray-100 rounded-md md:w-[165px] w-[150px] relative h-20">
          <section className="flex justify-center items-center h-full">
            <div className="rounded-md flex justify-center items-center w-[30px] h-[30px]">
              <button
                className={cn(
                  'flex justify-center items-center space-x-1 w-[30px] h-[30px] rounded-md',
                  isActive ? 'bg-[#1C9A6C]' : 'bg-[#C9C9C9]',
                )}
                onClick={handleClick}
              >
                <div>
                  <Plus />
                </div>
              </button>
            </div>
          </section>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};

export default EmptyPlayerCard;
