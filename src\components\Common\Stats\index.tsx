'use client';

import SimpleTabs from '@/components/UI/SimpleTabs';
import { useState } from 'react';
import AFLTeamStats from './AFLTeamStats';
import AFLSmartPlayStats from './AFLSmartPlayStats';
import AFLPlayerStats from './AFLPlayerStats';
import { useStatsContext } from '@/helpers/context/stats';
import { useCompetition } from '@/helpers/context/competitionContext';

const AFLStats = () => {
  const tabs = [
    { id: 'team-stats', name: 'Team Stats' },
    { id: 'player-stats', name: 'Player Stats' },
    { id: 'smartplay-stats', name: 'SmartPlay Stats' },
  ];

  const { eventDetailsResponse } = useCompetition();

  const homeTeam =
    eventDetailsResponse?.result?.eventDetails?.homeTeam?.name ?? '';
  const awayTeam =
    eventDetailsResponse?.result?.eventDetails?.awayTeam?.name ?? '';

  const homeTeamLogo =
    eventDetailsResponse?.result?.eventDetails?.homeTeam?.flag ?? '';
  const awayTeamLogo =
    eventDetailsResponse?.result?.eventDetails?.awayTeam?.flag ?? '';

  const teamNames: [string, string] = [homeTeam, awayTeam];
  const teamColors: [string, string] = ['#003764', '#4455C7'];
  const [activeTab, setActiveTab] = useState('team-stats');
  const { aflTeamStats } = useStatsContext();
  return (
    <div className="bg-white">
      <div className="md:max-w-[600px] w-full mt-2 md:mt-0 md:px-8 px-0">
        <SimpleTabs
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          tabs={tabs}
        />
      </div>
      <div className="md:px-8 px-0">
        {activeTab === 'team-stats' && (
          <AFLTeamStats
            teamNames={teamNames}
            teamColors={teamColors}
            stats={aflTeamStats?.result?.data?.stats!}
            homeTeamLogo={homeTeamLogo}
            awayTeamLogo={awayTeamLogo}
          />
        )}
        {activeTab === 'smartplay-stats' && <AFLSmartPlayStats />}
        {activeTab === 'player-stats' && <AFLPlayerStats />}
      </div>
    </div>
  );
};

export default AFLStats;
