{
  "extends": ["next/core-web-vitals", "next/typescript"],
  "plugins": ["unicorn", "prettier", "simple-import-sort", "unused-imports"],
  "rules": {
    "prettier/prettier": [
      "error",
      {
        "endOfLine": "auto"
      }
    ],
    "unicorn/filename-case": [
      "error",
      {
        "cases": {
          "camelCase": true,
          "pascalCase": true,
          "kebabCase": true
        },
        "ignore": [".next"]
      }
    ]
  },
  "overrides": [
    {
      "files": ["**/*.ts", "**/*.tsx", "**/*.js"],
      "plugins": ["@typescript-eslint", "unused-imports", "simple-import-sort"],
      "extends": ["next/core-web-vitals", "next/typescript"],
      "parserOptions": {
        "project": "./tsconfig.json"
      },
      "rules": {
        "unicorn/filename-case": "off",
        "@typescript-eslint/ban-ts-comment": [
          "error",
          {
            "ts-expect-error": "allow-without-description" // Allows @ts-expect-error without description
          }
        ],
        "@typescript-eslint/no-explicit-any": "off",
        "react-hooks/exhaustive-deps": "off",
        "@typescript-eslint/no-shadow": "off",
        "prettier/prettier": [
          "error",
          {
            "endOfLine": "auto"
          }
        ],
        "react/no-unstable-nested-components": "off",
        "react/react-in-jsx-scope": "off",
        "react/destructuring-assignment": "off",
        "react/require-default-props": "off",
        "@typescript-eslint/comma-dangle": "off",
        "@typescript-eslint/consistent-type-imports": "error",
        "import/prefer-default-export": "off",
        "simple-import-sort/imports": "error",
        "simple-import-sort/exports": "error",
        "@typescript-eslint/no-unused-vars": "off",
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": [
          "error",
          {
            "argsIgnorePattern": "^_",
            "varsIgnorePattern": "^_",
            "caughtErrorsIgnorePattern": "^_"
          }
        ]
      }
    },
    {
      "files": [
        "**/__tests__/**/*.[jt]s?(x)",
        "**/?(*.)+(spec|test).[jt]s?(x)"
      ],
      "extends": ["plugin:testing-library/react"]
    }
  ]
}
