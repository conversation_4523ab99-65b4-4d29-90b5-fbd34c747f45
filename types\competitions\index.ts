import { ReservePlayerPayload } from '@/helpers/context/createTeamContext';
import type { CompetitionStatusProps } from '..';
import { footballPlayersByRole, FootBallRole } from '../football';
import { RugbyPlayer } from '../rugby-league';

// Define the nested types
export interface Team {
  id: number;
  name: string;
  gender: string;
  flag: string | null;
}

export interface CricketTournament {
  id: number;
  name: string;
  gender: string;
}

export type PrizePool = {
  winPlace: number;
  placeType: string;
  coins: number;
  description: string;
};

type DrawPool = {
  drawNumber: number;
  drawType: string;
  coins: number;
  description: string;
};

type EventConfiguration = {
  id: number;
  sportId: number;
  eventId: number;
  startTime: string;
  status: string;
  eventType: string;
  prizePool: PrizePool[];
  entryCoin: number;
  drawPool: DrawPool[];
  minUserEntry: number | null;
  createdAt: string;
  updatedAt: string;
  prizePoolFirst: number;
};

export type Event = {
  id: number;
  eventName: string;
  awayTeamId: number;
  homeTeamId: number;
  startTime: string;
  Eid: string;
  status: string;
  SportId: number;
  CricketTournamentId: number;
  awayTeam: Team;
  homeTeam: Team;
  CricketTournament: CricketTournament;
  eventConfiguration: EventConfiguration;
  liveScore?: number;
};

type EventSummary = {
  findCompletedEvents: number;
  upcomingEventCount: number;
  liveEventCount: number;
};

// Define the API response type
export interface EventResponse {
  status: boolean;
  message: string;
  result: Event[];
  eventCountData?: EventSummary;
  count: number;
}

type CricketSeason = {
  id: number;
  name: string;
  year: string;
  fantasy_sport_salary_cap: number;
};

export type RLTournament = {
  id: number;
  name: string;
  gender: 'M' | 'F' | 'Other'; // Assuming gender can have multiple values
};

export type ARTournament = {
  id: number;
  name: string;
  gender: 'M' | 'F' | 'Other'; // Assuming gender can have multiple values
};

export interface CompetitionResponse {
  status: boolean;
  message: string;
  result: {
    eventConfiguration: {
      id: number;
      sportId: number;
      eventId: number;
      startTime: string; // ISO Date string
      round: string | null;
      status: string;
      eventType: string;
      prizePool: number; // JSON string
      entryCoin: number;
      drawPool: string; // JSON string
      minUserEntry: number | null;
      prizePoolFirst: number;
      freeCompExist?: boolean;
      freeCompId?: number;
      freeCompAvailable?: boolean;
    };
    eventDetails: {
      id: number;
      eventName: string;
      awayTeamId: number;
      homeTeamId: number;
      startTime: string; // ISO Date string
      Eid: string;
      status: CompetitionStatusProps;
      SoccerTournament: {
        id: number;
        name: string;
        gender: string;
      };
      SoccerSeasonId?: number;
      SoccerTournamentId?: number;
      RLSeason?: {
        fantasy_sport_salary_cap: number;
      };
      SoccerSeason?: {
        fantasy_sport_salary_cap: number;
      };
      ARSeason: {
        fantasy_sport_salary_cap: number;
      };
      RLTournament?: RLTournament;
      ARTournament?: ARTournament;
      ARTournamentId: number;
      ARSeasonId: number;
      SportId: number;
      CricketTournamentId: number;
      CricketSeasonId?: number;
      RLTournamentId?: number;
      RLSeasonId?: number;
      awayTeam: {
        id: number;
        name: string;
        gender: string;
        flag: string | null;
      };
      homeTeam: {
        id: number;
        name: string;
        gender: string;
        flag: string | null;
      };
      CricketTournament: {
        id: number;
        name: string;
        gender: string;
      };
      CricketSeason: CricketSeason;
      ScoreBoard: ScoreBoard;
      winnerCode: number;
    };
    sportRule: {
      id: number;
      positionType: 'bowler' | 'wicketKeeper' | 'batsman' | 'allRounder';
      minPlayer: number;
      maxPlayer: number;
      positionId: number | null;
      SportId: number;
      createdAt: string; // ISO Date string
      updatedAt: string; // ISO Date string
    }[];
    teamSpend: [
      {
        id: number;
        positionType: string;
        totalSpent: string;
        average: string;
      },
    ];

    reservePlayersCount: number;

    prizePoolArray?: PrizePool[];
    dreamTeams: [{ id: number; name?: string; round?: string }];
    userEntry: number;
    currentRank?: number;
    totalRank?: number;
    totalSpendValue?: number;
    winningPrice?: number;
    name?: string;
  };
}

export type PlayerRoleType =
  | 'Batsman'
  | 'Batter'
  | 'Batting Allrounder'
  | 'WK-Batter'
  | 'Bowler'
  | 'Bowling Allrounder'
  | 'WK-Batsman';

export type PlayerStats = {
  lastScore: number;
  totalScore: number;
  lastThreeMatch: number;

  lastFiveMatch: number;
  totalPlayed: number;
  sel?: number;
  playerCurrentSalary: number;
  playerLastSalary: number;
  avg: number;
  liveScore?: number;
  livePoint?: number;
  totalPoint?: number;
  lastPoint?: number;
};

export type RugbyPlayerPosition =
  | 'Wing'
  | 'Utility'
  | 'Second Row'
  | 'Prop Forward'
  | 'Prop'
  | 'Outside Back'
  | 'Lock'
  | 'Interchange'
  | 'Hooker'
  | 'Halfback'
  | 'Fullback'
  | 'Full Back'
  | 'Forward'
  | 'Five Eighth'
  | 'Did Not Play'
  | 'Centre'
  | 'Back Row'
  | 'Back'
  | '21st Man'
  | '20th Man'
  | '19th Man'
  | '18th Man'
  | 'FB'
  | 'Hooker'
  | 'half back'
  | 'Five Eighth'
  | 'Center'
  | 'Centres'
  | 'Outside Back'
  | 'Second Row'
  | 'Replacement'
  | '23rd Man'
  | 'Five-Eighth'
  | '22nd Man'
  | '24th Man'
  | '25th Man'
  | '29th Man'
  | '28th Man'
  | '26th Man'
  | '27th Man'
  | 'SH'
  | 'C'
  | 'PR'
  | 'HB'
  | 'W'
  | 'HO'
  | 'BR'
  | 'B'
  | 'UB'
  | 'UF'
  | 'NULL'
  | 'L'
  | 'F'
  | 'Winger'
  | '2nd Row';

type CricketPlayerPosition =
  | 'wicketKeeper'
  | 'bowler'
  | 'batsman'
  | 'allRounder'
  | 'captain'
  | 'viceCaptain';

type SoccerPlayerPosition = 'G' | 'D' | 'M' | 'F';

// Define the type for a player
export type Player = {
  id: number;
  role:
    | PlayerRoleType
    | RugbyPlayerPosition
    | FootBallRole
    | SoccerPlayerPosition;
  squadsId: number;
  tournamentId: number;
  playedLastMatch?: boolean;
  playerId: number;
  name: string;
  scoreData: PlayerStats;
  teamName: string;
  image?: string | null;
  playerValue?: number | null;
  isAdded?: boolean;
  positionType?: CricketPlayerPosition | RugbyPlayerPosition;
  // UI Types
  isCaiptain?: boolean;
  isCaptain?: boolean;
  isViceCaiptain?: boolean;
  isViceCaptain?: boolean;
  // Additional required properties
  rank: number;
  type: string[];
  team: string;
  lastScore: number;
  total: number;
  avg: number;
  matches: number;
  selectionPercent: number;
  avgThreeGames: number;
  price: string;
  avatar: string;
  reserveRank?: number;
  lineupStatus: 'unannounced' | 'announced' | 'substitute';
};

export type CricketPlayerStats = {
  rank: number;
  player: string;
  team: string;
  price: string;
  change: string;
  totalScore: number;
  position: 'BAT' | 'BALL' | 'ALL' | 'WK'; // Defined cricket positions
  RUN: number;
  '4sB': number;
  '6sB': number;
  'SR>150': number;
  'SR 120-150': number;
  'SR<50': number;
  MO: number;
  DB: number;
  ER50: number;
  ER65: number;
  ER90: number;
  CA: number;
  RO: number;
  ST: number;
  DH: number;
  AR: number;
};

// Define the type for a player
export type FootballPlayer = {
  id: number;
  role: FootBallRole;
  squadsId: number;
  tournamentId: number;
  playerId: number;
  name: string;
  reserveRank?: number;
  scoreData: PlayerStats;
  teamName: string;
  image?: string | null;
  playerValue?: number | null;
  playedLastMatch?: boolean;
  isAdded?: boolean;
  positionType?:
    | 'wicketKeeper'
    | 'bowler'
    | 'batsman'
    | 'allRounder'
    | 'captain'
    | 'viceCaptain';
  // UI Types
  isCaiptain?: boolean;
  isViceCaiptain?: boolean;
  lineupStatus?: 'unannounced' | 'announced' | 'substitute';
};

export type PlayersByRoleType = {
  WKP: Player[];
  BAT: Player[];
  BOW: Player[];
  ALL: Player[];
};

export type PlayersByRoleLimit = {
  WKP: number;
  BAT: number;
  BOW: number;
  ALL: number;
};

// Define the type for the API response
export type GetPlayersResponse = {
  status: boolean;
  message: string;
  result: Player[];
};

export type GetPlayerListAPIResponse<T> = {
  status: boolean;
  message: string;
  result: T[];
};

export type GetRugbyPlayersResponse = {
  status: boolean;
  message: string;
  result: RugbyPlayer[];
};

type dreamPlayerPayload = {
  playerId: number;
  playerValue: number;
  role?: string;
};

export type PlayerData = {
  wicketKeeper: dreamPlayerPayload[];
  bowler: dreamPlayerPayload[];
  batsman: dreamPlayerPayload[];
  captain: dreamPlayerPayload[];
  viceCaptain: dreamPlayerPayload[];
  allRounder: dreamPlayerPayload[];
  reserve: ReservePlayerPayload[];
};

export type RugbyPlayerData = {
  backs: dreamPlayerPayload[];
  halves: dreamPlayerPayload[];
  backRow: dreamPlayerPayload[];
  frontRowForwards: dreamPlayerPayload[];
  interchange: dreamPlayerPayload[];
  captain: dreamPlayerPayload[];
  viceCaptain: dreamPlayerPayload[];
  reserve: ReservePlayerPayload[];
};

export type AFLPlayerData = {
  backLine: dreamPlayerPayload[];
  halfBackLine: dreamPlayerPayload[];
  midfield: dreamPlayerPayload[];
  halfForwardLine: dreamPlayerPayload[];
  forwardLine: dreamPlayerPayload[];
  followers: dreamPlayerPayload[];
  interchange: dreamPlayerPayload[];
  captain: dreamPlayerPayload[];
  viceCaptain: dreamPlayerPayload[];
  reserve: ReservePlayerPayload[];
};

export type SoccerPlayerData = {
  GKP: dreamPlayerPayload[];
  DEF: dreamPlayerPayload[];
  MID: dreamPlayerPayload[];
  FWD: dreamPlayerPayload[];
  captain: dreamPlayerPayload[];
  viceCaptain: dreamPlayerPayload[];
};

export type CreateTeamPayload = {
  sportId?: string | null;
  eventId?: string | null;
  eventName?: string | null;
  tournamentId?: string | null;
  competitionId?: string | null;
  coins?: number | null;
  bonusCoins?: number | null;
  playerData: PlayerData;
  name?: string;
};

export type CreateRugbyTeamPayload = {
  sportId?: string | null;
  eventId?: string | null;
  eventName?: string | null;
  tournamentId?: string | null;
  competitionId?: string | null;
  playerData: RugbyPlayerData;
  name?: string;
  coins?: number | null;
  bonusCoins?: number | null;
};

export type LastEnterPlayerData = {
  player: FootballPlayer;
  tabSection: keyof footballPlayersByRole;
};

export type LastEntryType = {
  players: LastEnterPlayerData[];
  mode: 'FEELING_LUCKY' | 'FAVORITE' | 'EXPERT_FAVORITE' | 'MANUAL';
};

export type CreateAFLTeamPayload = {
  sportId?: string | null;
  eventId?: string | null;
  eventName?: string | null;
  tournamentId?: string | null;
  competitionId?: string | null;
  playerData: AFLPlayerData;
  name?: string;
  coins?: number | null;
  bonusCoins?: number | null;
};

export type CreateSoccerTeamPayload = {
  sportId?: string | null;
  eventId?: string | null;
  eventName?: string | null;
  tournamentId?: string | null;
  competitionId?: string | null;
  playerData: SoccerPlayerData;
  name?: string;
};

// Define the structure for individual players
export interface PlayerFavouriteType {
  maxSelected: number;
  playerId: number;
  positionType: string;
}

export type PlayerpositionType =
  | 'wicketKeeper'
  | 'bowler'
  | 'batsman'
  | 'allRounder'
  | 'captain'
  | 'viceCaptain';

// Define the structure for the favoriteTeam object
export interface FavoriteTeam {
  allRounder: PlayerFavouriteType[];
  batsman: PlayerFavouriteType[];
  bowler: PlayerFavouriteType[];
  captain: PlayerFavouriteType[];
  viceCaptain: PlayerFavouriteType[];
  wicketKeeper: PlayerFavouriteType[];
}

export interface ValidFavoriteTeamSelection {
  allRounder: PlayerFavouriteType[];
  batsman: PlayerFavouriteType[];
  bowler: PlayerFavouriteType[];
  wicketKeeper: PlayerFavouriteType[];
}

export interface PlayerSelection {
  maxSelected: number;
  playerId: number;
  positionType: string;
}

export interface TeamSelectionData {
  possibleCaptains: PlayerSelection[];
  possibleViceCaptains: PlayerSelection[];
  validTeamSelection: ValidFavoriteTeamSelection;
}

export type DreamTeamPlayer = {
  id: number;
  dreamTeamId: number;
  playerId: number;
  positionType:
    | 'wicketKeeper'
    | 'bowler'
    | 'batsman'
    | 'allRounder'
    | 'captain'
    | 'viceCaptain';
};

type ExpertFavoriteResponse = {
  id: number;
  sportId: number;
  competitionId: number;
  userId: number;
  eventId: number;
  tournamentId: number;
  startTime: string; // ISO date string
  status: string; // Add other possible statuses if known
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  DreamTeamPlayers: DreamTeamPlayer[];
};

// Define the structure for the entire response
export interface GetFavouriteTeamResponse {
  status: boolean;
  message: string;
  result: {
    favoriteTeam: FavoriteTeam;
    expertFavorite: ExpertFavoriteResponse; // Change `any` if you know the type of `expertFavorite`
  };
}

export type DreamTeam = {
  id: number;
  name?: string;
  round?: string;
};

export interface ScoreBoard {
  id: number;
  eventId: number;
  Tr1C1: any;
  Tr2C1: any;
  Tr1C2: any;
  Tr2C2: any;
  Tr1CW1: any;
  Tr2CW1: any;
  Tr1CW2: any;
  Tr2CW2: any;
  Tr1CD2: any;
  Tr1CD1: any;
  Tr2CD1: any;
  Tr2CD2: any;
  Tr1CO1: any;
  Tr2CO1: any;
  Tr1CO2: any;
  Tr2CO2: any;
  Tr2: any;
  Tr1: any;
  EpsL: string;
  Epr: number;
  Ecov: any;
  ErnInf: string;
  Et: any;
  EtTx: string;
  ECo: string;
  Ebat: any;
  TPa: any;
  TCho: any;
  Esd: any;
  Exd: string;
  LuUT: any;
  Eact: any;
  EO: any;
  EOX: any;
  LuC: any;
}

interface PlayerScoreData {
  lastScore: number;
  totalScore: number;
  lastThreeMatch: number;
  lastFiveMatch: number;
  totalPlayed: number;
  sel: number;
  avg: number;
  playerCurrentSalary: number;
  playerLastSalary: number;
}

export interface FantasyTeamResponse {
  status: boolean;
  message: string;
  scoreBoard: ScoreBoard;
  result: {
    wicketKeeper: Player[];
    reserve?: Player[];
    batsman: Player[];
    bowler: Player[];
    allRounder: Player[];
    captain: Player[];
    viceCaptain: Player[];
    backRow: RugbyPlayer[];
    backs: RugbyPlayer[];
    halves: RugbyPlayer[];
    interchange: RugbyPlayer[];
    frontRowForwards: RugbyPlayer[];
    backLine: FootballPlayer[];
    followers: FootballPlayer[];
    forwardLine: FootballPlayer[];
    halfBackLine: FootballPlayer[];
    halfForwardLine: FootballPlayer[];
    midfield: FootballPlayer[];
    totalLivePoints?: number;
    totalSpendValue?: number;
    currentRank?: number;
    totalRank?: number;
    name?: string;
  };
}

type UserResult = {
  id: number;
  userId: number;
  firstName: string;
  lastName: string;
  filePath: string | null;
  rank: number;
  biggestValue: number;
};

export type BiggestValueApiResponse = {
  status: boolean;
  message: string;
  result: UserResult[];
};

export interface LeagueListResponse {
  status: boolean;
  result: {
    count: number;
    rows: Tournament[];
    limit: number;
    offset: number;
  };
}

export interface Tournament {
  id: number;
  name: string;
  SportId: number;
}

export interface TeamListResponse {
  status: boolean;
  result: {
    count: number;
    rows: TeamList[];
  };
}

interface TeamList {
  id: number;
  name: string;
  SportId: number;
}

export interface DreamTeamData {
  wicketKeeper: Player[];
  bowler: Player[];
  batsman: Player[];
  allRounder: Player[];
  captain: Player[];
  viceCaptain: Player[];
  totalSpendValue: number;
  totalLivePoints: number;
  currentRank: number;
  totalRank: number;
  winningPrice: string | null;
  name: string;
}

export interface FetchRoundScoreResponse {
  status: boolean;
  message: string;
  result: Player[];
}

export type EventTimeStatus = {
  isLockout: boolean;
  isLive: boolean;
  isCompleted: boolean;
};

export type PaymentMethod = 'smartCoins' | 'bonusCoins' | 'both';
