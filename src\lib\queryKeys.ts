import { getAFLTeamStats } from '@/helpers/fetchers/stats';

export const userProfile = 'user-profile';
export const updateUserProfileKey = 'update-user-profile';

export const quyerKeys = {
  updateUserProfileKey: 'update-user-profile',
  userProfile: 'user-profile',
  getAllPlans: 'get-all-plans',
  getCards: 'get-cards',
  purchasePlan: 'purchase-plan',
  upgradePlan: 'upgrade-plan',
  getCurrentPlan: 'get-current-plan',
  cancelPlan: 'cancel-plan',
  holdPlan: 'hold-plan',
  getAllCoins: 'get-all-coins',
  resumePlan: 'resume-plan',
  getFantasyUser: 'get-fantasy-user',
  getAllBankDetails: 'get-all-bank-details',
  getCouponCode: 'get-coupone-code',
  getAllCompetitons: 'get-all-competitions',
  getUserSession: 'get-user-session',
  getCompetiton: 'get-single-competition',
  getAllPlayers: 'get-all-players',
  getStandingsList: 'get-standings-list',
  getUpcomingList: 'get-upcoming-list',
  getRoundList: 'get-round-list',
  getPlayerRoundScore: 'get-player-round-score',
  getLeaderBoardList: 'get-leader-board-list',
  getFavouriteTeam: 'get-favourite-team',
  getDreamTeam: 'get-dream-team',
  getSeasonList: 'get-season-list',
  getLeagueList: 'get-league-list',
  getLeagueSeason: 'get-league-season',
  getBiggestValue: 'get-biggest-value',
  getLeaguePlayerList: 'get-league-player-list',
  getCaptainPlayerList: 'get-captain=player-list',
  getPlayerDetailsList: 'get-player-details-list',
  deleteBankDetails: 'delete-bank-details',
  getMatchCommentary: 'get-match-commentary',
  getCommentaryByMatchId: 'get-commentary-by-match-id',
  getMatchDetails: 'get-match-details',
  getMatchLineUp: 'get-match-line-up',
  getGameStats: 'get-game-stats',
  validateCard: 'validate-card',
  getAFLPlayerStats: 'get-afl-player-stats',
  getAFLSmartPlayStats: 'get-afl-smart-play-stats',
  getLiveCricketCommentary: 'get-live-cricket-commentary',
  getAFLTeamStats: 'get-afl-team-stats',
  getSmartPlayCricketStats: 'get-smart-play-cricket-stats',
  getAllCompetitionsList: 'get-all-competitions-list',
  getAllTeamList: 'get-all-team-list',
  getSoccerStandingsList: 'get-soccer-standings-list',
  getSoccerTeamStats: 'get-soccer-team-stats',
  getSoccerPlayerStats: 'get-soccer-player-stats',
  getSoccerSmartPlayStats: 'get-soccer-smart-play-stats',
  getSoccerLineUp: 'get-soccer-line-up',
  GET_PLAYER_LIST: 'get-player-list',
};
