'use client';

import type { Column, ColumnDef } from '@tanstack/react-table';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import PlayerValueChange from '@/components/Competition/PlayerValueChange';
import { useCompetition } from '@/helpers/context/competitionContext';
import type { PlayersByRole } from '@/helpers/context/createTeamContext';
import { useTeam } from '@/helpers/context/createTeamContext';
import { cn, formatNumberWithCommas } from '@/lib/utils';

import { getDefaultProfileImage } from '../../../../db/db';
import type { Player } from '../../../../types/competitions';
import { Badge } from '../badge';
import SortingDownIcon from '../Icons/SortingDownIcon';
import SortingUpIcon from '../Icons/SortingUpIcon';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ubarMenu,
  MenubarTrigger,
} from '../menubar';
import PlayerAvatar from '../PlayerAvatar/indext'; // Fixed typo in import path
import { getPlayerRole } from '@/components/Common/Player/PlayerCardContent';
import { Plus } from 'lucide-react';
import AddPlayer from '@/components/Icons/AddPlayer';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';
import { Tooltip } from '@material-tailwind/react';

const renderPlayerCell = (
  player: Player,
  playersByRole: Record<string, Player[]>,
  playerIndex: number,
) => {
  // Update URL params

  const searchParams = useSearchParams();
  const { refetchDreamTeam } = useCompetition();
  const event_id = searchParams.get('event_id');
  const sport_id = searchParams.get('sport_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const dreamTeamId = searchParams.get('dreamTeamId');
  const seasonId = searchParams.get('seasonId');
  const router = useRouter();
  const pathname = usePathname();
  const { setOpenReserveModal } = useTeam();
  const { setOpenReserveModal: setOpenFootballReserveModal } = useFootballContext();
  const { setOpenReserveModal: setOpenRugbyLeagueReserveModal } = useRugbyLeagueContext();

  const handleOpenReserveModal = () => {
    if (sport_id === '9') {
      setOpenFootballReserveModal(true);
    } else if (sport_id === '12') {
      setOpenRugbyLeagueReserveModal(true);
    } else {
      setOpenReserveModal(true);
    }
  };

  const editTeamHandelar = (dreamTeamId: string | null, playerId: number) => {
    let playerRoleType: keyof PlayersByRole | undefined = undefined;
    Object.entries(playersByRole).forEach(([role, players]) => {
      players.forEach((player) => {
        if (player.playerId === playerId) {
          playerRoleType = role as keyof PlayersByRole;
        }
      });
    });
    const query = {
      event_id,
      sport_id,
      tournament_id,
      dreamTeamId: dreamTeamId,
      competition_id,
      add_more: 'true',
      seasonId,
      playerId: `${playerId}`,
      role: playerRoleType,
    };

    refetchDreamTeam();
    // @ts-expect-error
    return router.push(`${pathname}?${new URLSearchParams(query).toString()}`);
  };


  if (!player) {
    return <div className="w-full h-full space-x-2 flex justify-start items-center p-2 cursor-pointer" onClick={handleOpenReserveModal}>
      <AddPlayer />
      <p className="text-black text-xs font-semibold">
        Reserve {playerIndex + 1}
      </p>
    </div>;
  }

  return (
    <Menubar className="border-0 bg-transparent" asChild>
      <MenubarMenu>
        <MenubarTrigger>
          {/* <div className="grid md:grid-cols-[30%_30%_40%] grid-cols-[20%_60%_20%] w-full gap-x-4 md:gap-x-0"> */}
          <div className="flex gap-x-3 w-full px-2">
            <div className="flex justify-start items-center">
              <PlayerAvatar
                avatarUrl={player?.image ?? getDefaultProfileImage()}
              />
            </div>

            {
              !player.reserveRank && <MenubarContent
                align="center"
                className="bg-transparent"
                sideOffset={-30}
              >
                <div className="bg-[#1e2c3a] rounded-lg p-3 flex  gap-1 relative">
                  {/* Header with close button */}
                  <div className="flex justify-between items-start w-full">
                    <div className="flex flex-col">
                      <span className="text-white text-xs font-semibold">
                        {player.name}
                      </span>
                      <span className="text-gray-400 text-[9px]">
                        {player.teamName}
                      </span>
                      <span className="text-gray-400 text-[9px]">
                        {getPlayerRole(player.role) === 'WKP'
                          ? 'WKP/BAT'
                          : getPlayerRole(player.role)}
                      </span>
                    </div>
                  </div>
                  <div className="w-full space-y-1">
                    <div className="flex justify-end space-x-1 w-full">
                      <Badge className="text-white bg-secondary-100 text-[9px] p-0 px-1 cursor-not-allowed">
                        Captain
                      </Badge>
                    </div>
                    <div className=" flex justify-end">
                      <Badge className="text-white bg-secondary-100 text-[9px] p-0 px-1 cursor-not-allowed">
                        Vice Captain
                      </Badge>
                    </div>
                    <div className=" flex justify-end">
                      <Badge
                        className="text-white bg-secondary-100 text-[9px] p-0 px-1 cursor-pointer"
                        onClick={() =>
                          editTeamHandelar(dreamTeamId, player.playerId)
                        }
                      >
                        Substitute
                      </Badge>
                    </div>
                  </div>
                </div>
              </MenubarContent>
            }

            <div className="flex justify-start gap-x-2">
              <div className="flex flex-col justify-start items-start">
                <p className="truncate ... w-[150px] text-left flex items-center gap-x-1">
                  <span className='w-[150px] truncate'>           {player?.name}</span>
                  {
                    player.reserveRank && <Badge className="text-white bg-secondary-200 text-[9px] p-0 px-1 cursor-not-allowed">
                      R{player.reserveRank}
                    </Badge>


                  }
                </p>
                <div className="text-xs text-gray-500 truncate ... w-[70x]">
                  {player?.teamName}
                </div>


                <PlayerValueChange
                  formatToCustomStyle={formatNumberWithCommas}
                  playerCurrentSalary={player?.playerValue ?? 0}
                  playerLastSalary={player?.scoreData?.playerCurrentSalary}
                />
              </div>
              <span className="text-[9px] text-gray-500">
                {sport_id === '12'
                  ? getPlayerRole(player?.role)
                  : (player?.role === 'Batter' && 'BAT') ||
                  (player?.role === 'WK-Batter' && 'WKP') ||
                  (player?.role === 'Bowler' && 'BOW') ||
                  (player?.role === 'Batting Allrounder' && 'ALL')}
              </span>

              {(player?.positionType === 'captain' || player.isCaptain) && (
                <div className="ml-2 p-3 w-5 h-5 bg-[#FC4714] flex flex-col justify-center items-center text-white rounded-full">
                  C
                </div>
              )}

              {(player?.positionType === 'viceCaptain' ||
                player.isViceCaptain) && (
                  <div className="ml-2 w-5 h-5 p-3 bg-[#003764] flex flex-col justify-center items-center text-white rounded-full">
                    VC
                  </div>
                )}
            </div>
          </div>
        </MenubarTrigger>
      </MenubarMenu>
    </Menubar>
  );
};

const renderSortHeader = (column: Column<Player, unknown>, label: string) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

export const upcomingPlayerListColumn: ColumnDef<Player>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => <div>{renderSortHeader(column, 'Name')}</div>,
    cell: ({ row }) => {
      const player = row.original;
      const playerIndex = row.index;

      const {
        state: { playersByRole },
      } = useTeam();

      return renderPlayerCell(player, playersByRole, playerIndex);
    },
  },

  {
    accessorKey: 'scoreData.lastScore',
    header: ({ column }) => renderSortHeader(column, 'LS'),
    cell: ({ row }) => {
      if (row.original.lineupStatus === 'unannounced') {
        return <Tooltip content={`Do Not Play`} placement="bottom" className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]">
          <p className="text-xs text-black-100">DNP</p>
        </Tooltip>
      }

      return <div>{row.original.scoreData.lastScore || '-'}</div>
    },
  },

  {
    accessorKey: 'scoreData.avg',
    header: ({ column }) => renderSortHeader(column, 'AVG'),
    cell: ({ row }) => (
      <div className="flex justify-center items-center">
        {row.original.scoreData?.avg?.toFixed(2) || '-'}
      </div>
    ),
  },

  {
    accessorKey: 'scoreData.sel',
    header: ({ column }) => (
      <div className="w-fit">{renderSortHeader(column, 'SEL%')}</div>
    ),

    cell: ({ row }) => (
      <div>
        {row?.original?.scoreData?.sel ? `${row.original.scoreData.sel}%` : '-'}
      </div>
    ),
  },
  {
    accessorKey: 'playedLastMatch',
    header: ({ column }) => renderSortHeader(column, 'PLG'),
    cell: ({ row }) => (
      <div className={cn(row.original.playedLastMatch ? 'text-green-500' : 'text-red-500')}>{row.original.playedLastMatch ? 'Y' : 'N'}</div>
    ),
  }
];
