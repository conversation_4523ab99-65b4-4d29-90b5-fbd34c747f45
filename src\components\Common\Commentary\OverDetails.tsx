import { generateUniqueId } from '@/lib/utils';
import React from 'react';

type BallDetail = {
  result: string;
};

type Batsman = {
  name: string;
  runs: number;
  balls: number;
};

type Bowler = {
  name: string;
  overs: number;
  runs: number;
  wickets: number;
};

type TeamStats = {
  teamName: string;
  runs: number;
  overs: number;
  crr: number;
};

type MatchStats = {
  runsScored: number;
  batsmen: <PERSON><PERSON>[];
  bowlers: Bowler[];
};

export type OverData = {
  overNumber: number;
  overs: {
    ballDetails: BallDetail[];
  };
  matchStats: MatchStats;
  teamStats: TeamStats;
};

type OverDetailsProps = {
  overData: OverData;
  formatBowlerStats: (overs: number, runs: number, wickets: number) => string;
};

const OverDetails: React.FC<OverDetailsProps> = ({
  overData,
  formatBowlerStats,
}) => {
  return (
    <div className="grid lg:grid-cols-4 grid-cols-2 lg:gap-4 gap-0 mb-4 bg-black-400 h-full">
      <div
        className="text-sm border-r border-white p-2 md:p-0 flex flex-col items-start md:items-center justify-center"
        style={{ paddingLeft: 0 }}
      >
        <div className="flex w-full h-full">
          <div className="bg-primary-200 text-xs mt-[-8px] md:mt-0 lg:text-sm lg:hidden text-white p-2 md:p-0 rounded-md w-[60px] font-bold h-full flex items-center justify-center mr-2">
            {overData.overNumber}
          </div>
          <div className="bg-primary-200 mr-2 text-xs border hidden lg:text-sm text-white p-2 md:p-0 rounded-md w-[60px] font-bold lg:flex items-center justify-center">
            {overData.overNumber}
          </div>
          <div className="text-xs lg:text-sm flex flex-col justify-center items-center w-full">
            <div>
              <span>Runs Scored:</span>{' '}
              <span className="font-bold">
                {overData.matchStats.runsScored}
              </span>
            </div>
            <div className="text-gray-600 flex space-x-2">
              {overData.overs.ballDetails.map((ball) => (
                <div key={generateUniqueId()}>{ball.result}</div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="text-xs lg:text-sm md:border-r p-2 lg:p-0 border-white flex flex-col items-center justify-center">
        <div className="flex w-full space-x-1">
          <div>{overData.teamStats.teamName}:</div>
          <div className="font-bold text-black-100">
            {overData.teamStats.runs}/{overData.teamStats.overs}
          </div>
        </div>
        <div className="text-gray-600 text-left w-full">
          CRR: {overData.teamStats.crr}
        </div>
      </div>

      <div className="text-xs lg:text-sm border-r border-t p-2 border-white flex flex-col items-start justify-center">
        {overData.matchStats.batsmen.map((batsman) => (
          <div
            key={generateUniqueId()}
            className="grid grid-cols-2 place-content-start gap-x-2 text-gray-700 lg:text-sm text-xs"
          >
            <div>{batsman.name}</div>
            <div className="font-medium text-black-100 text-right">
              {batsman.runs} ({batsman.balls})
            </div>
          </div>
        ))}
      </div>

      <div className="text-sm border-t border-white lg:border-none flex flex-col items-start p-2 lg:p-0 px-2 justify-center">
        {overData.matchStats.bowlers.map((bowler) => (
          <div
            key={generateUniqueId()}
            className="grid grid-cols-2 place-content-start gap-x-2 text-gray-700 text-xs lg:text-sm"
          >
            <div className="font-medium">{bowler.name}</div>
            <div className="text-black-100 font-medium text-right">
              {formatBowlerStats(bowler.overs, bowler.runs, bowler.wickets)}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default OverDetails;
