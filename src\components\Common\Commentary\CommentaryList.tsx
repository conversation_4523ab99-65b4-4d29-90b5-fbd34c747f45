import React from 'react';
import { CommentaryItemAPI } from '../../../../types/commentry';
import { formatCommentary, generateUniqueId } from '@/lib/utils';

interface CommentaryListProps {
  commentaries: CommentaryItemAPI[];
}

const CommentaryList: React.FC<CommentaryListProps> = ({ commentaries }) => {
  return (
    <div>
      {commentaries.map((commentary) => {
        return (
          <div
            key={generateUniqueId()}
            className="mt-5"
            dangerouslySetInnerHTML={{
              __html: formatCommentary(commentary),
            }}
          />
        );
      })}
    </div>
  );
};

export default CommentaryList;
