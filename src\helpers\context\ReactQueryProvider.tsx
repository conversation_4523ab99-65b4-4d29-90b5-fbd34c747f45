'use client'; // This file needs to be a client component

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';

// Create the query client instance
const queryClient = new QueryClient();

export default function ReactQueryProvider({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
