'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Zap, Play } from 'lucide-react';
import { useChatContext } from '@copilotkit/react-ui';

interface SmartPlayButtonProps {
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
}

function SoccerAIButton({
  onClick,
  className = '',
  disabled = false,
}: SmartPlayButtonProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  return (
    <motion.button
      className={`relative overflow-hidden rounded-full px-6 py-3 font-bold text-white shadow-lg ${className}`}
      style={{
        background: 'linear-gradient(135deg, #0062cc 0%, #0052b0 100%)',
      }}
      disabled={disabled}
      onClick={onClick}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onTapStart={() => setIsPressed(true)}
      onTap={() => setIsPressed(false)}
      onTapCancel={() => setIsPressed(false)}
      whileHover={{
        scale: 1.05,
        boxShadow: '0 10px 25px -5px rgba(0, 98, 204, 0.5)',
      }}
      whileTap={{ scale: 0.95 }}
    >
      {/* Orange energy lines */}
      <motion.div
        className="absolute inset-0 z-0"
        initial={{ opacity: 0 }}
        animate={{ opacity: isHovered ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      >
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute h-1 rounded-full bg-orange-500"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 30 + 10}px`,
              opacity: 0.7,
            }}
            initial={{ opacity: 0, scale: 0, rotate: Math.random() * 360 }}
            animate={{
              opacity: [0, 0.8, 0],
              scale: [0, 1, 0],
              x: [0, Math.random() * 40 - 20],
              y: [0, Math.random() * 40 - 20],
              rotate: `${Math.random() * 360}deg`,
            }}
            transition={{
              duration: 0.8 + Math.random() * 0.5,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 1,
            }}
          />
        ))}
      </motion.div>

      {/* Button content */}
      <motion.div
        className="relative flex items-center justify-center gap-2"
        animate={{
          x: isPressed ? 2 : 0,
          y: isPressed ? 2 : 0,
        }}
      >
        <motion.div
          animate={{
            rotate: isHovered ? [0, 15, -15, 0] : 0,
            scale: isHovered ? [1, 1.2, 1] : 1,
          }}
          transition={{
            duration: 0.6,
            repeat: isHovered ? Number.POSITIVE_INFINITY : 0,
            repeatType: 'reverse',
          }}
        >
          <Zap className="h-5 w-5 text-orange-400" />
        </motion.div>

        <span className="text-base tracking-wide">SmartPlay AI</span>

        <motion.div
          animate={{
            scale: isHovered ? [1, 1.2, 1] : 1,
          }}
          transition={{
            duration: 0.7,
            repeat: isHovered ? Number.POSITIVE_INFINITY : 0,
          }}
        >
          <Play className="h-5 w-5 fill-orange-400 text-orange-400" />
        </motion.div>
      </motion.div>

      {/* Orange pulse effect */}
      {isHovered && (
        <motion.div
          className="absolute inset-0 rounded-full"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{
            opacity: [0, 0.3, 0],
            scale: [0.8, 1.2, 1.5],
          }}
          transition={{
            duration: 1,
            repeat: Number.POSITIVE_INFINITY,
          }}
          style={{
            background: 'linear-gradient(135deg, #ff6b00 0%, #ff9500 100%)',
          }}
        />
      )}

      {/* Sport-style motion lines */}
      <motion.div
        className="absolute -right-2 -top-2 h-16 w-16 opacity-0"
        animate={{ opacity: isHovered ? 1 : 0 }}
      >
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={`line-${i}`}
            className="absolute h-1 w-6 rounded-full bg-orange-500"
            style={{
              top: 8 + i * 5,
              right: 8 + i * 3,
              transformOrigin: 'right center',
              opacity: 0.7 - i * 0.15,
            }}
            initial={{ scaleX: 0 }}
            animate={{ scaleX: isHovered ? 1 : 0 }}
            transition={{
              duration: 0.3,
              delay: i * 0.1,
            }}
          />
        ))}
      </motion.div>
    </motion.button>
  );
}

const SoccerAIAction = ({}: SmartPlayButtonProps) => {
  const { open, setOpen } = useChatContext();

  return (
    <div onClick={() => setOpen(!open)}>
      <SoccerAIButton />;
    </div>
  );
};

export default SoccerAIAction;
