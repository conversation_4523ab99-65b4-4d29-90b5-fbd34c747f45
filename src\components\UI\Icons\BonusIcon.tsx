import React from 'react';

const BonusIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="60"
      height="60"
      viewBox="0 0 60 60"
    >
      <defs>
        <linearGradient
          id="linear-gradient"
          x1="0.5"
          y1="1"
          x2="0.5"
          gradientUnits="objectBoundingBox"
        >
          <stop offset="0" stopColor="#bb401e" />
          <stop offset="0.152" stopColor="#fc4714" />
          <stop offset="0.675" stopColor="#fc7914" />
          <stop offset="1" stopColor="#fed1c4" />
        </linearGradient>
      </defs>
      <g id="BonusIcon" transform="translate(-9043 -12408)">
        <rect
          id="Rectangle_62060"
          data-name="Rectangle 62060"
          width="60"
          height="60"
          rx="30"
          transform="translate(9043 12408)"
          fill="url(#linear-gradient)"
        />
        <g
          id="Group_116731"
          data-name="Group 116731"
          transform="translate(9055 12420)"
        >
          <g id="Group_116732" data-name="Group 116732">
            <path
              id="Path_203910"
              data-name="Path 203910"
              d="M31.7,22.671a14.327,14.327,0,0,0,.56-2.2"
              fill="none"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
            <path
              id="Path_203911"
              data-name="Path 203911"
              d="M29.656,26.573a14.582,14.582,0,0,0,1.149-1.831"
              fill="none"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
            <path
              id="Path_203912"
              data-name="Path 203912"
              d="M26.435,29.756a14.619,14.619,0,0,0,1.7-1.435"
              fill="none"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
            <path
              id="Path_203913"
              data-name="Path 203913"
              d="M32.468,18A14.429,14.429,0,0,0,27.637,7.209"
              fill="none"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
            <circle
              id="Ellipse_14830"
              data-name="Ellipse 14830"
              cx="17"
              cy="17"
              r="17"
              transform="translate(1 1)"
              fill="none"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
            <path
              id="Path_203914"
              data-name="Path 203914"
              d="M4.3,13.329a14.326,14.326,0,0,0-.56,2.2"
              fill="none"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
            <path
              id="Path_203915"
              data-name="Path 203915"
              d="M6.344,9.427A14.594,14.594,0,0,0,5.2,11.259"
              fill="none"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
            <path
              id="Path_203916"
              data-name="Path 203916"
              d="M9.565,6.244a14.619,14.619,0,0,0-1.7,1.435"
              fill="none"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
            <path
              id="Path_203917"
              data-name="Path 203917"
              d="M3.532,18A14.419,14.419,0,0,0,7.77,28.23"
              fill="none"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
            <path
              id="Path_203918"
              data-name="Path 203918"
              d="M18.965,8.957,21.109,13.3a1.078,1.078,0,0,0,.811.588l4.794.7a1.076,1.076,0,0,1,.6,1.836l-3.47,3.382a1.077,1.077,0,0,0-.309.953l.819,4.775a1.076,1.076,0,0,1-1.562,1.134L18.5,24.413a1.076,1.076,0,0,0-1,0l-4.288,2.254a1.076,1.076,0,0,1-1.562-1.134l.819-4.775a1.075,1.075,0,0,0-.31-.953L8.689,16.423a1.076,1.076,0,0,1,.6-1.836l4.8-.7a1.076,1.076,0,0,0,.81-.588l2.144-4.345A1.077,1.077,0,0,1,18.965,8.957Z"
              fill="none"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};

export default BonusIcon;
