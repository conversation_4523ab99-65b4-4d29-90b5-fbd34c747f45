export default function CricketLegend() {
  return (
    <>
      <div className="text-bt-primery-500 font-bold md:px-6 px-0 mt-4">
        Legend
      </div>
      <div className="w-full max-w-4xl md:px-6 px-0 grid md:grid-cols-3 grid-cols-2 gap-4 text-sm py-4 pt-4 text-xs md:text-sm">
        {/* Left Column */}
        <div className="space-y-1">
          <div className="flex gap-2">
            <span className="font-bold">RUN</span>
            <span className="text-muted-foreground">- Runs Scored</span>
          </div>
          <div className="flex gap-2">
            <span className="font-bold">SR</span>
            <span className="text-muted-foreground">- Strike Rate Bonus</span>
          </div>
          <div className="flex gap-2">
            <span className="font-bold">4sB</span>
            <span className="text-muted-foreground">- Four Bonus</span>
          </div>
          <div className="flex gap-2">
            <span className="font-bold">6sB</span>
            <span className="text-muted-foreground">- Six Bonus</span>
          </div>
          <div className="flex gap-2">
            <span className="font-bold">WIC</span>
            <span className="text-muted-foreground">- Wickets</span>
          </div>
        </div>

        {/* Middle Column */}
        <div className="space-y-1">
          <div className="flex gap-2">
            <span className="font-bold">MO</span>
            <span className="text-muted-foreground">- Maiden Overs Bowled</span>
          </div>
          <div className="flex gap-2">
            <span className="font-bold">DB</span>
            <span className="text-muted-foreground">- Dot Balls</span>
          </div>
          <div className="flex gap-2">
            <span className="font-bold">ER</span>
            <span className="text-muted-foreground">- Economy Rate Bonus</span>
          </div>
          <div className="flex gap-2">
            <span className="font-bold">CA</span>
            <span className="text-muted-foreground">- Catches</span>
          </div>
          <div className="flex gap-2">
            <span className="font-bold">RO</span>
            <span className="text-muted-foreground">- Run outs</span>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-1">
          <div className="flex gap-2">
            <span className="font-bold">ST</span>
            <span className="text-muted-foreground">- Stumping</span>
          </div>
          <div className="flex gap-2">
            <span className="font-bold">DH</span>
            <span className="text-muted-foreground">- Direct Hit Run Out</span>
          </div>
          <div className="flex gap-2">
            <span className="font-bold">AR</span>
            <span className="text-muted-foreground">- All Rounder Bonus</span>
          </div>
        </div>
      </div>
    </>
  );
}

import React from 'react';
import { Legend } from 'recharts';

interface StatItem {
  column: string;
  label: string;
  text: string;
}

interface StatsLegendProps {
  stats: StatItem[];
}

export const StatsLegend: React.FC<StatsLegendProps> = ({ stats }) => {
  // @ts-ignore
  const uniqueColumns = [...new Set(stats?.map((item: any) => item?.column))]; // Get unique columns

  return (
    <div>
      <h2 className="text-blue-600 font-semibold mb-2">Legend</h2>

      <div className="grid grid-cols-2 sm:grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        {uniqueColumns?.map((col) => (
          <div key={col}>
            <h3 className="font-semibold">{col}</h3>
            {stats
              .filter((item) => item.column === col)
              .map(({ label, text }) => (
                <p key={label} className="text-sm">
                  <strong>{label}:</strong> {text}
                </p>
              ))}
          </div>
        ))}
      </div>
    </div>
  );
};
