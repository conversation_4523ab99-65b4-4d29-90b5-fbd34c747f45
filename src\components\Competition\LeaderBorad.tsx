import { useQuery } from '@tanstack/react-query';
import type { ColumnDef } from '@tanstack/react-table';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';

import axiosInstance from '@/helpers/axios/axiosInstance';
import { LIVE_POLLIN_TIME } from '@/helpers/constants/index';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Config } from '@/helpers/context/config';
import useScreen from '@/hooks/useScreen';
import { quyerKeys } from '@/lib/queryKeys';

import type { FetchLeadingResponse, Team } from '../../../types';
import { ModalHeaderDownIcon, PlayerCaptainViewIcon } from '../images';
import DataTable from '../UI/DataTabel';
import { Drawer, DrawerContent } from '../UI/drawer';
import CompetitionDetailsHeader from './CompetitionDetailsHeader';
import {
  completedLeaderBoardTeamColumns,
  leaderboardTeamColumns,
  liveLeaderBoardTeamColumns,
} from '../UI/DataTabel/columns/createTeamColumn';

export const fetchLeaderDetails = async (
  competitionId: string | null,
  eventId: string | null,
  tournamentId: string | null,
  status?: string | undefined,
): Promise<FetchLeadingResponse> => {
  const res = await axiosInstance.get<FetchLeadingResponse>(
    Config.fantasyURL +
    `/ladder-board/${competitionId}?eventId=${eventId}&SportId=4&tournamentId=${tournamentId}&status=${status}`,
  );
  return res?.data;
};

const LeaderBorad = () => {
  const searchParams = useSearchParams();
  const { width } = useScreen();

  const event_id = searchParams.get('event_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const { eventDetailsResponse, dreamTeamResponse } = useCompetition();
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  let playerTeamColums: ColumnDef<Team>[] = [];
  const [openDrawer, setOpenDrawer] = useState<boolean>(false);
  switch (eventStatus) {
    case 'finished':
      playerTeamColums = completedLeaderBoardTeamColumns;
      break;
    case 'inprogress':
    case 'innings break':
    case 'Strategic Timeout':
    case 'Stumps':
      playerTeamColums = liveLeaderBoardTeamColumns;
      break;
    case 'upcoming':
      playerTeamColums = leaderboardTeamColumns;
      break;
  }

  // fetch leader borad list
  const { data: leaderList, isFetching: isLoading } = useQuery({
    queryFn: () =>
      fetchLeaderDetails(
        competition_id,
        event_id,
        tournament_id,
        eventDetailsResponse?.result?.eventDetails?.status,
      ),
    refetchInterval: () =>
      eventDetailsResponse?.result?.eventDetails?.status === 'inprogress'
        ? LIVE_POLLIN_TIME
        : false,
    queryKey: [quyerKeys.getLeaderBoardList],
    enabled:
      !!tournament_id && !!eventDetailsResponse?.result?.eventDetails?.status,
  });



  const leaderBordList =
    eventDetailsResponse?.result?.eventDetails?.status === 'upcoming'
      ? leaderList?.result || [] // Default to an empty array if result is null/undefined
      : [
        ...(leaderList?.currentUser?.map((user, index, array) => ({
          ...user,
          myTeam: true,
          myLastTeam: index === array.length - 1, // Mark as myLastTeam if it's the last item
        })) || []), // Default to an empty array if currentUser is null/undefined
        ...(leaderList?.result || []), // Default to an empty array if result is null/undefined
      ];


  console.log(leaderBordList, 'leaderBordList');

  return (
    <>
      <div className="md:px-4 px-0">
        <div className="flex flex-col md:flex-row justify-between  md:space-x-2 md:space-y-0">
          <div className="w-full h-full  pb-2">
            <DataTable
              columns={playerTeamColums}
              // @ts-expect-error
              data={leaderBordList}
              isLoading={isLoading}
              stickyColumns={width <= 700 ? [0] : []}
              layout="position"
            />
          </div>
          {/* <div className="flex flex-col space-y-2 w-full md:w-[40%] ">
            <div className="hidden md:block">
              <RightSideDetails />
            </div>
          </div> */}
        </div>

        {width <= 800 && (
          <Drawer open={openDrawer} onOpenChange={setOpenDrawer}>
            <DrawerContent className="overflow-hidden bg-gradient-to-b from-[#E5EAEF] to-[#93A0AD]">
              <div className="absolute bg-primary-200 p-2 max-799:p-0 font-bold text-xl text-white w-full z-50 flex items-center justify-center h-[47px]">
                <button
                  className="flex items-center gap-1.5"
                  onClick={() => setOpenDrawer(false)}
                >
                  <ModalHeaderDownIcon />
                </button>
              </div>
              <div className="mt-[44px] overflow-y-auto mb-10">
                {/* <RightSideDetails /> */}
              </div>
            </DrawerContent>
          </Drawer>
        )}

        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center ">
          <PlayerCaptainViewIcon
            className="md:hidden block"
            onClick={() => setOpenDrawer(true)}
          />
        </div>
      </div>
    </>
  );
};

export default LeaderBorad;
