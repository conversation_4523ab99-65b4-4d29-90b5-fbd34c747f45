import { DesktopDatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import React from 'react';
import type { Control } from 'react-hook-form';
import { Controller } from 'react-hook-form';

import DatepickerBG from '@/assets/images/icons/date-picker-bg.svg';

interface RHFDatePickerProps {
  name: string;
  control: Control<any>;
  label?: string;
  placeHolder?: string;
  disablePast?: boolean;
  disableFuture?: boolean;
}

export const RHFDatePicker: React.FC<RHFDatePickerProps> = ({
  name,
  control,
  placeHolder,
  disablePast,
  disableFuture,
}) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DesktopDatePicker
              {...field}
              clearable
              autoOk
              // disableToolbar
              variant="inline"
              format="dd/MM/yyyy"
              placeholder="All"
              margin="normal"
              id="date-picker-inline"
              inputVariant="outlined"
              disablePast={disablePast}
              disableFuture={disableFuture}
              slots={{
                openPickerIcon: DatepickerBG, // Custom icon
              }}
              slotProps={{
                field: {
                  id: 'date-picker-inline',
                  // @ts-expect-error
                  placeholder: placeHolder,
                },
              }}
              className="common-date-picker"
            />
          </LocalizationProvider>
        )}
      />
    </LocalizationProvider>
  );
};

export default RHFDatePicker;
