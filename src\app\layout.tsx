import './globals.css';
import '@/app/layout.scss';
import '../assets/scss/global.scss';

import type { Metadata } from 'next';
import { Inter, Roboto } from 'next/font/google';
import { ToastContainer } from 'react-toastify';

import {
  arial,
  peckhamPressTrial,
  sfProTextRegular,
  sfProTextSemibold,
  theStamshonsDemo,
  veneerCleanSoft,
  veneerItalic,
  Apotek_Comp_Regular,
} from '@/app/fonts';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
import LoginPopUp from '@/components/LoginPopup';
import dynamic from 'next/dynamic';
import { OPEN_GRAPH_IMAGES } from '@/lib/constants/constants';
import HeaderEvents from '@/components/Header/HeaderEvents';
const RootProvider = dynamic(() => import('@/helpers/context/RootProvider'), {
  ssr: false,
});

export const metadata: Metadata = {
  title: 'Smart Play',
  description: 'Smart Play',
  openGraph: {
    images: [OPEN_GRAPH_IMAGES.SMART_PLAY_LOGO],
  },
};

const roboto = Roboto({
  weight: ['400', '700'],
  subsets: ['latin'],
});

const inter = Inter({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.className} ${roboto.className} ${arial.variable} ${peckhamPressTrial.variable} ${sfProTextRegular.variable} ${sfProTextSemibold.variable} ${theStamshonsDemo.variable} ${veneerItalic.variable} ${veneerCleanSoft.variable} ${Apotek_Comp_Regular.variable}`}
      >
        <RootProvider>
          <div className="wrapper bg-off-white-200">
            <div className="Main-Wrap max-1024:bg-off-white-200 bg-cover bg-no-repeat bg-center h-screen">
              <div className="container">
                <div className="external-wrapper" id="external-wrapper">
                  <Header />
                  <div className="main-full-layout">
                    <HeaderEvents />
                    <div className="full-layout layout-wrapper">
                      <div className="content">
                        <div className="inner-content min-h-screen">
                          {children}
                          <ToastContainer
                            style={{ padding: '5px' }}
                            limit={1}
                          />
                        </div>
                        <div className="Footer">
                          <Footer />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <LoginPopUp />
            {/*<CopilotPopup />*/}
          </div>
        </RootProvider>
      </body>
    </html>
  );
}
