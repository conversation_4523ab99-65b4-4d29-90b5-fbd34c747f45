import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { AmericanFootball } from './index';

const meta: Meta<typeof AmericanFootball> = {
  title: 'Components/SportsIcon',
  component: AmericanFootball,
};

export default meta;

type Story = StoryObj<typeof AmericanFootball>;

export const Default: Story = {
  args: {
    label: 'Click Me',
  },
};

export const WithAction: Story = {
  args: {
    label: 'Click Me',
    onClick: () => alert('Button Clicked!'),
  },
};
