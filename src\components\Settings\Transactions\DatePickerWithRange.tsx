'use client';

import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import moment from 'moment';
import React, { useEffect } from 'react';
import type { DateRange } from 'react-day-picker';

import { Button } from '@/components/UI/button';
import { Calendar } from '@/components/UI/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/UI/popover';
import { cn } from '@/lib/utils';

export function DatePickerWithRange({
  className,
  setDateFrom,
  setDateTo,
  setDate,
  date,
  activeTab,
  fetchHistoryData,
}: Readonly<{
  className?: string;
  setDateFrom: React.Dispatch<React.SetStateAction<string | null>>;
  setDateTo: React.Dispatch<React.SetStateAction<string | null>>;
  setDate: React.Dispatch<React.SetStateAction<DateRange | undefined>>;
  date: DateRange | undefined;
  activeTab: string;
  fetchHistoryData: (
    offset: any,
    type: any,
    dateFrom: any,
    dateTo: any,
  ) => void;
}>) {
  useEffect(() => {
    if (date?.from !== null && date?.to !== null) {
      setDateFrom(date?.from ? moment(date?.from).format('YYYY-MM-DD') : null);
      setDateTo(date?.to ? moment(date?.to).format('YYYY-MM-DD') : null);
      fetchHistoryData(
        0,
        activeTab === 'SmartCoins Activity' ? 'fantasy' : 'fantasyWithdraw',
        date?.from ? moment(date?.from).format('YYYY-MM-DD') : null,
        date?.to ? moment(date?.to).format('YYYY-MM-DD') : null,
      );
    }
  }, [date]);

  const renderDate = () => {
    if (date?.from) {
      if (date.to) {
        return (
          <>
            {format(date.from, 'LLL dd, y')} - {format(date.to, 'LLL dd, y')}
          </>
        );
      }
      return format(date.from, 'LLL dd, y');
    }
    return <span>Pick a date</span>;
  };

  return (
    <div className={cn('grid gap-2', className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={'outline'}
            className={cn(
              'w-[300px] justify-start text-left font-normal',
              !date && 'text-muted-foreground',
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {renderDate()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={setDate}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
