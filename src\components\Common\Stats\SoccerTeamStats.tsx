'use client';

import { useState } from 'react';
import Image from 'next/image';
import PlayerAvatar from '@/components/UI/PlayerAvatar/indext';
import { getDefaultTeamImage } from '../../../../db/db';
import { Config } from '../../../helpers/context/config';
import { useCompetition } from '@/helpers/context/competitionContext';

// Interfaces for type safety
interface StatItem {
  label: string;
  team1Value: number;
  team2Value: number;
  isPercentage?: boolean;
}

interface SoccerTeamStatsProps {
  teamNames: [string, string];
  teamColors: [string, string];
  stats: any; // TODO: Define proper type for soccer stats
  homeTeamLogo: string;
  awayTeamLogo: string;
}

export default function SoccerTeamStats({
  teamNames,
  teamColors,
  stats,
  homeTeamLogo,
  awayTeamLogo,
}: SoccerTeamStatsProps) {
  const { eventDetailsResponse } = useCompetition();

  const homeTeamStats = stats?.find(
    (item: any) =>
      item.team.id === eventDetailsResponse?.result?.eventDetails?.homeTeam?.id,
  );
  const awayTeamStats = stats?.find(
    (item: any) =>
      item.team.id === eventDetailsResponse?.result?.eventDetails?.awayTeam?.id,
  );

  const displayStats: StatItem[] =
    homeTeamStats && awayTeamStats
      ? Object.entries(homeTeamStats)
          .filter(([key]) => {
            // Exclude non-stat properties and already handled stats
            const excludedKeys = ['team'];
            return !excludedKeys.includes(key);
          })
          .map(([key, value]) => {
            // Convert camelCase to UPPERCASE with spaces
            const label = key
              .replace(/([A-Z])/g, ' $1')
              .toUpperCase()
              .trim();

            // Special cases for percentage stats
            const isPercentage = key === 'passesAcc' || key === 'dribbleSucc';

            return {
              label,
              team1Value: Number(value) || 0,
              team2Value: Number(awayTeamStats[key]) || 0,
              isPercentage,
            };
          })
      : [];

  // Function to calculate bar widths
  const calculateBarWidth = (team1Value: number, team2Value: number) => {
    if (team1Value === 0 && team2Value === 0) {
      return {
        team1Width: '50%',
        team2Width: '50%',
      };
    }
    const total = team1Value + team2Value;
    return {
      team1Width: `${(team1Value / total) * 100}%`,
      team2Width: `${(team2Value / total) * 100}%`,
    };
  };

  return (
    <div className="bg-white p-4">
      <div className="flex justify-between items-center mb-6 max-w-[600px] mx-auto">
        <div className="flex items-center space-x-2 my-2">
          <span className="font-semibold">{teamNames[0]}</span>
          <div className="w-8 h-8 relative">
            <Image
              src={Config.mediaURL + homeTeamLogo || getDefaultTeamImage()}
              alt={teamNames[0]}
              fill
              className="object-contain"
            />
          </div>
        </div>
        <div className="text-xl font-bold">VS</div>
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 relative">
            <Image
              src={Config.mediaURL + awayTeamLogo || getDefaultTeamImage()}
              alt={teamNames[1]}
              fill
              className="object-contain"
            />
          </div>
          <span className="font-semibold">{teamNames[1]}</span>
        </div>
      </div>

      <div className="space-y-6">
        {displayStats.map((stat, index) => {
          const { team1Width, team2Width } = calculateBarWidth(
            stat.team1Value,
            stat.team2Value,
          );

          return (
            <div key={index} className="w-full">
              <div className="text-center text-sm font-medium text-gray-600 mb-2">
                {stat.label}
              </div>
              <div className="flex items-center justify-between">
                <div
                  className="font-bold text-xl w-12 text-right mr-2"
                  style={{ color: teamColors[0] }}
                >
                  {stat.isPercentage
                    ? stat.team1Value.toFixed(1)
                    : Math.round(stat.team1Value)}
                </div>

                <div className="flex-1 h-4 flex">
                  <div
                    className="h-full rounded-l-sm"
                    style={{
                      backgroundColor: teamColors[0],
                      width: team1Width,
                      minWidth: '1px',
                    }}
                  ></div>
                  <div
                    className="h-full rounded-r-sm"
                    style={{
                      backgroundColor: teamColors[1],
                      width: team2Width,
                      minWidth: '1px',
                    }}
                  ></div>
                </div>

                <div
                  className="font-bold text-xl w-12 text-left ml-2"
                  style={{ color: teamColors[1] }}
                >
                  {stat.isPercentage
                    ? stat.team2Value.toFixed(1)
                    : Math.round(stat.team2Value)}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
