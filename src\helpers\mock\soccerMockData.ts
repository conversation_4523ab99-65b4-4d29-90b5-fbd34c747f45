import { faker } from '@faker-js/faker';
import {
  SoccerPlayer,
  SoccerPlayerRole,
} from '../context/soccer/createSoccerTeamContext';

const TEAMS = [
  'Manchester United',
  'Liverpool',
  'Arsenal',
  'Chelsea',
  'Manchester City',
  'Tottenham',
  'Barcelona',
  'Real Madrid',
  'Bayern Munich',
  'PSG',
];

const generateSoccerPlayer = (role: SoccerPlayerRole): SoccerPlayer => {
  return {
    id: faker.string.uuid(),
    name: faker.person.fullName(),
    team: faker.helpers.arrayElement(TEAMS),
    role,
    price: faker.number.int({ min: 5000000, max: 15000000 }),
    points: faker.number.int({ min: 0, max: 100 }),
    selected: false,
    teamName: faker.helpers.arrayElement(TEAMS),
    squadsId: faker.number.int({ min: 1, max: 1000 }),
    tournamentId: faker.number.int({ min: 1, max: 1000 }),
    playerId: faker.number.int({ min: 1, max: 1000 }),
    scoreData: {
      lastScore: faker.number.int({ min: 0, max: 20 }),
      totalScore: faker.number.int({ min: 0, max: 100 }),
      lastThreeMatch: faker.number.int({ min: 0, max: 30 }),
      lastFiveMatch: faker.number.int({ min: 0, max: 50 }),
      playerCurrentSalary: faker.number.int({ min: 5000000, max: 15000000 }),
      playerLastSalary: faker.number.int({ min: 5000000, max: 15000000 }),
      avg: faker.number.float({ min: 0, max: 10 }),
      totalPlayed: faker.number.int({ min: 0, max: 100 }),
    },
  };
};

export const generateSoccerPlayers = (): SoccerPlayer[] => {
  const players: SoccerPlayer[] = [];

  // Generate 10 goalkeepers
  for (let i = 0; i < 10; i++) {
    players.push(generateSoccerPlayer('GK'));
  }

  // Generate 30 defenders
  for (let i = 0; i < 30; i++) {
    players.push(generateSoccerPlayer('DEF'));
  }

  // Generate 30 midfielders
  for (let i = 0; i < 30; i++) {
    players.push(generateSoccerPlayer('MID'));
  }

  // Generate 20 forwards
  for (let i = 0; i < 20; i++) {
    players.push(generateSoccerPlayer('FWD'));
  }

  return players;
};

export const soccerTournamentMockData = {
  id: faker.string.uuid(),
  name: 'Premier League Fantasy',
  startDate: faker.date.future(),
  endDate: faker.date.future(),
  prizePool: faker.number.int({ min: 1000000, max: 5000000 }),
  entryFee: faker.number.int({ min: 100, max: 1000 }),
  totalTeams: faker.number.int({ min: 1000, max: 10000 }),
  status: 'upcoming',
};
