'use client';
import type { Column, ColumnDef } from '@tanstack/react-table';
import {
  CricketPlayerStats,
  FootballPlayer,
  Player,
} from '../../../../../../types/competitions';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import {
  renderCricketColumnSortHeader,
  renderCricketLivePlayerCell,
  renderCricketPlayerCell,
  renderCricketReservedPlayerCell,
  renderCricketSortHeader,
  renderFootballPlayerCell,
  renderFootballSortHeader,
  renderRugbyPlayerCell,
  renderRugbySortHeader,
  renderTeamsSortHeader,
} from './utils';
import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';
import { RugbyPlayer } from '../../../../../../types/rugby-league';
import { useTeam } from '@/helpers/context/createTeamContext';
import { Team } from '../../../../../../types';
import PlayerAvatar from '@/components/UI/PlayerAvatar/indext';
import {
  getDefaultProfileImage,
  getDefaultUserImage,
} from '../../../../../../db/db';
import { Badge } from '@/components/UI/badge';
import { Tooltip } from '@material-tailwind/react';
import Image from 'next/image';
import {
  calculateDifferenceRating,
  cn,
  formatNumberWithCommas,
} from '@/lib/utils';
import {
  MyChartDownIcon,
  MyChartUpIcon,
} from '@/components/Competition/ChartPrize';
import { useCompetition } from '@/helpers/context/competitionContext';
import { useSearchParams } from 'next/navigation';
import { useUserProfileContext } from '@/helpers/context/userContext';
import PlayerValueChange from '@/components/Competition/PlayerValueChange';
import { SmartPlayCricketPlayer } from '../../../../../../types/commentry';
import { CricketPlayer } from '@/lib/types/cricket';
import { useCricketStore } from '@/store/cricket/useCricketStore';

export const footballUpcomingColumn: ColumnDef<FootballPlayer>[] = [
  {
    accessorKey: 'id',
    header: ({ column }) => (
      <div className="md:w-[210px] w-[100px]">
        {renderFootballSortHeader(column, 'NO.', 'start')}
      </div>
    ),
    cell: ({ row }) => {
      const player = row.original;
      const playerRank = row.index + 1;

      const {
        activeTabPlayer,
        state: { playersByRole, playerByRoleLimit, reserveState: { reservePlayers, reservePlayersLimit } },
        addPlayer,
        removePlayer,
        openReserveModal,
        addReservePlayer,
        removeReservePlayer,
        activePlayerPosition,
        setActivePlayerPosition,
      } = useFootballContext();

      const isPlayerSelected = Object.values(playersByRole || {})
        ?.flat()
        ?.some((p) => p?.playerId === player.playerId);
      const { eventDetailsResponse } = useCompetition();
      const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
      const isDreamTeam = eventDetailsResponse?.result?.dreamTeams?.length ?? 0;
      const dreamTeamId = useSearchParams().get('dreamTeamId');
      const playerId = useSearchParams().get('playerId');

      // Check if player is selected as reserve
      const isReservePlayerSelected = reservePlayers.some(
        (p) => p?.playerId === player.playerId
      );

      // Check if reserve limit is reached
      const isReservePlayerLimitReached = reservePlayers.filter((p) => p !== null).length >= reservePlayersLimit;

      return renderFootballPlayerCell({
        player,
        activeTab: activeTabPlayer,
        removePlayer,
        addPlayer,
        playersByRole,
        playerRank,
        isPlayerSelected,
        playerTypeLimits: playerByRoleLimit,
        isLive: eventStatus === 'inprogress' || eventStatus === 'finished',
        teamCreated:
          eventStatus === 'upcoming' &&
          isDreamTeam > 0 &&
          !!dreamTeamId &&
          !playerId,
        row: row,
        openReserveModal,
        addReservePlayer,
        removeReservePlayer,
        activePlayerPosition,
        setActivePlayerPosition,
        reservePlayersLimit,
        reservePlayers
      });
    },

    sortingFn: (rowA, rowB) => rowA.index - rowB.index,
  },

  {
    accessorKey: 'scoreData.lastScore',
    header: ({ column }) => renderFootballSortHeader(column, 'LS'),
    cell: ({ row }) => {

      if (row.original.lineupStatus === 'unannounced') {
        return <Tooltip content={`Do Not Play`} placement="bottom" className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]">
          <p className="text-xs text-black-100">DNP</p>
        </Tooltip>
      }

      return <div>{row.original.scoreData.lastScore || '-'}</div>
    },
  },

  {
    accessorKey: 'scoreData.avg',
    header: ({ column }) => renderFootballSortHeader(column, 'AVG'),
    cell: ({ row }) => (
      <div className="flex justify-center items-center">
        {row.original.scoreData?.avg?.toFixed(2) || '-'}
      </div>
    ),
  },

  {
    accessorKey: 'scoreData.sel',
    header: ({ column }) => (
      <div className="w-fit">{renderFootballSortHeader(column, 'SEL%')}</div>
    ),

    cell: ({ row }) => (
      <div>
        {row?.original?.scoreData?.sel ? `${row.original.scoreData.sel}%` : '-'}
      </div>
    ),
  },
  {
    accessorKey: 'playedLastMatch',
    header: ({ column }) => renderFootballSortHeader(column, 'PLG'),
    cell: ({ row }) => (
      <div className={cn(row.original.playedLastMatch ? 'text-green-500' : 'text-red-500')}>{row.original.playedLastMatch ? 'Y' : 'N'}</div>
    ),
  },
  {
    accessorKey: 'name',
    filterFn: 'includesString',
  },
  {
    accessorKey: 'teamName',
    filterFn: 'includesString',
  },
];

export const rugbyLeagueUpcomingColumn: ColumnDef<RugbyPlayer>[] = [
  {
    accessorKey: 'id',
    header: ({ column }) => (
      <div className="md:w-[210px] w-[100px] px-2">
        {renderRugbySortHeader(column, 'NO.', 'start')}
      </div>
    ),
    sortingFn: (rowA, rowB) => rowA.index - rowB.index,
    cell: ({ row }) => {
      const player = row.original;
      const playerRank = row.index + 1;

      const {
        activeTabPlayer,
        state: { playersByRole, playerByRoleLimit, reserveState: { reservePlayers, reservePlayersLimit } },
        addPlayer,
        removePlayer,
        openReserveModal,
        addReservePlayer,
        removeReservePlayer,
        activePlayerPosition,
        setActivePlayerPosition
      } = useRugbyLeagueContext();

      const isPlayerSelected = Object.values(playersByRole || {})
        ?.flat()
        ?.some((p) => p?.playerId === player.playerId);

      const { eventDetailsResponse } = useCompetition();
      const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
      const isDreamTeam = eventDetailsResponse?.result?.dreamTeams?.length ?? 0;
      const dreamTeamId = useSearchParams().get('dreamTeamId');
      const playerId = useSearchParams().get('playerId');



      return renderRugbyPlayerCell({
        player,
        activeTab: activeTabPlayer,
        removePlayer,
        addPlayer,
        playersByRole,
        playerRank,
        isPlayerSelected,
        playerTypeLimits: playerByRoleLimit,
        isLive: eventStatus === 'inprogress' || eventStatus === 'finished',
        teamCreated:
          eventStatus === 'upcoming' &&
          isDreamTeam > 0 &&
          !!dreamTeamId &&
          !playerId,
        reservePlayersLimit,
        reservePlayers,
        isReserve: openReserveModal,
        addReservePlayer,
        removeReservePlayer,
        activePlayerPosition,
        setActivePlayerPosition
      });
    },
  },

  {
    accessorKey: 'scoreData.lastScore',
    header: ({ column }) => renderRugbySortHeader(column, 'LS'),
    cell: ({ row }) => <div>{row.original.scoreData.lastScore || '-'}</div>,
  },

  {
    accessorKey: 'scoreData.avg',
    header: ({ column }) => renderRugbySortHeader(column, 'AVG'),
    cell: ({ row }) => (
      <div className="flex justify-center items-center">
        {row.original.scoreData?.avg?.toFixed(2) || '-'}
      </div>
    ),
  },

  {
    accessorKey: 'scoreData.sel',
    header: ({ column }) => (
      <div className="w-fit">{renderRugbySortHeader(column, 'SEL%')}</div>
    ),

    cell: ({ row }) => (
      <div>
        {row?.original?.scoreData?.sel ? `${row.original.scoreData.sel}%` : '-'}
      </div>
    ),
  },
  {
    accessorKey: 'playedLastMatch',
    header: ({ column }) => renderRugbySortHeader(column, 'PLG'),
    cell: ({ row }) => (
      <div className={cn(row.original.playedLastMatch ? 'text-green-500' : 'text-red-500')}>{row.original.playedLastMatch ? 'Y' : 'N'}</div>
    ),
  },
  {
    accessorKey: 'name',
    filterFn: 'includesString',
  },
  {
    accessorKey: 'teamName',
    filterFn: 'includesString',
  },
];

export const cricketCloumns: ColumnDef<Player>[] = [
  {
    accessorKey: 'id',
    header: ({ column }) => (
      <div className="md:w-[210px] w-[100px]">
        {renderCricketSortHeader(column, 'NO.', 'start')}
      </div>
    ),
    cell: ({ row }) => {
      const player = row.original;
      const playerRank = row.index + 1;

      const {
        state: { playersByRole, playerTypeLimits, reserveState: { reservePlayers, reservePlayersLimit } },
        removePlayer,
        addPlayer,
        activeTab,
        openReserveModal,
        addReservePlayer,
        removeReservePlayer,
        activePlayerPosition,
        setActivePlayerPosition


      } = useTeam();

      const { eventDetailsResponse } = useCompetition();
      const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
      const isDreamTeam = eventDetailsResponse?.result?.dreamTeams?.length ?? 0;

      const isPlayerSelected = Object.values(playersByRole || {})
        ?.flat()
        ?.some((p) => p?.playerId === player.playerId);
      const dreamTeamId = useSearchParams().get('dreamTeamId');
      const isReservePlayerSelected = reservePlayers.some((p) => p?.playerId === player.playerId);
      const isReservePlayerLimitReached = reservePlayers.filter((p) => p !== null).length >= reservePlayersLimit;

      const isLive = eventStatus === 'inprogress' || eventStatus === 'finished' || eventStatus === 'Strategic Timeout' || eventStatus === 'Stumps';

      return renderCricketPlayerCell({
        player,
        activeTab,
        removePlayer,
        addPlayer,
        playersByRole,
        playerTypeLimits,
        playerRank,
        isPlayerSelected,
        isLive,
        teamCreated:
          eventStatus === 'upcoming' && isDreamTeam > 0 && !!dreamTeamId,
        row,
        isReserve: openReserveModal,
        addReservePlayer,
        removeReservePlayer,
        isReservePlayerSelected,
        isReservePlayerLimitReached,
        activePlayerPosition,
        setActivePlayerPosition,
        reservePlayersLimit,
        reservePlayers
      });
    },
  },

  {
    accessorKey: 'scoreData.lastScore',
    header: ({ column }) => renderCricketSortHeader(column, 'LS'),
    cell: ({ row }) => <div>{row.original.scoreData.lastScore || '-'}</div>,
  },

  {
    accessorKey: 'scoreData.avg',
    header: ({ column }) => renderCricketSortHeader(column, 'AVG'),
    cell: ({ row }) => (
      <div className="flex justify-center items-center">
        {row.original.scoreData?.avg?.toFixed(2) || '-'}
      </div>
    ),
  },

  {
    accessorKey: 'scoreData.sel',
    header: ({ column }) => (
      <div className="w-fit">{renderCricketSortHeader(column, 'SEL%')}</div>
    ),

    cell: ({ row }) => (
      <div>
        {row?.original?.scoreData?.sel ? `${row.original.scoreData.sel}%` : '-'}
      </div>
    ),
  },
  {
    accessorKey: 'playedLastMatch',
    header: ({ column }) => renderCricketSortHeader(column, 'PLG'),
    cell: ({ row }) => (
      <div className={cn(row.original.playedLastMatch ? 'text-green-500' : 'text-red-500')}>{row.original.playedLastMatch ? 'Y' : 'N'}</div>
    ),
  },
  {
    accessorKey: 'name',
    filterFn: 'includesString',
  },
  {
    accessorKey: 'teamName',
    filterFn: 'includesString',
  },
];

export const cricketReservedPlayerColumn: ColumnDef<CricketPlayer>[] = [
  {
    accessorKey: 'id',
    header: ({ column }) => (
      <div className="md:w-[210px] w-[100px]">
        {renderCricketColumnSortHeader(column, 'Rank')}
      </div>
    ),
    cell: ({ row }) => {
      const player = row.original;
      const playerRank = row.index + 1;

      const {
        reserveState: {
          remainingBudget,
          reservePlayers,
          reservePlayersLimit,
          totalBudget,
        },
        addReservePlayer,
        removeReservePlayer,
      } = useCricketStore();

      const { eventDetailsResponse } = useCompetition();
      const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
      const isDreamTeam = eventDetailsResponse?.result?.dreamTeams?.length ?? 0;

      const isPlayerSelected = reservePlayers.some(
        (p) => p.playerId === player.playerId,
      );
      const dreamTeamId = useSearchParams().get('dreamTeamId');

      return renderCricketReservedPlayerCell({
        player,
        activeTab: 'ALL',
        removePlayer: () => removeReservePlayer(player),
        addPlayer: (player) => addReservePlayer(player),
        playersByRole: reservePlayers,
        playerTypeLimits: reservePlayersLimit,
        playerRank,
        isPlayerSelected,
        isLive: eventStatus === 'inprogress' || eventStatus === 'finished',
        teamCreated:
          eventStatus === 'upcoming' && isDreamTeam > 0 && !!dreamTeamId,
        row,
      });
    },
  },

  {
    accessorKey: 'scoreData.lastScore',
    header: ({ column }) => renderCricketColumnSortHeader(column, 'LS'),
    cell: ({ row }) => <div>{row.original.scoreData.lastScore}</div>,
  },
  {
    accessorKey: 'scoreData.avg',
    header: ({ column }) => renderCricketColumnSortHeader(column, 'AVG'),
    cell: ({ row }) => (
      <div className="flex justify-center items-center">
        {row.original.scoreData?.avg?.toFixed(2)}
      </div>
    ),
  },

  {
    accessorKey: 'scoreData.sel',
    header: ({ column }) => (
      <div className="w-fit">
        {renderCricketColumnSortHeader(column, 'SEL%')}
      </div>
    ),

    cell: ({ row }) => (
      <div>
        {row?.original?.scoreData?.sel ? `${row.original.scoreData.sel}%` : '-'}
      </div>
    ),
  },

  {
    accessorKey: 'scoreData.avg',
    header: ({ column }) => renderCricketColumnSortHeader(column, 'PLG'),
    cell: ({ row }) => (
      <div className="flex justify-center items-center text-green-500">Y</div>
    ),
  },

  {
    accessorKey: 'name',
    filterFn: 'includesString',
  },
  {
    accessorKey: 'teamName',
    filterFn: 'includesString',
  },
];

export const completedLeaderBoardTeamColumns: ColumnDef<Team>[] = [
  {
    accessorKey: 'nickName',
    header: ({ column }) => (
      <div className="px-4">{renderTeamsSortHeader(column, 'Team/Coach', "start")}</div>
    ),
    cell: ({ row }) => {
      return (
        <div
          className={cn(
            'flex justify-between flex-wrap space-y-1   items-center px-2 py-1',
          )}
        >
          <div className="flex justify-center gap-x-3 ml-3">
            <p>{row?.original?.rank}</p>
            <PlayerAvatar
              avatarUrl={row?.original?.profileImage ?? getDefaultUserImage()}
              isUser={true}
            />
            <p className="text-[14px] leading-[16px] font-semibold font-inter text-black-100 ml-3 capitalize">
              {row?.original?.nickName
                ? row?.original?.nickName
                : row?.original?.firstName + ' ' + row?.original?.lastName!}
            </p>
          </div>
          {row?.original?.name && (
            <Badge
              variant={'default'}
              className={`w-full md:w-fit flex justify-center items-center ${row?.original?.myTeam ? 'bg-secondary-100' : 'bg-primary-600'} text-white ml-[9px] capitalize`}
            >
              {row?.original?.name}
            </Badge>
          )}
        </div>
      );
    },
  },

  {
    accessorKey: 'winningPrice',
    header: ({ column }) => (
      <div className="px-4 flex justify-center items-center">
        {renderTeamsSortHeader(column, 'Winning SmartCoins')}
      </div>
    ),

    cell: ({ row }) =>
      row?.original?.winningPrice ? (
        <Tooltip
          content={`Winning Percentage: ${row?.original?.winnerPercentage}%`}
          placement="bottom"
          className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
        >
          <div
            className={cn('flex space-x-1 justify-center py-1 items-center')}
          >
            <Image
              src="/fantasy/images/smartbCoin.svg"
              width={17}
              height={17}
              alt="coin"
              unoptimized={true}
            />
            <span className="text-base text-secondary-100 font-bold">
              {row?.original?.winningPrice}
            </span>
          </div>
        </Tooltip>
      ) : (
        '-'
      ),
  },
  {
    accessorKey: 'teamName',

    header: ({ column }) => (
      <div className="px-4 flex justify-center items-center">
        {renderTeamsSortHeader(column, 'Total Score')}
      </div>
    ),

    cell: ({ row }) => (
      <div className="flex space-x-1 justify-center items-center">
        <span className="text-base text-secondary-100 font-bold">
          {row?.original?.totalScore ? row?.original?.totalScore : '-'}
        </span>
      </div>
    ),
  },

  {
    accessorKey: 'teamValue',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderTeamsSortHeader(column, 'Team Value')}
      </div>
    ),

    cell: ({ row }) => (
      <div>
        {row.original.teamValue
          ? '$' + formatNumberWithCommas(+row.original.teamValue)
          : '-'}
      </div>
    ),
  },
];

export const liveLeaderBoardTeamColumns: ColumnDef<Team>[] = [
  {
    accessorKey: 'nickName',
    header: ({ column }) => (
      <div className="px-4">{renderTeamsSortHeader(column, 'Team/Coach')}</div>
    ),
    cell: ({ row }) => {
      return (
        <div
          className={cn(
            'flex flex-col md:flex-row  justify-between items-center px-2 py-1',
          )}
        >
          <div className="flex items-center gap-x-3 ml-3">
            <p>{row?.original?.rank}</p>
            <PlayerAvatar
              avatarUrl={
                row?.original?.profileImage ?? getDefaultProfileImage()
              }
              isUser={true}
            />
            <p className="text-[14px] leading-[16px] font-semibold font-inter text-black-100 ml-3 capitalize">
              {row?.original?.nickName
                ? row?.original?.nickName
                : row?.original?.firstName + ' ' + row?.original?.lastName!}
            </p>
          </div>
          {row?.original?.name && (
            <Badge
              variant={'default'}
              className={`w-full mt-1 md:mt-0 flex justify-center items-center md:w-fit ${row?.original?.myTeam ? 'bg-secondary-100' : 'bg-primary-600'} text-white ml-[9px] capitalize`}
            >
              {row?.original?.name}
            </Badge>
          )}
        </div>
      );
    },
  },

  {
    accessorKey: 'winningPrice',
    header: ({ column }) => (
      <div className="px-4 flex justify-center items-center">
        {renderTeamsSortHeader(column, 'Winning SmartCoins')}
      </div>
    ),

    cell: ({ row }) =>
      row?.original?.winningPrice ? (
        <Tooltip
          content={`Winning Percentage: ${row?.original?.winnerPercentage}%`}
          placement="bottom"
          className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
        >
          <div
            className={cn('flex space-x-1 justify-center py-1 items-center')}
          >
            <Image
              src="/fantasy/images/smartbCoin.svg"
              width={17}
              height={17}
              alt="coin"
              unoptimized={true}
            />
            <span className="text-base text-secondary-100 font-bold">
              {row?.original?.winningPrice}
            </span>
          </div>
        </Tooltip>
      ) : (
        '-'
      ),
  },
  {
    accessorKey: 'liveScore',

    header: ({ column }) => (
      <div className="px-4 flex justify-center items-center">
        {renderTeamsSortHeader(column, 'Live Score')}
      </div>
    ),

    cell: ({ row }) => (
      <div className="flex space-x-1 justify-center items-center">
        <span className="text-base text-secondary-100 font-bold">
          {row?.original?.liveScore ? row?.original?.liveScore : '-'}
        </span>
      </div>
    ),
  },

  {
    accessorKey: 'teamValue',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderTeamsSortHeader(column, 'Team Value')}
      </div>
    ),

    cell: ({ row }) => (
      <div>
        {row.original.teamValue
          ? '$' + formatNumberWithCommas(+row.original.teamValue)
          : '-'}
      </div>
    ),
  },
];

export const leaderboardTeamColumns: ColumnDef<Team>[] = [
  {
    accessorKey: 'rank',
    header: ({ column }) => (
      <div className="px-4">{renderTeamsSortHeader(column, 'Team/Coach', 'start')}</div>
    ),
    cell: ({ row }) => {
      const { user } = useUserProfileContext();
      return (
        <div className="flex justify-between items-center px-2 py-1">
          <div className="flex items-center gap-x-2 ml-3">
            <p>{row?.original?.rank}</p>
            <PlayerAvatar
              avatarUrl={row?.original?.profileImage ?? getDefaultUserImage()}
              isUser={true}
            />
            <p className="text-[14px] leading-[16px] font-semibold font-inter text-black-100 ml-3 capitalize">
              {row?.original?.nickName
                ? row?.original?.nickName
                : row?.original?.firstName + ' ' + row?.original?.lastName!}
            </p>
          </div>
          {row?.original?.name && (
            <Badge
              variant={'default'}
              className={`${user?.id === row?.original?.smartbUserId ? 'bg-secondary-100' : 'bg-primary-600'} text-white ml-[9px] rounded-[6px] capitalize`}
            >
              {row?.original?.name}
            </Badge>
          )}
        </div>
      );
    },
  },

  {
    accessorKey: 'teamValue',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderTeamsSortHeader(column, 'Team Value')}
      </div>
    ),

    cell: ({ row }) => (
      <div>${formatNumberWithCommas(+row?.original?.teamValue)}</div>
    ),
  },
];

export const liveCricketPlayerListColumn: ColumnDef<Player>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <div>{renderCricketSortHeader(column, 'Name')}</div>
    ),
    cell: ({ row }) => {
      const player = row.original;

      return renderCricketLivePlayerCell(player);
    },
  },

  {
    accessorKey: 'scoreData.livePoint',
    header: ({ column }) => renderCricketSortHeader(column, 'Live Scores'),

    cell: ({ row }) => {
      const player = row?.original;
      let liveScore = row?.original?.scoreData?.livePoint ?? 0;
      if (player.positionType === 'captain') {
        liveScore *= 2;
      }

      if (player?.positionType === 'viceCaptain') {
        liveScore *= 1.5;
      }

      if (player?.lineupStatus === 'unannounced') {
        return <Tooltip content={`Do Not Play`} placement="bottom" className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]">
          <p className="text-xs text-black-100">DNP</p>
        </Tooltip>
      }

      return <div>{liveScore || '-'}</div>;
    },
  },

  {
    accessorKey: 'scoreData.playerCurrentSalary',
    header: ({ column }) => (
      <div>{renderCricketSortHeader(column, 'Price Changes')}</div>
    ),
    cell: ({ row }) => {
      const playerCurrentSalary = row?.original?.playerValue ?? 0;
      const playerLastSalary = row?.original?.scoreData?.playerCurrentSalary;
      const { change, status } = calculateDifferenceRating(
        playerLastSalary,
        playerCurrentSalary,
      );

      if (status === 'positive') {
        return (
          <div className="flex justify-center items-center">
            <MyChartUpIcon
              fillColor={change === 0 ? '#707070' : '#1C9A6C'}
              unFilledColor={'#707070'}
              numberOfFilled={change}
            />
          </div>
        );
      }
      return (
        <div className="flex justify-center items-center">
          <MyChartDownIcon
            fillColor={change === 0 ? '#707070' : '#E2662C'}
            unFilledColor={'#707070'}
            numberOfFilled={change}
          />
        </div>
      );
    },
  },

  {
    accessorKey: 'scoreData.totalPlayed',
    header: ({ column }) => (
      <div className="w-20">
        {renderCricketSortHeader(column, 'Matches Played')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center w-10 mx-auto">
        {row?.original?.scoreData?.totalPlayed || '-'}
      </div>
    ),
  },
  {
    accessorKey: 'scoreData.sel',
    header: ({ column }) => (
      <div className="w-fit">{renderCricketSortHeader(column, 'Sel%')}</div>
    ),

    cell: ({ row }) => (
      <div>
        {row?.original?.scoreData?.sel ? `${row.original.scoreData.sel}%` : '-'}
      </div>
    ),
  },
];

export const completedCricketPlayerListColumn: ColumnDef<Player>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <div>{renderCricketSortHeader(column, 'Name')}</div>
    ),
    cell: ({ row }) => {
      const player = row?.original;

      return renderCricketLivePlayerCell(player);
    },
    enableColumnFilter: true,
  },

  {
    accessorKey: 'scoreData.lastScore',

    header: ({ column }) => renderCricketSortHeader(column, 'Scores'),
    cell: ({ row }) => {
      let totalSocre: number = row?.original?.scoreData?.lastScore || 0;

      if (row?.original?.positionType === 'captain') {
        totalSocre *= 2;
      }
      if (row?.original?.positionType === 'viceCaptain') {
        totalSocre *= 1.5;
      }

      if (row?.original?.lineupStatus === 'unannounced') {
        return <Tooltip content={`Do Not Play`} placement="bottom" className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]">
          <p className="text-xs text-black-100">DNP</p>
        </Tooltip>
      }

      return <div>{totalSocre || '-'}</div>;
    },
  },

  {
    accessorKey: 'scoreData.playerCurrentSalary',
    header: ({ column }) => (
      <div>{renderCricketSortHeader(column, 'Price Changes')}</div>
    ),
    cell: ({ row }) => {
      const playerCurrentSalary = row?.original?.playerValue ?? 0;
      const playerLastSalary = row?.original?.scoreData?.playerCurrentSalary;
      const { change, status } = calculateDifferenceRating(
        playerLastSalary,
        playerCurrentSalary,
      );

      if (status === 'positive') {
        return (
          <div className="flex justify-center items-center">
            <MyChartUpIcon
              fillColor={change === 0 ? '#707070' : '#1C9A6C'}
              unFilledColor={'#707070'}
              numberOfFilled={change}
            />
          </div>
        );
      }
      return (
        <div className="flex justify-center items-center">
          <MyChartDownIcon
            fillColor={change === 0 ? '#707070' : '#E2662C'}
            unFilledColor={'#707070'}
            numberOfFilled={change}
          />
        </div>
      );
    },
  },

  {
    accessorKey: 'scoreData.totalPlayed',
    header: ({ column }) => (
      <div className="w-20">
        {renderCricketSortHeader(column, 'Matches Played')}
      </div>
    ),
    cell: ({ row }) => {
      return (
        <div className="text-center w-10 mx-auto">
          {row?.original?.scoreData?.totalPlayed || '-'}
        </div>
      );
    },
  },
  {
    accessorKey: 'scoreData.sel',
    header: ({ column }) => (
      <div className="w-fit">{renderCricketSortHeader(column, 'Sel%')}</div>
    ),

    cell: ({ row }) => (
      <div>
        {row?.original?.scoreData?.sel ? `${row.original.scoreData.sel}%` : '-'}
      </div>
    ),
  },
];

export const liveCricketSmartPlayScorePlayerListColumn: ColumnDef<Player>[] = [
  {
    accessorKey: 'rank',
    header: ({ column }: { column: Column<any> }) => (
      <div className="text-center">{renderCricketSortHeader(column, '#')}</div>
    ),
    cell: ({ row }) => {
      const rank = row.index + 1;
      return <div className="text-center">{rank}</div>;
    },
  },
  {
    accessorKey: 'name',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'Player')}
      </div>
    ),
    cell: ({ row }) => {
      const player = row.original;
      return (
        <div className="text-center">{renderCricketLivePlayerCell(player)}</div>
      );
    },
  },
  {
    accessorKey: 'scoreData.livePoint',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'Total Score')}
      </div>
    ),
    cell: ({ row }) => {
      const player = row?.original;
      let liveScore = player?.scoreData?.livePoint ?? 0;
      if (player?.positionType === 'captain') liveScore *= 2;
      if (player?.positionType === 'viceCaptain') liveScore *= 1.5;
      return <div className="text-center">{liveScore || '-'}</div>;
    },
  },
  ...[
    { key: 'totalPlayed', label: 'RUN' },
    { key: 'totalPlayed', label: '4sB' },
    { key: 'totalPlayed', label: '6sB' },
    { key: 'totalPlayed', label: 'SR' },
    { key: 'totalPlayed', label: 'WIC' },
    { key: 'totalPlayed', label: 'MO' },
    { key: 'totalPlayed', label: 'DB' },
    { key: 'totalPlayed', label: 'ER' },
    { key: 'totalPlayed', label: 'CA' },
    { key: 'totalPlayed', label: 'RO' },
    { key: 'totalPlayed', label: 'ST' },
    { key: 'totalPlayed', label: 'DH' },
    { key: 'totalPlayed', label: 'AR' },
  ].map(({ key, label }) => ({
    accessorKey: `scoreData.${key}`,
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, label)}
      </div>
    ),
    cell: ({ row }: { row: any }) => (
      <div className="text-center w-10 mx-auto">
        {row?.original?.scoreData?.[key] || '-'}
      </div>
    ),
  })),
];

export const newCricketTableHeader: ColumnDef<SmartPlayCricketPlayer>[] = [
  {
    id: 'rank',
    accessorKey: 'rank',
    header: ({ column }: { column: Column<any> }) => (
      <div className="text-center">{renderCricketSortHeader(column, '#')}</div>
    ),
    cell: ({ row }) => {
      const rank = row.index + 1;
      return <div className="text-center">{rank}</div>;
    },
    sortingFn: (rowA, rowB) => rowA.index - rowB.index, // Sort based on index
  },
  {
    id: 'player',
    accessorKey: 'name',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'Player')}
      </div>
    ),
    cell: ({ row }) => {
      const player = row.original;
      return (
        <div className="text-center">
          <div className="grid md:grid-cols-[30%_30%_40%] grid-cols-[20%_60%_20%] w-full gap-x-2">
            <div className="flex justify-center items-center">
              <PlayerAvatar
                avatarUrl={player.image || getDefaultProfileImage()}
              />
            </div>
            <div className="flex justify-start">
              <div className="flex flex-col justify-start items-start">
                <p className="truncate ... w-[100px] text-left">
                  {player.name}
                </p>
                <div className="text-xs text-gray-500 truncate ... w-[70x]">
                  {player.teamName}
                </div>
                <PlayerValueChange
                  formatToCustomStyle={formatNumberWithCommas}
                  playerCurrentSalary={player.scoreData.playerCurrentSalary}
                  playerLastSalary={player.scoreData.playerLastSalary}
                />
              </div>
              <span className="text-[9px] text-gray-500">{player.role}</span>
            </div>
          </div>
        </div>
      );
    },
  },
  {
    id: 'scoreData.totalPoints',
    accessorKey: 'scoreData.totalPoints',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'Total Points')}
      </div>
    ),
  },
  {
    id: 'scoreData.runPoint',
    accessorKey: 'scoreData.runPoint',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'Runs')}
      </div>
    ),
  },
  {
    id: 'scoreData.fourPoint',
    accessorKey: 'scoreData.fourPoint',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, '4s')}
      </div>
    ),
  },
  {
    id: 'scoreData.sixPoint',
    accessorKey: 'scoreData.sixPoint',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, '6s')}
      </div>
    ),
  },
  {
    id: 'scoreData.sr150',
    accessorKey: 'scoreData.sr150',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'SR>150')}
      </div>
    ),
  },
  {
    id: 'scoreData.sr120to150',
    accessorKey: 'scoreData.sr120to150',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'SR 120-150')}
      </div>
    ),
  },
  {
    id: 'scoreData.sr50',
    accessorKey: 'scoreData.sr50',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'SR<50')}
      </div>
    ),
  },
  {
    id: 'scoreData.moPoint',
    accessorKey: 'scoreData.moPoint',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'Maidens')}
      </div>
    ),
  },
  {
    id: 'scoreData.dbPoint',
    accessorKey: 'scoreData.dbPoint',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'Dot Balls')}
      </div>
    ),
  },
  {
    id: 'scoreData.er5',
    accessorKey: 'scoreData.er5',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'ER<5.0')}
      </div>
    ),
  },
  {
    id: 'scoreData.er5to6',
    accessorKey: 'scoreData.er5to6',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'ER 5.0-6.5')}
      </div>
    ),
  },
  {
    id: 'scoreData.er9',
    accessorKey: 'scoreData.er9',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'ER>9.0')}
      </div>
    ),
  },
  {
    id: 'scoreData.caPoint',
    accessorKey: 'scoreData.caPoint',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'Catches')}
      </div>
    ),
  },
  {
    id: 'scoreData.roPoint',
    accessorKey: 'scoreData.roPoint',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'Run Outs')}
      </div>
    ),
  },
  {
    id: 'scoreData.stPoint',
    accessorKey: 'scoreData.stPoint',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'Stumpings')}
      </div>
    ),
  },
  {
    id: 'scoreData.allrounderPoint',
    accessorKey: 'scoreData.allrounderPoint',
    header: ({ column }: { column: Column<any> }) => (
      <div className="flex justify-center items-center">
        {renderCricketSortHeader(column, 'All-Rounder')}
      </div>
    ),
  },
];
