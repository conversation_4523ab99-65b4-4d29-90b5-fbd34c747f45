'use client';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import Coins from '@/assets/images/settings/coins.png';
import CoinsBG from '@/assets/images/settings/coinsBG.png';
import BuyCoinspage from '@/components/Settings/BuyCoins';
import MyDetails from '@/components/Settings/MyDetails';
import TransactionsPage from '@/components/Settings/Transactions';
import WithdrawPage from '@/components/Settings/Withdraw';
import Breadcrumbs from '@/components/UI/Breadcrumbs';
import { Button } from '@/components/UI/button';
import CustomTabs from '@/components/UI/CustomTab';
import { useFantasyUser } from '@/helpers/context/fantasyUserContext';
import { useAuthContext } from '@/helpers/context/authContext';
import { RefferEvent } from '@/components/Settings/RefferEvent';

export default function Settings() {
  const [activeTab, setActiveTab] = useState<number>(1);
  const [screenWidth, setScreenWidth] = useState(0);
  const { token, setLoginPopUp } = useAuthContext();

  const searchParams = useSearchParams();
  const buy_smart_b_coins = searchParams.get('buy_smart_b_coins');
  const buycoins = searchParams.get('buycoins');
  const membership = searchParams.get('membership');
  const platform = searchParams.get('platform');

  useEffect(() => {
    if (buy_smart_b_coins) {
      setActiveTab(3);
    }
  }, [buy_smart_b_coins]);

  useEffect(() => {
    if (platform === 'mobile' && !token) {
      setLoginPopUp(true);
    }
  }, [platform, token, setLoginPopUp]);

  useEffect(() => {
    if (buycoins === 'true' || membership === 'true') {
      setActiveTab(3);
    }
  }, [buycoins, membership]);

  const { user: fantasyUser } = useFantasyUser();
  useEffect(() => {
    // This will only run on the client, not on the server
    setScreenWidth(window.innerWidth);

    // Optional: Add event listener to handle screen resize
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup the event listener on component unmount
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const tabs = [
    {
      label: 'My Details',
      labelId: 1,
    },
    {
      label: 'Buy Coins',
      labelId: 3,
    },
    {
      label: 'Refer & Earn',
      labelId: 5,
    },
    {
      label: 'Transactions',
      labelId: 2,
    },

    {
      label: 'Withdraw',
      labelId: 4,
    },
  ];

  const breadcrumbsLinks = [
    { label: 'Home', href: '/' },
    { label: 'SmartPlay', href: '#' },
    { label: 'Settings', href: '#' },
  ];

  const renderActiveTab = () => {
    switch (activeTab) {
      case 1:
        return <MyDetails setActiveTab={setActiveTab} />;
      case 2:
        return <TransactionsPage />;
      case 3:
        return <BuyCoinspage />;
      case 4:
        return <WithdrawPage setActiveTab={setActiveTab} />;
      case 5:
        return <RefferEvent />;
      default:
        return null;
    }
  };

  return (
    <div className="mt-4 rounded-lg bg-off-white-200 ">
      <div className="pt-[33px] max-799:pt-[18px] pb-[12px] max-799:pb-[9px] bg-off-white-200 px-[27px] max-1024:px-0 rounded-t-lg">
        <Breadcrumbs links={breadcrumbsLinks} />
        <h1 className="text-[31.36px] max-799:text-[22.4px] max-799:leading-[28px] font-normal text-black-100 font-veneerCleanSoft">
          Settings
        </h1>
      </div>
      <div className="pt-[21px] max-1024:pt-1.5 px-[27px] max-1024:pb-[14px] bg-off-white-200 ">
        <CustomTabs<number>
          tabs={tabs}
          setActiveTab={setActiveTab}
          activeTab={activeTab}
        />
      </div>
      <div className="flex max-1024:flex-col-reverse gap-1.5 max-1024:gap-0 max-1024:gap-y-1">
        <div className="bg-off-white-200 w-full">
          <div className="pt-[27px] max-1024:pt-[22px] px-[27px] max-1024:px-0 pb-[80px] max-1024:pb-[25px]">
            {renderActiveTab()}
          </div>
        </div>
        {/* {(activeTab !== 4 || screenWidth > 1023) && (
          <div className="bg-white w-[219px] min-w-[219px] min-h-full max-1024:w-full max-1024:flex max-1024:items-center max-1024:justify-between  max-1024:py-[10px] max-1024:px-3 max-1024:rounded-lg shadow-[0px_1px_3px_0px_#0000002b]">
            <div
              className="pt-[46px] max-1024:py-0 px-[40px] max-1024:px-0 pb-[22px] bg-cover bg-no-repeat text-center max-1024:flex max-1024:items-center max-1024:gap-3.5"
              style={{
                backgroundImage:
                  screenWidth > 1023 ? `url(${CoinsBG.src})` : 'none',
              }}
            >
              <div className="w-[62px] max-799:w-[50px] h-[62px] max-799:h-[50px] mx-auto">
                <Image
                  src={Coins}
                  alt="coins"
                  className="m-auto w-full h-full"
                  unoptimized={true}
                />
              </div>
              <div className="mt-[6px]">
                <p className="text-[32px] max-799:text-[22.4px] leading-[45px] max-799:leading-[27px] font-semibold font-inter text-primary-200">
                  {fantasyUser?.coins
                    ? fantasyUser?.coins - (fantasyUser?.holdCoins ?? 0)
                    : 0}
                </p>
                <p className="mt-[6px] text-[12px] leading-[15px] font-normal font-inter text-primary-200">
                  SmartCoins available
                </p>
              </div>
            </div>

            <div className="px-[20px] max-1024:pl-0 max-1024:pr-0 mb-5 max-1024:mb-0 max-1024:flex max-1024:flex-col">
              <Button
                onClick={() => setActiveTab(3)}
                className="w-full max-1024:w-auto"
              >
                Buy coins
              </Button>

              <Button
                variant="outline"
                className="w-full max-1024:w-auto mt-[15px] max-1024:mt-1.5"
                onClick={() => setActiveTab(4)}
              >
                Withdraw
              </Button>
            </div>
          </div>
        )} */}
      </div>
    </div>
  );
}
