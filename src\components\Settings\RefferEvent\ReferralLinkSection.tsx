import React from 'react';
import { Copy } from 'lucide-react';
import SocialIcon from './SocialIcon';
import WhatsApp from '@/components/Icons/Socials/WhatsApp';
import Facebook from '@/components/Icons/Socials/Facebook';
import Twitter from '@/components/Icons/Socials/Twitter';
import Messenger from '@/components/Icons/Socials/Messenger';
import SnapChat from '@/components/Icons/Socials/SnapChat';
import {
  FacebookShareButton,
  TwitterShareButton,
  LinkedinShareButton,
  WhatsappShareButton,
  FacebookMessengerShareButton,
  TelegramShareButton,
} from 'react-share';
import LinkedIn from '@/components/Icons/Socials/LinkedIn';
import { Config } from '@/helpers/context/config';
import Message from '@/components/Icons/Socials/Message';

interface ReferralLinkSectionProps {
  referralLink: string;
  copyToClipboard: () => void;
}

const ReferralLinkSection: React.FC<ReferralLinkSectionProps> = ({ referralLink, copyToClipboard }) => (
  <div>
    <h3 className="text-xl text-[#191919] mb-3">
      Share your unique referral link
    </h3>
    <div className="border rounded-lg overflow-hidden mb-4">
      <input
        type="text"
        value={referralLink}
        readOnly
        className="w-full p-3 text-gray-700 bg-gray-50"
      />
    </div>
    <button
      onClick={copyToClipboard}
      className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 rounded-lg font-medium flex items-center justify-center mb-6"
    >
      Copy link <Copy size={16} className="ml-2" />
    </button>
    <p className="text-gray-700 mb-4">Or share via</p>
    <div className="flex justify-between max-w-[320px] px-4">
      <WhatsappShareButton url={referralLink} title="Check out this link">
        <WhatsApp />
      </WhatsappShareButton>
      <FacebookShareButton url={referralLink} title="Check out this link">
        <Facebook />
      </FacebookShareButton>
      <FacebookMessengerShareButton url={referralLink} title="Check out this link" appId={Config.FacebookAppID || ''}>
        <Messenger />
      </FacebookMessengerShareButton>
      <TwitterShareButton url={referralLink} title="Check out this link">
        <Twitter />
      </TwitterShareButton>
      <SnapChat />
      <LinkedinShareButton url={referralLink} title="Check out this link">
        <LinkedIn />
      </LinkedinShareButton>
      <div className="flex flex-col items-center gap-2">
        <button
          onClick={() => {
            window.open(`sms:?body=${encodeURIComponent(referralLink)}`);
          }}
        >
          <Message />
        </button>
      </div>

    </div>
  </div>
);

export default ReferralLinkSection; 