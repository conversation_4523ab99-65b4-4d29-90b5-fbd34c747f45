import React from 'react';

interface PopularTagProps extends React.SVGProps<SVGSVGElement> {
  fill?: string;
  stroke?: string;
  text?: string;
  textColor?: string;
}

const PopularTag: React.FC<PopularTagProps> = ({
  fill = '#1c9a6c',
  stroke = '#4c7292',
  text = '',
  textColor = '#fff',
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="242"
      height="46"
      viewBox="0 0 242 46"
      {...props}
    >
      <defs>
        <clipPath id="clip-path">
          <path
            d="M18,0H224a18,18,0,0,1,18,18V46a0,0,0,0,1,0,0H0a0,0,0,0,1,0,0V18A18,18,0,0,1,18,0Z"
            transform="translate(839 574)"
            fill="#fff"
          />
        </clipPath>
        <filter id="Path_313855" x="-14" y="-8" width="270" height="57" filterUnits="userSpaceOnUse">
          <feOffset dy="2" in="SourceAlpha" />
          <feGaussianBlur stdDeviation="4.5" result="blur" />
          <feFlood floodOpacity="0.341" />
          <feComposite operator="in" in2="blur" />
          <feComposite in="SourceGraphic" />
        </filter>
      </defs>

      <g transform="translate(-839 -574)" clipPath="url(#clip-path)">
        <g>
          <g transform="translate(839 578)">
            <g>
              <g transform="matrix(1, 0, 0, 1, 0, -4)" filter="url(#Path_313855)">
                <path
                  d="M242,0V3.67h-1.835a47.039,47.039,0,0,0-23.521,6.3l-23.2,13.4a41.986,41.986,0,0,1-21,5.63H69.55a41.986,41.986,0,0,1-21-5.63L25.338,9.968a47,47,0,0,0-23.5-6.3H0V0Z"
                  transform="translate(0 4)"
                  fill={fill}
                  stroke={stroke}
                  strokeWidth="1"
                />
              </g>
            </g>
          </g>
          <rect width="243" height="7" transform="translate(839 574)" fill={fill} />
        </g>

        <text
          transform="translate(960 596)"
          fill={textColor}
          fontSize="14"
          fontFamily="Inter-SemiBold, Inter"
          fontWeight="600"
        >
          <tspan x="-56.104" y="0">{text}</tspan>
        </text>
      </g>
    </svg>
  );
};

export default PopularTag;
