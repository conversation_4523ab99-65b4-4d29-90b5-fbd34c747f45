'use client';

import { useState, useEffect, useMemo } from 'react';
import { FiltersState } from '../Player/PlayerFilter';
import DataTable from '@/components/UI/DataTabel';
import { useStatsContext } from '@/helpers/context/stats';
import { StatsLegend } from '../Commentary/CricketLegend';
import { Button } from '@/components/UI/button';
import { Search, SlidersHorizontal } from 'lucide-react';
import { Input } from '@/components/UI/input';
import CustomisePanel from '../Commentary/CustomisePanel';
import CustomisePanelMobile from '../Commentary/CustomisePanelMobile';
import { VisibilityState } from '@tanstack/react-table';
import { IconButton } from '@material-tailwind/react';
import SettingsIcon from '@/components/UI/Icons/SettingsIcon';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Card } from '@/components/UI/card';
import { cn } from '@/lib/utils';
import Select, { components } from 'react-select';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { Checkbox } from '@/components/UI/checkbox';
import { useCompetition } from '@/helpers/context/competitionContext';
import { SOCCER_SMARTPLAY_STATS_LEGEND } from '@/helpers/constants';
import { SoccerSmartPlayStatsColumns } from '@/components/UI/DataTabel/SoccerSmartPlayStatsColumn';
import { Category } from '@/types/stats';

interface SoccerSmartPlayStatsProps {
  columnVisibility: VisibilityState;
  onColumnVisibilityChange: (visibility: VisibilityState) => void;
  searchQuery: string;
}

const soccerCategories: Category[] = [
  {
    id: 'player',
    name: 'Player Info',
    children: [
      { id: 'name', name: 'Name' },
      { id: 'position', name: 'Position' },
      { id: 'team', name: 'Team' },
    ],
  },
  {
    id: 'stats',
    name: 'Statistics',
    children: [
      { id: 'fantasyPoints', name: 'Fantasy Points' },
      { id: 'goals', name: 'Goals' },
      { id: 'assists', name: 'Assists' },
      { id: 'shots', name: 'Shots' },
      { id: 'passes', name: 'Passes' },
    ],
  },
];

const positionOptions = [
  {
    value: 1,
    label: 'All Positions',
  },
  {
    value: 2,
    label: 'Goalkeeper',
  },
  {
    value: 3,
    label: 'Defender',
  },
  {
    value: 4,
    label: 'Midfielder',
  },
  {
    value: 5,
    label: 'Forward',
  },
];

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const SoccerSmartPlayStats = ({
  columnVisibility,
  onColumnVisibilityChange,
  searchQuery,
}: SoccerSmartPlayStatsProps) => {
  const { soccerSmartPlayStats } = useStatsContext();
  const [showFilter, setShowFilter] = useState(false);
  const [showTableCustomization, setShowTableCustomization] = useState(false);
  const [showTableCustomizationMobile, setShowTableCustomizationMobile] =
    useState(false);
  const [showMobileFilter, setShowMobileFilter] = useState(false);
  const [showLegends, setShowLegends] = useState(false);
  const [selectedPositions, setSelectedPositions] = useState<number[]>([]);
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const [selected, setSelected] = useState<Record<string, boolean>>(
    soccerCategories.reduce(
      (acc, category) => {
        acc[category.id] = true;
        category.children.forEach((child) => {
          acc[child.id] = true;
        });
        return acc;
      },
      {} as Record<string, boolean>,
    ),
  );
  const { eventDetailsResponse } = useCompetition();

  const homeTeam = eventDetailsResponse?.result?.eventDetails?.homeTeam;
  const awayTeam = eventDetailsResponse?.result?.eventDetails?.awayTeam;

  const handlePositionChange = (value: number) => {
    setSelectedPositions((prev) => {
      if (prev.includes(value)) {
        return prev.filter((pos) => pos !== value);
      }
      return [...prev, value];
    });
  };

  const handleTeamChange = (value: string) => {
    setSelectedTeams((prev) => {
      if (prev.includes(value)) {
        return prev.filter((team) => team !== value);
      }
      return [...prev, value];
    });
  };

  const resetFilters = () => {
    setSelectedPositions([]);
    setSelectedTeams([]);
  };

  const filteredData = useMemo(() => {
    if (!searchQuery?.trim() || !soccerSmartPlayStats?.result?.rows) {
      return soccerSmartPlayStats?.result?.rows || [];
    }

    return soccerSmartPlayStats.result.rows.filter((player) =>
      player.player.name.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [searchQuery, soccerSmartPlayStats?.result?.rows]);

  if (filteredData.length === 0) {
    return (
      <div className="flex justify-center items-start h-screen">
        <p className="text-lg text-gray-600 font-bold mt-10">No data found</p>
      </div>
    );
  }

  return (
    <div className="relative">
      <DataTable
        columns={SoccerSmartPlayStatsColumns}
        data={filteredData}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={onColumnVisibilityChange}
      />

      {showTableCustomizationMobile && (
        <CustomisePanelMobile
          isOpen={showTableCustomizationMobile}
          setIsOpen={setShowTableCustomizationMobile}
          categories={soccerCategories}
          selected={selected}
          setSelected={setSelected}
        />
      )}

      <Drawer open={showMobileFilter} onOpenChange={setShowMobileFilter}>
        <DrawerContent className="bg-white mb-[70px]">
          <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md">
            FILTERS
          </div>
          <div className="p-2 space-y-2 mt-10 h-screen">
            {/* Mobile filter content */}
          </div>
        </DrawerContent>
      </Drawer>

      {showFilter && (
        <div className="absolute top-14 right-0 w-80 z-40">
          <Card className="p-4">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Position
                </label>
                <Select
                  className="React desktop-odds-select"
                  options={positionOptions}
                  classNamePrefix="select"
                  placeholder="Select Position"
                  isSearchable={false}
                  components={{ DropdownIndicator }}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Team
                </label>
                <Select
                  className="React desktop-odds-select"
                  options={[
                    { value: 'all', label: 'All Teams' },
                    { value: homeTeam?.name, label: homeTeam?.name },
                    { value: awayTeam?.name, label: awayTeam?.name },
                  ]}
                  classNamePrefix="select"
                  placeholder="Select Team"
                  isSearchable={false}
                  components={{ DropdownIndicator }}
                />
              </div>
            </div>
          </Card>
        </div>
      )}

      {showFilter && (
        <div className="absolute top-14 right-0 w-80 z-40">
          <Card className="p-4">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Position
                </label>
                <div className="space-y-2">
                  {positionOptions.map((option) => (
                    <div key={option.value} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`desktop-position-${option.value}`}
                        checked={selectedPositions.includes(option.value)}
                        onChange={() => handlePositionChange(option.value)}
                        className="h-4 w-4 text-primary-200 focus:ring-primary-200 border-gray-300 rounded"
                      />
                      <label
                        htmlFor={`desktop-position-${option.value}`}
                        className="ml-2 text-sm text-gray-700"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Team
                </label>
                <div className="space-y-2">
                  {[
                    { value: 'all', label: 'All Teams' },
                    { value: homeTeam?.name, label: homeTeam?.name },
                    { value: awayTeam?.name, label: awayTeam?.name },
                  ].map((option) => (
                    <div key={option.value} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`desktop-team-${option.value}`}
                        checked={selectedTeams.includes(option.value || '')}
                        onChange={() => handleTeamChange(option.value || '')}
                        className="h-4 w-4 text-primary-200 focus:ring-primary-200 border-gray-300 rounded"
                      />
                      <label
                        htmlFor={`desktop-team-${option.value}`}
                        className="ml-2 text-sm text-gray-700"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex justify-end mt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowFilter(false)}
                  className="bg-primary-200 text-white hover:bg-primary-300"
                >
                  Apply Filters
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}

      <div className="mt-4">
        <StatsLegend stats={SOCCER_SMARTPLAY_STATS_LEGEND} />
      </div>
    </div>
  );
};

export default SoccerSmartPlayStats;
