import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Card } from '@/components/UI/card';
import { Button } from '@/components/UI/button';
import { Checkbox } from '@/components/UI/checkbox';
import { Table } from '@tanstack/react-table';
import { SmartPlayCricketPlayer } from '../../../types/commentry';
import type { Player } from '../../../types/competitions';

type CricketPlayerFilterMobileProps<T> = {
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedTeam: string;
  setSelectedTeam: (team: string) => void;
  homeTeam?: string;
  awayTeam?: string;
};

const CricketPlayerFilterMobile = <T extends { teamName: string }>({
  open,
  setOpen,
  selectedTeam,
  setSelectedTeam,
  homeTeam,
  awayTeam,
}: CricketPlayerFilterMobileProps<T>) => {
  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="bg-white mb-[70px]">
        <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md">
          FILTERS
        </div>
        <div className="p-2 space-y-2 mt-10 h-screen">
          <Card className="p-4 shadow-none">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Filters</h3>
                <Button
                  variant="outline"
                  onClick={() => setSelectedTeam('all')}
                  className="text-sm"
                >
                  Reset Filters
                </Button>
              </div>
              <div>
                <label className="block text-xl font-medium text-black-100 mb-2">
                  By Team
                </label>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Checkbox
                      id="all-teams"
                      checked={selectedTeam === 'all'}
                      onCheckedChange={() => setSelectedTeam('all')}
                      className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                    />
                    <label
                      htmlFor="all-teams"
                      className="ml-2 text-sm text-gray-700"
                    >
                      All Teams
                    </label>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id="home-team"
                      checked={selectedTeam === homeTeam}
                      onCheckedChange={() => setSelectedTeam(homeTeam || '')}
                      className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                    />
                    <label
                      htmlFor="home-team"
                      className="ml-2 text-sm text-gray-700"
                    >
                      {homeTeam}
                    </label>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id="away-team"
                      checked={selectedTeam === awayTeam}
                      onCheckedChange={() => setSelectedTeam(awayTeam || '')}
                      className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                    />
                    <label
                      htmlFor="away-team"
                      className="ml-2 text-sm text-gray-700"
                    >
                      {awayTeam}
                    </label>
                  </div>
                </div>
              </div>
              <div className="flex justify-end mt-4">
                <Button
                  variant="outline"
                  onClick={() => setOpen(false)}
                  className="bg-primary-200 text-white hover:bg-primary-300"
                >
                  Apply Filters
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default CricketPlayerFilterMobile;
