'use client';
import React from 'react';

const SmartPlayLogo = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="140.384"
      height="42.335"
      viewBox="0 0 140.384 42.335"
    >
      <defs>
        <radialGradient
          id="radial-gradient"
          cx="0.393"
          cy="-0.106"
          r="0.963"
          gradientTransform="matrix(0.147, 0.997, -1.898, 0.077, -0.894, -0.335)"
          gradientUnits="objectBoundingBox"
        >
          <stop offset="0" stopColor="#e84b23" />
          <stop offset="0.151" stopColor="#e84a23" />
          <stop offset="0.257" stopColor="#e74823" />
          <stop offset="0.35" stopColor="#ec5130" />
          <stop offset="0.435" stopColor="#f36648" />
          <stop offset="0.514" stopColor="#ff806c" />
          <stop offset="0.59" stopColor="#ff9487" />
          <stop offset="0.663" stopColor="#ffc3bc" />
          <stop offset="0.731" stopColor="#ffe4e0" />
          <stop offset="0.797" stopColor="#fff" />
          <stop offset="1" stopColor="#fff" />
        </radialGradient>
        <radialGradient
          id="radial-gradient-2"
          cx="0.393"
          cy="-0.106"
          r="0.964"
          gradientTransform="translate(-0.448) scale(1.896 1)"
          gradientUnits="objectBoundingBox"
        >
          <stop offset="0" stopColor="#ff4713" />
          <stop offset="0.151" stopColor="#fc4714" />
          <stop offset="0.257" stopColor="#f34617" />
          <stop offset="0.35" stopColor="#e3451c" />
          <stop offset="0.435" stopColor="#cd4423" />
          <stop offset="0.514" stopColor="#b1422c" />
          <stop offset="0.59" stopColor="#8e4037" />
          <stop offset="0.663" stopColor="#643d44" />
          <stop offset="0.731" stopColor="#353a53" />
          <stop offset="0.797" stopColor="#003764" />
        </radialGradient>
      </defs>
      <g
        id="Group_115137"
        data-name="Group 115137"
        transform="translate(22555 -4622)"
      >
        <g
          id="Group_114963"
          data-name="Group 114963"
          transform="translate(-22463.148 4625.236)"
        >
          <path
            id="Path_183543"
            data-name="Path 183543"
            d="M332.46,39.783c0,.652-.087.7-2.432.7-2.042,0-2.172,0-2.172-.608V10.809c0-.651.217-.695,2.172-.695,2.172,0,2.432.044,2.432.738Z"
            transform="translate(-313.346 -10.068)"
            fill="#fc4714"
          />
          <path
            id="Path_183544"
            data-name="Path 183544"
            d="M361.055,59.645c0,.522-.348.434-1.955.434-2.042,0-2.346,0-2.346-.652,0-.565.087-1-.261-.782a3.094,3.094,0,0,1-2.52,1.434c-3.214,0-5.517-1.955-5.517-6.168v-8.47c0-4.648,2.91-6.255,5.473-6.255a3.221,3.221,0,0,1,2.433,1.26c.3.391.347.087.347-.695,0-.565.478-.608,2.346-.608,1.651,0,2,0,2,.478Zm-4.561-7.081V46.57c0-1.347-.3-2.867-1.738-2.867-1.39,0-1.737,1.521-1.737,2.867v5.994c0,1.651.3,2.91,1.737,2.91s1.738-1.26,1.738-2.91"
            transform="translate(-327.259 -29.67)"
            fill="#fc4714"
          />
          <path
            id="Path_183545"
            data-name="Path 183545"
            d="M404.677,40.018c-2.52,10.339-4.691,19.07-6.994,28.366-.13.521-.956.565-2.085.565-.912,0-2.085-.044-1.955-.695.652-3.084,1.086-5.126,1.694-8.123a5.028,5.028,0,0,0,.131-1.434,5.146,5.146,0,0,0-.131-1.347c-1.52-6.429-2.606-11.033-4.083-17.462-.087-.434,1.086-.478,2.259-.478s2.215.044,2.3.608c.7,4.387,1.216,7.037,1.868,11.121,0,.13.044.434.26.434.26,0,.26-.3.3-.478.782-4.344,1.26-6.82,1.955-11.164.087-.478,1.086-.521,2.389-.521,1,0,2.215.13,2.085.608"
            transform="translate(-356.155 -29.851)"
            fill="#fc4714"
          />
          <path
            id="Path_183546"
            data-name="Path 183546"
            d="M293.687,11.495a7.4,7.4,0,0,0-4.779-1.52h-4.431c-.522,0-.869-.044-1.086.261a1.766,1.766,0,0,0-.217,1.086V19.3l6.189,9.557a6.829,6.829,0,0,0,4.28-1.591,6.485,6.485,0,0,0,2.172-5.039v-5.69a6.074,6.074,0,0,0-2.129-5.039M290.6,23.832a1.972,1.972,0,0,1-1.564.695,5.566,5.566,0,0,1-.956-.044c-.3-.13-.261-.478-.261-.912V15.318a.907.907,0,0,1,.131-.608c.174-.173.651-.173,1.173-.173a1.808,1.808,0,0,1,1.434.521,3.641,3.641,0,0,1,.565,2.389v4.17a3.139,3.139,0,0,1-.521,2.215"
            transform="translate(-283.174 -9.972)"
            fill="#fc4714"
          />
          <path
            id="Path_183547"
            data-name="Path 183547"
            d="M283.174,48.628v16.9c0,.956.261.956,2.216.956,2,0,2.432.044,2.432-.695v-9.47a3.375,3.375,0,0,1,.037-.53Z"
            transform="translate(-283.174 -36.076)"
            fill="#fc4714"
          />
        </g>
        <g
          id="SmartB_Logo_Full_Colour_dark_background_"
          data-name="SmartB Logo Full Colour (dark background)"
          transform="translate(-22555 4622)"
        >
          <path
            id="Path_183553"
            data-name="Path 183553"
            d="M28.979,31.793H25.434L23.08,26.348q-.085-.226-.326-.837t-.539-1.39q-.3-.78-.61-1.6t-.567-1.5v3.659q0,.993-.014,1.872t-.043,1.39l-.142,3.857H16.529l1.134-19.2H21.69l4.424,11.061q.2.511.525,1.432t.638,2q.312-1.049.638-1.971t.553-1.461l4.424-11.061h3.942l1.134,19.2h-4.4l-.17-3.857q-.029-.538-.043-1.418t-.014-1.858q0-.978.014-1.957t.014-1.687q-.227.6-.5,1.347t-.553,1.461q-.284.709-.539,1.319t-.4.95Z"
            transform="translate(2.655 2.022)"
            fill="#fff"
          />
          <path
            id="Path_183554"
            data-name="Path 183554"
            d="M43.538,12.592h4.169l7.034,19.2H50L48.586,27.17H42.375L40.9,31.793H36.249Zm4,11.118q-.284-.822-.581-1.716t-.567-1.744q-.27-.851-.5-1.6t-.4-1.262q-.17.539-.4,1.29t-.5,1.6q-.27.851-.567,1.73t-.581,1.7Z"
            transform="translate(5.822 2.022)"
            fill="#fff"
          />
          <path
            id="Path_183555"
            data-name="Path 183555"
            d="M53.648,12.6q.255,0,1.021-.014t1.7-.014q.936,0,1.815-.014t1.361-.014a8.891,8.891,0,0,1,5.445,1.39,4.859,4.859,0,0,1,1.815,4.112,4.865,4.865,0,0,1-.879,2.992,5.761,5.761,0,0,1-2.382,1.829,5.369,5.369,0,0,1,1.5,1.276A11.592,11.592,0,0,1,66.3,26.058q.6,1.121,1.205,2.567T68.849,31.8h-5.02q-.908-1.985-1.531-3.5t-1.163-2.553a6.415,6.415,0,0,0-1.064-1.56,1.721,1.721,0,0,0-1.262-.525h-.482V31.8h-4.68Zm4.68,8.14h1.191a2.468,2.468,0,0,0,1.688-.61,2.419,2.419,0,0,0,.695-1.914,3.818,3.818,0,0,0-.128-1.007,2.249,2.249,0,0,0-.411-.822,1.968,1.968,0,0,0-.78-.567,3.056,3.056,0,0,0-1.205-.213H58.327V20.74Z"
            transform="translate(8.616 2.015)"
            fill="#fff"
          />
          <path
            id="Path_183556"
            data-name="Path 183556"
            d="M79.522,12.592l-.011,3.432H75.087V31.793h-4.68V16.024H65.842V12.592Z"
            transform="translate(10.574 2.022)"
            fill="#fff"
          />
          <path
            id="Path_183557"
            data-name="Path 183557"
            d="M13.163,9.434a4.541,4.541,0,0,1-.474,1.415c.707,1.011,2.1,3.2,2.756,4.263,1.073-2.056,2.335-4.249,2.28-6.636A8.8,8.8,0,0,0,.2,7.636a7.467,7.467,0,0,0,.611,4.025,31.615,31.615,0,0,0,1.748,3.651A91.044,91.044,0,0,0,8.8,24.895c.862-1.094,1.736-2.272,2.546-3.4a4.278,4.278,0,1,1-6.309,1.4L.362,22.856C-1.056,27.13,1.866,32,6.07,33.3a8.816,8.816,0,0,0,11.516-8.69c-.318-2.43-1.595-4.628-2.732-6.758a91.418,91.418,0,0,0-5.94-9.014C8.079,9.966,7.186,11.1,6.381,12.251a4.28,4.28,0,1,1,6.782-2.817Z"
            transform="translate(0 0)"
            fill="url(#radial-gradient)"
          />
        </g>
        <g id="SmartB_Logo" transform="translate(-22555 4621.9)">
          <g
            id="Group_29111"
            data-name="Group 29111"
            transform="translate(19.196 14.601)"
          >
            <path
              id="Path_12109"
              data-name="Path 12109"
              d="M109.749,93.2H106.2l-2.348-5.445c-.059-.158-.158-.434-.335-.829-.158-.414-.335-.868-.533-1.381s-.395-1.046-.612-1.6-.395-1.046-.572-1.5v3.65c0,.671,0,1.282-.02,1.874,0,.592-.02,1.046-.039,1.381l-.138,3.867H97.3L98.425,74h4.025l4.419,11.068c.138.335.316.809.533,1.44.217.612.434,1.282.631,1.993.217-.69.414-1.361.631-1.973s.395-1.1.552-1.46L113.655,74H117.6l1.144,19.2h-4.4l-.178-3.867c-.02-.355-.039-.829-.039-1.42s-.02-1.2-.02-1.855,0-1.3.02-1.953c0-.651.02-1.223.02-1.7-.158.395-.316.848-.493,1.342s-.355.986-.552,1.46-.375.908-.533,1.322c-.178.414-.3.73-.395.947Z"
              transform="translate(-97.3 -73.921)"
              fill="#003764"
            />
            <path
              id="Path_12110"
              data-name="Path 12110"
              d="M220.5,74h4.163l7.043,19.2h-4.735l-1.42-4.617h-6.215l-1.48,4.617H213.2Zm4,11.127c-.2-.552-.375-1.125-.572-1.716s-.395-1.184-.572-1.736c-.178-.572-.335-1.1-.493-1.6s-.276-.927-.395-1.263c-.118.355-.237.789-.395,1.282s-.316,1.026-.493,1.6-.375,1.144-.572,1.736-.395,1.144-.572,1.7Z"
              transform="translate(-190.334 -73.921)"
              fill="#003764"
            />
            <path
              id="Path_12111"
              data-name="Path 12111"
              d="M315.52,73.679c.178,0,.513,0,1.026-.02.513,0,1.085-.02,1.7-.02s1.223,0,1.815-.02c.592,0,1.046-.02,1.361-.02a8.993,8.993,0,0,1,5.445,1.381,5.531,5.531,0,0,1,.927,7.1,5.754,5.754,0,0,1-2.387,1.835,5.142,5.142,0,0,1,1.5,1.282,11.468,11.468,0,0,1,1.243,1.914c.395.75.789,1.6,1.2,2.565s.848,2.032,1.342,3.176H325.68c-.612-1.322-1.125-2.486-1.539-3.512-.414-1.006-.809-1.855-1.164-2.545a6.237,6.237,0,0,0-1.065-1.559,1.715,1.715,0,0,0-1.263-.533h-.473v8.148H315.5V73.679Zm4.676,8.148h1.184a2.257,2.257,0,0,0,2.387-2.525,4.136,4.136,0,0,0-.118-1.006,2.053,2.053,0,0,0-.414-.829,2,2,0,0,0-.789-.572,3.123,3.123,0,0,0-1.2-.217H320.2Z"
              transform="translate(-272.452 -73.6)"
              fill="#003764"
            />
            <path
              id="Path_12112"
              data-name="Path 12112"
              d="M401.072,74l-.02,3.433h-4.419V93.2h-4.676V77.433H387.4V74Z"
              transform="translate(-330.167 -73.921)"
              fill="#003764"
            />
          </g>
          <path
            id="Path_12113"
            data-name="Path 12113"
            d="M13.159,9.511l-.079.434-.059.256-.1.276-.217.434,1.243,1.855,1.519,2.427.138-.256.118-.237.118-.237.1-.2.1-.178.118-.217.178-.335.138-.276.158-.355.178-.395.118-.276.2-.473.079-.237.1-.276.1-.276.02-.1.02-.079.02-.059.02-.079.02-.079.039-.118.039-.118.02-.118.02-.1.02-.1.02-.1.02-.118.02-.138.02-.118.02-.118.02-.158V8.248l-.02-.2-.02-.158-.02-.118-.02-.158-.02-.118-.02-.138-.079-.2-.039-.2-.039-.2-.059-.2-.039-.178-.039-.158-.059-.2L17.2,5.821l-.059-.178-.079-.178-.059-.138-.059-.158-.1-.2-.138-.256-.118-.2-.118-.2-.158-.237-.138-.2-.1-.138L16,3.631l-.1-.118-.079-.1-.079-.1-.1-.118-.1-.118-.1-.118-.1-.118-.1-.1-.1-.1-.138-.138-.138-.138-.118-.1-.118-.1L14.5,2.053l-.138-.118L14.2,1.816l-.138-.1-.118-.059L13.83,1.58l-.1-.059-.118-.1-.138-.079-.2-.118-.138-.079-.178-.079L12.8.988l-.178-.1L12.488.83,12.311.751l-.2-.079-.2-.079L11.758.534,11.581.475l-.2-.059-.2-.059L11.009.317,10.871.278,10.654.238,10.456.2,10.3.179l-.158-.02L9.963.139,9.805.12H9.667L9.509.1H8.4L8.227.12H8.089l-.158.02-.158.02L7.6.179,7.4.218,7.26.238,7.083.278,6.925.317,6.767.356,6.629.4,6.491.435,6.333.475,6.175.534,6.037.593,5.9.652,5.721.712,5.583.771,5.465.81,5.327.85,5.189.909,5.051.968l-.118.079-.178.1L4.636,1.2l-.178.1-.138.079-.138.1L4,1.6l-.178.138-.2.118-.178.138-.158.118-.138.118-.1.079L2.9,2.467l-.158.138-.1.1-.138.138-.118.138-.138.158-.158.178-.118.138-.118.158-.079.158-.1.138-.118.158-.118.178-.118.2-.079.138-.118.237-.118.237-.1.2-.079.2L.75,5.624l-.059.138-.079.217-.059.158-.079.237-.059.2-.02.2-.039.158-.039.2L.276,7.3l-.039.237-.02.2L.2,7.952l-.02.2-.02.237v.158l-.02.2v.217l.02.256.02.2.02.2.039.237.059.276.059.237.059.2.059.217.079.217.079.256.079.237.1.256.118.276.1.256.059.138.079.2.079.2.079.2.158.335.118.256.118.237.1.217.118.256.118.237.118.217.1.178.1.2.158.276.138.237L2.8,15.9l.158.276.158.276.158.276.158.276.158.256.217.355.158.256.158.276.2.316.158.256.178.256.138.217.158.256.2.316.138.217.2.3.256.375.217.316.118.178.158.237.138.2.138.2L6.649,22l.2.276.178.256.158.217.217.316.138.2.158.237.158.237.158.217.2.276.2.276.2.276.178.237.178-.237.3-.375.454-.612.316-.414.316-.434.316-.434.395-.533.217-.3.059-.1.118.1.1.079.079.059.118.1.079.079.1.1.158.178.079.1.079.1.079.118.039.059.039.079.059.079.059.079.039.079.059.118.059.118.059.138.039.118.059.178.039.138.039.118.039.138.02.138.02.138.02.118.02.138.02.138v.671l-.02.138-.02.158-.02.138-.039.158-.039.118-.039.138-.039.138-.059.138-.059.138-.079.178-.059.138-.079.138-.118.2-.1.138-.138.2-.1.118-.118.138-.138.138-.178.178-.138.118-.158.138-.2.138-.2.118-.237.138-.138.059-.217.1-.217.079-.2.059-.158.039-.158.039-.178.02-.178.02-.138.02H8.464l-.138-.02-.158-.02-.138-.02-.118-.02-.138-.039-.1-.02-.1-.039-.138-.039-.2-.079-.158-.059-.158-.079-.138-.079-.138-.079-.158-.1-.2-.138-.2-.158-.2-.178-.138-.138-.138-.158-.138-.178-.118-.178-.158-.237L5.07,26.99l-.118-.237-.079-.217-.079-.217L4.7,26l-.059-.3L4.6,25.432l-.02-.237v-.513l.039-.316.039-.256.059-.276.059-.217.138-.375.079-.158.1-.178L.414,22.867l-.039.118L.335,23.1.3,23.262.256,23.4l-.039.158-.079.2-.039.2-.02.158-.039.2-.02.178L0,24.662v.631l.02.217.02.256.02.178.02.2.02.2.039.217.039.256.059.276.079.276.118.355.079.217.1.256.1.217.079.178.079.2.079.158.059.138.059.1.079.138.079.138.118.2.1.158.118.158.1.158.118.158.1.118.158.178.158.178.138.158.138.138.138.138.256.237.256.217.178.138.178.138.217.158.217.158.178.118.237.138.2.118.2.1.237.118.276.138.256.118.2.079.2.079.217.079.256.079.237.059.276.059.3.059.3.059.335.039.3.039.2.02.217.02h.73l.256-.02.2-.02.237-.02.276-.039.3-.059.355-.079.316-.079.256-.079.276-.1.3-.1.335-.138.276-.118.217-.1.276-.138.256-.158.237-.138.276-.178.316-.237.217-.178.3-.256.276-.256.316-.316.138-.158.1-.118.1-.118.138-.158.118-.158.138-.2.1-.138.1-.138.1-.158.059-.138.118-.2.079-.138.1-.178.1-.217.1-.217.118-.276.079-.2.079-.2.079-.237.059-.217.059-.2.079-.3.039-.217.039-.276.039-.237.02-.256.02-.237.02-.3V24.86l-.02-.217-.02-.237-.02-.2-.039-.256-.059-.276-.059-.237-.1-.316-.1-.3-.1-.276-.1-.256-.079-.217-.138-.3-.2-.454-.217-.493-.2-.395L16,20.125l-.178-.335-.2-.395-.178-.335-.2-.375-.2-.375-.2-.375-.217-.355-.3-.493-.217-.375-.237-.375-.178-.3-.178-.3-.256-.414-.256-.414-.2-.3-.2-.3-.217-.335-.276-.493-.256-.375-.256-.395-.217-.335-.256-.375-.256-.375-.2-.276-.276-.395-.2-.276-.178-.256-.178-.256-.237-.335L9.45,9.609l-.237-.316-.3-.395-.1.138-.2.237-.178.237-.237.3-.256.335-.178.237-.256.335-.217.355-.217.316-.3.414-.256.316-.138.2-.079-.039-.079-.059-.079-.059-.237-.2-.158-.178-.178-.217-.138-.178-.158-.256-.1-.158-.079-.178-.079-.158-.1-.237-.079-.237-.079-.276-.039-.256L4.7,9.412l-.02-.3V8.741L4.7,8.5l.02-.2.02-.217.039-.178.039-.178.059-.178L4.932,7.4l.059-.178.079-.2.118-.217.118-.237.118-.158.138-.2L5.761,6l.2-.2.178-.158.2-.158.2-.138.217-.138L7,5.052l.256-.118.2-.079L7.635,4.8l.217-.059.217-.059.316-.039.316-.02h.316l.316.02.256.039.355.079.355.118.276.1.395.178.335.217.3.217.256.217.178.178.178.2.158.2.138.2.138.2.079.158.118.256.1.237.1.256.079.256.059.316.039.276.02.3v.335Z"
            transform="translate(0 0)"
            fill="url(#radial-gradient-2)"
          />
        </g>
      </g>
    </svg>
  );
};

export default SmartPlayLogo;
