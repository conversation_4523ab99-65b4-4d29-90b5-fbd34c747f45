@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
@import '../scss/variable.scss';

body {
  margin: 0;
  padding: 0 !important;
  box-sizing: border-box;
  outline: none;

  .select__control {
    background-color: $color-Light-grey !important;
    border-radius: 8px;
    max-height: 45px;
    // max-width: 400px;
    width: 100%;

    .select__single-value {
      font-family: $regulerFont !important;
      font-size: 16px;
      line-height: 22.4px;
      font-weight: 400;
      color: $color-Black;
      padding: 0px 8px;
    }

    .select__placeholder {
      font-family: $regulerFont !important;
      font-size: 16px;
      line-height: 22.4px;
      font-weight: 400;
      color: $color-Black;
      padding: 0px 8px;
    }

    .select__input-container {
      font-family: $regulerFont !important;
      font-size: 16px;
      line-height: 22.4px;
      font-weight: 400;
      color: $color-Black;
      padding: 0px 8px;
    }

    .select__indicator-separator {
      width: 0px;
    }

    .select__dropdown-indicator {
      padding: 10px;
    }
  }

  .select__menu-portal {
    z-index: 99;
  }

  .select__control,
  .react-select__control {
    &.select__control--menu-is-open {
      box-shadow: none;
    }

    .select__indicator svg {
      cursor: pointer;
    }

    .select__indicator-separator {
      display: none;
    }

    .select__single-value {
      font-size: 16px;
      line-height: 20px;
      padding-left: 10px;
      outline: none;
      cursor: pointer;
      margin: 0px;
      font-family: 'Inter', sans-serif;
    }

    .select__value-container {
      padding: 0px;
      margin: 0px;

      .select__input-container {
        font-size: 16px;
        font-family: 'Inter', sans-serif;
      }
    }

    .select__placeholder {
      font-size: 16px;
      line-height: 20px;
      padding-left: 10px;
      cursor: pointer;
      font-family: $regulerFont;
    }
  }

  .select__menu {
    margin: 0px;
    padding: 0px;
    z-index: 999;
  }

  .select__menu-list {
    padding: 0px;
  }

  .select__menu,
  .react-select__menu {
    .select__menu-list,
    .react-select__menu-list {
      .select__option,
      .react-select__option {
        cursor: pointer;
        font-size: 16px;
        color: $color-Black;
        font-family: $regulerFont;
        line-height: 19px;
        padding: 11px;

        &.select__option--is-focused {
          background-color: $color-grey;
          color: #000;
        }

        &.select__option--is-selected {
          background-color: $color-grey;
          color: $color-Black;
        }
      }
    }

    .react-select__menu-list {
      .select__group {
        .select__group-heading {
          margin-bottom: 0.5rem;
          color: green;
          font-weight: bolder;
          font-size: inherit;
        }
      }
    }
  }

  .common-date-picker {
    margin: 0;
    width: 100%;

    .MuiInputLabel-shrink.Mui-focused {
      color: $color-Accent-1;
    }

    .Mui-focused {
      .MuiOutlinedInput-notchedOutline {
        border-color: $color-Accent-1;
        border-width: 1;
      }
    }

    .MuiOutlinedInput-root {
      background: #e7e9ec;
      border-radius: 8px;
      padding: 0;
      margin-right: 4px;

      @media only screen and (max-width: 1023px) {
        max-width: initial;
        width: 100%;
        margin-right: 0px;
      }

      input {
        padding: 13px 11px;
        font-family: $regulerFont !important;
        font-size: 16px;
        line-height: 19px;
        font-weight: 600;
        color: $color-Black;

        @media (max-width: 799px) {
          padding: 11px 9px;
        }
      }

      fieldset {
        border: none;
      }

      .MuiIconButton-root {
        padding: 0px 8px 0px 0px;
        margin: 0px;

        &:hover {
          background-color: transparent;
        }

        .MuiTouchRipple-root {
          display: none;
        }
      }
    }
  }

  #date-picker-inline::placeholder {
    color: #191919;
    opacity: 1;
  }

  .common-input-wrap {
    input[type='number']::-webkit-inner-spin-button,
    input[type='number']::-webkit-outer-spin-button {
      display: none;
    }

    label {
      font-weight: 400;
      font-family: $regulerFont;
      letter-spacing: 0px;
    }
  }
}

.peer {
  background-color: white !important;
}

@media (min-width: 768px) {
  .responsive-bg {
    background-color: white !important;
  }
}

.filter-select {
  .react-select__control--menu-is-open:hover {
    border-color: $color-Accent-1 !important;
  }

  .react-select__value-container {
    flex-wrap: nowrap;
    overflow-x: auto;

    .react-select__multi-value {
      min-width: auto;
    }
  }

  .react-select__menu {
    .react-select__menu-list {
      .react-select__option {
        border-radius: 8px;
        width: 98%;
        margin: 0px auto 2px;
      }
    }
  }

  .react-select__value-container::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }

  .react-select__value-container::-webkit-scrollbar-track {
    background: #ffffff;
  }

  .react-select__value-container::-webkit-scrollbar-thumb {
    background: $color-Accent-1;
    border-radius: 2px;
  }

  .react-select__value-container::-webkit-scrollbar-thumb:hover {
    background: $color-Accent-1;
  }
}
