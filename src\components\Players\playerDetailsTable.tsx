'use client';
import { useQuery } from '@tanstack/react-query';
import { AnimatePresence, motion } from 'motion/react';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useState } from 'react';

import DownPriceArrow from '@/assets/images/icons/downPriceArrow.svg';
import SortingDown from '@/assets/images/icons/sortingDown.svg';
import SortingUp from '@/assets/images/icons/sortingUp.svg';
import UpPriceArrow from '@/assets/images/icons/upPriceArrow.svg';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { Config } from '@/helpers/context/config';
import { quyerKeys } from '@/lib/queryKeys';
import { calculateDifferenceInK, formatToCustomStyle } from '@/lib/utils';

import { getDefaultProfileImage } from '../../../db/db';
import type { PlayerStateApiResponse } from '../../../types';
import Loader from '../Loader';
import PlayerAvatar from '../UI/PlayerAvatar/indext';
import PlayerStateModal from '../UI/playerStateModal';

interface PlayedProfileDetailsModal {
  selectedSort: string | null;
  selectedLeague: number | null;
  selectedSeason: number | null;
  setMockSuggestions: Dispatch<SetStateAction<Suggestion[]>>;
}

type PlayerScoreData = {
  lastScore: number;
  totalScore: number;
  lastThreeMatch: number;
  lastFiveMatch: number;
  totalPlayed: number;
  avg: number;
  sel: number;
  playerCurrentSalary: number;
  playerLastSalary: number;
};

type Player = {
  id: number;
  role: string;
  squadsId: number;
  tournamentId: number;
  playerId: number;
  name: string;
  image: string | null;
  teamName: string;
  teamId: number;
  scoreData: PlayerScoreData;
  isAdded: boolean;
};

interface SortingType {
  playersIdShort: boolean | null;
  playerLastScoreShort: boolean | null;
  playerTotalScoreShort: boolean | null;
  playerAvgScoreShort: boolean | null;
  playerMatchesShort: boolean | null;
  player3GMAvgShort: boolean | null;
  player5GMAvgShort: boolean | null;
  playerSelShort: boolean | null;
}

type Suggestion = {
  id: string;
  name: string;
};

const fetchLeaguePlayerDetails = async (
  selectedLeague: any,
  selectedSeason: any,
): Promise<PlayerStateApiResponse> => {
  let url = `${Config.fantasyURL}/events/player-list/${selectedLeague}?SportId=4`;
  if (selectedSeason) {
    url += `&seasonId=${selectedSeason}`;
  }

  const res = await axiosInstance.get<PlayerStateApiResponse>(url);
  return res?.data;
};

const PlayerDetailsTable = ({
  selectedSort,
  selectedLeague,
  selectedSeason,
  setMockSuggestions,
}: PlayedProfileDetailsModal) => {
  const { data: leaguePlayerList, isLoading } = useQuery({
    queryFn: () => fetchLeaguePlayerDetails(selectedLeague, selectedSeason),
    queryKey: [quyerKeys.getLeaguePlayerList, selectedLeague, selectedSeason],
    enabled: !!selectedLeague,
  });
  const sortedTableData = leaguePlayerList?.result;

  const [playerModal, setPlayerModal] = useState<boolean>(false);
  const [playersData, setPlayersData] = useState<Player[]>([]);
  const [sortingType, setSortingType] = useState<SortingType>({
    playersIdShort: null,
    playerLastScoreShort: null,
    playerTotalScoreShort: null,
    playerAvgScoreShort: null,
    playerMatchesShort: null,
    player3GMAvgShort: null,
    player5GMAvgShort: null,
    playerSelShort: null,
  });

  const handlePlayerDetailsModal = (item: any) => {
    setPlayerModal(true);
  };

  const handlePlayerDetailsModalClose = () => {
    setPlayerModal(false);
  };

  useEffect(() => {
    if (sortedTableData) {
      // @ts-expect-error
      setPlayersData(sortedTableData);
      const mockSuggestions = sortedTableData?.map((item: any) => {
        return {
          id: item?.id,
          name: item?.name,
        };
      });
      setMockSuggestions(mockSuggestions);
    }
  }, [sortedTableData]);
  useEffect(() => {
    if (selectedSort !== null) {
      if (selectedSort === 'Player (alphabetically A-Z)') {
        const data = [...playersData];
        data?.sort((a: any, b: any) => a?.name.localeCompare(b?.name));
        setPlayersData(data);
      } else if (selectedSort === 'Player (alphabetically Z-A)') {
        const data = [...playersData];
        data?.sort((a: any, b: any) => b?.name.localeCompare(a?.name));
        setPlayersData(data);
      } else if (selectedSort === 'Team (alphabetically A-Z)') {
        const data = [...playersData];
        data?.sort((a: any, b: any) => a?.teamName.localeCompare(b?.teamName));
        setPlayersData(data);
      } else if (selectedSort === 'Team (alphabetically Z-A)') {
        const data = [...playersData];
        data?.sort((a: any, b: any) => b?.teamName.localeCompare(a?.teamName));
        setPlayersData(data);
      }
    }
  }, [selectedSort]);

  const onSortByPlayerId = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...playersData];
      data?.sort((a: any, b: any) => a?.id - b?.id);
      setPlayersData(data);
      setSortingType({
        playersIdShort: true,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    } else {
      const data = [...playersData];
      data?.sort((a: any, b: any) => b?.id - a?.id);
      setPlayersData(data);
      setSortingType({
        playersIdShort: false,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    }
  };

  // sort function for last score
  const onSortByPlayerLastScore = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...playersData];
      data?.sort(
        (a: any, b: any) => a?.scoreData?.lastScore - b?.scoreData?.lastScore,
      );
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: true,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    } else {
      const data = [...playersData];
      data?.sort(
        (a: any, b: any) => b?.scoreData?.lastScore - a?.scoreData?.lastScore,
      );
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: false,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    }
  };

  // sort function total score

  const onSortByPlayerTotalScore = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...playersData];
      data?.sort(
        (a: any, b: any) => a?.scoreData?.totalScore - b?.scoreData?.totalScore,
      );
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: true,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    } else {
      const data = [...playersData];
      data?.sort(
        (a: any, b: any) => b?.scoreData?.totalScore - a?.scoreData?.totalScore,
      );
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: false,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    }
  };

  // sort function avg score

  const onSortByPlayerAvgScore = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...playersData];
      data?.sort((a: any, b: any) => a?.scoreData?.avg - b?.scoreData?.avg);
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: true,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    } else {
      const data = [...playersData];
      data?.sort((a: any, b: any) => b?.scoreData?.avg - a?.scoreData?.avg);
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: false,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    }
  };

  // sort function matches

  const onSortByPlayerMatchesScore = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...playersData];
      data?.sort(
        (a: any, b: any) =>
          a?.scoreData?.totalPlayed - b?.scoreData?.totalPlayed,
      );
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: true,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    } else {
      const data = [...playersData];
      data?.sort(
        (a: any, b: any) =>
          b?.scoreData?.totalPlayed - a?.scoreData?.totalPlayed,
      );
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: false,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    }
  };

  // sort function 3gma avg

  const onSortByPlayer3GMAvgScore = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...playersData];
      data?.sort(
        (a: any, b: any) =>
          a?.scoreData?.lastThreeMatch - b?.scoreData?.lastThreeMatch,
      );
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: true,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    } else {
      const data = [...playersData];
      data?.sort(
        (a: any, b: any) =>
          b?.scoreData?.lastThreeMatch - a?.scoreData?.lastThreeMatch,
      );
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: false,
        player5GMAvgShort: null,
        playerSelShort: null,
      });
    }
  };

  // sort function 5gma avg

  const onSortByPlayer5GMAvgScore = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...playersData];
      data?.sort(
        (a: any, b: any) =>
          a?.scoreData?.lastFiveMatch - b?.scoreData?.lastFiveMatch,
      );
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: true,
        playerSelShort: null,
      });
    } else {
      const data = [...playersData];
      data?.sort(
        (a: any, b: any) =>
          b?.scoreData?.lastFiveMatch - a?.scoreData?.lastFiveMatch,
      );
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: false,
        playerSelShort: null,
      });
    }
  };

  // sort function sel sort

  const onSortByPlayerSelSort = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...playersData];
      data?.sort((a: any, b: any) => a?.scoreData?.sel - b?.scoreData?.sel);
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: true,
      });
    } else {
      const data = [...playersData];
      data?.sort((a: any, b: any) => b?.scoreData?.sel - a?.scoreData?.sel);
      setPlayersData(data);
      setSortingType({
        playersIdShort: null,
        playerLastScoreShort: null,
        playerTotalScoreShort: null,
        playerAvgScoreShort: null,
        playerMatchesShort: null,
        player3GMAvgShort: null,
        player5GMAvgShort: null,
        playerSelShort: false,
      });
    }
  };

  const getPlayerRoleType = (role: any) => {
    if (role === 'Batter') {
      return 'BAT';
    } else if (role === 'Bowler') {
      return 'BOW';
    } else if (role === 'WK-Batter') {
      return 'WKP / BAT';
    } else if (role === 'Bowling Allrounder' || role === 'Batting Allrounder') {
      return 'AR';
    } else {
      return '';
    }
  };

  // Compute class names for sorting icons
  let sortingUpClass = '';
  let sortingDownClass = '';

  if (sortingType?.playersIdShort === null) {
    sortingDownClass = 'asc-sort';
  } else if (sortingType?.playersIdShort) {
    sortingUpClass = 'asc-sort';
  } else {
    sortingDownClass = 'desc-sort';
  }

  // Extract logic for determining the class name into a function or a separate variable
  function getPlayerLastScoreShortClass(sortingType: any) {
    if (sortingType?.playerLastScoreShort === null) {
      return '';
    }
    return sortingType?.playerLastScoreShort ? 'asc-sort' : '';
  }

  // Compute the class name using the function
  const playerLastScoreShortClass = getPlayerLastScoreShortClass(sortingType);

  let sortClass;

  if (sortingType?.playerTotalScoreShort === null) {
    sortClass = '';
  } else if (sortingType?.playerTotalScoreShort) {
    sortClass = ''; // Based on the condition
  } else {
    sortClass = 'desc-sort';
  }

  let avgScoreSortClass;

  if (sortingType?.playerAvgScoreShort === null) {
    avgScoreSortClass = '';
  } else if (sortingType?.playerAvgScoreShort) {
    avgScoreSortClass = 'asc-sort';
  } else {
    avgScoreSortClass = 'desc-sort';
  }

  // Determine the loading, players data, and no data states
  let tableContent;

  if (isLoading) {
    tableContent = (
      <motion.tr
        key="loading"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <td colSpan={8} className="py-2">
          <Loader />
        </td>
      </motion.tr>
    );
  } else if (playersData?.length > 0) {
    tableContent = playersData.map((match, index) => (
      <motion.tr
        key={match.id} // Use a unique key for each row
        layout="position"
        exit={{ opacity: 0 }}
        className={`${
          index % 2 === 0 ? 'bg-[#FDFDFD]' : 'bg-[#F7F7F8]'
        } ${index === playersData.length - 1 ? '' : 'border-b-[1px] border-black-300'}`}
      >
        <motion.td
          className={`px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-medium text-black-100 max-1300:border-r-[2px] max-1300:border-white text-left sticky left-0 z-[9] ${
            index % 2 === 0 ? 'bg-[#FDFDFD]' : 'bg-[#F7F7F8]'
          }`}
          layout="position"
          exit={{ opacity: 0 }}
        >
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center flex-col gap-y-1">
              <div>{match?.id}</div>
              <PlayerAvatar
                avatarUrl={match?.image ?? getDefaultProfileImage()}
                className="!ml-0"
              />
            </div>

            <div className="flex items-center gap-1.5">
              <div>
                <p className="mb-1 text-[12px] leading-[14px] font-inter font-semibold text-black-100">
                  <button
                    className="cursor-pointer"
                    onClick={() => handlePlayerDetailsModal(match)}
                  >
                    {match?.name}{' '}
                  </button>
                  <span className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100">
                    {getPlayerRoleType(match?.role)}
                  </span>
                </p>
                <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100">
                  {match?.teamName}
                </p>
                <div className="flex items-center gap-1 text-[11.42px] leading-[14px] font-inter font-normal text-black-100">
                  {Number(
                    match?.scoreData?.playerCurrentSalary -
                      match?.scoreData?.playerLastSalary,
                  ) >= 0 ? (
                    <UpPriceArrow />
                  ) : (
                    <DownPriceArrow />
                  )}
                  <span>
                    {' '}
                    {'$'}
                    {formatToCustomStyle(match?.scoreData?.playerCurrentSalary)}
                  </span>
                  <span
                    className={
                      Number(
                        match?.scoreData?.playerCurrentSalary -
                          match?.scoreData?.playerLastSalary,
                      ) > 0
                        ? 'text-[#1C9A6C]'
                        : 'text-[#D84727]'
                    }
                  >
                    {calculateDifferenceInK(
                      match?.scoreData?.playerCurrentSalary,
                      match?.scoreData?.playerLastSalary,
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </motion.td>
        <motion.td
          layout="position"
          exit={{ opacity: 0 }}
          className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-center"
        >
          {match?.scoreData?.lastScore ? match?.scoreData?.lastScore : '-'}
        </motion.td>
        {/* Repeat other table columns as above */}

        <motion.td
          layout="position"
          exit={{ opacity: 0 }}
          className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-center"
        >
          {match?.scoreData?.totalScore ? match?.scoreData?.totalScore : '-'}
        </motion.td>

        <motion.td
          layout="position"
          exit={{ opacity: 0 }}
          className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-center"
        >
          {match?.scoreData?.avg ? match?.scoreData?.avg : '-'}
        </motion.td>

        <motion.td
          layout="position"
          exit={{ opacity: 0 }}
          className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-center"
        >
          {match?.scoreData?.totalPlayed ? match?.scoreData?.totalPlayed : '-'}
        </motion.td>

        <motion.td
          layout="position"
          exit={{ opacity: 0 }}
          className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-center"
        >
          {match?.scoreData?.lastThreeMatch
            ? match?.scoreData?.lastThreeMatch
            : '-'}
        </motion.td>

        <motion.td
          layout="position"
          exit={{ opacity: 0 }}
          className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-center"
        >
          {match?.scoreData?.lastFiveMatch
            ? match?.scoreData?.lastFiveMatch
            : '-'}
        </motion.td>

        <motion.td
          layout="position"
          exit={{ opacity: 0 }}
          className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-center"
        >
          {match?.scoreData?.sel ? match?.scoreData?.sel + '%' : '-'}
        </motion.td>
      </motion.tr>
    ));
  } else {
    tableContent = (
      <motion.tr
        key="no-data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <td colSpan={8}>
          <div className="mt-2 p-2 text-center">
            <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter text-black-100">
              No Data Available
            </p>
          </div>
        </td>
      </motion.tr>
    );
  }

  return (
    <div>
      <div className=" rounded-lg  shadow-[0px_1px_3px_0px_#0000002b]">
        <div className="upcoming-matches-table overflow-x-auto rounded-lg max-799:rounded-none border border-black-300 max-h-[900px] h-full overflow-y-auto no-scrollbar">
          <table className="w-full min-w-max table-auto border-collapse ">
            <thead className="sticky top-0 bg-primary-200 z-10">
              <tr className="bg-primary-200 ">
                <th className="w-[242px] px-[15px] py-1.5 text-[11.42px] leading-[14px] text-white text-left max-1300:border-r-[2px] max-1300:border-white sticky left-0 z-[9] bg-primary-200">
                  <button
                    className="flex items-center gap-1 cursor-pointer"
                    onClick={async () =>
                      // @ts-expect-error
                      onSortByPlayerId(sortingType?.playersIdShort)
                    }
                  >
                    Rank{' '}
                    <div className="flex items-center flex-col">
                      <span>
                        <SortingUp className={sortingUpClass} />
                      </span>
                      <span className="mt-[1.3px]">
                        <SortingDown className={sortingDownClass} />
                      </span>
                    </div>
                  </button>
                </th>
                <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                  <button
                    className="flex items-center gap-1 cursor-pointer  "
                    onClick={async () =>
                      onSortByPlayerLastScore(
                        // @ts-expect-error
                        sortingType?.playerLastScoreShort,
                      )
                    }
                  >
                    Last <br /> Score %{' '}
                    <div className="flex items-center flex-col">
                      <span>
                        <SortingUp className={playerLastScoreShortClass} />
                      </span>
                      <span className="mt-[1.3px]">
                        <SortingDown className={playerLastScoreShortClass} />
                      </span>
                    </div>
                  </button>
                </th>
                <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                  <button
                    className="flex items-center gap-1 cursor-pointer  "
                    onClick={async () =>
                      onSortByPlayerTotalScore(
                        // @ts-expect-error
                        sortingType?.playerTotalScoreShort,
                      )
                    }
                  >
                    Total <br /> Score{' '}
                    <div className="flex items-center flex-col">
                      <span>
                        <SortingUp className={sortClass} />
                      </span>
                      <span className="mt-[1.3px]">
                        <SortingDown className={sortClass} />
                      </span>
                    </div>
                  </button>
                </th>
                <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                  <button
                    className="flex items-center gap-1 cursor-pointer  "
                    onClick={async () =>
                      // @ts-expect-error
                      onSortByPlayerAvgScore(sortingType?.playerAvgScoreShort)
                    }
                  >
                    Avg <br /> Score{' '}
                    <div className="flex items-center flex-col">
                      <span>
                        <SortingUp className={avgScoreSortClass} />
                      </span>
                      <span className="mt-[1.3px]">
                        <SortingDown className={avgScoreSortClass} />
                      </span>
                    </div>
                  </button>
                </th>
                <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                  <button
                    className="flex items-center gap-1 cursor-pointer  "
                    onClick={async () =>
                      onSortByPlayerMatchesScore(
                        // @ts-expect-error
                        sortingType?.playerMatchesShort,
                      )
                    }
                  >
                    Matches <br /> Played{' '}
                    <div className="flex items-center flex-col">
                      <span>
                        <SortingUp className={sortingUpClass} />
                      </span>
                      <span className="mt-[1.3px]">
                        <SortingDown className={sortingDownClass} />
                      </span>
                    </div>
                  </button>
                </th>
                <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                  <button
                    className="flex items-center gap-1 cursor-pointer  "
                    onClick={async () =>
                      onSortByPlayer3GMAvgScore(
                        // @ts-expect-error
                        sortingType?.player3GMAvgShort,
                      )
                    }
                  >
                    3 Gm <br /> Avg{' '}
                    <div className="flex items-center flex-col">
                      <span>
                        <SortingUp className={sortingUpClass} />
                      </span>
                      <span className="mt-[1.3px]">
                        <SortingDown className={sortingDownClass} />
                      </span>
                    </div>
                  </button>
                </th>
                <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                  <button
                    className="flex items-center gap-1 cursor-pointer  "
                    onClick={async () =>
                      onSortByPlayer5GMAvgScore(
                        // @ts-expect-error
                        sortingType?.player5GMAvgShort,
                      )
                    }
                  >
                    5 Gm <br /> Avg{' '}
                    <div className="flex items-center flex-col">
                      <span>
                        <SortingUp className={sortingUpClass} />
                      </span>
                      <span className="mt-[1.3px]">
                        <SortingDown className={sortingDownClass} />
                      </span>
                    </div>
                  </button>
                </th>
                <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                  <button
                    className="flex items-center gap-1 cursor-pointer  "
                    onClick={async () =>
                      // @ts-expect-error
                      onSortByPlayerSelSort(sortingType?.playerSelShort)
                    }
                  >
                    Sel%{' '}
                    <div className="flex items-center flex-col">
                      <span>
                        <SortingUp className={sortingUpClass} />
                      </span>
                      <span className="mt-[1.3px]">
                        <SortingDown className={sortingDownClass} />
                      </span>
                    </div>
                  </button>
                </th>
              </tr>
            </thead>
            <tbody>
              <AnimatePresence>{tableContent}</AnimatePresence>
            </tbody>
            ;
          </table>
        </div>
      </div>
      <PlayerStateModal
        playerModal={playerModal}
        handlePlayerDetailsModalClose={handlePlayerDetailsModalClose}
      />
    </div>
  );
};

export default PlayerDetailsTable;
