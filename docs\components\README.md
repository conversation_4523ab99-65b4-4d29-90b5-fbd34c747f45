# Components Documentation

## Common Components

### Overview

Reusable components that are shared across different modules of the application.

### Components List

1. **Commentary Components**

   - Location: `src/components/Common/Commentary/`
   - Purpose: Display live match commentary and updates
   - Key Features:
     - Real-time updates
     - Match events
     - Player actions

2. **Player Components**

   - Location: `src/components/Common/Player/`
   - Purpose: Player information display and management
   - Features:
     - Player cards
     - Statistics display
     - Role management

3. **Scorecard Components**
   - Location: `src/components/Common/Scorecard/`
   - Purpose: Display match and player scores
   - Features:
     - Live scoring
     - Historical performance
     - Team statistics

## Sport-Specific Components

### Football (AFL) Components

1. **Team Builder**

   - Location: `src/components/Competition/Football/`
   - Features:
     - Team formation display
     - Player selection interface
     - Role-based positioning

2. **Player Selection**
   - Location: `src/app/(routes)/competitions/football/`
   - Features:
     - Position-specific selection
     - Salary cap management
     - Team composition rules

### Soccer Components

1. **Team Builder**

   - Location: `src/components/Competition/Soccer/`
   - Features:
     - Formation selection
     - Player positioning
     - Team strategy

2. **Player Selection**
   - Location: `src/app/(routes)/competitions/soccer/`
   - Features:
     - Role-based selection
     - Statistics integration
     - Team balance management

### Rugby League Components

1. **Team Builder**
   - Location: `src/components/Competition/Rugby/`
   - Features:
     - Position management
     - Team composition
     - Player roles

## UI Components

### Basic Components

1. **Buttons**

   - Location: `src/components/UI/button.tsx`
   - Variants:
     - Primary
     - Secondary
     - Ghost
     - Link

2. **Cards**

   - Location: `src/components/UI/CardListing/`
   - Types:
     - Player cards
     - Team cards
     - Competition cards

3. **Tables**
   - Location: `src/components/UI/DataTabel/`
   - Features:
     - Sorting
     - Filtering
     - Pagination
     - Custom columns

### Form Components

1. **Inputs**

   - Location: `src/components/UI/FloatingInput/`
   - Types:
     - Text input
     - Number input
     - Search input

2. **Select Components**

   - Location: `src/components/UI/CommonSelect/`
   - Features:
     - Single select
     - Multi-select
     - Async select

3. **Modals and Dialogs**
   - Location: `src/components/UI/CustomDialog/`
   - Features:
     - Confirmation dialogs
     - Form modals
     - Information modals

## Layout Components

### Page Layout

1. **Header**

   - Location: `src/components/Header/`
   - Features:
     - Navigation menu
     - User profile
     - Notifications

2. **Footer**

   - Location: `src/components/Footer/`
   - Features:
     - Links
     - Social media
     - Copyright

3. **Content Wrapper**
   - Location: `src/components/Layout/ContentWrapper.tsx`
   - Features:
     - Responsive layout
     - Padding management
     - Container width

### Loading Components

1. **Loaders**
   - Location: `src/components/Loading/`
   - Types:
     - Dream team loader
     - Skeleton loader
     - Data table loader

## Icons and Images

1. **Icon Components**

   - Location: `src/components/Icons/`
   - Categories:
     - Action icons
     - Sport icons
     - Navigation icons
     - Social icons

2. **Smart Image**
   - Location: `src/components/Common/SmartImage/`
   - Features:
     - Lazy loading
     - Fallback handling
     - Responsive sizing
