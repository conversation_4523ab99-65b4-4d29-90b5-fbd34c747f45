import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import SmallButton from './SmallButton';

const meta: Meta<typeof SmallButton> = {
  title: 'Components/SmallButton',
  component: SmallButton,
};

export default meta;

type Story = StoryObj<typeof SmallButton>;

export const Default: Story = {
  args: {
    label: 'Click Me',
  },
};

export const WithAction: Story = {
  args: {
    label: 'Click Me',
    onClick: () => alert('Button Clicked!'),
  },
};
