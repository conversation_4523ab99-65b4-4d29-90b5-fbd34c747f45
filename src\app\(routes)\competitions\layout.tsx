import { getAuthCookie, getCurrentPath } from '@/app/auth';
import PageTransitionEffect from '@/components/motions/PageTransitionEffect';
import { Config } from '@/helpers/context/config';

import { Metadata, ResolvingMetadata } from 'next';
import { CompetitionResponse } from '../../../../types/competitions';

type Props = {
  params: { id: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

// Get Competition by ID
export const fetchCompitionServerSide = async (
  eventId: string | null,
  sportId: string | null,
  competitionId: string | null,
): Promise<CompetitionResponse | undefined> => {
  try {
    const res = await fetch(
      `${Config.fantasyURL}/events/competition-details/${competitionId}?eventId=${eventId}&sportId=${sportId}`,
    );
    return res.json();
  } catch (error) {
    console.error(error)
  }
};

export async function generateMetadata(
  { params, searchParams }: Props,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const currentPath = await getCurrentPath();
  const search = new URL(currentPath ?? '');
  const event_id = search.searchParams.get('event_id');
  const sport_id = search.searchParams.get('sport_id');
  const competition_id = search.searchParams.get('competition_id');
  const data = await fetchCompitionServerSide(
    event_id,
    sport_id,
    competition_id,
  );

  const eventName = data?.result?.eventDetails?.eventName ?? '';
  const homeTeamFlag = data?.result?.eventDetails?.homeTeam?.flag ?? '';
  const awayTeamFlag = data?.result?.eventDetails?.awayTeam?.flag ?? '';
  const homeTeamImage = Config.mediaURL + homeTeamFlag;
  const awayTeamImage = Config.mediaURL + awayTeamFlag;

  return {
    title: eventName,
    keywords: eventName,
    description: eventName,
    openGraph: {
      images: [homeTeamImage, awayTeamImage],
    },
  };
}

const layout = ({ children }: { children: React.ReactNode }) => {
  return (
    <PageTransitionEffect variant="quickFade">{children}</PageTransitionEffect>
  );
};

export default layout;
