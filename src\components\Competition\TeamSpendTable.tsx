import React from 'react';

import { useCompetition } from '@/helpers/context/competitionContext';
import { formatNumberWithCommas } from '@/lib/utils';

const TeamSpendTable: React.FC = () => {
  const { eventDetailsResponse } = useCompetition();

  const teamSpendData = eventDetailsResponse?.result?.teamSpend;

  return (
    <table className="w-full rounded-md shadow-md overflow-hidden">
      <thead className="bg-primary-200 text-white rounded-t-md">
        <tr className="text-base">
          <th className="font-semibold text-center px-2 py-1">Position</th>
          <th className="font-semibold text-center px-2 py-1">Total Spend</th>
          <th className="font-semibold text-center px-2 py-1">Average</th>
        </tr>
      </thead>
      <tbody className="mt-2">
        {teamSpendData?.map((data, index) => (
          <tr
            key={data?.id}
            className={`${index % 2 === 1 ? 'bg-gray-50' : ''} text-sm`}
          >
            <td className="text-center px-2 py-1 capitalize">
              {data?.positionType}
            </td>
            <td className="text-center px-2 py-1">
              {data?.totalSpent
                ? '$' + formatNumberWithCommas(+data?.totalSpent)
                : '-'}
            </td>
            <td className="text-center px-2 py-1">
              {data?.average
                ? '$' + formatNumberWithCommas(+data?.average)
                : '-'}
            </td>
          </tr>
        ))}
      </tbody>
      {teamSpendData === undefined ||
        // @ts-expect-error
        (teamSpendData?.length === 0 && (
          <tbody className="mt-2">
            <tr className="text-center">
              <td className="px-[15px] py-3" colSpan={3}>
                <p className="text-[14px] leading-4 font-semibold font-inter text-black-100">
                  No Data Available
                </p>
              </td>
            </tr>
          </tbody>
        ))}
    </table>
  );
};

export default TeamSpendTable;
