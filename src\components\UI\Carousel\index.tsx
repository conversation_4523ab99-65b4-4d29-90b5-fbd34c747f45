// CarouselComponent.tsx
import * as React from 'react';

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/UI/carousel'; // Adjust the import according to your carousel implementation

interface CarouselComponentProps {
  children: React.ReactNode; // Accepts any valid React nodes as children
}

const CarouselComponent: React.FC<CarouselComponentProps> = ({ children }) => {
  return (
    <Carousel className="w-full">
      <CarouselContent>
        {React.Children.map(children, (child) => (
          <CarouselItem className="w-40">
            <div className="p-1">{child}</div>
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  );
};

export default CarouselComponent;
