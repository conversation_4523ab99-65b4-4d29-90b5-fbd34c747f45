import { useCallback, useState } from 'react';

type SortDirection = 'asc' | 'desc' | null;

interface SortConfig<T> {
  key: keyof T | null;
  direction: SortDirection;
}

export const useTableSorting = <T,>() => {
  const [sortConfig, setSortConfig] = useState<SortConfig<T>>({
    key: null,
    direction: null,
  });

  const handleSort = useCallback((key: keyof T) => {
    setSortConfig((prevConfig) => {
      if (prevConfig?.key === key) {
        let newDirection: SortDirection;

        if (prevConfig?.direction === 'desc') {
          newDirection = 'asc';
        } else if (prevConfig?.direction === 'asc') {
          newDirection = null;
        } else {
          newDirection = 'desc';
        }
        return { key, direction: newDirection };
      }
      return { key, direction: 'desc' }; // Default to descending when a new column is clicked
    });
  }, []);

  const sortedData = useCallback(
    (data: T[]) => {
      if (!sortConfig?.key || !sortConfig?.direction) return data;

      const { key, direction } = sortConfig;

      return [...data].sort((a, b) => {
        const compare = (valueA: any, valueB: any) => {
          if (valueA < valueB) return -1;
          if (valueA > valueB) return 1;
          return 0;
        };

        const primaryComparison = compare(a[key], b[key]);

        // Example: Add secondary sorting logic for specific keys
        // if (key === "Rank" && primaryComparison === 0) {
        //   return direction === "asc"
        //     ? compare(a["Last Score"], b["Last Score"])
        //     : -compare(a["Last Score"], b["Last Score"]);
        // }

        return direction === 'asc' ? primaryComparison : -primaryComparison;
      });
    },
    [sortConfig],
  );

  return { sortConfig, handleSort, sortedData };
};
