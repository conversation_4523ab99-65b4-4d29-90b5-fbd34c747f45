import React, { useEffect } from 'react';
import type { UseFormReset } from 'react-hook-form';

import type { CardFormData } from '@/components/UI/CardInput';
import CardInput from '@/components/UI/CardInput';
import CardListing from '@/components/UI/CardListing';
import AmericanExpressIcon from '@/components/UI/Icons/AmericanExpressIcon';
import MasterCardIcon from '@/components/UI/Icons/MasterCardIcon';
import VisaIcon from '@/components/UI/Icons/VisaIcon';
import { Label } from '@/components/UI/label';
import { RadioGroup, RadioGroupItem } from '@/components/UI/radio-group';

import type { CardType } from '../../../../types';
import { usePlanContext } from '@/helpers/context/userPlanContext';

interface PaymentSelectionProps {
  cards: CardType[] | undefined;
  selectedCoinsId?: {
    coinPrice: number;
  };
  result?: {
    fee: number;
    total: number;
  };
  register: any;
  errors: any;
  setValue: any;

  setSelectedOption: React.Dispatch<React.SetStateAction<string | undefined>>;
  selectedOption: string | undefined;
  reset: UseFormReset<CardFormData>;
}

const PaymentSelection: React.FC<PaymentSelectionProps> = ({
  cards,
  selectedCoinsId,
  result,
  register,
  errors,
  setValue,
  setSelectedOption,
  selectedOption,
  reset,
}) => {
  const { validateUserCardData, validateUserCard, setValidateUserCardData } =
    usePlanContext();

  return (
    <div className="p-[18px]">
      <RadioGroup
        value={selectedOption} // Change from defaultValue to value
        onValueChange={(value: string) => {
          if (value === 'new') {
            setValidateUserCardData(undefined);
          }
          if (value !== 'new') {
            validateUserCard({
              cardId: Number(value),
              cardHolderName: '',
              cardExp: '',
              cardNumber: '',
              cvv: '',
              amount: selectedCoinsId?.coinPrice || 0,
            });
          }
          setSelectedOption(value);
        }}
      >
        <div>
          <p className="text-[16px] leading-[19px] font-inter font-semibold text-black-100 mb-1">
            Saved cards
          </p>
          {cards?.map((card) => (
            <CardListing
              key={card.id} // Add key prop for React list rendering
              value={card.id}
              id={card.id}
              cardNumber={card.last4}
              expiry={card.cardExp}
            />
          ))}
        </div>
        <div className="w-full space-y-2">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="new" id="new" />
            <Label
              htmlFor="new"
              className="text-[16px] leading-[19px] font-inter font-semibold text-black-100"
            >
              <div className="flex items-center gap-3">
                <p className="text-[16px] leading-[19px] font-inter font-semibold text-black-100">
                  Use a different card
                </p>
                <div className="flex items-center gap-1">
                  <MasterCardIcon />
                  <VisaIcon />
                  <AmericanExpressIcon />
                </div>
              </div>
            </Label>
          </div>
          {selectedOption === 'new' && (
            <div>
              <CardInput
                register={register}
                errors={errors}
                setValue={setValue}
                reset={reset}
                price={selectedCoinsId?.coinPrice}
              />
            </div>
          )}
        </div>
      </RadioGroup>
      <div className="flex flex-col justify-between items-end pt-[10px] border-b border-secondary-100 mt-6 pb-2">
        <div className="flex gap-x-2 w-full justify-between">
          <p>Sub Total:</p>
          <p>{selectedCoinsId ? '$' + selectedCoinsId?.coinPrice : '-'}</p>
        </div>
        <div className="flex gap-x-2 w-full justify-between">
          <p>Credit Card Fees:</p>
          <p>
            {selectedCoinsId
              ? `$${validateUserCardData?.data?.cardFee || 0}`
              : '-'}
          </p>
        </div>
      </div>
      <div className="flex gap-x-2 w-full justify-between font-bold mt-2">
        <p>Total Cost:</p>
        <p>
          {selectedCoinsId
            ? `$${validateUserCardData?.data?.totalAmount || selectedCoinsId?.coinPrice}`
            : '-'}
        </p>
      </div>
    </div>
  );
};

export default PaymentSelection;
