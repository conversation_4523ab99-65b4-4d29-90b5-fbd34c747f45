'use client';

interface SortingDownIconProps {
  isActive: boolean; // Determines the active state of the icon
}

const SortingDownIcon: React.FC<SortingDownIconProps> = ({ isActive }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="8.658"
      height="5.102"
      viewBox="0 0 8.658 5.102"
      style={{
        opacity: isActive ? 1 : 0.5, // Adjust opacity based on isActive
      }}
    >
      <path
        id="Path_20870"
        data-name="Path 20870"
        d="M3.781.227a.775.775,0,0,1,1.1,0L8.429,3.78A.775.775,0,0,1,7.882,5.1H.776A.775.775,0,0,1,.228,3.78Z"
        transform="translate(8.658 5.102) rotate(180)"
        fill="#c9c9c9"
      />
    </svg>
  );
};

export default SortingDownIcon;
