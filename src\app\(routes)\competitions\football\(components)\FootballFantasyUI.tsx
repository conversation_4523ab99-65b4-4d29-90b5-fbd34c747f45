import React from 'react';
import { generateUniqueId } from '@/lib/utils';
import { footballPlayersByRole } from '../../../../../../types/football';
import { FootballPlayer } from '../../../../../../types/competitions';
import EmptyPlayerCard from '@/components/Common/Player/EmpltyPlayerCard';
import FootballPlayerCard from '@/components/Common/Player/FootballPlayerCard';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import { useCompetition } from '@/helpers/context/competitionContext';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { getDefaultProfileImage } from '../../../../../../db/db';
import PlayerAvatar from '@/components/UI/PlayerAvatar/indext';
import { formatNumberWithCommas } from '@/lib/utils';
import { PlusIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { PUBLIC_IMAGES } from '@/lib/constants/constants';

const FootballFantasyUI = ({ type = 'AFL' }) => {
  const {
    state: {
      playersByRole: {
        BL: SELECTEDBL,
        HBL: SELECTEDHBL,
        MID: SELECTEDMID,
        HFL: SELECTEDHFL,
        FL: SELECTEDFL,
        FOL: SELECTEDFOL,
        IC: SELECTEDIC,
      },
      playerByRoleLimit: { BL, HBL, MID, HFL, FL, FOL, IC },
      reserveState: { reservePlayers, reservePlayersLimit },
    },
    setActiveTabPlayer,
    activeTabPlayer,
    setShowPlayerTabel,
    setOpenReserveModal,
    setActivePlayerPosition,
    activePlayerPosition,
  } = useFootballContext();
  const searchParams = useSearchParams();
  const add_more = searchParams.get('add_more');
  const { eventDetailsResponse, refetchDreamTeam } = useCompetition();
  const dreamTeamId = searchParams.get('dreamTeamId');

  const isPlayerLocked =
    add_more !== 'true' &&
    (eventDetailsResponse?.result?.eventDetails?.status === 'inprogress' ||
      eventDetailsResponse?.result?.eventDetails?.status === 'innings break' ||
      eventDetailsResponse?.result?.eventDetails?.status === 'drink');
  const router = useRouter();
  const pathname = usePathname();




  const handleEditTeam = () => {
    const query = {
      event_id: searchParams.get('event_id'),
      sport_id: searchParams.get('sport_id'),
      tournament_id: searchParams.get('tournament_id'),
      dreamTeamId,
      competition_id: searchParams.get('competition_id'),
      add_more: 'true',
      seasonId: searchParams.get('seasonId'),
    };
    refetchDreamTeam();
    // @ts-expect-error
    router.push(`${pathname}?${new URLSearchParams(query).toString()}`);
  };


  const renderEmptyCards = (
    selectedPlayers: FootballPlayer[],
    limit: number,
    tabSection: keyof footballPlayersByRole,
    handleEditTeam: () => void
  ) => {
    return Array.from({
      length: Math.max(0, limit - (selectedPlayers?.length || 0)),
    }).map((_, index) => (
      <EmptyPlayerCard
        key={generateUniqueId()}
        tabSection={tabSection}
        setActiveTab={setActiveTabPlayer}
        isActive={tabSection === activeTabPlayer}
        setShowPlayerTabel={setShowPlayerTabel}
        handleEditTeam={handleEditTeam}
      />
    ));
  };

  const renderSection = (
    title: string,
    selectedPlayers: FootballPlayer[],
    limit: number,
    tabSection: keyof footballPlayersByRole,
  ) => (
    <>
      <div className="text-center">
        <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
          {title}: {selectedPlayers?.length}/{limit}
        </span>
      </div>
      <div className="flex justify-center md:gap-8 gap-4 flex-wrap">
        {selectedPlayers?.map((player: FootballPlayer) => (
          <FootballPlayerCard
            key={player.id}
            player={player}
            activePlayerTab={tabSection}
            setActivePlayerTab={setActiveTabPlayer}
            setShowPlayerTabel={() => { }}
            isPlayerLocked={isPlayerLocked}
          />
        ))}
        {renderEmptyCards(selectedPlayers, limit, tabSection, handleEditTeam)}
      </div>
    </>
  );

  const renderReserveSection = () => (
    <>
      <div className="text-center">
        <div className="w-full bg-secondary-100 text-white px-3 py-1 text-sm text-black">
          RESERVES
        </div>
      </div>
      <div className="flex justify-center md:gap-8 gap-5 flex-wrap my-10 pb-4">
        {reservePlayers?.map((player, index) => (
          <div key={index} className="flex flex-col gap-2 items-center">
            {player ? (
              <FootballPlayerCard
                key={player.id}
                player={player}
                activePlayerTab="BL"
                setActivePlayerTab={() => { }}
                setShowPlayerTabel={() => { }}
                isActive={false}
                isPlayerLocked={false}
                isReserveType={true}
                playerIndex={index}
              />
            ) : (
              <div className="relative">
                <div className="p-4 bg-white shadow-sm border border-gray-100 rounded-lg md:w-40 w-[150px] relative h-20">
                  <div className="flex justify-center items-centershadow-md rounded-full absolute top-[-15px] left-1/2 transform -translate-x-1/2">
                    <Image
                      src={PUBLIC_IMAGES.DEFAULT_PLAYER_IMAGE}
                      alt="default player image"

                    />
                  </div>
                  <div className="rounded-md absolute bottom-[5px] left-1/2 transform -translate-x-1/2 p-2 py-1">
                    <button
                      className="flex justify-center items-center space-x-1 mt-2"
                      onClick={() => {
                        setActivePlayerPosition(index);
                        setOpenReserveModal(true);
                      }}
                    >
                      <div className={cn(
                        'text-white rounded-md',
                        index === activePlayerPosition ? 'bg-[#1C9A6C]' : 'bg-[#C9C9C9]',
                      )}>
                        <PlusIcon size={30} />
                      </div>

                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </>
  );

  const renderAFLUI = () => (
    <div className="flex flex-col gap-8">
      {renderSection('Back Line', SELECTEDBL, BL, 'BL')}
      {renderSection('Half Back Line', SELECTEDHBL, HBL, 'HBL')}
      {renderSection('Midfield', SELECTEDMID, MID, 'MID')}
      {renderSection('Half Forward Line', SELECTEDHFL, HFL, 'HFL')}
      {renderSection('Forward Line', SELECTEDFL, FL, 'FL')}
      {renderSection('Followers', SELECTEDFOL, FOL, 'FOL')}
      {renderSection('Interchange', SELECTEDIC, IC, 'IC')}
    </div>
  );

  return (
    <div className="bg-[url('/fantasy/images/footballBG.png')] bg-no-repeat bg-center bg-cover relative rounded w-full h-auto flex flex-col justify-start items-center">
      <div className="max-w-6xl mx-auto md:p-8 p-3">
        {type === 'AFL' && renderAFLUI()}
      </div>
    </div>
  );
};

export default FootballFantasyUI;
