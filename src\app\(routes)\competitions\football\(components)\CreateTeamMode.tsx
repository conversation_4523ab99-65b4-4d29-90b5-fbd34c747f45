'use client';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { cn, LocalStorage } from '@/lib/utils';
import FootballFantasyUI from '../(components)/FootballFantasyUI';
import { Button } from '@/components/UI/button';
import CompetitionDetailsHeader from '@/components/Common/Competition/CompetitionDetailsHeader';
import { useCompetition } from '../../../../../helpers/context/competitionContext';
import { useSearchParams } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import FootballPlayerSelectionUI from '../(components)/FootballPlayerSelectionUI';
import {
  footballPlayersByRole,
  FootballPlayersByRoleLimit,
} from '../../../../../../types/football';
import {
  FootballPlayer,
  LastEntryType,
} from '../../../../../../types/competitions';
import PreLoader from '@/components/UI/PreLoader';
import { setApiMessage } from '@/helpers/commonFunctions';
import { useAuthContext } from '@/helpers/context/authContext';
import { quyerKeys } from '@/lib/queryKeys';
import { CheckedState } from '@radix-ui/react-checkbox';
import { Token } from '../../../../../../db/db';
import { useUserProfileContext } from '@/helpers/context/userContext';
import { Config } from '@/helpers/context/config';
import { ALL_AFL_ROLES } from '@/helpers/constants/index';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import DreamTeamLoader from '@/components/Loading/DreamTeamLoader';

type CreateTeamModeProps = {
  handleTeamSubmitConfirmation: ({
    coins,
    bonusCoins,
  }: {
    coins: number;
    bonusCoins: number;
  }) => void;
  coins: number;
  bonusCoins: number;
  teamSubmitConfirmation: boolean;
  setTeamSubmitConfirmation: Dispatch<SetStateAction<boolean>>;
  activeTabPlayer: keyof footballPlayersByRole;
  setActiveTabPlayer: Dispatch<SetStateAction<keyof footballPlayersByRole>>;
  footballRolePlayers: footballPlayersByRole;
  playerByRoleLimit: FootballPlayersByRoleLimit;
  remainingBudget: number;
  lastEntry: LastEntryType;
  clearTeam: () => void;
  removePlayer: (playerId: number, role: keyof footballPlayersByRole) => void;
  showPlayerTabel: boolean;
};

const CreateTeamMode = ({
  handleTeamSubmitConfirmation,
  setTeamSubmitConfirmation,
  teamSubmitConfirmation,
  activeTabPlayer,
  setActiveTabPlayer,
  footballRolePlayers,
  playerByRoleLimit,
  remainingBudget,
  lastEntry,
  clearTeam,
  removePlayer,
  showPlayerTabel,
}: CreateTeamModeProps) => {
  const { BL, HBL, MID, HFL, FL, FOL, IC } = footballRolePlayers || {};
  const {
    BL: BLLIMIT,
    HBL: HBLLIMIT,
    MID: MIDLIMIT,
    HFL: HFLLIMIT,
    FL: FLLIMIT,
    FOL: FOLLIMIT,
    IC: ICLIMIT,
  } = playerByRoleLimit || {};
  const queryClient = useQueryClient();
  const [acceptTerms, setAcceptTerms] = useState<CheckedState>(false);
  const [submitedSuccess, setSubmitedSuccess] = useState(false);

  const { setLoginPopUp } = useAuthContext();


  const {
    rugbyLeaguePlayersByRole,
    eventDetailsResponse,
    refetchDreamTeam,
    isDreamTeamResponseLoading,
    AFLPlayersByRoles,
  } = useCompetition();

  const { openReserveModal, setOpenReserveModal, createReservePlayerPayload, state: { reserveState: { reservePlayers } } } = useFootballContext();
  const searchParams = useSearchParams();
  const dreamTeamId = searchParams.get('dreamTeamId');
  const event_id = searchParams.get('event_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');


  const localEventId = LocalStorage.getItem('event_id');
  const isSameEvent = localEventId === event_id;
  const localEventType = LocalStorage.getItem('event_type');
  const isSameEventType = localEventType === eventDetailsResponse?.result?.eventConfiguration.eventType;
  const playerId = searchParams.get('playerId');

  const headerStats = {
    selectedPlayer: `Player ${BL?.length + HBL?.length + MID?.length + HFL?.length + FL?.length + FOL?.length + IC?.length}/${BLLIMIT + HBLLIMIT + MIDLIMIT + HFLLIMIT + FLLIMIT + FOLLIMIT + ICLIMIT}`,
    remainingSalary: remainingBudget,
    totalScore: 200,
  };

  const values = useUserProfileContext();

  const testUserProfile = {
    nickName: values?.user?.nickName,
    profilePicUrl: Config.mediaURL! + values?.user?.Media?.filePath,
  };

  const testTeamData = {
    favouriteTeam: {
      players: ['Player 1', 'Player 2', 'Player 3'],
    },
    expertTeam: {
      players: ['Player 4', 'Player 5', 'Player 6'],
    },
  };

  const testEventDetails = {
    salaryCap: 5000000,
    eventName: 'Champions League Final',
  };

  const testCompetitionData = {
    currentRank: 10,
    totalRank: 100,
    totalScore: 300,
    totalLivePoints: 150,
  };

  const clearLastEntery = () => {
    const lastPlayer = lastEntry.players[lastEntry.players.length - 1];

    if (lastEntry?.mode === 'MANUAL' && lastPlayer) {
      removePlayer(lastPlayer?.player?.playerId, lastPlayer?.tabSection);
    }
    if (
      lastEntry.mode === 'EXPERT_FAVORITE' ||
      lastEntry.mode === 'FAVORITE' ||
      lastEntry.mode === 'FEELING_LUCKY'
    ) {
      clearTeam();
    }
  };

  const playersByRole: footballPlayersByRole = {
    BL,
    HBL,
    MID,
    HFL,
    FL,
    FOL,
    IC,
  };

  const playerLimits: FootballPlayersByRoleLimit = {
    BL: BLLIMIT,
    HBL: HBLLIMIT,
    MID: MIDLIMIT,
    HFL: HFLLIMIT,
    FL: FLLIMIT,
    FOL: FOLLIMIT,
    IC: ICLIMIT,
  };

  let captain: FootballPlayer | undefined = undefined;
  let viceCaptain: FootballPlayer | undefined = undefined;
  ALL_AFL_ROLES.forEach((role) => {
    playersByRole[role]?.forEach((player) => {
      if (player?.isCaiptain) {
        captain = player;
      }
      if (player?.isViceCaiptain) {
        viceCaptain = player;
      }
    });
  });

  const handelTeamCreation = () => {
    if (Token) {
      if (!captain) {
        setApiMessage('error', 'Please Select Captain');
      }

      if (!viceCaptain) {
        setApiMessage('error', 'Please Select Vice Captain');
      }
      if (captain && viceCaptain) {
        queryClient.refetchQueries({
          queryKey: [quyerKeys.getFantasyUser],
          exact: true,
        });

        if (
          eventDetailsResponse?.result?.eventConfiguration?.eventType ===
          'free' ||
          dreamTeamId
        ) {
          const reservePlayerPayload = reservePlayers.filter((player) => player !== null).map((player, index) => ({
            playerId: player?.playerId ?? 0,
            playerValue: player?.scoreData?.playerCurrentSalary ?? 0,
            reserve: true,
            reserveRank: player?.reserveRank ?? 0,
          }));
          createReservePlayerPayload(reservePlayerPayload);
          handleTeamSubmitConfirmation({
            coins: 0,
            bonusCoins: 0,
          });
        } else {
          // setOpenReserveModal(true);
          setTeamSubmitConfirmation(true);
        }
      }
    } else {
      // setOpenReserveModal(true);
      setLoginPopUp(true);
    }
  };


  useEffect(() => {
    if (!isSameEvent || !isSameEventType) {
      LocalStorage.removeItem('afl_reserve_players');
      clearTeam();
    }
  }, [isSameEvent, event_id, isSameEventType, eventDetailsResponse]);

  const validTeamSelection =
    Object.values(playerLimits).reduce((a, b) => a + b, 0) ===
    Object.values(playersByRole).reduce((a, b) => a + b?.length, 0);



  return (
    <div className="bg-off-white-200 md:px-[32px] p-0">
      <CompetitionDetailsHeader
        stats={headerStats}
        status="team-creation"
        activeTab="Preview"
        userProfile={testUserProfile}
        teamData={testTeamData}
        eventDetails={testEventDetails}
        competitionData={testCompetitionData}
        playerLimits={{
          BL: BLLIMIT,
          HBL: HBLLIMIT,
          MID: MIDLIMIT,
          HFL: HFLLIMIT,
          FL: FLLIMIT,
          FOL: FOLLIMIT,
          IC: ICLIMIT,
        }}
        playersByRole={{
          BL,
          HBL,
          MID,
          HFL,
          FL,
          FOL,
          IC,
        }}
        sportType="football"
      />

      <div
        className="grid grid-cols-1 lg:grid-cols-2 h-full
gap-x-2 mt-2"
      >
        <div className="w-full h-full">
          {(isDreamTeamResponseLoading && playerId) ? <DreamTeamLoader /> : <FootballFantasyUI />}
        </div>
        <FootballPlayerSelectionUI
          activeTab={activeTabPlayer}
          setActiveTab={setActiveTabPlayer}
          playerByRole={AFLPlayersByRoles}
          stats={headerStats}
        />
      </div>

      {/* Bottom Action Buttons */}
      {!openReserveModal && !showPlayerTabel && !teamSubmitConfirmation && (
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center bg-white w-full">
          <div
            className={cn(
              'flex w-full space-x-2 p-4 md:space-x-0  md:mt-0 justify-around items-center',
            )}
          >
            <div className="space-x-2 flex flex-wrap gap-2 justify-center">
              <Button
                size="sm"
                variant="ghost"
                className=" !bg-[#335F83] text-white w-40"
                onClick={clearLastEntery}
              >
                Clear last entry
              </Button>
              <Button
                size="sm"
                className="w-40 "
                onClick={handelTeamCreation}
                disabled={!validTeamSelection}
              > {dreamTeamId ? 'Edit Team' : 'Submit Team'}
              </Button>
              <Button
                size="sm"
                variant="link"
                className=" text-secondary-100 w-40 border-secondary-100 border"
                onClick={() => {
                  LocalStorage.removeItem('afl_dream_team');
                  clearTeam();
                }}
              >
                Clear All
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateTeamMode;
