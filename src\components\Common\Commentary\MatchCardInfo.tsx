import { Card, CardContent, CardHeader, CardTitle } from '@/components/UI/card';
import { generateUniqueId } from '@/lib/utils';
import { MatchDetailUI } from '../../../../db/commentary/indext';
import { CommentaryItem, CommentaryItemAPI } from '../../../../types/commentry';
import CommentaryList from './CommentaryList';

interface MatchInfoProps {
  details: MatchDetailUI[];
  commentaryList: CommentaryItemAPI[];
  hideDetails?: boolean;
}

export default function MatchInfoCard({
  details,
  commentaryList,
  hideDetails = false,
}: Readonly<MatchInfoProps>) {
  return (
    <Card className="w-full">
      {!hideDetails && (
        <CardHeader>
          <CardTitle>Match Info</CardTitle>
        </CardHeader>
      )}
      <CardContent className="space-y-6">
        {/* Match Details */}
        {!hideDetails && (
          <div className="space-y-2">
            {details.map((detail: MatchDetailUI) => (
              <div
                key={generateUniqueId()}
                className="flex justify-between md:space-x-20 space-x-2 py-2 border-b border-b-lightborder last:border-b-0"
              >
                <span className="w-24 text-black font-bold">
                  {detail.label}
                </span>
                <span className=" flex justify-center items-center px-2 text-gray-600">
                  -
                </span>
                <span className="flex-1">{detail.value}</span>
              </div>
            ))}
          </div>
        )}

        <div>
          <CommentaryList commentaries={commentaryList} />
        </div>
      </CardContent>
    </Card>
  );
}
