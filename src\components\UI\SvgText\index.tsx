export default function SvgText() {
  return (
    <svg
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 340.89031891979175 87.16516629854596"
      width="160"
      height="70"
    >
      <rect
        x="0"
        y="0"
        width="340.89031891979175"
        height="87.16516629854596"
        fill="none"
      ></rect>
      <g
        strokeOpacity="0.6"
        fillOpacity="0.6"
        transform="translate(110.26857703684436 47.29972998368248) rotate(0 110.3108709414737 14.932718157431736)"
      >
        <text
          x="0"
          y="20.92969776945643"
          fontFamily="Virgil, Segoe UI Emoji"
          fontSize="23.8923490518909px"
          fill="#1e1e1e"
          textAnchor="start"
          style={{ whiteSpace: 'pre' }}
          direction="ltr"
          dominantBaseline="alphabetic"
        >
          Click on the profile
        </text>
      </g>
      <g strokeLinecap="round">
        <g
          strokeOpacity="0.6"
          fillOpacity="0.6"
          transform="translate(98.14074259525296 60.598433414410465) rotate(0 -44.07037129762648 -25.299216707205233)"
        >
          <path
            d="M0 0 C-9.05 -1.17, -39.6 1.44, -54.29 -7 C-68.98 -15.43, -82.5 -43.33, -88.14 -50.6"
            stroke="#1e1e1e"
            strokeWidth="1.5"
            fill="none"
            strokeDasharray="8 9"
          ></path>
        </g>
        <g
          strokeOpacity="0.6"
          fillOpacity="0.6"
          transform="translate(98.14074259525296 60.598433414410465) rotate(0 -44.07037129762648 -25.299216707205233)"
        >
          <path
            d="M-68.31 -35.38 C-76.11 -41.36, -83.91 -47.35, -88.14 -50.6"
            stroke="#1e1e1e"
            strokeWidth="1.5"
            fill="none"
          ></path>
        </g>
        <g
          strokeOpacity="0.6"
          fillOpacity="0.6"
          transform="translate(98.14074259525296 60.598433414410465) rotate(0 -44.07037129762648 -25.299216707205233)"
        >
          <path
            d="M-82.74 -26.19 C-84.86 -35.79, -86.99 -45.39, -88.14 -50.6"
            stroke="#1e1e1e"
            strokeWidth="1.5"
            fill="none"
          ></path>
        </g>
      </g>
      <mask></mask>
    </svg>
  );
}
