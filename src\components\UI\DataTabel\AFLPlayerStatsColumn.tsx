import { faker } from '@faker-js/faker';
import { Column, ColumnDef, Row } from '@tanstack/react-table';
import PlayerAvatar from '@/components/UI/PlayerAvatar/indext';
import { getDefaultProfileImage, teams } from '../../../../db/db';
import PlayerValueChange from '@/components/Competition/PlayerValueChange';
import { formatNumberWithCommas } from '@/lib/utils';
import { AFLPlayerStats } from '@/helpers/fetchers/stats';
import SortingUpIcon from '../Icons/SortingUpIcon';
import SortingDownIcon from '../Icons/SortingDownIcon';

const renderSortHeader = (
  column: Column<AFLPlayerStats, unknown>,
  label: string,
) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

export const AFLPlayerStatsColumns: ColumnDef<AFLPlayerStats>[] = [
  {
    id: 'rank',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, '#')}
      </div>
    ),
    cell: ({ row, table }) => {
      // Find this row's position in the sorted/filtered data
      const sortedRows = table.getRowModel().rows;
      const rowIndex = sortedRows.findIndex((r) => r.id === row.id);
      return <div className="text-center">{rowIndex + 1}</div>;
    },
    sortingFn: (rowA, rowB) => rowA.index - rowB.index,
  },
  {
    accessorKey: 'player',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'Player')}
      </div>
    ),
    cell: ({ row }: { row: Row<AFLPlayerStats> }) => {
      const player = row.original;
      const image = player?.player?.image ?? getDefaultProfileImage();
      return (
        <div className="text-center">
          <div className="flex space-x-2 max-w-[250px]">
            <div className="flex justify-start items-center">
              <PlayerAvatar avatarUrl={image} />
            </div>
            <div className="flex justify-start">
              <div className="flex flex-col justify-start items-start">
                <p className="truncate ... w-[100px] text-left">
                  {player?.player?.name}
                </p>
                <div className="text-xs text-gray-500 truncate ... w-[70x]">
                  {player?.team?.name}
                </div>
                <PlayerValueChange
                  formatToCustomStyle={formatNumberWithCommas}
                  playerCurrentSalary={0}
                  playerLastSalary={0}
                />
              </div>
              <span className="text-[9px] text-gray-500">
                {player?.position ?? '-'}
              </span>
            </div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'position',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'POS')}
      </div>
    ),
  },
  {
    accessorKey: 'disposals',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'D')}
      </div>
    ),
  },
  {
    accessorKey: 'kicks',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'K')}
      </div>
    ),
  },
  {
    accessorKey: 'handballs',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'HB')}
      </div>
    ),
  },
  {
    accessorKey: 'inside_fifty',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'I50s')}
      </div>
    ),
  },
  {
    accessorKey: 'hitouts',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'HO')}
      </div>
    ),
  },
  {
    accessorKey: 'clearances',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'CLR')}
      </div>
    ),
  },
  {
    accessorKey: 'hitouts_to_advantage',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'HA')}
      </div>
    ),
  },
  {
    accessorKey: 'goals',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'G')}
      </div>
    ),
  },
  {
    accessorKey: 'behinds',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'BE')}
      </div>
    ),
  },
  {
    accessorKey: 'goal_assists',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'GA')}
      </div>
    ),
  },
  {
    accessorKey: 'tackles',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'TAC')}
      </div>
    ),
  },
  {
    accessorKey: 'marks',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'M')}
      </div>
    ),
  },
  {
    accessorKey: 'contested_marks',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'CM')}
      </div>
    ),
  },
];
