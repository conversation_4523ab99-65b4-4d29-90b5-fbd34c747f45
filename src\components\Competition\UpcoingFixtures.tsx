import moment from 'moment';
import Image from 'next/image';

import DefaultTeam from '@/assets/images/icons/defaultTeam.png';
import WinArrow from '@/assets/images/icons/winArrow.svg';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Config } from '@/helpers/context/config';

const UpcoingFixturesPage = () => {
  const { eventDetailsResponse } = useCompetition();
  const eventDetails = eventDetailsResponse?.result?.eventDetails;

  const fetchDayName = (date: any) => {
    const days = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    const d = new Date(date);
    const dayName = days[d.getDay()];
    return dayName;
  };

  const teamLogo = (item: any) => {
    let flagUrl: any;

    if (item?.flag) {
      if (item?.flag.includes('uploads')) {
        flagUrl = Config?.mediaURL + item?.flag;
      } else {
        flagUrl = item?.flag;
      }
    } else {
      flagUrl = DefaultTeam;
    }

    return (
      <Image
        src={flagUrl}
        alt="icon"
        width={36}
        height={36}
        className="object-contain"
        unoptimized={true}
      />
    );
  };

  const renderScore = (teamScore: any, isHomeTeam: boolean) => {
    const score = isHomeTeam ? teamScore?.Tr1C1 : teamScore?.Tr2C1;
    const wickets1 = isHomeTeam ? teamScore?.Tr1CW1 : teamScore?.Tr2CW1;
    const declared1 = isHomeTeam ? teamScore?.Tr1CD1 : teamScore?.Tr2CD1;
    const score2 = isHomeTeam ? teamScore?.Tr1C2 : teamScore?.Tr2C2;
    const wickets2 = isHomeTeam ? teamScore?.Tr1CW2 : teamScore?.Tr2CW2;
    const declared2 = isHomeTeam ? teamScore?.Tr1CD2 : teamScore?.Tr2CD2;

    const hasExtended = isHomeTeam && teamScore?.Exd > '1';

    // Helper function to construct score with declared info
    const constructScore = (
      wickets: number | undefined,
      declared: number | undefined,
    ) => {
      let declaredText = '';
      if (declared === 1) {
        declaredText = 'd';
      }
      return wickets !== undefined ? `/${wickets}${declaredText}` : '';
    };

    // Render extended score
    if (hasExtended) {
      const firstInningScore = constructScore(wickets1, declared1);
      const secondInningScore = constructScore(wickets2, declared2);

      return (
        <span className="text-[14px] leading-[16px] font-inter text-secondary-100 font-semibold mt-[3px]">
          {score ?? ''}
          {firstInningScore} {(score2 || score2 === 0) && `& ${score2}`}
          {secondInningScore}{' '}
        </span>
      );
    }

    // Render normal score
    const firstInningScore = constructScore(wickets1, declared1);

    return (
      <span className="text-[14px] leading-[16px] font-inter text-secondary-100 font-semibold mt-[3px]">
        {score ?? ''}
        {score && firstInningScore}{' '}
      </span>
    );
  };

  const fetchCricketScore = (teamScore: any, teamType: any) => {
    const isHomeTeam = teamType === 'hometeam';
    return renderScore(teamScore, isHomeTeam);
  };

  const scoreBoard = eventDetails?.ScoreBoard;
  const winnerCode = eventDetails?.winnerCode;
  const homeTeam = eventDetails?.homeTeam;
  const awayTeam = eventDetails?.awayTeam;

  return (
    <div className="mt-1.5 border border-black-300 rounded-lg px-[15px] py-3">
      <div className="mb-2">
        <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-700 mb-[9px]">{`${
          eventDetails?.startTime
            ? fetchDayName(eventDetails?.startTime) +
              ' ' +
              moment.utc(eventDetails?.startTime).local().format('DD/MM/YYYY')
            : ''
        } | ${
          eventDetails?.startTime
            ? moment.utc(eventDetails?.startTime).local().format('hh:mm A')
            : ''
        }`}</p>
        <div className="flex items-center justify-between pb-[9px] w-full px-4">
          {/* Home Team Section */}
          <div className="flex items-center justify-start gap-1.5 flex-1 min-w-0">
            <div className="flex-shrink-0">
              <div className="w-9 h-9 rounded-full">{teamLogo(homeTeam)}</div>
            </div>
            <div className="min-w-0">
              <p className="md:text-[14px] text-xs leading-[16px] font-semibold font-inter text-black-100 truncate">
                {homeTeam?.name}
              </p>
              <div className="flex items-center gap-x-2">
                <div className="flex flex-col justify-center items-center">
                  {scoreBoard ? fetchCricketScore(scoreBoard, 'hometeam') : ''}
                </div>
                <div className="flex justify-center items-center pt-[3px]">
                  {winnerCode === 1 && <WinArrow />}
                </div>
              </div>
            </div>
          </div>

          {/* VS Section */}
          <div className="text-base px-4 flex-shrink-0">VS</div>

          {/* Away Team Section */}
          <div className="flex items-center justify-end gap-1.5 flex-1 min-w-0">
            <div className="min-w-0">
              <p className="md:text-[14px] text-xs leading-[16px] font-semibold font-inter text-black-100 truncate text-right">
                {awayTeam?.name}
              </p>
              <div className="flex justify-end items-center gap-x-2">
                <div className="flex justify-center items-center pb-[3px] rotate-180">
                  {winnerCode === 2 && <WinArrow />}
                </div>
                <div className="flex flex-col justify-center items-center">
                  {scoreBoard ? fetchCricketScore(scoreBoard, 'awayteam') : ''}
                </div>
              </div>
            </div>
            <div className="flex-shrink-0">
              <div className="w-9 h-9 rounded-full">{teamLogo(awayTeam)}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpcoingFixturesPage;
