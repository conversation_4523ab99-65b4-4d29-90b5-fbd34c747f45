import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/UI/table';

const invoices = [
  {
    invoice: 'Runs',
    paymentStatus: 'Paid',
    totalAmount: '20',
    paymentMethod: 'Credit Card',
  },
  {
    invoice: 'Fours',
    paymentStatus: 'Pending',
    totalAmount: '20',
    paymentMethod: 'PayPal',
  },
  {
    invoice: 'Sixes',
    paymentStatus: 'Unpaid',
    totalAmount: '20',
    paymentMethod: 'Bank Transfer',
  },
  {
    invoice: 'Strike Rate',
    paymentStatus: 'Paid',
    totalAmount: '20',
    paymentMethod: 'Credit Card',
  },
  {
    invoice: 'Catches Taken',
    paymentStatus: 'Paid',
    totalAmount: '20',
    paymentMethod: 'PayPal',
  },
  {
    invoice: 'Stumping',
    paymentStatus: 'Pending',
    totalAmount: '20',
    paymentMethod: 'Bank Transfer',
  },
];

export function PlayerScoreTable() {
  return (
    <Table className="w-full">
      <TableHeader className="bg-[#E7E9EC]">
        <TableRow>
          <TableHead></TableHead>
          <TableHead>QTY</TableHead>
          <TableHead>Scores</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {invoices.map((invoice) => (
          <TableRow
            key={invoice.invoice}
            className="border-b border-lightborder"
          >
            <TableCell className="font-medium   flex  justify-start pl-10">
              <p className="text-left  ">{invoice.invoice}</p>
            </TableCell>
            <TableCell>{invoice.totalAmount}</TableCell>
            <TableCell>{invoice.totalAmount}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}

export default PlayerScoreTable;
