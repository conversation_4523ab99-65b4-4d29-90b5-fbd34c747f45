export type PlayerStats = {
  lastScore: number;
  totalScore: number;
  lastThreeMatch: number;
  lastFiveMatch: number;
  totalPlayed: number;
  sel?: number;
  playerCurrentSalary: number;
  playerLastSalary: number;
  avg: number;
  liveScore?: number;
  livePoint?: number;
  totalPoint?: number;
  lastPoint?: number;
};

export type BasePlayer = {
  id: string;
  name: string;
  team: string;
  teamName: string;
  role: string;
  price: number;
  points: number;
  selected: boolean;
  number?: number; // Player's jersey number (optional)
  image?: string;
  isCaptain?: boolean;
  isViceCaptain?: boolean;
  playerValue?: number;
  squadsId: number;
  tournamentId: number;
  playerId: number;
  scoreData: {
    lastScore: number;
    totalScore: number;
    lastThreeMatch: number;
    lastFiveMatch: number;
    playerCurrentSalary: number;
    playerLastSalary: number;
    avg: number;
    livePoint?: number;
    totalPlayed: number;
    sel?: number;
  };
};
