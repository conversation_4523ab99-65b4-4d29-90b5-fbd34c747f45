'use client'
import React from 'react'

interface SkeletonDataTableProps {
  rows?: number
}

export const SkeletonDataTable = ({ rows = 10 }: SkeletonDataTableProps) => {
  // Create 10 skeleton rows to match the example
  const skeletonRows = Array(rows || 10).fill(null)
  return (
    <div className="bg-white w-full shadow overflow-hidden">

      {/* Skeleton Rows */}
      <div className="divide-y divide-gray-100">
        {skeletonRows.map((_, index) => (
          <div key={index} className="p-4">
            <div className="grid grid-cols-5 gap-4">
              {/* Name Column with Avatar */}
              <div className="flex items-center space-x-3">
                {/* Avatar Skeleton */}
                <div className="w-10 h-10 bg-gray-100 rounded-full animate-pulse"></div>
                <div className="flex flex-col space-y-2">
                  {/* Name Skeleton */}
                  <div className="h-4 bg-gray-100 rounded w-24 animate-pulse"></div>
                  {/* Team Skeleton */}
                  <div className="h-3 bg-gray-100 rounded w-32 animate-pulse"></div>
                  {/* Salary Skeleton */}
                  <div className="h-3 bg-gray-100 rounded w-20 animate-pulse"></div>
                </div>
                {/* Role Badge (shown randomly) */}
                {Math.random() > 0.8 && (
                  <div className="ml-2 w-6 h-6 rounded-full bg-gray-100 animate-pulse"></div>
                )}
              </div>
              {/* LS Column */}
              <div className="flex justify-center items-center">
                <div className="h-4 bg-gray-100 rounded w-4 animate-pulse"></div>
              </div>
              {/* AVG Column */}
              <div className="flex justify-center items-center">
                <div className="h-4 bg-gray-100 rounded w-12 animate-pulse"></div>
              </div>
              {/* SEL% Column */}
              <div className="flex justify-center items-center">
                <div className="h-4 bg-gray-100 rounded w-4 animate-pulse"></div>
              </div>
              {/* PLG Column */}
              <div className="flex justify-center items-center">
                <div className="h-4 bg-gray-100 rounded w-6 animate-pulse"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
