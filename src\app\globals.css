@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  color: var(--background);
  background: var(--foreground);
  font-family: Inter, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }
}

.tab-svg-icon svg {
  margin: 0px auto;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  display: none;
}

@layer base {
  :root {
    --radius: 0.5rem;
  }
}
