'use client';

import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import * as React from 'react';
import { useController } from 'react-hook-form';

import { Button } from '@/components/UI/button';
import { Calendar } from '@/components/UI/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/UI/popover';
import { cn } from '@/lib/utils';

interface DatePickerProps {
  placeholder: string;
  control: any;
  name: string;
}

export function DatePicker({
  placeholder,
  control,
  name,
}: Readonly<DatePickerProps>) {
  const {
    field: { value, onChange },
    fieldState: { error },
  } = useController({ name, control });

  return (
    <div>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={'outline'}
            size={'lg'}
            className={cn(
              'w-full justify-start text-left font-normal',
              !value && 'text-muted-foreground',
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {value ? (
              format(new Date(value), 'PPP')
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={value ? new Date(value) : undefined}
            onSelect={(selectedDate) => onChange(selectedDate)}
            initialFocus
          />
        </PopoverContent>
      </Popover>
      {error && <span className="text-red-500 text-sm">{error.message}</span>}
    </div>
  );
}
