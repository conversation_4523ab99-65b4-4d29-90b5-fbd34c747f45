import PageTransitionEffect from '@/components/motions/PageTransitionEffect';
import UserMemeberShipProvider from '@/helpers/context/userMembershipContext';
import UserPlanContextProvider from '@/helpers/context/userPlanContext';

type LayoutProps = Readonly<{
  children: React.ReactNode;
}>;

export default function Layout({ children }: LayoutProps) {
  return (
    <UserPlanContextProvider>
      <PageTransitionEffect variant="quickFade">
        <UserMemeberShipProvider>{children}</UserMemeberShipProvider>
      </PageTransitionEffect>
    </UserPlanContextProvider>
  );
}
