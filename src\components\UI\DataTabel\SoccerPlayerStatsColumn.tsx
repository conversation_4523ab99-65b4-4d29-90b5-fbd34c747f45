'use client';

import { Column, ColumnDef, Row } from '@tanstack/react-table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/UI/avatar';
import { cn, formatNumberWithCommas } from '@/lib/utils';
import { SoccerPlayerStat } from '@/helpers/fetchers/stats';
import { Config } from '@/helpers/context/config';
import { getDefaultProfileImage } from '../../../../db/db';
import SortingUpIcon from '../Icons/SortingUpIcon';
import SortingDownIcon from '../Icons/SortingDownIcon';
import PlayerAvatar from '@/components/UI/PlayerAvatar/indext';
import PlayerValueChange from '@/components/Competition/PlayerValueChange';

const renderSortHeader = (
  column: Column<SoccerPlayerStat, unknown>,
  label: string,
) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

export const SoccerPlayerStatsColumns: ColumnDef<SoccerPlayerStat>[] = [
  {
    id: 'rank',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, '#')}
      </div>
    ),
    cell: ({ row, table }) => {
      const sortedRows = table.getRowModel().rows;
      const rowIndex = sortedRows.findIndex((r) => r.id === row.id);
      return <div className="text-center">{rowIndex + 1}</div>;
    },
    sortingFn: (rowA, rowB) => rowA.index - rowB.index,
  },
  {
    accessorKey: 'player',
    header: ({ column }) => (
      <div className="flex justify-start items-center">
        {renderSortHeader(column, 'Player')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      const image = player?.player?.image ?? getDefaultProfileImage();
      return (
        <div className="text-center">
          <div className="flex space-x-2 max-w-[250px]">
            <div className="flex justify-start items-center">
              <PlayerAvatar avatarUrl={image} />
            </div>
            <div className="flex justify-start">
              <div className="flex flex-col justify-start items-start">
                <p className="truncate ... w-[100px] text-left">
                  {player?.player?.name}
                </p>
                <div className="text-xs text-gray-500 truncate ... w-[70x]">
                  {player?.team?.name}
                </div>
                <PlayerValueChange
                  formatToCustomStyle={formatNumberWithCommas}
                  playerCurrentSalary={0}
                  playerLastSalary={0}
                />
              </div>
              <span className="text-[9px] text-gray-500">
                {player?.position ?? '-'}
              </span>
            </div>
          </div>
        </div>
      );
    },
  },
  // General Stats
  {
    accessorKey: 'goals',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'GLS')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.goals ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'assists',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'A')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.assists ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'appearances',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'APR')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.appearances ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'minutesPlayed',
    id: 'minutesPlayed',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'MP')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.minutesPlayed ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'yellowCards',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'YC')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.yellowCards ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'redCards',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'RC')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.redCards ?? '-'}</div>;
    },
  },

  // Attack Stats

  {
    accessorKey: 'shots',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'S')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.shots ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'shotsOnGoal',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'ST')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.shotsOnGoal ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'hit_woodwork',
    id: 'hit_woodwork',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'HW')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.hit_woodwork ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'offside',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'OFF')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.offsides ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'touches',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'T')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{'-'}</div>;
    },
  },
  {
    accessorKey: 'passes',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'P')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.passes ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'crosses',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'C')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.total_crosses ?? '-'}</div>;
    },
  },

  // Defending Stats
  {
    accessorKey: 'interceptions',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'INT')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.interceptions ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'blocks',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'B')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.blocks ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'tackles',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'TAC')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.tackles ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'clearances',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'CLR')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.clearances ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'own_goals',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'OG')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{'-'}</div>;
    },
  },
  {
    accessorKey: 'errors_leading_to_goal',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'ERR')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{'-'}</div>;
    },
  },
  {
    accessorKey: 'penalties_conceded',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'PC')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.pen_committed ?? '-'}</div>;
    },
  },

  // Goalkeeping Stats
  {
    accessorKey: 'clean_sheets',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'CS')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.cleanSheet ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'goals_conceded',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'GC')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.goalsConceded ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'saves',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'SAV')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.saves ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'penalties_saved',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'PS')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{player?.pen_save ?? '-'}</div>;
    },
  },
  {
    accessorKey: 'goal_kicks',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'GK')}
      </div>
    ),
    cell: ({ row }: { row: Row<SoccerPlayerStat> }) => {
      const player = row.original;
      return <div className="text-center">{'-'}</div>;
    },
  },
];
