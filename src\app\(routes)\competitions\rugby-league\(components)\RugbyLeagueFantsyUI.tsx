import React from 'react';
import Image from 'next/image';
import { generateUniqueId } from '@/lib/utils';
import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';
import {
  RugbyLeaguePlayersByRole,
  RugbyPlayer,
} from '../../../../../../types/rugby-league';
import EmptyPlayerCard from '@/components/Common/Player/EmpltyPlayerCard';
import RugbyPlayerCard from '@/components/Common/Player/RugbyPlayerCard';
import { useCompetition } from '@/helpers/context/competitionContext';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { PlusIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { PUBLIC_IMAGES } from '@/lib/constants/constants';

const RugbyLeagueFantsyUI: React.FC = () => {
  const {
    state: {
      playersByRole: {
        BAC: SELECTED_BAC = [],
        BR: SELECTED_BR = [],
        FRF: SELECTED_FRF = [],
        HAL: SELECTED_HAL = [],
        IC: SELECTED_IC = [],
      },
      playerByRoleLimit: { BAC = 0, BR = 0, FRF = 0, HAL = 0, IC = 0 },
      reserveState: { reservePlayers = [], reservePlayersLimit = 0 } = {},
    },
    setActiveTabPlayer,
    activeTabPlayer,
    setShowPlayerTabel,
    openReserveModal,
    setOpenReserveModal,
    activePlayerPosition,
    setActivePlayerPosition,
  } = useRugbyLeagueContext();
  const searchParams = useSearchParams();
  const add_more = searchParams.get('add_more');
  const { eventDetailsResponse, refetchDreamTeam } = useCompetition();
  const router = useRouter();
  const pathname = usePathname();
  const dreamTeamId = searchParams.get('dreamTeamId');

  const handleEditTeam = () => {
    const query = {
      event_id: searchParams.get('event_id'),
      sport_id: searchParams.get('sport_id'),
      tournament_id: searchParams.get('tournament_id'),
      dreamTeamId,
      competition_id: searchParams.get('competition_id'),
      add_more: 'true',
      seasonId: searchParams.get('seasonId'),
    };
    refetchDreamTeam();
    // @ts-expect-error
    router.push(`${pathname}?${new URLSearchParams(query).toString()}`);
  };

  const renderEmptyCards = (
    selectedPlayers: RugbyPlayer[],
    limit: number,
    tabSection: keyof RugbyLeaguePlayersByRole,
  ) => {
    return Array.from({
      length: Math.max(0, limit - (selectedPlayers?.length || 0)),
    }).map((_, index) => (
      <EmptyPlayerCard
        key={generateUniqueId()}
        tabSection={tabSection}
        setActiveTab={setActiveTabPlayer}
        isActive={tabSection === activeTabPlayer}
        setShowPlayerTabel={setShowPlayerTabel}
        handleEditTeam={handleEditTeam}
      />
    ));
  };

  const isPlayerLocked =
    add_more !== 'true' &&
    (eventDetailsResponse?.result?.eventDetails?.status === 'inprogress' ||
      eventDetailsResponse?.result?.eventDetails?.status === 'innings break' ||
      eventDetailsResponse?.result?.eventDetails?.status === 'drink');

  // Reserve Section
  const renderReserveSection = () => (
    <>
      <div className="text-center">
        <div className="w-full bg-secondary-100 text-white px-3 py-1 text-sm text-black">
          RESERVES
        </div>
      </div>
      <div className="flex justify-center md:gap-8 gap-5 flex-wrap my-10 pb-4">
        {reservePlayers?.map((player, index) => (
          <div key={index} className="flex flex-col gap-2 items-center">
            {player ? (
              <RugbyPlayerCard
                key={player.id}
                player={player}
                activePlayerTab="BAC"
                setActivePlayerTab={() => { }}
                setShowPlayerTabel={() => { }}
                isActive={false}
                isPlayerLocked={false}
                isReserveType={true}
                playerIndex={index}
              />
            ) : (
              <div className="relative">
                <div className="p-4 bg-white shadow-sm border border-gray-100 rounded-lg md:w-40 w-[150px] relative h-20">
                  <div className="flex justify-center items-centershadow-md rounded-full absolute top-[-15px] left-1/2 transform -translate-x-1/2">
                    <Image
                      src={PUBLIC_IMAGES.DEFAULT_PLAYER_IMAGE}
                      alt="default player image"
                    />
                  </div>
                  <div className="rounded-md absolute bottom-[5px] left-1/2 transform -translate-x-1/2 p-2 py-1">
                    <button
                      className="flex justify-center items-center space-x-1 mt-2"
                      onClick={() => {
                        setActivePlayerPosition(index);
                        setOpenReserveModal(true);
                      }}
                    >
                      <div className={cn(
                        'text-white rounded-md',
                        index === activePlayerPosition ? 'bg-[#1C9A6C]' : 'bg-[#C9C9C9]',
                      )}>
                        <PlusIcon size={30} />
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </>
  );

  return (
    <div className="relative rounded w-full h-auto flex flex-col justify-start items-center">
      <Image
        src="/fantasy/images/NRLFantsy.png"
        alt="Football Background"
        className="rounded-md hidden md:block"
        layout="fill"
        objectFit="cover"
        quality={100}
      />
      <Image
        src="/fantasy/images/NRLFantsyMobile.png"
        alt="Football Background"
        className="rounded-md md:hidden block"
        layout="fill"
        objectFit="cover"
        quality={100}
      />
      <div className="relative z-10">
        <div className="max-w-6xl mx-auto md:p-8 p-3">
          <div className="flex flex-col gap-[3rem]">
            <div className="text-center">
              <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
                Backs: {SELECTED_BAC?.length || 0}/{BAC}
              </span>
            </div>
            <div className="flex justify-center gap-2 gap-y-8 flex-wrap">
              {SELECTED_BAC?.length > 0 &&
                SELECTED_BAC?.toSorted(
                  (a, b) => a?.playerId - b?.playerId,
                )?.map((player) => (
                  <RugbyPlayerCard
                    key={player.id}
                    player={player}
                    activePlayerTab="BAC"
                    setActivePlayerTab={setActiveTabPlayer}
                    setShowPlayerTabel={() => { }}
                    isPlayerLocked={isPlayerLocked}
                  />
                ))}
              {renderEmptyCards(SELECTED_BAC, BAC, 'BAC')}
            </div>
            <div className="text-center">
              <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
                Halves: {SELECTED_HAL?.length || 0}/{HAL}
              </span>
            </div>
            <div className="flex justify-center gap-2 gap-y-8 flex-wrap">
              {SELECTED_HAL?.length > 0 &&
                SELECTED_HAL?.toSorted(
                  (a, b) => a?.playerId - b?.playerId,
                )?.map((player) => (
                  <RugbyPlayerCard
                    key={player.id}
                    player={player}
                    activePlayerTab="HAL"
                    setActivePlayerTab={setActiveTabPlayer}
                    setShowPlayerTabel={() => { }}
                    isPlayerLocked={isPlayerLocked}
                  />
                ))}
              {renderEmptyCards(SELECTED_HAL, HAL, 'HAL')}
            </div>
            <div className="text-center">
              <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
                Back Rowers: {SELECTED_BR?.length || 0}/{BR}
              </span>
            </div>
            <div className="flex justify-center gap-2 gap-y-8 flex-wrap">
              {SELECTED_BR?.length > 0 &&
                SELECTED_BR?.toSorted((a, b) => a?.playerId - b?.playerId)?.map(
                  (player) => (
                    <RugbyPlayerCard
                      key={player.id}
                      player={player}
                      activePlayerTab="BR"
                      setActivePlayerTab={setActiveTabPlayer}
                      setShowPlayerTabel={() => { }}
                      isPlayerLocked={isPlayerLocked}
                    />
                  ),
                )}
              {renderEmptyCards(SELECTED_BR, BR, 'BR')}
            </div>
            <div className="text-center">
              <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
                Front Row Forwards: {SELECTED_FRF?.length}/{FRF}
              </span>
            </div>
            <div className="flex justify-center gap-2 gap-y-8 flex-wrap">
              {SELECTED_FRF?.length > 0 &&
                SELECTED_FRF?.toSorted(
                  (a, b) => a?.playerId - b?.playerId,
                )?.map((player) => (
                  <RugbyPlayerCard
                    key={player.id}
                    player={player}
                    activePlayerTab="FRF"
                    setActivePlayerTab={setActiveTabPlayer}
                    setShowPlayerTabel={() => { }}
                    isPlayerLocked={isPlayerLocked}
                  />
                ))}
              {renderEmptyCards(SELECTED_FRF, FRF, 'FRF')}
            </div>
            <div className="text-center">
              <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
                Interchange: {SELECTED_IC?.length}/{IC}
              </span>
            </div>
            <div className="flex justify-center gap-2 gap-y-8 flex-wrap">
              {SELECTED_IC?.length > 0 &&
                SELECTED_IC?.toSorted((a, b) => a?.playerId - b?.playerId)?.map(
                  (player) => (
                    <RugbyPlayerCard
                      key={player.id}
                      player={player}
                      activePlayerTab="IC"
                      setActivePlayerTab={setActiveTabPlayer}
                      setShowPlayerTabel={() => { }}
                      isPlayerLocked={isPlayerLocked}
                    />
                  ),
                )}
              {renderEmptyCards(SELECTED_IC, IC, 'IC')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RugbyLeagueFantsyUI;
