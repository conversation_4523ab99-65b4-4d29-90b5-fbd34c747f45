import {
  addRugbyPlayersWithinBudget,
  findRugbyCaptainViceCaptain,
  generateRandomRugbyTeam,
  generateRugbyExpertTeamPayload,
  generateRugbyFavouriteTeamPayload,
  generateRugbyTeamPayload,
  LocalStorage,
} from '@/lib/utils';
import {
  LastEnterPlayerData,
  RugbyLeagueAction,
  RugbyLeaguePlayersByRole,
  RugbyPlayer,
  RugbyTeamComposition,
  RugbyTeamState,
} from '../../../../types/rugby-league';
import { CreateRugbyTeamPayload } from '../../../../types/competitions';
import { setApiMessage } from '@/helpers/commonFunctions';
import { ALL_RUGBY_ROLES } from '@/helpers/constants/index';

const calculateRemainingBudget = (
  totalBudget: number,
  playersByRole: RugbyLeaguePlayersByRole,
): number => {
  const totalCost = Object.values(playersByRole)
    .flat()
    .reduce((sum, player) => sum + player?.scoreData.playerCurrentSalary, 0);
  return totalBudget - totalCost;
};

const resetCaptainStatus = (
  playersByRole: RugbyLeaguePlayersByRole,
): RugbyLeaguePlayersByRole => {
  const updatedPlayersByRole = { ...playersByRole };
  Object.keys(updatedPlayersByRole).forEach((roleKey) => {
    updatedPlayersByRole[roleKey as keyof RugbyLeaguePlayersByRole] =
      updatedPlayersByRole[roleKey as keyof RugbyLeaguePlayersByRole].map(
        (player) => {
          if (player.isCaiptain) {
            return { ...player, isCaiptain: false };
          }
          return player;
        },
      );
  });
  return updatedPlayersByRole;
};

const resetViceCaptainStatus = (
  playersByRole: RugbyLeaguePlayersByRole,
): RugbyLeaguePlayersByRole => {
  const updatedPlayersByRole = { ...playersByRole };
  Object.keys(updatedPlayersByRole).forEach((roleKey) => {
    updatedPlayersByRole[roleKey as keyof RugbyLeaguePlayersByRole] =
      updatedPlayersByRole[roleKey as keyof RugbyLeaguePlayersByRole].map(
        (player) => {
          if (player.isViceCaiptain) {
            return { ...player, isViceCaiptain: false };
          }
          return player;
        },
      );
  });
  return updatedPlayersByRole;
};

export const rugbyLeagueReducer = (
  state: RugbyTeamState,
  action: RugbyLeagueAction,
): RugbyTeamState => {
  const localRugbyLeaguePlayersByRole =
    LocalStorage.getItem<RugbyLeaguePlayersByRole>('rugby_league_dream_team');
  switch (action.type) {
    case 'ADD_PLAYER': {
      const { player, role } = action.payload;
      const newPlayersByRole = {
        ...state.playersByRole,
        [role]: [
          ...state.playersByRole[role],
          { ...player, isCaiptain: false, isViceCaiptain: false },
        ],
      };
      const LastEnterPlayer: LastEnterPlayerData = {
        player,
        tabSection: role,
      };

      return {
        ...state,
        playersByRole: newPlayersByRole,
        lastEntry: {
          ...state.lastEntry,
          players: [...(state.lastEntry?.players ?? []), LastEnterPlayer],
          mode: 'MANUAL',
        },
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          newPlayersByRole,
        ),
      };
    }
    case 'REMOVE_PLAYER': {
      const { role, playerId } = action.payload;
      const newPlayersByRole = {
        ...state.playersByRole,
        [role]: state.playersByRole[role].filter(
          (player) => player.playerId !== playerId,
        ),
      };

      const updatedLastEntryPlayers = state.lastEntry.players.filter(
        (entry) => entry?.player?.playerId !== playerId,
      );

      return {
        ...state,
        playersByRole: newPlayersByRole,
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          newPlayersByRole,
        ),
        lastEntry: {
          ...state.lastEntry,
          players: updatedLastEntryPlayers,
        },
      };
    }

    case 'SET_CAPTAIN': {
      const { role, playerId } = action.payload;
      let updatedPlayersByRole = resetCaptainStatus(state.playersByRole);

      // Set the new captain
      updatedPlayersByRole[role] = updatedPlayersByRole[role].map((player) => {
        // If player is already captain
        if (player.playerId === playerId && player.isCaiptain) {
          return { ...player, isCaiptain: false };
        }
        // if players is fresh
        if (
          player.playerId === playerId &&
          !player.isCaiptain &&
          !player.isViceCaiptain
        ) {
          return { ...player, isCaiptain: true };
        }
        // If player is already vice captain and assing them captain
        if (player.isViceCaiptain && player.playerId === playerId) {
          return { ...player, isCaiptain: true, isViceCaiptain: false };
        }

        return player;
      });

      return {
        ...state,
        playersByRole: updatedPlayersByRole,
      };
    }

    case 'SET_VICE_CAPTAIN': {
      const { role, playerId } = action.payload;
      let updatedPlayersByRole = resetViceCaptainStatus(state.playersByRole);

      // Set the new vice captain
      updatedPlayersByRole[role] = updatedPlayersByRole[role].map((player) => {
        if (player.isCaiptain && player.playerId === playerId) {
          return { ...player, isCaiptain: false, isViceCaiptain: true };
        }
        if (!player.isViceCaiptain && player.playerId === playerId) {
          return { ...player, isViceCaiptain: !player.isViceCaiptain };
        }
        return player;
      });

      return {
        ...state,
        playersByRole: updatedPlayersByRole,
      };
    }

    case 'CREATE_DREAM_TEAM': {
      const {
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
        coins,
        bonusCoins,
      } = action.payload;

      const { viceCaptain, captain } = findRugbyCaptainViceCaptain(
        state.playersByRole,
      );

      const playerPayloadData = generateRugbyTeamPayload(
        state.playersByRole,
        captain,
        viceCaptain,
        state.reserveState?.reservePlayerPayload || [],
      );

      const createDreamTeamPayload: CreateRugbyTeamPayload = {
        playerData: playerPayloadData,
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
        coins,
        bonusCoins,
      };

      return { ...state, createDreamTeamPayload };
    }

    case 'GET_DREAM_TEAM': {
      const { fantasyTeamResponse, playerId, role } = action.payload;
      const dreamPlayersByRole = fantasyTeamResponse.result;

      type RoleKey = keyof RugbyLeaguePlayersByRole; // 'HAL' | 'BR' | 'BAC' | 'FRF' | 'IC'
      const allRoles: RoleKey[] = ['HAL', 'BR', 'BAC', 'FRF', 'IC'];

      const newDreamPlayerByRole: RugbyLeaguePlayersByRole = {
        HAL: [],
        BR: [],
        BAC: [],
        FRF: [],
        IC: [],
      };

      const [playerCaptain] = dreamPlayersByRole?.captain || [];
      const [playerViceCaptain] = dreamPlayersByRole?.viceCaptain || [];

      allRoles?.forEach((role) => {
        switch (role) {
          case 'HAL':
            newDreamPlayerByRole['HAL'] = dreamPlayersByRole?.halves?.map(
              (player: any) => {
                if (player?.playerId === playerCaptain?.playerId) {
                  return { ...player, isCaiptain: true };
                }
                if (player?.playerId === playerViceCaptain?.playerId) {
                  return { ...player, isViceCaiptain: true };
                }
                return player;
              },
            );
            break;
          case 'BR':
            newDreamPlayerByRole['BR'] = dreamPlayersByRole?.backRow?.map(
              (player: any) => {
                if (player?.playerId === playerCaptain?.playerId) {
                  return { ...player, isCaiptain: true };
                }
                if (player?.playerId === playerViceCaptain?.playerId) {
                  return { ...player, isViceCaiptain: true };
                }
                return player;
              },
            );
            break;
          case 'BAC':
            newDreamPlayerByRole['BAC'] = dreamPlayersByRole?.backs?.map(
              (player: any) => {
                if (player?.playerId === playerCaptain?.playerId) {
                  return { ...player, isCaiptain: true };
                }
                if (player?.playerId === playerViceCaptain?.playerId) {
                  return { ...player, isViceCaiptain: true };
                }
                return player;
              },
            );
            break;
          case 'FRF':
            newDreamPlayerByRole['FRF'] =
              dreamPlayersByRole?.frontRowForwards?.map((player) => {
                if (player?.playerId === playerCaptain?.playerId) {
                  return { ...player, isCaiptain: true };
                }
                if (player?.playerId === playerViceCaptain?.playerId) {
                  return { ...player, isViceCaiptain: true };
                }
                return player;
              });
            break;
          case 'IC':
            newDreamPlayerByRole['IC'] = dreamPlayersByRole?.interchange?.map(
              (player: RugbyPlayer) => {
                if (player?.playerId === playerCaptain?.playerId) {
                  return { ...player, isCaiptain: true };
                }
                if (player?.playerId === playerViceCaptain?.playerId) {
                  return { ...player, isViceCaiptain: true };
                }
                return player;
              },
            );
            break;
        }
      });

      const roleToRemove = role;
      const playerIdToRemove = playerId;
      const updatedPlayersByRole = { ...newDreamPlayerByRole };
      if (roleToRemove && playerIdToRemove) {
        updatedPlayersByRole[roleToRemove] = updatedPlayersByRole[
          roleToRemove
        ]?.filter((player) => player?.playerId !== playerIdToRemove);
      }

      // reserve players with correct position
      // create array with reserve limit with null values
      const reservePlayers = Array(state.reserveState.reservePlayersLimit).fill(
        null,
      );

      fantasyTeamResponse.result.reserve?.forEach((player) => {
        const rank = (player.reserveRank ?? 0) - 1;
        if (rank < state.reserveState.reservePlayersLimit) {
          reservePlayers[rank] = player;
        }
      });

      return {
        ...state,
        playersByRole: updatedPlayersByRole,
        reserveState: {
          ...state.reserveState,
          reservePlayers,
        },
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          updatedPlayersByRole,
        ),
      };
    }

    case 'CREATE_LUCKY_TEAM': {
      try {
        const { playersByRole } = action.payload;
        const teamComposion: RugbyTeamComposition = {
          IC: {
            min: state.playerByRoleLimit.IC,
            max: state.playerByRoleLimit.IC,
          },
          BR: {
            min: state.playerByRoleLimit.BR,
            max: state.playerByRoleLimit.BR,
          },

          BAC: {
            min: state.playerByRoleLimit.BAC,
            max: state.playerByRoleLimit.BAC,
          },
          FRF: {
            min: state.playerByRoleLimit.FRF,
            max: state.playerByRoleLimit.FRF,
          },
          HAL: {
            min: state.playerByRoleLimit.HAL,
            max: state.playerByRoleLimit.HAL,
          },
          TOTAL_PLAYERS: 17,
        };
        const luckyPlayersByRole = generateRandomRugbyTeam(
          playersByRole,
          teamComposion,
        );
        const { captain, viceCaptain } =
          findRugbyCaptainViceCaptain(luckyPlayersByRole);

        const luckyPlayerPayloadData = generateRugbyTeamPayload(
          luckyPlayersByRole,
          captain,
          viceCaptain,
          state.reserveState?.reservePlayerPayload || [],
        );

        const validLuckyPlayersByRole = addRugbyPlayersWithinBudget(
          luckyPlayersByRole,
          state.remainingBudget,
        );

        const {
          eventId: luckyEventId,
          eventName: luckyEventName,
          sportId: luckySportId,
          tournamentId: luckyTournamentId,
          competitionId: luckyCompetitionId,
          name: luckyTeamName,
        } = action.payload;

        const createLuckyDreamTeamPayload: CreateRugbyTeamPayload = {
          playerData: luckyPlayerPayloadData,
          eventId: luckyEventId,
          eventName: luckyEventName,
          sportId: luckySportId,
          tournamentId: luckyTournamentId,
          competitionId: luckyCompetitionId,
          name: luckyTeamName,
        };

        return {
          ...state,
          playersByRole: validLuckyPlayersByRole,
          createDreamTeamPayload: createLuckyDreamTeamPayload,
          remainingBudget: calculateRemainingBudget(
            state.totalBudget,
            validLuckyPlayersByRole,
          ),
          lastEntry: {
            ...state.lastEntry,
            mode: 'FEELING_LUCKY',
          },
        };
      } catch (error) {
        if (error instanceof Error) {
          setApiMessage('error', error?.message);
        }

        return state;
      }
    }

    case 'CREATE_FAVOURITE_TEAM': {
      const { favoriteTeam, playersByRole: favouritePlayerByRole } =
        action.payload;

      const resetPlayersByRole: RugbyLeaguePlayersByRole = {
        BAC: [],
        BR: [],
        FRF: [],
        HAL: [],
        IC: [],
      };

      ALL_RUGBY_ROLES.forEach((role) => {
        const rolesPlayer = favouritePlayerByRole[role].map((player) => {
          if (player.isCaiptain) {
            return { ...player, isCaiptain: false };
          }
          if (player.isViceCaiptain) {
            return { ...player, isViceCaiptain: false };
          }
          return player;
        });
        resetPlayersByRole[role] = rolesPlayer;
      });

      const newPlayersByRole = generateRugbyFavouriteTeamPayload(
        favoriteTeam,
        state.playerByRoleLimit,
        resetPlayersByRole,
      );
      const validPlayersByRole = addRugbyPlayersWithinBudget(
        newPlayersByRole,
        state.remainingBudget,
      );

      return {
        ...state,
        playersByRole: validPlayersByRole,
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          validPlayersByRole,
        ),

        lastEntry: {
          ...state.lastEntry,
          mode: 'FAVORITE',
        },
      };
    }

    case 'CREATE_EXPERT_TEAM': {
      const { dreamPlayers, playerByRole } = action.payload;

      const resetExpertPlayersByRole: RugbyLeaguePlayersByRole = {
        BAC: [],
        BR: [],
        FRF: [],
        HAL: [],
        IC: [],
      };
      ALL_RUGBY_ROLES.forEach((role) => {
        const rolesPlayer = playerByRole[role].map((player) => {
          if (player.isCaiptain) {
            return { ...player, isCaiptain: false };
          }
          if (player.isViceCaiptain) {
            return { ...player, isViceCaiptain: false };
          }
          return player;
        });
        resetExpertPlayersByRole[role] = rolesPlayer;
      });

      const newPlayerByRole = generateRugbyExpertTeamPayload(
        dreamPlayers,
        resetExpertPlayersByRole,
      );

      const validExpertPlayersByRole = addRugbyPlayersWithinBudget(
        newPlayerByRole,
        state.remainingBudget,
      );

      return {
        ...state,
        playersByRole: validExpertPlayersByRole,
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          validExpertPlayersByRole,
        ),

        lastEntry: {
          ...state.lastEntry,
          mode: 'EXPERT_FAVORITE',
        },
      };
    }

    case 'SET_TOTAL_BALANCE': {
      const { amount } = action.payload;
      return { ...state, totalBudget: amount, remainingBudget: amount };
    }

    case 'CLEAR_TEAM': {
      return {
        ...state,
        reserveState: {
          ...state.reserveState,
          reservePlayers: LocalStorage.getItem<RugbyPlayer[]>(
            'rugby_league_reserve_players',
          ) || [null],
          reservePlayersLimit: 1,
        },
        playersByRole: localRugbyLeaguePlayersByRole || {
          BR: [],
          BAC: [],
          HAL: [],
          FRF: [],
          IC: [],
        },
        remainingBudget: state.totalBudget,
      };
    }

    case 'ADD_RESERVE_PLAYER': {
      const { player, position } = action.payload;
      const remainingBudget =
        state.remainingBudget - player.scoreData?.playerCurrentSalary;
      // Add player to correct position
      const newReservePlayers = [...(state.reserveState?.reservePlayers || [])];
      // Calculate correct reserve rank by counting existing non-null players
      const existingPlayerCount = newReservePlayers.filter(
        (p) => p !== null,
      ).length;
      const reserveRank = existingPlayerCount + 1;
      newReservePlayers[position] = { ...player, reserveRank };
      return {
        ...state,
        reserveState: {
          ...state.reserveState,
          reservePlayers: newReservePlayers,
        },
        remainingBudget,
      };
    }

    case 'REMOVE_RESERVE_PLAYER': {
      const { playerId, position } = action.payload;
      const player = state.reserveState?.reservePlayers.find(
        (player) => player?.playerId === playerId,
      );
      if (!player) {
        return state;
      }
      const remainingBudget =
        state.remainingBudget + player.scoreData?.playerCurrentSalary;
      // Remove the player and recalculate reserve ranks for remaining players
      const updatedReservePlayers = (
        state.reserveState?.reservePlayers || []
      ).map((player) => (player?.playerId === playerId ? null : player));
      // Recalculate reserve ranks for remaining players
      let rankCounter = 1;
      const finalReservePlayers = updatedReservePlayers.map((player) => {
        if (player !== null) {
          return { ...player, reserveRank: rankCounter++ };
        }
        return null;
      });
      return {
        ...state,
        reserveState: {
          ...state.reserveState,
          reservePlayers: finalReservePlayers,
        },
        remainingBudget,
      };
    }

    case 'CREATE_RESERVE_PLAYER_PAYLOAD': {
      const { reservePlayerPayload } = action.payload;
      return {
        ...state,
        reserveState: { ...state.reserveState, reservePlayerPayload },
      };
    }

    case 'CLEAR_RESERVE_PLAYERS': {
      return {
        ...state,
        reserveState: {
          ...state.reserveState,
          reservePlayers: Array(
            state.reserveState?.reservePlayersLimit || 4,
          ).fill(null),
        },
      };
    }

    default:
      return state;
  }
};
