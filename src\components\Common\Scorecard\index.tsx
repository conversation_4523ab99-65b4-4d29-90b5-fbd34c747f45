'use client';
import React, { useState } from 'react';
import cricket from '../../../../data/cricketData.json';
import SimpleTabs from '@/components/UI/SimpleTabs';
import MatchInfoScoreCard from './MatchInfoScoreCard';
import CricketScoreboardHeader from '../Commentary/CricketScoreboardHeader';
import { IconButton } from '@material-tailwind/react';
import { Search, SlidersHorizontal } from 'lucide-react';
import { Input } from '@/components/UI/input';
import { useDataTable } from '@/hooks/useDataTable';
import { newCricketTableHeader } from '@/components/UI/DataTabel/columns/createTeamColumn';
import { FiltersState } from '../Player/PlayerFilter';
import { Button } from '@/components/UI/button';
import SettingsIcon from '@/components/UI/Icons/SettingsIcon';
import {
  CricketBatInnings,
  CricketBowlInnings,
} from '../../../../types/commentry';
import SmartPlayScores from './SmartPlayScores';
import { useQuery } from '@tanstack/react-query';
import {
  getGameStats,
  getMatchDetails,
  getSmartPlayCricketStats,
} from '@/helpers/fetchers/commentry';
import { quyerKeys } from '@/lib/queryKeys';
import InningsCard from './InningsCard';
import { useCompetition } from '@/helpers/context/competitionContext';
import moment from 'moment';
import { StatsLegend } from '../Commentary/CricketLegend';
import { formatCricketLineup, getFooterText } from '@/lib/utils';

const tabs = [
  { id: 'game-scores', name: 'Game Scores' },
  { id: 'smartplay-scores', name: 'SmartPlay Scores' },
];

const statsData = [
  { column: 'Batting', label: 'RUN', text: 'Runs Scored' },
  { column: 'Batting', label: '4sB', text: 'Four Bonus' },
  { column: 'Batting', label: '6sB', text: 'Six Bonus' },
  {
    column: 'Strike Rate Bonus',
    label: 'SR>150',
    text: 'Strike Rate above 150',
  },
  { column: 'Bowling', label: 'MO', text: 'Maiden Overs Bowled' },
  { column: 'Economy Rate', label: 'ER<5.0', text: 'Economy Rate below 5.0' },
];

const CricketScoreboard: React.FC = () => {
  const { eventDetailsResponse } = useCompetition();
  const eventId = eventDetailsResponse?.result?.eventDetails?.id;
  const seasonId =
    eventDetailsResponse?.result?.eventDetails?.CricketSeason?.id;
  const tournamentId =
    eventDetailsResponse?.result?.eventDetails?.CricketTournament?.id;
  const sportId = eventDetailsResponse?.result?.eventDetails?.SportId;

  const { data: smartPlayCricketStats } = useQuery({
    queryFn: () =>
      getSmartPlayCricketStats({
        tournamentId: tournamentId ?? 0,
        seasonId: seasonId ?? 0,
        eventId: eventId ?? 0,
        sportId: sportId ?? 0,
      }),
    queryKey: [quyerKeys.getSmartPlayCricketStats],
    enabled: !!tournamentId && !!seasonId && !!eventId && !!sportId,
  });

  const { data: gameStats } = useQuery({
    queryFn: () => getGameStats(Number(eventId)),
    queryKey: [quyerKeys.getGameStats],
    enabled: !!eventId,
  });

  const { table } = useDataTable({
    columns: newCricketTableHeader,
    data: smartPlayCricketStats?.result || [],
  });

  const { data: matchDetailsData } = useQuery({
    queryFn: () => getMatchDetails(Number(eventId)),
    queryKey: [quyerKeys.getMatchDetails],
    enabled: !!eventId,
  });

  const homeTeam = gameStats?.result?.CricketMatchInnings[0];
  const awayTeam = gameStats?.result?.CricketMatchInnings[1];

  const battingTeam = homeTeam?.CricketBatInnings?.map((player) => ({
    ...player,
    isExpanded: false,
  }));
  const bowlingTeam = awayTeam?.CricketBowlInnings?.map((player) => ({
    ...player,
    expand: false,
  }));

  const awayBattingTeam = awayTeam?.CricketBatInnings?.map((player) => ({
    ...player,
    isExpanded: false,
  }));
  const homeBowlingTeam = homeTeam?.CricketBowlInnings?.map((player) => ({
    ...player,
    expand: false,
  }));
  const [showFilter, setShowFilter] = useState(false);
  const [showMobileFilter, setShowMobileFilter] = useState(false);
  const [showTableCustomization, setShowTableCustomization] = useState(false);
  const [showTableCustomizationMobile, setShowTableCustomizationMobile] =
    useState(false);
  const [cricketData, setCricketData] = useState(cricket);
  const [filters, setFilters] = useState<FiltersState>({
    matchesPlayed: false,
    priceRange: [0, 100],
    dualPosition: false,
    teams: {
      awayTeam: false,
      homeTeam: false,
    },
    breakeven: [0, 100],
    projectedScore: [0, 100],
    projectedValueChange: [6300, 9300],
    selectionPercentage: [0, 100],
  });

  const [batter, setBatter] = useState<CricketBatInnings[]>([]);
  const [bowler, setBowler] = useState<CricketBowlInnings[]>([]);
  const togglePlayerDetails = (
    playerType: 'batsman' | 'bowler',
    playerId: number,
  ) => {
    if (playerType === 'batsman') {
      const updatedBatter = batter.map((player) => {
        if (player.playerId === playerId) {
          return { ...player, expand: !player.expand };
        } else {
          return player;
        }
      });

      setBatter(updatedBatter);
    } else if (playerType === 'bowler') {
      const updatedBowler = bowler.map((player) => {
        if (player.playerId === playerId) {
          return { ...player, expand: !player.expand };
        }
        return player;
      });
      setBowler(updatedBowler);
    }
  };

  const [activeTab, setActiveTab] = React.useState('game-scores');
  const sortedData = table.getSortedRowModel().rows.map((row) => row.original);
  const [showLegends, setShowLegends] = useState(false);

  const homeTeamName =
    eventDetailsResponse?.result?.eventDetails?.homeTeam?.name ?? '-';
  const awayTeamName =
    eventDetailsResponse?.result?.eventDetails?.awayTeam?.name ?? '-';
  const teamData = gameStats?.result?.CricketMatchInnings;

  // Extract innings data
  const homeTeamInnings = teamData?.[0];
  const awayTeamInnings = teamData?.[1];

  // Function to format team score
  const formatScore = (team: any, onlyScore?: boolean) => {
    if (!team) return '';
    if (onlyScore) {
      return `
      ${team?.Pt ?? ''}${team?.Wk?.toString() ? `/${team?.Wk}` : ''
        }${team?.Ov?.toString() ? ` (${team?.Ov})` : ''}
      `;
    }
    return `${team?.Ti} - ${team?.Pt ?? ''}${team?.Wk?.toString() ? `/${team?.Wk}` : ''
      }${team?.Ov?.toString() ? ` (${team?.Ov})` : ''}`;
  };

  const getExtraData = (teamData: any) => {
    if (!teamData) return null;

    return (
      <p>
        <strong>{teamData?.Ex ?? ''}</strong> {' ('}
        <span>b </span> {teamData?.B ?? '0'}, <span>lb </span>{' '}
        {teamData?.LB ?? '0'}, <span>nb </span> {teamData?.NB ?? '0'},{' '}
        <span>w </span> {teamData?.WB ?? '0'}, <span>p </span>{' '}
        {teamData?.Pen ?? '0'}
        {')'}
      </p>
    );
  };

  return (
    <div className="w-full p-2 mx-auto overflow-hidden">
      <div style={{ marginLeft: -60, marginRight: -60, marginTop: -22 }}>
        <CricketScoreboardHeader />
      </div>
      <div className="flex justify-center md:justify-between items-center flex-col md:flex-row">
        <div className="md:max-w-[380px] w-full">
          <SimpleTabs
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            tabs={tabs}
          />
        </div>
        {activeTab === 'smartplay-scores' && (
          <div className="flex items-center md:max-w-[520px] w-full space-x-2 my-2">
            <div className="relative">
              <Search
                className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={16}
              />
              <Input
                placeholder="Search (by player name)"
                className="border-gray-100 pl-10"
                onChange={(e) => {
                  table.getColumn('player')?.setFilterValue(e.target.value);
                }}
              />
            </div>
            <div className="flex items-center gap-2 w-[60%] md:w-fit justify-between md:justify-start sort-container">
              <div>
                <Button
                  {...({} as any)}
                  className="bg-secondary-100 hidden md:block"
                  onClick={() =>
                    setShowTableCustomization(!showTableCustomization)
                  }
                >
                  <div className="flex space-x-2">
                    <SettingsIcon /> <span>Customise</span>
                  </div>
                </Button>
                <IconButton
                  {...({} as any)}
                  className="bg-secondary-100 md:hidden block"
                  onClick={() =>
                    setShowTableCustomizationMobile(
                      !showTableCustomizationMobile,
                    )
                  }
                >
                  <SettingsIcon />
                </IconButton>
              </div>
              <div>
                <IconButton
                  {...({} as any)}
                  className="bg-secondary-100 hidden md:block"
                  onClick={() => setShowFilter(!showFilter)}
                >
                  <SlidersHorizontal />
                </IconButton>
                <IconButton
                  {...({} as any)}
                  className="bg-secondary-100 md:hidden block"
                  onClick={() => setShowMobileFilter(!showMobileFilter)}
                >
                  <SlidersHorizontal />
                </IconButton>
              </div>
            </div>
            <div className="relative">
              <div
                onMouseEnter={() => setShowLegends(true)}
                onMouseLeave={() => setShowLegends(false)}
              >
                <Button variant="outline" className="md:block hidden">
                  Legend
                </Button>
                {showLegends && (
                  <>
                    <div className="absolute top-full right-0 h-2 w-full bg-transparent" />
                    <div className="absolute top-[calc(100%+8px)] right-0 z-50 bg-white rounded-md shadow-lg p-4 min-w-[500px]">
                      <StatsLegend stats={statsData} />
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {activeTab === 'game-scores' && (
        <div className="space-y-2 mt-2">
          <InningsCard
            title={formatScore(homeTeamInnings)}
            batsmen={battingTeam || []}
            bowlers={bowlingTeam || []}
            powerPlays={cricketData.innings.powerPlays}
            togglePlayerDetails={togglePlayerDetails}
            extra={getExtraData(teamData)}
            total={formatScore(homeTeamInnings, true)}
          />
          <InningsCard
            title={formatScore(awayTeamInnings)}
            batsmen={awayBattingTeam || []}
            bowlers={homeBowlingTeam || []}
            powerPlays={cricketData.innings.powerPlays}
            togglePlayerDetails={togglePlayerDetails}
            extra={getExtraData(teamData)}
            total={formatScore(awayTeamInnings, true)}
          />
          <div>
            <MatchInfoScoreCard
              matchInfo={{
                match: matchDetailsData?.result?.eventName ?? '-',
                date:
                  moment(matchDetailsData?.result?.startTime).format(
                    'DD MMM YYYY',
                  ) ?? '-',
                time:
                  moment(matchDetailsData?.result?.startTime).format(
                    'hh:mm A',
                  ) ?? '-',
                venue: matchDetailsData?.result?.CricketStadium?.name ?? '-',
                toss: getFooterText(
                  matchDetailsData?.result,
                  matchDetailsData?.result?.ScoreBoard?.TPa,
                  matchDetailsData?.result?.ScoreBoard?.TCho,
                ),
                players: formatCricketLineup(
                  matchDetailsData?.result?.CricketLineups ?? [],
                  matchDetailsData?.result?.homeTeam?.id ?? 0,
                ),
              }}
              matchDetailsData={matchDetailsData}
            />
          </div>
        </div>
      )}

      {activeTab === 'smartplay-scores' && (
        <div className="mt-2">
          <SmartPlayScores
            filters={filters}
            setFilters={setFilters}
            setShowFilter={setShowFilter}
            setShowMobileFilter={setShowMobileFilter}
            showFilter={showFilter}
            showMobileFilter={showMobileFilter}
            showTaleCustomization={showTableCustomization}
            showTableCustomizationMobile={showTableCustomizationMobile}
            setShowTableCustomizationMobile={setShowTableCustomizationMobile}
            sortedData={sortedData || []}
            setShowTableCustomization={setShowTableCustomization}
            table={table}
            homeTeam={homeTeamName}
            awayTeam={awayTeamName}
          />
        </div>
      )}
    </div>
  );
};

export default CricketScoreboard;
