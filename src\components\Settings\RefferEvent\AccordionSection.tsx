import React from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AccordionSectionProps {
  title: string;
  open: boolean;
  setOpen: (open: boolean) => void;
  children: React.ReactNode;
}

const AccordionSection: React.FC<AccordionSectionProps> = ({
  title,
  open,
  setOpen,
  children,
}) => {
  return (
    <div className="bg-white mb-[18px] border border-[#E7E9EC] rounded-[18px]">
      <button
        className={cn(
          'w-full flex items-center justify-between py-2 px-[18px] rounded-[18px_18px_0px_0px] text-left focus:outline-none bg-[linear-gradient(90deg,_rgba(0,55,100,1)_40%,_rgba(68,85,199,1)_100%)] ',
        )}
        onClick={() => setOpen(!open)}
      >
        <span className="text-white text-[43.9px] leading-[43.9px] font-apotekCompRegular font-normal">
          {title}
        </span>
        <ChevronDown
          className={cn(
            'h-5 w-5 text-white transition-transform duration-200',
            open ? 'transform rotate-180' : '',
          )}
        />
      </button>
      {open && <div>{children}</div>}
    </div>
  );
};

export default AccordionSection;
