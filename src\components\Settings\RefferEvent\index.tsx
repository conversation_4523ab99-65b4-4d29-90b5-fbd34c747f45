import React, { useState } from 'react';
import CoinGraphic from './CoinGraphic';
import StatsSection from './StatsSection';
import ReferralLinkSection from './ReferralLinkSection';
import HowItWorks from './HowItWorks';
import AccordionSection from './AccordionSection';
import FAQContent from './FAQContent';
import TermsAndConditionsContent from './TermsAndConditionsContent';
import RefferHeaderBG from '../../../../public/images/RefferHeaderBG.png';
import RefferHeaderBGMobile from '../../../../public/images/RefferHeaderMobileBG.png';
import LeftCoins from '../../../../public/images/leftcoins.png';
import RightCoins from '../../../../public/images/rightcoins.png';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { setApiMessage } from '@/helpers/commonFunctions';
import { useFantasyUser } from '@/helpers/context/fantasyUserContext';

export const RefferEvent = () => {
  const [faqOpen, setFaqOpen] = useState(true);
  const [termsOpen, setTermsOpen] = useState(true);
  const origin = window.location.origin;
  const { user } = useFantasyUser();
  const referralLink = `${origin}/fantasy?referral_type=fantasy&referral=${user.referralCode}`;
  const copyToClipboard = () => {
    navigator.clipboard.writeText(referralLink);
    setApiMessage('success', 'Link copied to clipboard!');
  };
  return (
    <div className="max-w-[1284px] mx-auto bg-white rounded-3xl mt-4 shadow-sm border border-gray-100">
      {/* Header */}
      <div
        className={cn(
          'bg-no-repeat bg-center bg-cover rounded-t-3xl p-6 text-white text-center relative overflow-hidden',
        )}
      >
        <Image
          src={RefferHeaderBG}
          alt="Reffer Header BG"
          className="absolute top-0 left-0 w-full h-full hidden lg:block"
        />
        <Image
          src={RefferHeaderBGMobile}
          alt="Reffer Header BG"
          className="absolute top-0 left-0 w-full h-full block lg:hidden"
        />
        <div className="relative z-10">
          <h1 className="lg:text-3xl text-[16px] font-semibold mb-1">
            Refer and earn Bonus Coins
          </h1>
          <p className="text-[#FFE2CC] text-sm md:text-base lg:max-w-[500px] max-w-[250px] mx-auto">
            Invite friends, earn Bonus Coins! Share your link and unlock
            rewards.
          </p>
        </div>

        <Image
          src={LeftCoins}
          alt="Left Coins"
          className="absolute top-[10px] lg:top-0 left-[22px] sm:left-[80px] bottom-0 lg:left-[160px] w-[40px] md:w-[125px]"
        />
        <Image
          src={RightCoins}
          alt="Right Coins"
          className="absolute top-[10px] lg:top-0 right-[22px] sm:right-[80px] bottom-0 lg:right-[160px] w-[40px] md:w-[125px]"
        />
      </div>
      {/* Main Content */}
      <div className="p-[9px] md:p-6 pb-8 pt-7">
        {/* Stats Section */}
        <StatsSection
          totalReferralCoins={user.totalReferralCoins ?? 0}
          totalReferralTracked={user.totalReferralTrack ?? 0}
          totalReferralCompleted={user.completedReferralTrack ?? 0}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left side - Referral Link */}
          <ReferralLinkSection
            referralLink={referralLink}
            copyToClipboard={copyToClipboard}
          />
          {/* Right side - How it works */}
          <HowItWorks />
        </div>
        {/* Accordion sections */}
        <div className="mt-8 rounded-[18px] overflow-hidden">
          <AccordionSection title="FAQs" open={faqOpen} setOpen={setFaqOpen}>
            <FAQContent />
          </AccordionSection>
          <AccordionSection
            title="TERMS & CONDITIONS"
            open={termsOpen}
            setOpen={setTermsOpen}
          >
            <TermsAndConditionsContent />
          </AccordionSection>
        </div>
      </div>
    </div>
  );
};
