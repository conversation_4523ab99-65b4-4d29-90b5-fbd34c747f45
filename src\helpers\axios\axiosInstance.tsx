'use client';
import axios from 'axios';

import { Token } from '../../../db/db';

const axiosInstance = axios.create({
  // baseURL: Config?.baseURL, // Replace with your API base URL
  headers: {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': 'true',
    Accept: 'application/json',
  },
});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // You can modify the request config here
    // For example, add an auth token
    const token = Token;
    const unquotedToken = token?.replace(/"/g, '');
    if (unquotedToken) {
      config.headers['Authorization'] = `Bearer ${unquotedToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(
      new Error(error instanceof Error ? error.message : String(error)),
    );
  },
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    // You can modify the response data here
    return response;
  },
  (error) => {
    return Promise.reject(
      new Error(error instanceof Error ? error.message : String(error)),
    );
  },
);

export default axiosInstance;
