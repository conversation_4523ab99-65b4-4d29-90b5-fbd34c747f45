import React from 'react';

const TermsAndConditionsContent: React.FC = () => (
  <div className="p-[27px] max-799:p-3">
    <div className="mb-7 max-799:mb-[19px]">
      <span className="block text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[18px] max-799:mb-2.5">
        1. Program Overview
      </span>
      <p className="ml-[22px] max-799:ml-3 text-[16px] max-799:text-[14px] leading-[22px] max-799:leading-[19px] font-normal font-inter text-black-100">
        The Referral Program allows registered users ("Referrers") to earn
        BonusCoins by inviting others ("Referees") to sign up and participate in
        SmartCoin matches using a unique referral link.
      </p>
    </div>
    <div className="mb-6 max-799:mb-[19px]">
      <span className="block text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[18px] max-799:mb-2.5">
        2. Eligibility
      </span>
      <ul className="ml-[22px] max-799:ml-3 list-disc pl-6 text-[16px] max-799:text-[14px] leading-[22px] max-799:leading-[19px] font-normal font-inter text-black-100">
        <li className="mb-3">
          Referrers must be registered users with an active account.
        </li>
        <li className="mb-3">
          Referees must be new users who have not previously registered with the
          platform.
        </li>
        <li>
          The Referral Program is open to participants aged 18 or older, unless
          otherwise specified by applicable law.
        </li>
      </ul>
    </div>
    <div className="mb-6 max-799:mb-[19px]">
      <span className="block text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[18px] max-799:mb-2.5">
        3. How It Works
      </span>
      <ul className="ml-[22px] max-799:ml-3 list-disc pl-6 text-[16px] max-799:text-[14px] leading-[22px] max-799:leading-[19px] font-normal font-inter text-black-100">
        <li className="mb-3">
          <span className="font-semibold">Referral Link:</span> Each Referrer
          receives a unique referral code accessible in their user profile.
        </li>
        <li className="mb-3">
          <span className="font-semibold">Sharing the Link:</span> Referrers may
          share their referral link with others via any permitted communication
          method (e.g., email, social media, or direct messaging).
        </li>
        <li className="mb-3">
          <span className="font-semibold">Linking Accounts:</span> When a
          Referee signs up using a Referrer's unique referral link, their
          account is linked to the Referrer's account.
        </li>
        <li>
          <span className="font-semibold">Earning BonusCoins:</span> The
          Referrer earns BonusCoins equal to 10% of the SmartCoins used by the
          Referee in a SmartCoin match. The SmartCoins must be purchased by the
          Referee and used in a match for the Referrer to qualify for the
          reward.
        </li>
      </ul>
    </div>
    <div className="mb-6 max-799:mb-[19px]">
      <span className="block text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[18px] max-799:mb-2.5">
        4. Reward Distribution
      </span>
      <ul className="ml-[22px] max-799:ml-3 list-disc pl-6 text-[16px] max-799:text-[14px] leading-[22px] max-799:leading-[19px] font-normal font-inter text-black-100">
        <li className="mb-3">
          BonusCoins are credited to the Referrer's account after the Referee
          completes a SmartCoin match using purchased SmartCoins.
        </li>
        <li className="mb-3">
          Rewards are calculated based on the total value of SmartCoins used in
          the match, not the amount purchased.
        </li>
        <li>
          BonusCoins are non-transferable, non-exchangeable, and cannot be
          redeemed for cash or other currencies unless explicitly stated.
        </li>
      </ul>
    </div>
    <div className="mb-6 max-799:mb-[19px]">
      <span className="block text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[18px] max-799:mb-2.5">
        5. Restrictions
      </span>
      <ul className="ml-[22px] max-799:ml-3 list-disc pl-6 text-[16px] max-799:text-[14px] leading-[22px] max-799:leading-[19px] font-normal font-inter text-black-100">
        <li className="mb-3">
          Referrers cannot use their own referral link to create additional
          accounts or earn rewards.
        </li>
        <li className="mb-3">
          Any fraudulent activity, including but not limited to creating fake
          accounts or manipulating the referral system, will result in
          disqualification from the program and possible account suspension.
        </li>
        <li>
          The platform reserves the right to verify the eligibility of both
          Referrers and Referees.
        </li>
      </ul>
    </div>
    <div className="mb-6 max-799:mb-[19px]">
      <span className="block text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[18px] max-799:mb-2.5">
        6. Program Changes and Termination
      </span>
      <ul className="ml-[22px] max-799:ml-3 list-disc pl-6 text-[16px] max-799:text-[14px] leading-[22px] max-799:leading-[19px] font-normal font-inter text-black-100">
        <li className="mb-3">
          The platform reserves the right to modify, suspend, or terminate the
          Referral Program at any time without prior notice.
        </li>
        <li>
          Changes to the program, including reward rates or eligibility
          criteria, will be communicated through the platform's official
          channels.
        </li>
      </ul>
    </div>
    <div className="mb-6 max-799:mb-[19px]">
      <span className="block text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[18px] max-799:mb-2.5">
        7. Liability
      </span>
      <ul className="ml-[22px] max-799:ml-3 list-disc pl-6 text-[16px] max-799:text-[14px] leading-[22px] max-799:leading-[19px] font-normal font-inter text-black-100">
        <li className="mb-3">
          The platform is not responsible for lost, misdirected, or unused
          referral links.
        </li>
        <li>
          Referrers are responsible for ensuring their Referees use the correct
          referral link during sign-up.
        </li>
      </ul>
    </div>
    <div className="mb-6 max-799:mb-[19px]">
      <span className="block text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[18px] max-799:mb-2.5">
        8. Compliance with Laws
      </span>
      <p className="ml-[22px] max-799:ml-3 text-[16px] max-799:text-[14px] leading-[22px] max-799:leading-[19px] font-normal font-inter text-black-100">
        Participants must comply with all applicable laws and regulations when
        participating in the Referral Program.
      </p>
    </div>
    <div>
      <span className="block text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[18px] max-799:mb-2.5">
        9. Contact
      </span>
      <p className="ml-[22px] max-799:ml-3 text-[16px] max-799:text-[14px] leading-[22px] max-799:leading-[19px] font-normal font-inter text-black-100">
        For questions or concerns about the Referral Program, contact our
        support team at{' '}
        <a
          href="mailto:<EMAIL>"
          className="text-secondary-100 underline"
        >
          <EMAIL>
        </a>
        .
      </p>
    </div>
  </div>
);

export default TermsAndConditionsContent;
