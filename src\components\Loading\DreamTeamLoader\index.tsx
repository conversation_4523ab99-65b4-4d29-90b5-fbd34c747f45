import React from 'react'
const PlayerCardSkeleton = () => (
  <div className="bg-white rounded-md shadow-md w-48 p-3 animate-pulse">
    <div className="flex items-center gap-2">
      <div className="w-8 h-8 rounded-full bg-gray-100"></div>
      <div className="h-4 w-12 bg-gray-100 rounded"></div>
    </div>
    <div className="h-5 w-32 bg-gray-100 rounded mt-2"></div>
    <div className="h-4 w-24 bg-gray-100 rounded mt-2"></div>
    <div className="h-4 w-20 bg-gray-100 rounded mt-2"></div>
    <div className="flex justify-between items-center mt-2">
      <div className="h-4 w-16 bg-gray-100 rounded"></div>
      <div className="h-4 w-20 bg-gray-100 rounded"></div>
    </div>
  </div>
)
const ReserveCardSkeleton = () => (
  <div className="bg-white rounded-md shadow-md w-48 p-3 animate-pulse">
    <div className="flex justify-center mb-2">
      <div className="w-12 h-12 rounded-full bg-gray-100"></div>
    </div>
    <div className="flex justify-center">
      <div className="w-8 h-8 rounded-full bg-gray-100"></div>
    </div>
  </div>
)
const PositionLabel = () => (
  <div className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
  </div>
)

type DreamTeamLoaderProps = {
  showReserve?: boolean;
}


export const DreamTeamLoader = ({ showReserve = false }: DreamTeamLoaderProps) => {
  return (
    <div className="w-full min-h-screen bg-green-700 bg-opacity-90 relative overflow-hidden">
      <div className="absolute inset-0 flex flex-col">
        {/* Field pattern - alternating stripes */}
        {Array.from({
          length: 10,
        }).map((_, i) => (
          <div
            key={i}
            className={`flex-1 ${i % 2 === 0 ? 'bg-green-700' : 'bg-green-800'}`}
          />
        ))}
      </div>
      <div className="relative z-10 flex flex-col items-center w-full h-full py-4">
        {/* Wicket-keeper section */}
        <div className="mb-4 mt-6">
          <div className="flex justify-center mb-2">
            <PositionLabel />
          </div>
          <PlayerCardSkeleton />
        </div>
        {/* Batsmen section */}
        <div className="mb-4">
          <div className="flex justify-center mb-2">
            <PositionLabel />
          </div>
          <div className="flex flex-wrap justify-center gap-4">
            <PlayerCardSkeleton />
            <PlayerCardSkeleton />
            <PlayerCardSkeleton />
          </div>
        </div>
        {/* Middle section - All rounders */}
        <div className="mb-4">
          <div className="flex flex-wrap justify-center gap-4 mb-4">
            <PlayerCardSkeleton />
            <PlayerCardSkeleton />
          </div>
          <div className="flex justify-center mb-2">
            <PositionLabel />
          </div>
          <div className="flex justify-center">
            <PlayerCardSkeleton />
          </div>
        </div>
        {/* Bowlers section */}
        <div className="mb-4">
          <div className="flex justify-center mb-2">
            <PositionLabel />
          </div>
          <div className="flex flex-wrap justify-center gap-4 mb-4">
            <PlayerCardSkeleton />
            <PlayerCardSkeleton />
            <PlayerCardSkeleton />
          </div>
          <div className="flex justify-center">
            <PlayerCardSkeleton />
          </div>
        </div>
        {showReserve && (
          <>
            {/* Reserves section */}
            <div className="text-center w-full bg-secondary-100">
              <div className="w-full rounded-t-md text-white px-3 py-1 text-sm text-black">
                RESERVES
              </div>
            </div>
            <div className="flex flex-wrap justify-center gap-4 mt-4">
              <ReserveCardSkeleton />
              <ReserveCardSkeleton />
              <ReserveCardSkeleton />
            </div>
            <div className="flex justify-center mt-4">
              <ReserveCardSkeleton />
            </div>
          </>
        )}
      </div>
    </div>
  )
}


export default DreamTeamLoader;