import type { Dispatch, SetStateAction } from 'react';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Checkbox } from '@/components/UI/checkbox';
import { Button } from '@/components/UI/button';
import { useCompetition } from '@/helpers/context/competitionContext';
import type { Table } from '@tanstack/react-table';
import { SoccerPlayer } from '@/lib/types/soccer';

type MobileSoccerPlayerFilterProps = {
  open: boolean;
  setOpen: (show: boolean) => void;
  playerTable: Table<SoccerPlayer>;
  filters: SoccerFiltersState;
  setFilters: React.Dispatch<React.SetStateAction<SoccerFiltersState>>;
};

export type SoccerFiltersState = {
  selectedTeam: string;
};

const MobileSoccerPlayerFilter = ({
  open,
  setOpen,
  playerTable,
  filters,
  setFilters,
}: MobileSoccerPlayerFilterProps) => {
  const { eventDetailsResponse } = useCompetition();
  const homeTeam = eventDetailsResponse?.result?.eventDetails?.homeTeam?.name;
  const awayTeam = eventDetailsResponse?.result?.eventDetails?.awayTeam?.name;

  const handleTeamChange = (team: string) => {
    setFilters((prev) => ({
      ...prev,
      selectedTeam: team,
    }));

    if (team === 'all') {
      playerTable.getColumn('teamName')?.setFilterValue('');
    } else {
      playerTable.getColumn('teamName')?.setFilterValue(team);
    }
  };

  const handleClearAll = () => {
    setFilters({
      selectedTeam: 'all',
    });
    playerTable.resetColumnFilters();
    setOpen(false);
  };

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="overflow-hidden bg-gradient-to-b from-[#E5EAEF] to-[#93A0AD]">
        <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50">
          FILTERS
        </div>
        <div
          className="w-full p-6 rounded-lg shadow-lg mt-10"
          style={{ height: 'calc(100vh - 200px)', overflowY: 'auto' }}
        >
          <div className="space-y-6">
            <div className="flex flex-col space-y-4">
              <div className="w-full">
                <label className="block text-xl font-medium text-black-100 mb-2">
                  By Team
                </label>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Checkbox
                      id="all-teams"
                      checked={filters.selectedTeam === 'all'}
                      onCheckedChange={() => handleTeamChange('all')}
                      className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                    />
                    <label
                      htmlFor="all-teams"
                      className="ml-2 text-sm text-gray-700"
                    >
                      All Teams
                    </label>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id="home-team"
                      checked={filters.selectedTeam === homeTeam}
                      onCheckedChange={() => handleTeamChange(homeTeam || '')}
                      className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                    />
                    <label
                      htmlFor="home-team"
                      className="ml-2 text-sm text-gray-700"
                    >
                      {homeTeam}
                    </label>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id="away-team"
                      checked={filters.selectedTeam === awayTeam}
                      onCheckedChange={() => handleTeamChange(awayTeam || '')}
                      className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                    />
                    <label
                      htmlFor="away-team"
                      className="ml-2 text-sm text-gray-700"
                    >
                      {awayTeam}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="w-full max-w-screen-lg px-5 mx-auto flex flex-col space-y-2 fixed bottom-0 left-1/2 transform -translate-x-1/2 mb-4">
              <Button onClick={() => setOpen(false)}>Apply</Button>
              <Button
                variant="link"
                className="underline text-white"
                onClick={handleClearAll}
              >
                Clear All
              </Button>
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default MobileSoccerPlayerFilter;
