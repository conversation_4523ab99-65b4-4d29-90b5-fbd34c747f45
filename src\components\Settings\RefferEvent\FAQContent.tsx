import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FAQItem {
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    question: 'How do I find my referral link?',
    answer:
      'Your unique referral link is available in your user profile. Navigate to the "Settings" section on the platform, where you\'ll find your link listed under the "Refer & Earn" tab.',
  },
  {
    question: 'How do I share my referral link?',
    answer:
      'You can share your referral link via email, social media, text messages, or any other permitted communication method. Ensure your friends use your link when signing up to link their account to yours.',
  },
  {
    question: 'When do I receive BonusCoins?',
    answer:
      'You earn BonusCoins equal to 10% of the SmartCoins your referred friend uses in a SmartCoin match. The reward is credited to your account after the match is completed.',
  },
  {
    question: 'Do I earn BonusCoins for every SmartCoin my friend purchases?',
    answer:
      'No, BonusCoins are awarded based on the SmartCoins your friend uses in a SmartCoin match, not the total amount purchased.',
  },
  {
    question: 'Can I use my own referral link to earn BonusCoins?',
    answer:
      'No, using your own referral link to create additional accounts or earn rewards is strictly prohibited and may result in disqualification from the program or account suspension.',
  },
  {
    question: 'Is there a limit to how many friends I can refer?',
    answer:
      "There is no limit to the number of friends you can refer. You'll earn BonusCoins for each referred friend who signs up with your link and participates in a SmartCoins match.",
  },
  {
    question: 'What can I do with BonusCoins?',
    answer:
      "BonusCoins can be used within the platform as outlined in the platform's terms of service. They are non-transferable and cannot be redeemed for cash or other currencies unless specified.",
  },
  {
    question: 'How do I know if my friend used my referral link?',
    answer:
      'You can check the status of your referrals in your user profile under the Refer & Earn section, where linked accounts and earned BonusCoins are displayed.',
  },
  {
    question:
      "What happens if my friend doesn't use my referral link correctly?",
    answer:
      "If your friend doesn't use your referral link during sign-up, their account won't be linked to yours, and you won't earn BonusCoins for their activity. Ensure they enter the correct link.",
  },
  {
    question: "Can I participate if I'm under 18?",
    answer:
      'The Referral Program is open to users aged 18 or older, unless otherwise permitted by applicable law. Check your local regulations to confirm eligibility.',
  },
  {
    question: "What should I do if I don't receive my BonusCoins?",
    answer:
      "If you believe you're eligible for BonusCoins but haven't received them, contact our support team through the platform's help center for assistance.",
  },
  {
    question: 'Can the Referral Program change?',
    answer:
      'Yes, the platform reserves the right to modify or terminate the Referral Program at any time. Any changes will be communicated through official platform channels.',
  },
];

const FAQContent: React.FC = () => {
  const [openQuestions, setOpenQuestions] = useState<number | null>(null);

  const toggleQuestion = (index: number) => {
    // setOpenQuestions((prev) =>
    //   prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index],
    // );
    setOpenQuestions(openQuestions === index ? null : index);
  };

  return (
    <div className="p-[27px] max-799:p-3">
      {faqData?.map((faq, index) => (
        <div
          key={index}
          className={`${openQuestions === index ? 'border-l-[7px] border-primary-200' : ''} bg-[#F3F4F5] px-[18px] py-3 mb-3 max-799:mb-[9px] rounded-[8px]`}
        >
          <button
            onClick={() => toggleQuestion(index)}
            className="w-full flex items-center justify-between text-left"
          >
            <span
              className={`${openQuestions === index ? 'text-secondary-100' : 'text-black-100'} text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[20px] font-semibold font-inter `}
            >
              - {faq?.question}
            </span>
            {/* <ChevronDown
              className={cn(
                'h-4 w-4 text-gray-400 transition-transform duration-200',
                openQuestions?.includes(index) ? 'transform rotate-180' : '',
              )}
            /> */}
          </button>
          {openQuestions === index && (
            <div className="ml-4 max-799:ml-3 text-[16px] max-799:text-[14px] leading-[22.4px] max-799:leading-[19px] font-normal font-inter text-gray-400 mt-3 max-799:mt-2">
              {faq?.answer}
            </div>
          )}
        </div>
      ))}
      <p className="text-[16px] leading-[19px] font-normal font-inter text-black-100">
        For additional questions, please contact our support team at{' '}
        <a href="mailto:<EMAIL>" className="text-secondary-100">
          <EMAIL>
        </a>
        .
      </p>
    </div>
  );
};

export default FAQContent;
