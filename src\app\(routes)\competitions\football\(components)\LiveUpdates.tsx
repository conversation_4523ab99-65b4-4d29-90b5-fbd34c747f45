import React, { useState, useEffect, SetStateAction, Dispatch } from 'react';
import { useQuery } from '@tanstack/react-query';
import { LIVE_POLLIN_TIME } from '@/helpers/constants/index';
import { quyerKeys } from '@/lib/queryKeys';
import { fetchMatchCommentary } from '@/helpers/fetchers/commentary';
import {
  Award,
  RotateCw,
  Flag,
  ArrowRightLeft,
  MessagesSquare,
  Clock,
} from 'lucide-react';
import { Config } from '@/helpers/context/config';
import { useCricketCommentary } from '@/helpers/context/commentry/cricket';
import { useCompetition } from '@/helpers/context/competitionContext';

interface LiveUpdatesProps {
  matchId?: string;
  sportId?: number;
  compCard?: {
    localteam: {
      name: string;
      score: string | number;
      flag?: string;
    };
    visitorteam: {
      name: string;
      score: string | number;
      flag?: string;
    };
    status: {
      value: string;
    };
  };
}

interface CricketMatchInfo {
  localteam: {
    name: string;
    score: string | number;
  };
  visitorteam: {
    name: string;
    score: string | number;
  };
  status: {
    value: string;
  };
}

interface CricketCommentaryResult {
  count: number;
  result: any[];
  matchInfo?: CricketMatchInfo;
}

interface CricketCommentaryResponse {
  status: boolean;
  result: CricketCommentaryResult;
}

interface CommentaryItem {
  commText: string;
  timestamp: string;
  ballNbr: number;
  event: string;
  type: string;
  time: string;
  description: string;
  teamName?: string;
  teamFlag?: string;
  playerName?: string;
}

interface CommentaryMatchData {
  localteam?: {
    id: number;
    name: string;
    nameCode?: string;
    flag?: string;
    score: string | number;
    shortName?: string;
  };
  visitorteam?: {
    id: number;
    name: string;
    nameCode?: string;
    flag?: string;
    score: string | number;
    shortName?: string;
  };
  status?: {
    value: string;
  };
  events?: {
    event: any[] | any;
  };
}

interface ScoreBoard {
  id: number;
  eventId: number;
  Tr1: number;
  Tr2: number;
  Trh1: number | null;
  Trh2: number | null;
  Tr1OR: number | null;
  Tr2OR: number | null;
  Eps: string;
  Esid: string | null;
  Epr: string | null;
  Ecov: string | null;
  Ern: string | null;
  Ewt: number;
  Et: string | null;
  ErnInf: string;
  Esd: string;
  Edf: string | null;
  LuUT: string;
  Eact: string | null;
  EO: string | null;
  EOX: string | null;
  LuC: number;
  Ehid: string | null;
  Spid: number;
  Pid: string | null;
  Eloff: string | null;
  isSyncInfo: number;
  createdAt: string;
  updatedAt: string;
}

interface EventDetails {
  id: number;
  eventName: string;
  awayTeamId: number;
  homeTeamId: number;
  startTime: string;
  status: string;
  SportId: number;
  SoccerTournamentId: number;
  SoccerSeasonId: number;
  winnerCode: number;
  awayTeam: {
    id: number;
    name: string;
    gender: string;
    flag: string;
  };
  homeTeam: {
    id: number;
    name: string;
    gender: string;
    flag: string;
  };
  SoccerTournament: {
    id: number;
    name: string;
    gender: string | null;
  };
  SoccerSeason: {
    id: number;
    name: string;
    year: string;
    fantasy_sport_salary_cap: number;
  };
  ScoreBoard: ScoreBoard;
}

const LiveUpdates = ({ matchId, sportId, compCard }: LiveUpdatesProps) => {
  const [filter, setFilter] = useState<string>('Full Commentary');
  const isAfl = sportId === 9;
  const isNrl = sportId === 12;
  const isCricket = sportId === 4;
  const isSoccer = sportId === 8;

  const { eventDetailsResponse } = useCompetition();

  const {
    setMatchId,
    state: { liveCommentary, isLiveCommentaryLoading },
    setEventFilter,
  } = useCricketCommentary();

  // Set match ID for cricket commentary
  useEffect(() => {
    if (isCricket && matchId) {
      setMatchId(parseInt(matchId));
    }
  }, [isCricket, matchId, setMatchId]);

  // Add styles for fallback to initial
  useEffect(() => {
    const styleTag = document.createElement('style');
    styleTag.innerHTML = `
      .fallback-to-initial {
        position: relative;
      }
      .fallback-to-initial::after {
        content: attr(data-initial);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-weight: bold;
        font-size: 1rem;
        color: #1e40af; /* text-blue-900 */
      }
    `;
    document.head.appendChild(styleTag);

    return () => {
      document.head.removeChild(styleTag);
    };
  }, []);

  const { data: commentaryData, isLoading } = useQuery({
    queryKey: [quyerKeys.getMatchCommentary, matchId, sportId],
    queryFn: () => fetchMatchCommentary(matchId, sportId),
    refetchInterval: LIVE_POLLIN_TIME,
    enabled: !!matchId && !!sportId && !isCricket,
  });

  // Get match info based on sport type
  const getMatchInfo = () => {
    if (isSoccer && eventDetailsResponse?.result?.eventDetails) {
      const eventDetails = eventDetailsResponse.result.eventDetails;
      return {
        localteam: {
          name: eventDetails.homeTeam?.name || '',
          score: eventDetails.ScoreBoard?.Tr1 || 0,
          flag: eventDetails.homeTeam?.flag
            ? `${Config.mediaURL}${eventDetails.homeTeam.flag}`
            : '',
        },
        visitorteam: {
          name: eventDetails.awayTeam?.name || '',
          score: eventDetails.ScoreBoard?.Tr2 || 0,
          flag: eventDetails.awayTeam?.flag
            ? `${Config.mediaURL}${eventDetails.awayTeam.flag}`
            : '',
        },
        status: {
          value: eventDetails.status || 'In Progress',
        },
      };
    }
    return commentaryData?.matchInfo;
  };

  const matchInfo = getMatchInfo();

  // Helper function to determine event style and icon
  const getEventStyle = (eventType: string) => {
    if (isCricket) {
      switch (eventType) {
        case 'WICKET':
        case 'OUT':
          return {
            icon: <Award className="h-4 w-4" />,
            color: 'text-red-500',
            bgColor: 'bg-red-50',
            textColor: 'text-red-700',
          };
        case 'FOUR':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-blue-500',
            bgColor: 'bg-blue-50',
            textColor: 'text-blue-700',
          };
        case 'SIX':
          return {
            icon: <Award className="h-4 w-4" />,
            color: 'text-purple-500',
            bgColor: 'bg-purple-50',
            textColor: 'text-purple-700',
          };
        default:
          return {
            icon: <MessagesSquare className="h-4 w-4" />,
            color: 'text-gray-500',
            bgColor: 'bg-gray-50',
            textColor: 'text-gray-700',
          };
      }
    } else if (isNrl) {
      switch (eventType) {
        case 'Try':
        case 'Penalty Try':
          return {
            icon: <Award className="h-4 w-4" />,
            color: 'text-emerald-500',
            bgColor: 'bg-emerald-50',
            textColor: 'text-emerald-700',
          };
        case 'Conversion':
        case 'CONVERSION':
          return {
            icon: <RotateCw className="h-4 w-4" />,
            color: 'text-blue-500',
            bgColor: 'bg-blue-50',
            textColor: 'text-blue-700',
          };
        case 'Penalty':
        case 'PENALTY':
        case 'Penalty Goal':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-purple-500',
            bgColor: 'bg-purple-50',
            textColor: 'text-purple-700',
          };
        case 'Conv Miss':
        case '2Pts FG Miss':
          return {
            icon: <RotateCw className="h-4 w-4" />,
            color: 'text-orange-500',
            bgColor: 'bg-orange-50',
            textColor: 'text-orange-700',
          };
        case 'Field Goal':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-indigo-500',
            bgColor: 'bg-indigo-50',
            textColor: 'text-indigo-700',
          };
        default:
          return {
            icon: <MessagesSquare className="h-4 w-4" />,
            color: 'text-gray-500',
            bgColor: 'bg-gray-50',
            textColor: 'text-gray-700',
          };
      }
    } else if (isAfl) {
      switch (eventType) {
        case 'goal':
          return {
            icon: <Award className="h-4 w-4" />,
            color: 'text-emerald-500',
            bgColor: 'bg-emerald-50',
            textColor: 'text-emerald-700',
          };
        case 'behind':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-blue-500',
            bgColor: 'bg-blue-50',
            textColor: 'text-blue-700',
          };
        case 'rushed':
          return {
            icon: <ArrowRightLeft className="h-4 w-4" />,
            color: 'text-purple-500',
            bgColor: 'bg-purple-50',
            textColor: 'text-purple-700',
          };
        default:
          return {
            icon: <MessagesSquare className="h-4 w-4" />,
            color: 'text-gray-500',
            bgColor: 'bg-gray-50',
            textColor: 'text-gray-700',
          };
      }
    } else if (isSoccer) {
      switch (eventType) {
        case 'goal':
          return {
            icon: <Award className="h-4 w-4" />,
            color: 'text-green-500',
            bgColor: 'bg-green-50',
            textColor: 'text-green-700',
          };
        case 'yellowCard':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-yellow-500',
            bgColor: 'bg-yellow-50',
            textColor: 'text-yellow-700',
          };
        case 'redCard':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-red-500',
            bgColor: 'bg-red-50',
            textColor: 'text-red-700',
          };
        case 'substitution':
          return {
            icon: <ArrowRightLeft className="h-4 w-4" />,
            color: 'text-blue-500',
            bgColor: 'bg-blue-50',
            textColor: 'text-blue-700',
          };
        default:
          return {
            icon: <MessagesSquare className="h-4 w-4" />,
            color: 'text-gray-500',
            bgColor: 'bg-gray-50',
            textColor: 'text-gray-700',
          };
      }
    }

    return {
      icon: <MessagesSquare className="h-4 w-4" />,
      color: 'text-gray-500',
      bgColor: 'bg-gray-50',
      textColor: 'text-gray-700',
    };
  };

  const getTeamIcon = (teamFlag: string | undefined): string | undefined => {
    if (!teamFlag || teamFlag.trim() === '') return undefined;

    if (teamFlag.startsWith('http')) {
      return teamFlag;
    }

    // Always prepend media URL if not already a full URL
    return `${Config.mediaURL}${teamFlag}`;
  };

  const getTeamInitial = (team: any) => {
    if (!team) return 'T';

    if (team.nameCode && team.nameCode.trim() !== '') {
      return team.nameCode.substring(0, 1).toUpperCase();
    }

    if (team.shortName && team.shortName.trim() !== '') {
      return team.shortName.substring(0, 1).toUpperCase();
    }

    if (team.name && team.name.trim() !== '') {
      return team.name.substring(0, 1).toUpperCase();
    }

    return 'T';
  };

  // Render filter options based on sport
  const renderFilterOptions = () => {
    if (isCricket) {
      return (
        <select
          value={filter}
          onChange={(e) => {
            if (e.target.value !== 'Full Commentary') {
              setEventFilter(e.target.value);
              setFilter(e.target.value);
            } else {
              setEventFilter(undefined);
              setFilter(e.target.value);
            }
          }}
          className="w-48 p-2 rounded border bg-blue-600 text-white border-blue-700"
        >
          <option value="Full Commentary">Full Commentary</option>
          <option value="Wicket">Wickets</option>
          <option value="Four">Fours</option>
          <option value="Six">Sixes</option>
        </select>
      );
    } else if (isNrl) {
      return (
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="w-48 p-2 rounded border bg-blue-600 text-white border-blue-700"
        >
          <option value="Full Commentary">Full Commentary</option>
          <option value="Try">Try</option>
          <option value="Conversion">Conversion</option>
          <option value="Conv Miss">Conv Miss</option>
          <option value="Penalty">Penalty</option>
          <option value="Penalty Try">Penalty Try</option>
          <option value="Penalty Goal">Penalty Goal</option>
          <option value="Field Goal">Field Goal</option>
          <option value="2Pts FG Miss">2 Point Field Goal Miss</option>
        </select>
      );
    } else if (isAfl) {
      return (
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="w-48 p-2 rounded border bg-blue-600 text-white border-blue-700"
        >
          <option value="Full Commentary">Full Commentary</option>
          <option value="goal">Goal</option>
          <option value="behind">Behind</option>
        </select>
      );
    } else if (isSoccer) {
      return (
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="w-48 p-2 rounded border bg-blue-600 text-white border-blue-700"
        >
          <option value="Full Commentary">Full Commentary</option>
          <option value="goal">Goal</option>
          <option value="yellowCard">Yellow Card</option>
          <option value="redCard">Red Card</option>
          <option value="substitution">Substitution</option>
        </select>
      );
    }

    return null;
  };

  if (isLoading || (isCricket && isLiveCommentaryLoading)) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-200"></div>
      </div>
    );
  }

  // Check if we have the transformed data format or the original API response
  const isTransformedData =
    Array.isArray(commentaryData?.result) &&
    commentaryData.result.length > 0 &&
    'description' in (commentaryData.result[0] || {});

  const originalMatchData = !isTransformedData
    ? (commentaryData?.result as any)
    : null;
  const transformedEvents = isTransformedData
    ? (commentaryData?.result as unknown as CommentaryItem[])
    : [];

  // For transformed data, simply filter by type if needed
  const displayEvents = isTransformedData
    ? filter === 'Full Commentary'
      ? transformedEvents
      : transformedEvents.filter((event) => event.type === filter)
    : [];

  // For original API data, extract and filter events
  let originalEvents: any[] = [];
  if (originalMatchData?.events?.event) {
    const events = originalMatchData.events.event;
    originalEvents = Array.isArray(events) ? events : [events];

    if (filter !== 'Full Commentary') {
      originalEvents = originalEvents.filter((event) => event.type === filter);
    }
  }

  // Handle cricket commentary events
  let cricketEvents: any[] = [];
  if (isCricket && liveCommentary?.result?.result) {
    const eventsData = Array.isArray(liveCommentary.result.result)
      ? liveCommentary.result.result
      : [liveCommentary.result.result];

    cricketEvents = eventsData.map((event: any) => ({
      id: event.id ? event.id.toString() : '',
      type: event.event || 'NONE',
      team: event.T || '',
      min: event.Ov,
      player_name: event.T?.split(' to ')[1] || '',
      T: event.T,
      Run: event.Run,
      event: event.event || undefined,
      ballNbr: event.ballNbr,
      over_ended: event.over_ended,
      legbyes: event.legbyes || undefined,
      batTeamName: event.batTeamName,
    }));

    if (filter !== 'Full Commentary') {
      cricketEvents = cricketEvents.filter((event) => event.type === filter);
    }

    // Sort by over number and ball number
    cricketEvents.sort((a, b) => {
      const [overA, ballA] = (a.min || '0.0').split('.').map(Number);
      const [overB, ballB] = (b.min || '0.0').split('.').map(Number);

      if (overA !== overB) return overB - overA;
      return ballB - ballA;
    });
  }

  const src = getTeamIcon(compCard?.localteam?.flag);

  return (
    <div className="bg-off-white-100 mt-2 py-2">
      <div className="md:px-[32px] px-0">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* Match Info Header */}
          {(isCricket && compCard) || (isSoccer && matchInfo) ? (
            <div className="bg-gradient-to-r from-[#0A2D5A] to-[#1A4889] text-white p-4 flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative w-10 h-10 bg-white rounded-full flex items-center justify-center">
                  <img
                    src={getTeamIcon(
                      isSoccer
                        ? matchInfo?.localteam?.flag
                        : compCard?.localteam?.flag,
                    )}
                    alt={
                      isSoccer
                        ? matchInfo?.localteam?.name
                        : compCard?.localteam?.name
                    }
                    className="object-contain"
                    width={24}
                    height={24}
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.parentElement?.parentElement?.classList.add(
                        'fallback-to-initial',
                      );
                    }}
                  />
                </div>
                <span className="font-semibold">
                  {isSoccer
                    ? matchInfo?.localteam?.name || 'Team 1'
                    : compCard?.localteam?.name || 'Team 1'}
                </span>
              </div>

              <div className="flex flex-col items-center">
                <div className="text-2xl font-bold">
                  {isSoccer
                    ? `${matchInfo?.localteam?.score || '0'} - ${matchInfo?.visitorteam?.score || '0'}`
                    : `${compCard?.localteam?.score || '0'} - ${compCard?.visitorteam?.score || '0'}`}
                </div>
                <div className="text-sm opacity-80">
                  {isSoccer
                    ? matchInfo?.status?.value || 'In Progress'
                    : compCard?.status?.value || 'In Progress'}
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className="font-semibold">
                  {isSoccer
                    ? matchInfo?.visitorteam?.name || 'Team 2'
                    : compCard?.visitorteam?.name || 'Team 2'}
                </span>
                <div className="relative w-10 h-10 bg-white rounded-full flex items-center justify-center">
                  <img
                    src={getTeamIcon(
                      isSoccer
                        ? matchInfo?.visitorteam?.flag
                        : compCard?.visitorteam?.flag,
                    )}
                    alt={
                      isSoccer
                        ? matchInfo?.visitorteam?.name
                        : compCard?.visitorteam?.name
                    }
                    className="object-contain"
                    width={24}
                    height={24}
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.parentElement?.parentElement?.classList.add(
                        'fallback-to-initial',
                      );
                    }}
                  />
                </div>
              </div>
            </div>
          ) : null}

          {/* Filter Options */}
          <div className="p-3 bg-gray-50 border-b">{renderFilterOptions()}</div>

          {/* Commentary List */}
          <div className="p-4">
            <h2 className="text-xl font-semibold mb-4">Live Match Updates</h2>
            <div className="space-y-4">
              {isCricket ? (
                cricketEvents.length > 0 ? (
                  cricketEvents.map((event, index) => {
                    const eventStyle = getEventStyle(event.type);
                    const timeDisplay = event.min || '0.0';

                    return (
                      <div
                        key={index}
                        className="border-b pb-3 last:border-b-0 flex items-start gap-3"
                      >
                        <div className="flex-shrink-0 flex flex-col items-center">
                          <div className="text-gray-600 font-medium flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{timeDisplay}</span>
                          </div>
                        </div>

                        <div className="flex-1">
                          {event.type !== 'INTERCHANGE' && (
                            <div
                              className={`inline-flex items-center gap-2 px-2 py-1 rounded-md text-sm font-medium mb-2 ${eventStyle.bgColor} ${eventStyle.textColor}`}
                            >
                              {eventStyle.icon}
                              <span>{event.type.toUpperCase()}</span>
                            </div>
                          )}
                          <p className="text-black-100">{event.T}</p>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <p className="text-center text-gray-500">
                    No updates available
                  </p>
                )
              ) : displayEvents.length > 0 ? (
                displayEvents.map((event, index) => {
                  const eventStyle = getEventStyle(event.type);

                  return (
                    <div
                      key={index}
                      className="border-b pb-3 last:border-b-0 flex items-start gap-3"
                    >
                      <div className="flex-shrink-0 flex flex-col items-center">
                        <div className="text-gray-600 font-medium flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{event.time}</span>
                        </div>

                        {/* Display team logo if available */}
                        {event.teamFlag && getTeamIcon(event.teamFlag) ? (
                          <div
                            className="mt-1 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center overflow-hidden"
                            data-initial={
                              event.teamName
                                ? event.teamName.substring(0, 1).toUpperCase()
                                : 'T'
                            }
                          >
                            <div className="relative w-5 h-5">
                              <img
                                src={getTeamIcon(event.teamFlag)}
                                alt={event.teamName || ''}
                                className="object-contain"
                                width={20}
                                height={20}
                                onError={(e) => {
                                  e.currentTarget.style.display = 'none';
                                  e.currentTarget.parentElement?.parentElement?.classList.add(
                                    'fallback-to-initial',
                                  );
                                }}
                              />
                            </div>
                          </div>
                        ) : event.teamName ? (
                          <div className="mt-1 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <span className="font-bold text-gray-700">
                              {event.teamName.substring(0, 1).toUpperCase()}
                            </span>
                          </div>
                        ) : null}
                      </div>

                      <div className="flex-1">
                        {event.type !== 'INTERCHANGE' && (
                          <div
                            className={`inline-flex items-center gap-2 px-2 py-1 rounded-md text-sm font-medium mb-2 ${eventStyle.bgColor} ${eventStyle.textColor}`}
                          >
                            {eventStyle.icon}
                            <span>{event.type.toUpperCase()}</span>
                          </div>
                        )}
                        <p className="text-black-100">{event.description}</p>

                        {/* Display player and team name if available */}
                        {event.playerName && event.teamName && (
                          <div className="text-sm text-gray-500 mt-1">
                            - {event.playerName}, {event.teamName}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })
              ) : (
                <p className="text-center text-gray-500">
                  No updates available
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveUpdates;
