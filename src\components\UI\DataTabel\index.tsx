'use client';

import type {
  ColumnDef,
  ExpandedState,
  VisibilityState,
  ColumnMeta,
} from '@tanstack/react-table';
import { flexRender, getExpandedRowModel } from '@tanstack/react-table';
import type { CSSProperties } from 'react';
import React, { useState } from 'react';

import Loader from '@/components/Loader';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/UI/table';
import { useDataTable } from '@/hooks/useDataTable';
import { cn, generateUniqueId } from '@/lib/utils';
import PlayerScoreTable from '@/components/Common/Commentary/PlayerScoreTable';
import { SkeletonDataTable } from '@/components/Loading/SkeletonDataTable';

// Extend ColumnMeta type
declare module '@tanstack/react-table' {
  interface ColumnMeta<TData extends unknown, TValue> {
    hidden?: boolean;
  }
}

interface noDataMessageProps {
  title: string;
  description: string;
  hideDescription?: boolean;
  hideTitle?: boolean;
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  stickyColumns?: number[];
  initialColumnVisibility?: VisibilityState;
  columnVisibility?: VisibilityState; // New prop for external column visibility control
  onColumnVisibilityChange?: (visibility: VisibilityState) => void; // Callback for visibility changes
  isLoading?: boolean;
  layout?: 'size' | 'position' | boolean;
  maxHeight?: string; // New prop for controlling the max height
  hideHeader?: boolean;
  hideCellVisibility?: boolean;
  skeletonRows?: number;
  noDataMessage?: noDataMessageProps;
}

export default function DataTable<TData, TValue>({
  columns,
  data,
  stickyColumns = [],
  initialColumnVisibility,
  columnVisibility,
  onColumnVisibilityChange,
  isLoading,
  layout = 'size',
  maxHeight = '100%', // Default height of 700px
  hideHeader = false,
  hideCellVisibility = false,
  skeletonRows = 10,
  noDataMessage = {
    title: 'No data available',
    description: 'Please try again later',
  },
}: Readonly<DataTableProps<TData, TValue>>) {
  const { table } = useDataTable({
    columns,
    data: isLoading ? [] : data,
    initialColumnVisibility,
    columnVisibility,
    onColumnVisibilityChange,
  });

  // Function to get sticky styles based on column index
  const getStickyStyle = (index: number) => {
    if (!stickyColumns.includes(index)) return {};

    // Calculate left position based on previous sticky columns
    const leftOffset = stickyColumns
      .filter((colIndex) => colIndex < index)
      .reduce((acc, _) => acc + 200, 0); // Assuming each column is 200px wide

    return {
      position: 'sticky',
      left: `${leftOffset}px`,
      zIndex: 20,
      backgroundColor: 'inherit',
      boxShadow: '2px 0 4px rgba(0, 0, 0, 0.1)',
    } as CSSProperties;
  };

  const getRowClassNames = (isSticky: boolean, rowIndex: number) => {
    let rowClasses = '';

    if (isSticky) {
      rowClasses += rowIndex % 2 === 0 ? 'bg-white ' : 'bg-gray-50 ';
      rowClasses += 'border-r border-gray-100';
    }

    return rowClasses.trim();
  };

  const renderRows = () => {
    if (table?.getRowModel()?.rows?.length) {
      return table.getRowModel().rows.map((row, rowIndex) => (
        <React.Fragment key={generateUniqueId()}>
          <TableRow
            data-state={row?.getIsSelected() && 'selected'}
            className={cn(
              // @ts-expect-error
              row?.original?.myLastTeam && 'border-b border-secondary-100',
              rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50',
            )}
          >
            {row.getVisibleCells().map((cell, index) => {
              // Skip rendering if column has meta.hidden set to true
              if (cell.column.columnDef.meta?.hidden) {
                return null;
              }
              const isSticky = stickyColumns.includes(index);
              return (
                <TableCell
                  key={cell.id}
                  style={getStickyStyle(index)}
                  className={cn(getRowClassNames(isSticky, rowIndex))}
                >
                  {hideCellVisibility && index !== 0 ? (
                    <div className="w-4 h-4 "></div>
                  ) : (
                    flexRender(cell.column.columnDef.cell, cell.getContext())
                  )}
                </TableCell>
              );
            })}
          </TableRow>
        </React.Fragment>
      ));
    }
    return null;
  };

  return (
    <div className="rounded-lg overflow-hidden">
      <div
        className="overflow-auto no-scrollbar"
        style={{
          maxHeight: maxHeight,
          position: 'relative',
        }}
      >
        <Table>
          <TableHeader className={cn("sticky top-0 h-10 left-0 z-30", hideHeader && "hidden")}>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow
                key={headerGroup.id}
                className={cn(
                  "bg-primary-200 text-white first:rounded-t-lg",
                )}
              >
                {headerGroup.headers.map((header, index) => {
                  // Skip rendering if column has meta.hidden set to true
                  if (header.column.columnDef.meta?.hidden) {
                    return null;
                  }
                  const isSticky = stickyColumns.includes(index);
                  return (
                    <TableHead
                      key={header.id}
                      style={getStickyStyle(index)}
                      className={`  
                        ${isSticky ? 'bg-gray-100' : ''}
                        ${isSticky ? 'z-30' : ''}
                      `}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="text-center"
                >
                  <SkeletonDataTable rows={skeletonRows} />
                </TableCell>
              </TableRow>
            ) : !data?.length ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    {!noDataMessage.hideTitle && <p className="text-lg font-semibold">{noDataMessage.title}</p>}
                    {!noDataMessage.hideDescription && <p className="text-base">{noDataMessage.description}</p>}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              renderRows()
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
