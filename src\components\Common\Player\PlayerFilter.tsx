import type { Table } from '@tanstack/react-table';
import { X } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import { useRef, useState } from 'react';

import { Checkbox } from '@/components/UI/checkbox';
import { Label } from '@/components/UI/label';
import { useCompetition } from '@/helpers/context/competitionContext';
import { FootballPlayer } from '../../../../types/competitions';
import { RugbyPlayer } from '../../../../types/rugby-league';
import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';

type Team = {
  homeTeam: boolean;
  awayTeam: boolean;
};

export type FiltersState = {
  matchesPlayed: boolean;
  priceRange: [number, number];
  dualPosition: boolean;
  teams: Team;
  breakeven: [number, number];
  projectedScore: [number, number];
  projectedValueChange: [number, number];
  selectionPercentage: [number, number];
};

type TeamKey = keyof FiltersState['teams'];
type FilterKey = keyof Omit<FiltersState, 'teams'>;

type FootballPlayerFilterTypeProps = {
  playerTable: Table<FootballPlayer> | Table<RugbyPlayer>;
  filters: FiltersState;
  setFilters: Dispatch<SetStateAction<FiltersState>>;
  setShowFilter: Dispatch<SetStateAction<boolean>>;
};

export default function FootballPlayerFilter({
  playerTable,
  filters,
  setFilters,
  setShowFilter,
}: Readonly<FootballPlayerFilterTypeProps>) {
  const { team, setTeam } = useRugbyLeagueContext();
  const handleCheckboxChange = (
    key: FilterKey | TeamKey,
    isTeam: boolean = false,
  ) => {
    setFilters((prev) => {
      if (isTeam) {
        return {
          ...prev,
          teams: {
            ...prev.teams,
            [key]: !prev.teams[key as TeamKey],
          },
        };
      }
      return {
        ...prev,
        [key]: !prev[key as FilterKey],
      };
    });
  };

  const handleClearAll = () => {
    setFilters({
      matchesPlayed: false,
      priceRange: [0, 100],
      dualPosition: false,
      teams: {
        homeTeam: false,
        awayTeam: false,
      },
      breakeven: [0, 100],
      projectedScore: [0, 100],
      projectedValueChange: [6300, 9300],
      selectionPercentage: [0, 100],
    });

    setShowFilter(false);
    playerTable.resetColumnFilters();
    setTeam({ home: false, away: false });
  };

  const { eventDetailsResponse } = useCompetition();

  const homeTeam = eventDetailsResponse?.result?.eventDetails?.homeTeam;
  const awayTeam = eventDetailsResponse?.result?.eventDetails?.awayTeam;

  const circketFilterRef = useRef<HTMLDivElement>(null);

  return (
    <section ref={circketFilterRef}>
      <div className="w-full p-6 bg-gray-100 rounded-lg shadow-lg hidden md:block">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-semibold">Filter</h2>
          <button
            onClick={handleClearAll}
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
          >
            Clear all
            <X className="w-4 h-4 ml-1" />
          </button>
        </div>

        <div className="">
          <div className="flex justify-between">
            {/* <div className="w-full">
              <h3 className="font-medium mb-2">Matches played</h3>
              <div className="flex items-center">
                <Checkbox
                  id="played-every-match"
                  checked={filters.matchesPlayed}
                  onCheckedChange={() => handleCheckboxChange('matchesPlayed')}
                />
                <Label htmlFor="played-every-match" className="ml-2">
                  Played every match
                </Label>
              </div>
            </div> */}
            {/* <div className="w-full">
              <h3 className="font-medium mb-2">By price range</h3>
              <DualRangeSlider
                value={filters.priceRange}
                onValueChange={handleDualRangeChange('priceRange')}
                min={0}
                max={100}
                step={1}
                className="mt-5"
              />
              <div className="flex justify-between text-sm text-gray-600">
                <span>${filters.priceRange[0]}k</span>
                <span>${filters.priceRange[1]}k</span>
              </div>
            </div> */}
          </div>
          {/* By postion */}
          <div className="flex justify-between">
            {/* By team */}

            <div className="w-full">
              <h3 className="font-medium mb-2">By team</h3>
              <div className="space-y-2">
                <div className="flex items-center">
                  <Checkbox
                    id="adelaide-strikers"
                    checked={team?.home}
                    onCheckedChange={() => {
                      // handleCheckboxChange('homeTeam', true);
                      setTeam({ home: true, away: false });

                      playerTable
                        .getColumn('teamName')
                        ?.setFilterValue(homeTeam?.name);
                    }}
                  />
                  <Label htmlFor="adelaide-strikers" className="ml-2">
                    {homeTeam?.name}
                  </Label>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    id="sydney-sixers"
                    checked={team?.away}
                    onCheckedChange={() => {
                      // handleCheckboxChange('awayTeam', true);
                      setTeam({ away: true, home: false });

                      playerTable
                        .getColumn('teamName')
                        ?.setFilterValue(awayTeam?.name);
                    }}
                  />
                  <Label htmlFor="sydney-sixers" className="ml-2">
                    {awayTeam?.name}
                  </Label>
                </div>
              </div>
            </div>
          </div>

          {/* <div>
            <h3 className="font-medium mb-2">By breakeven</h3>
            <DualRangeSlider
              value={filters.breakeven}
              onValueChange={handleDualRangeChange('breakeven')}
              min={0}
              max={100}
              step={1}
              className="mt-5"
            />
            <div className="flex justify-between text-sm text-gray-600">
              <span>{filters.breakeven[0]}</span>
              <span>{filters.breakeven[1]}</span>
            </div>
          </div> */}

          {/* <div>
            <h3 className="font-medium mb-2">By projected score</h3>
            <DualRangeSlider
              value={filters.projectedScore}
              onValueChange={handleDualRangeChange('projectedScore')}
              min={0}
              max={100}
              step={1}
              className="mt-5"
            />
            <div className="flex justify-between text-sm text-gray-600">
              <span>{filters.projectedScore[0]}</span>
              <span>{filters.projectedScore[1]}</span>
            </div>
          </div> */}

          {/* <div>
            <h3 className="font-medium mb-2">By projected value change</h3>
            <DualRangeSlider
              value={filters.projectedValueChange}
              onValueChange={handleDualRangeChange('projectedValueChange')}
              min={6300}
              max={9300}
              step={100}
              className="mt-5"
            />
            <div className="flex justify-between text-sm text-gray-600">
              <span>${filters.projectedValueChange[0]}</span>
              <span>${filters.projectedValueChange[1]}</span>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">By selection %</h3>
            <DualRangeSlider
              value={filters.selectionPercentage}
              onValueChange={handleDualRangeChange('selectionPercentage')}
              min={0}
              max={100}
              step={1}
              className="mt-5"
            />
            <div className="flex justify-between text-sm text-gray-600">
              <span>{filters.selectionPercentage[0]}</span>
              <span>{filters.selectionPercentage[1]}</span>
            </div>
          </div> */}
        </div>
      </div>
    </section>
  );
}
