'use client';
import CustomDialog from '@/components/UI/CustomDialog';
import Link from 'next/link';
import { Checkbox } from '@/components/UI/checkbox';
import { Button } from '@/components/UI/button';
import { Spinner } from '@material-tailwind/react';
import React, { Dispatch, SetStateAction, useState } from 'react';
import { RadioGroup, RadioGroupItem } from '@/components/UI/radio-group';
import { Label } from '@/components/UI/label';
import { setApiMessage } from '@/helpers/commonFunctions';
import { PaymentMethod } from '../../../../../types/competitions';
interface EventConfirmModalProps {
  paymentMethod: PaymentMethod;
  setPaymentMethod: Dispatch<SetStateAction<PaymentMethod>>;
  isOpen: boolean;
  onClose: () => void;
  user: { coins: number; holdCoins?: number, bonusCoins?: number } | null;
  eventConfigurationData: { entryCoin?: number } | null;
  isCreatingTeamPending: boolean;
  acceptTerms: boolean;
  setAcceptTerms: (checked: boolean) => void;
  handleTeamSubmitConfirmation: ({
    coins,
    bonusCoins,
  }: {
    coins: number;
    bonusCoins: number;
  }) => void;
  smartCoinsAmount: number;
  setSmartCoinsAmount: Dispatch<SetStateAction<number>>;
  bonusCoinsAmount: number;
  setBonusCoinsAmount: Dispatch<SetStateAction<number>>;
}

const EventConfirmModal: React.FC<EventConfirmModalProps> = ({
  paymentMethod,
  setPaymentMethod,
  isOpen,
  onClose,
  user,
  eventConfigurationData,
  isCreatingTeamPending,
  acceptTerms,
  setAcceptTerms,
  handleTeamSubmitConfirmation,
  smartCoinsAmount,
  setSmartCoinsAmount,
  bonusCoinsAmount,
  setBonusCoinsAmount,
}) => {
  const handelSubmitEvent = () => {
    if (notHaveEnoughBonusCoins && paymentMethod === 'bonusCoins') {
      setApiMessage("error", "You don’t have enough BonusCoins to join this event with the selected payment option.");
      return;
    } else {
      handleTeamSubmitConfirmation({
        coins: smartCoinsAmount,
        bonusCoins: bonusCoinsAmount,
      });
    }
  }

  const totalBonusCoins = user?.bonusCoins ?? 0;
  const entryCoin = eventConfigurationData?.entryCoin ?? 0;
  const notHaveEnoughBonusCoins = totalBonusCoins < entryCoin;
  const notHaveEnoughBonusCoinsWithBonusCoins = entryCoin > (smartCoinsAmount + bonusCoinsAmount) && paymentMethod === 'both';
  let smartCoinsToSubtract = 0;
  let bonusCoinsToSubtract = 0;
  switch (paymentMethod) {
    case "both":
      smartCoinsToSubtract = smartCoinsAmount;
      bonusCoinsToSubtract = bonusCoinsAmount;
      break;
    case "smartCoins":
      smartCoinsToSubtract = entryCoin;
      break;
    case "bonusCoins":
      bonusCoinsToSubtract = entryCoin;
      break;
  }
  const decreaseSmartCoins = () => {
    setSmartCoinsAmount((prev) => Math.max(0, prev - 1))
  }
  const increaseSmartCoins = () => {
    const newAmount = smartCoinsAmount + 1;
    if (paymentMethod === 'both' && (newAmount + bonusCoinsAmount) <= entryCoin) {
      setSmartCoinsAmount(newAmount);
    } else if (paymentMethod !== 'both' && newAmount <= entryCoin) {
      setSmartCoinsAmount(newAmount);
    }
  }
  const decreaseBonusCoins = () => {
    setBonusCoinsAmount((prev) => Math.max(0, prev - 1))
  }
  const increaseBonusCoins = () => {
    const newAmount = bonusCoinsAmount + 1;
    if (paymentMethod === 'both' && (smartCoinsAmount + newAmount) <= entryCoin) {
      setBonusCoinsAmount(newAmount);
    } else if (paymentMethod !== 'both' && newAmount <= entryCoin) {
      setBonusCoinsAmount(newAmount);
    }
  }

  // Handler for direct input in SmartCoins
  const handleSmartCoinsInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    const numValue = value === '' ? 0 : parseInt(value);

    if (paymentMethod === 'both') {
      // Ensure combined amount doesn't exceed entry coin
      if (numValue + bonusCoinsAmount <= entryCoin) {
        setSmartCoinsAmount(numValue);
      }
    } else {
      // For smartCoins only, just ensure it doesn't exceed entry coin
      setSmartCoinsAmount(Math.min(numValue, entryCoin));
    }
  };

  // Handler for direct input in BonusCoins
  const handleBonusCoinsInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    const numValue = value === '' ? 0 : parseInt(value);

    // First ensure the input doesn't exceed total available bonus coins
    const validatedAmount = Math.min(numValue, totalBonusCoins);

    if (paymentMethod === 'both') {
      // Ensure combined amount doesn't exceed entry coin
      if (smartCoinsAmount + validatedAmount <= entryCoin) {
        setBonusCoinsAmount(validatedAmount);
      }
    } else {
      // For bonusCoins only, ensure it doesn't exceed entry coin and total bonus coins
      setBonusCoinsAmount(Math.min(validatedAmount, entryCoin));
    }
  };

  return (
    <div className='max-w-[570px]'>
      <CustomDialog
        isOpen={isOpen}
        onClose={() => {
          setAcceptTerms(false);
          setPaymentMethod('smartCoins');
          onClose();
        }}
        title="Confirmation"
        maxWidth={570}
        className="Confirmation-required-modal"
        outerClickClose={true}
        titleClassName="font-apotekCompRegular text-left text-[30px] md:text-[43.9px]"
      >
        <div>
          <div className="bg-gray-100 rounded-lg py-[27px] max-799:py-3 px-[18px] max-799:px-3 ">
            <div className="flex items-center gap-1 gap-x-[13px] mb-2.5">
              <p className="w-[calc(100%-100px)] max-799:w-[calc(100%-90px)] h-[1px] bg-black-500"></p>
              <p className="text-[12px] w-[200px] text-center max-799:text-[11.42px] leading-[15px] max-799:leading-[14px] font-inter font-normal text-black-100">
                Current balance
              </p>
              <p className="w-[calc(100%-100px)] max-799:w-[calc(100%-90px)] h-[1px] bg-black-500"></p>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
                SmartCoins
              </p>
              <p className="flex items-center gap-1">
                <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
                  {((user?.coins ?? 0) - (user?.holdCoins ?? 0)).toFixed(2)}
                </span>
              </p>
            </div>
            <div className="flex items-center justify-between mt-[13px]">
              <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
                BonusCoins
              </p>
              <p className="flex items-center gap-1">
                <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
                  {totalBonusCoins.toFixed(2)}
                </span>
              </p>
            </div>
          </div>
          <div className="flex items-center justify-between mx-[18px] max-799:mx-3 mt-4 mb-[23px] max-799:mb-3 pb-2 border-b border-black-500">
            <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
              Entry coins:
            </p>
            <p className="flex items-center gap-1">
              <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
                {entryCoin.toFixed(2)}
              </span>
            </p>
          </div>
          <div className="flex items-center justify-between mx-[18px] max-799:mx-3 mt-4 mb-3">
            <RadioGroup defaultValue="smartCoins" onValueChange={(value) => {
              setPaymentMethod(value as PaymentMethod)
              if (value === "both") {
                setSmartCoinsAmount(0)
                setBonusCoinsAmount(0)
              } else {
                setSmartCoinsAmount(0)
                setBonusCoinsAmount(0)
              }
            }}>
              <div className="flex items-center gap-3">
                <RadioGroupItem value="smartCoins" id="smartCoins" />
                <Label htmlFor="smartCoins">SmartCoins</Label>
              </div>
              <div className="flex items-center gap-3">
                <RadioGroupItem value="bonusCoins" id="bonusCoins" disabled={notHaveEnoughBonusCoins} />
                <Label htmlFor="bonusCoins" className={notHaveEnoughBonusCoins ? 'text-gray-400 opacity-[0.7]' : ''}>BonusCoins</Label>
              </div>
              <div className="flex items-center gap-3">
                <RadioGroupItem value="both" id="both" disabled={totalBonusCoins === 0} />
                <Label htmlFor="both" className={totalBonusCoins === 0 ? 'text-gray-400 opacity-[0.7]' : ''}>SmartCoins + BonusCoins</Label>
              </div>
            </RadioGroup>
          </div>
          <div className='mx-[18px] max-799:mx-3 mt-4  max-799:mb-3 border-b border-secondary-100'>
            {paymentMethod === 'both' && (
              <div className="grid grid-cols-1 pb-3 place-items-center md:grid-cols-2 gap-4 md:max-w-[80%] max-w-full mx-auto">
                <div className="w-full pr-2">
                  <div className="text-center mb-2">SmartCoins</div>
                  <div className="flex w-full">
                    <button
                      onClick={decreaseSmartCoins}
                      className="w-10 rounded-l-md h-10 flex items-center justify-center border border-black-400 text-[#003764] text-xl"
                    >
                      −
                    </button>
                    <input
                      type="text"
                      value={smartCoinsAmount}
                      className="w-full h-10 text-center border-t border-b border-black-400"
                      min={0}
                      onChange={handleSmartCoinsInput}
                    />
                    <button
                      onClick={increaseSmartCoins}
                      className="w-10 h-10 rounded-r-md flex items-center justify-center border border-black-400 text-[#003764] text-xl"
                    >
                      +
                    </button>
                  </div>
                </div>
                <div className="w-full pl-2">
                  <div className="text-center mb-2">BonusCoins</div>
                  <div className="flex w-full">
                    <button
                      onClick={decreaseBonusCoins}
                      className="w-10 h-10 rounded-l-md flex items-center justify-center border border-black-400 text-[#003764] text-xl"
                    >
                      −
                    </button>
                    <input
                      type="text"
                      value={bonusCoinsAmount}
                      className="w-full h-10 text-center border-t border-b border-black-400"
                      min={0}
                      max={totalBonusCoins}
                      onChange={handleBonusCoinsInput}
                    />
                    <button
                      onClick={increaseBonusCoins}
                      className="w-10 h-10 rounded-r-md flex items-center justify-center border border-black-400 text-[#003764] text-xl"
                    >
                      +
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="flex mt-5 items-center text-secondary-100 justify-between px-[18px] max-799:px-3  mb-[35px] max-799:mb-[15px]">
            <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
              Total:
            </p>
            <p className="flex items-center gap-1">
              <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
                {entryCoin.toFixed(2)}
              </span>
            </p>
          </div>
          <div className="bg-gray-100 rounded-lg py-[27px] max-799:py-3 px-[18px] max-799:px-3">
            <div className="flex items-center gap-1 mb-2.5">
              <div className="w-[calc(100%-100px)] max-799:w-[calc(100%-90px)] h-[1px] bg-black-500"></div>
              <p className="text-[12px] w-[200px] text-center max-799:text-[11.42px] leading-[15px] max-799:leading-[14px] font-inter font-normal text-black-100">
                New balance
              </p>
              <p className="w-[calc(100%-100px)] max-799:w-[calc(100%-90px)] h-[1px] bg-black-500"></p>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
                SmartCoins
              </p>
              <p className="flex items-center gap-1">
                <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
                  {(
                    (user?.coins ?? 0) -
                    (user?.holdCoins ?? 0) -
                    smartCoinsToSubtract
                  ).toFixed(2)}
                </span>
              </p>
            </div>
            <div className="flex items-center justify-between mt-[13px]">
              <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
                BonusCoins
              </p>
              <p className="flex items-center gap-1">
                <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter">
                  {(
                    totalBonusCoins -
                    bonusCoinsToSubtract
                  ).toFixed(2)}
                </span>
              </p>
            </div>
          </div>
          <div className="flex items-start gap-1.5 mt-[15px]">
            <Checkbox
              id="save-card"
              onCheckedChange={(check) => {
                setAcceptTerms(!!check);
              }}
              checked={acceptTerms}
            />
            <label
              htmlFor="save-card"
              className="text-[12px] font-normal leading-[17px] font-inter text-black-100 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              By completing this payment, I agree to the{' '}
              <Link
                href="/terms-conditions"
                className="text-secondary-100 underline"
                target="_blank"
              >
                Terms and Conditions
              </Link>
              . Please note that entries cannot be cancelled once confirmed.
            </label>
          </div>
          <Button
            onClick={handelSubmitEvent}
            className="w-full mt-8 flex space-x-2 justify-center items-center"
            disabled={isCreatingTeamPending || !acceptTerms || notHaveEnoughBonusCoinsWithBonusCoins}
          >
            {isCreatingTeamPending && (
              <Spinner
                color="amber"
                aria-label="Loading"
                onResize={() => { }}
                onResizeCapture={() => { }}
                onPointerEnterCapture={() => { }}
                onPointerLeaveCapture={() => { }}
              />
            )}
            <span>Confirm and submit team</span>
          </Button>
        </div>
      </CustomDialog>
    </div>

  );
};

export default EventConfirmModal;