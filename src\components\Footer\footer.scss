@import '../../assets/scss/variable.scss';

.Footer {
  position: relative;

  .MuiGrid-root {
    padding: 0px;
  }

  .footer-wrap {
    @media (max-width: 767px) {
      margin: 0px -12px;
    }
  }

  .soc-footer-wrap {
    background-color: $color-Primary;

    .footer-content {
      max-width: 1281px;
      margin: 0px auto;

      @media (max-width: 767px) {
        max-width: none;
        padding: 23px 24px 15px;
      }
    }
  }

  .footer-content {
    background-color: $color-Primary;
    padding: 30px 12px 21px 57px;
    border-bottom: 1px solid $color-White;

    @media (max-width: 767px) {
      padding: 23px 12px 15px;
    }

    .footer-links-wrap {
      padding-top: 27px;

      @media (max-width: 767px) {
        padding-top: 23px;
      }
    }

    h4 {
      text-align: left;
      color: #ffffff;
      font-size: 16px;
      font-weight: 500;
      line-height: 19px;
      margin: 0px 0px 12px 0px;

      @media (max-width: 767px) {
        font-size: 11.42px;
        font-weight: 600;
        line-height: 14px;
        margin: 0px 0px 6px 0px;
      }
    }

    .app-logo {
      @media (max-width: 1279px) {
        display: flex;
        align-items: start;
        flex-direction: column;
      }
    }

    .link-wrap {
      a {
        display: block;
        color: #ffffff;
        font-size: 16px;
        font-weight: normal;
        line-height: 19px;
        text-align: left;
        margin-bottom: 18px;

        @media (max-width: 767px) {
          font-size: 11.42px;
          font-weight: 600;
          line-height: 14px;
          margin-bottom: 9px;
        }
      }

      .active {
        font-weight: 600;
        text-decoration: underline;
      }
    }

    .mobile-policy {
      padding: 23px 0px;

      @media (max-width: 479px) {
        padding: 15px 0px;
      }
    }

    .playstore-logo {
      @media (max-width: 1279px) {
        margin-right: 27.5px;
      }

      @media (max-width: 400px) {
        margin-right: 10px;
      }
    }

    .contact {
      display: flex;

      .email-icon-wrap {
        width: 23px;
        height: 18.4px;

        svg {
          width: 100%;
          height: 100%;
        }
      }

      span {
        text-align: left;
        font-size: 16px;
        line-height: 22.4px;
        color: #ffffff;
        font-weight: normal;
        margin-bottom: 10px;
        padding-left: 9px;
      }

      a {
        color: #ffffff;
      }
    }

    .playstore-img {
      width: 145px;
      height: 43px;
    }

    .social-icon-wrap {
      width: 60%;
      display: grid;
      grid-template-columns: auto auto auto;
      column-gap: 10px;
      row-gap: 12px;
      justify-content: inherit;

      @media only screen and (max-width: 1279px) {
        width: 30%;
      }

      @media only screen and (max-width: 767px) {
        width: 60%;
      }
    }

    .social-media-wrap {
      padding-top: 32px;

      @media (max-width: 767px) {
        padding-top: 0px;
      }

      p {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        line-height: 19px;

        @media only screen and (max-width: 799px) {
          padding: 8px 16px;
        }
      }
    }
  }

  .soc-footer-copyright-wrap {
    background-color: $color-Primary;

    .copyright {
      max-width: 1350px;
      margin: 0px auto;
    }
  }

  p.copyright {
    background-color: $color-Primary;
    margin: 0px;
    text-align: center;
    font-size: 11.42px;
    color: #ffff;
    line-height: 14px;
    padding: 18px 0px 15px 0px;
  }
}

.upper-footer {
  background-color: $color-White;
  text-align: center;
  padding: 18px 0px;

  .responsible-gambling-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    row-gap: 5px;

    .tag-line {
      font-size: 31.36px;
      line-height: 33px;
      font-weight: 700;
      font-family: $arialFont;

      @media (max-width: 767px) {
        font-size: 18px;
        line-height: 18px;
      }
    }

    .responsible-gambling-text {
      color: $color-Black;
      font-size: 18px;
      line-height: 33px;
      font-family: $arialFont;
      margin-left: 5px;

      @media (max-width: 767px) {
        font-size: 12px;
        line-height: 18px;
      }

      .responsible-gambling-helpline-no {
        color: $color-Accent-1;
        text-decoration: underline;
      }
    }

    .responsible-gambling-logo {
      svg {
        max-width: 200px;
        max-height: 20px;
      }
    }
  }
}

@media (max-width: 479px) {
  .Footer {
    .footer-content {
      .contact {
        span {
          font-size: 11.42px;
          line-height: 16px;
        }
      }

      .responsible-gambling-wrap {
        flex-direction: column;
        row-gap: 5px;
        padding-top: 5px;
      }
    }
  }
}
