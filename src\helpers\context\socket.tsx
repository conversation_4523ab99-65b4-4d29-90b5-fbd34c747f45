'use client';
import React, {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import type { Socket } from 'socket.io-client';
import { io } from 'socket.io-client';

interface SocketContextType {
  socket: Socket | null;
}

let socket: Socket | null = null;

export const connectSocket = (): Socket => {
  if (!socket) {
    socket = io({
      path: '/api/socket',
    });
  }
  return socket;
};

export const disconnectSocket = (): void => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [socket, setSocket] = useState<Socket | null>(null);

  useEffect(() => {
    const socketInstance = connectSocket();
    setSocket(socketInstance);

    return () => {
      disconnectSocket();
    };
  }, []);

  const socketValue = useMemo(() => ({ socket }), [socket]);

  return (
    <SocketContext.Provider value={socketValue}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
