'use client';
import Image from 'next/image';
import React from 'react';

import useScreen from '@/hooks/useScreen';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon?: string;
  alt?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  alt = '',
}) => {
  const { width } = useScreen();

  return (
    <div className="w-[23%] sm:w-[48%] md:w-[48%] lg:w-full h-auto min-h-[51px] flex flex-col bg-dark-card-gradient lg:bg-none items-center lg:items-start justify-center shadow-md lg:shadow-none space-y-1 p-2 sm:p-3 md:p-2 rounded-md md:bg-transparent">
      <div
        className={`lg:text-[#5D5D5D] text-white text-xs sm:text-sm md:text-[11.42px] lg:text-regular`}
      >
        {title}
      </div>
      <div className="flex items-center space-x-2">
        {icon && (
          <Image
            width={width >= 960 ? 27 : width >= 768 ? 20 : 17}
            height={width >= 960 ? 27 : width >= 768 ? 20 : 17}
            src={icon}
            alt={alt}
            unoptimized={true}
            className="object-contain"
          />
        )}
        <h2 className="font-semibold text-sm sm:text-base md:text-[14px] lg:text-[22.4px] text-white lg:text-secondary-100">
          {value}
        </h2>
      </div>
    </div>
  );
};

export default StatsCard;
