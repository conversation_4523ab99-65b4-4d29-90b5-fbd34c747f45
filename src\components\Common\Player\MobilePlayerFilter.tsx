import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';

import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Checkbox } from '@/components/UI/checkbox';
import { Label } from '@/components/UI/label';
import { DualRangeSlider } from '@/components/UI/DualRangeSlider';
import { Button } from '@/components/UI/button';

type MobileFootballPlayerFilterProps = {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
};

type FiltersState = {
  matchesPlayed: boolean;
  priceRange: [number, number];
  dualPosition: boolean;
  teams: {
    adelaideStrikers: boolean;
    sydneySixers: boolean;
  };
  breakeven: [number, number];
  projectedScore: [number, number];
  projectedValueChange: [number, number];
  selectionPercentage: [number, number];
};

type RangeFilterProps = {
  title: string;
  value: [number, number];
  onValueChange: (value: [number, number]) => void;
  min: number;
  max: number;
  step: number;
  prefix?: string;
  suffix?: string;
};

type CheckboxFilterProps = {
  id: string;
  label: string;
  checked: boolean;
  onChange: () => void;
};

const CheckboxFilter = ({
  id,
  label,
  checked,
  onChange,
}: CheckboxFilterProps) => (
  <div className="flex items-center">
    <Checkbox id={id} checked={checked} onCheckedChange={onChange} />
    <Label htmlFor={id} className="ml-2">
      {label}
    </Label>
  </div>
);

const RangeFilter = ({
  title,
  value,
  onValueChange,
  min,
  max,
  step,
  prefix = '',
  suffix = '',
}: RangeFilterProps) => (
  <div>
    <h3 className="font-medium mb-2">{title}</h3>
    <DualRangeSlider
      value={value}
      onValueChange={onValueChange}
      min={min}
      max={max}
      step={step}
      className="mt-5"
    />
    <div className="flex justify-between text-sm text-gray-600">
      <span>
        {prefix}
        {value[0]}
        {suffix}
      </span>
      <span>
        {prefix}
        {value[1]}
        {suffix}
      </span>
    </div>
  </div>
);

type TeamKey = keyof FiltersState['teams'];
type FilterKey = keyof Omit<FiltersState, 'teams'>;

const RANGE_FILTERS = [
  {
    key: 'priceRange' as const,
    title: 'By price range',
    min: 0,
    max: 100,
    step: 1,
    prefix: '$',
    suffix: 'k',
  },
  {
    key: 'breakeven' as const,
    title: 'By breakeven',
    min: 0,
    max: 100,
    step: 1,
  },
  {
    key: 'projectedScore' as const,
    title: 'By projected score',
    min: 0,
    max: 100,
    step: 1,
  },
  {
    key: 'projectedValueChange' as const,
    title: 'By projected value change',
    min: 6300,
    max: 9300,
    step: 100,
    prefix: '$',
  },
  {
    key: 'selectionPercentage' as const,
    title: 'By selection %',
    min: 0,
    max: 100,
    step: 1,
  },
];

const TEAM_FILTERS = [
  {
    id: 'adelaide-strikers',
    key: 'adelaideStrikers' as const,
    label: 'Adelaide Strikers',
  },
  {
    id: 'sydney-sixers',
    key: 'sydneySixers' as const,
    label: 'Sydney Sixers',
  },
];

const MobileFootballPlayerFilter = ({
  open,
  setOpen,
}: MobileFootballPlayerFilterProps) => {
  const [filters, setFilters] = useState<FiltersState>({
    matchesPlayed: false,
    priceRange: [0, 100],
    dualPosition: false,
    teams: {
      adelaideStrikers: false,
      sydneySixers: false,
    },
    breakeven: [0, 100],
    projectedScore: [0, 100],
    projectedValueChange: [6300, 9300],
    selectionPercentage: [0, 100],
  });

  const handleCheckboxChange = (
    key: FilterKey | TeamKey,
    isTeam: boolean = false,
  ) => {
    setFilters((prev) => {
      if (isTeam) {
        return {
          ...prev,
          teams: {
            ...prev.teams,
            [key]: !prev.teams[key as TeamKey],
          },
        };
      }
      return {
        ...prev,
        [key]: !prev[key as FilterKey],
      };
    });
  };

  const handleDualRangeChange =
    (key: keyof FiltersState) => (value: [number, number]) => {
      setFilters((prev) => ({ ...prev, [key]: value }));
    };

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="overflow-hidden bg-gradient-to-b from-[#E5EAEF] to-[#93A0AD]">
        <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50">
          FILTERS
        </div>
        <div
          className="w-full p-6 rounded-lg shadow-lg mt-10"
          style={{ height: 'calc(100vh - 200px)', overflowY: 'auto' }}
        >
          <div className="space-y-6">
            <div className="flex flex-col space-y-4">
              <div className="w-full">
                <h3 className="font-medium mb-2">Matches played</h3>
                <CheckboxFilter
                  id="played-every-match"
                  label="Played every match"
                  checked={filters.matchesPlayed}
                  onChange={() => handleCheckboxChange('matchesPlayed')}
                />
              </div>

              <div className="w-full">
                <h3 className="font-medium mb-2">By team</h3>
                <div className="space-y-2">
                  {TEAM_FILTERS.map((team) => (
                    <CheckboxFilter
                      key={team.id}
                      id={team.id}
                      label={team.label}
                      checked={filters.teams[team.key]}
                      onChange={() => handleCheckboxChange(team.key, true)}
                    />
                  ))}
                </div>
              </div>
            </div>

            {RANGE_FILTERS.map((filter) => (
              <RangeFilter
                key={filter.key}
                title={filter.title}
                value={filters[filter.key]}
                onValueChange={handleDualRangeChange(filter.key)}
                min={filter.min}
                max={filter.max}
                step={filter.step}
                prefix={filter.prefix}
                suffix={filter.suffix}
              />
            ))}
          </div>

          <div className="relative">
            <div className="w-full max-w-screen-lg px-5 mx-auto flex flex-col space-y-2 fixed bottom-0 left-1/2 transform -translate-x-1/2 mb-4">
              <Button>Apply</Button>
              <Button variant="link" className="underline text-white">
                Clear All
              </Button>
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default MobileFootballPlayerFilter;
