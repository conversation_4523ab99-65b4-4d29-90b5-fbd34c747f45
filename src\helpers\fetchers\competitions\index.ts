'use client';

import moment from 'moment';

import axiosInstance from '@/helpers/axios/axiosInstance';
import { Config } from '@/helpers/context/config';

import type {
  BiggestValueApiResponse,
  CompetitionResponse,
  CreateAFLTeamPayload,
  CreateRugbyTeamPayload,
  CreateSoccerTeamPayload,
  CreateTeamPayload,
  EventResponse,
  FantasyTeamResponse,
  GetFavouriteTeamResponse,
  GetPlayerListAPIResponse,
  GetPlayersResponse,
  LeagueListResponse,
  TeamListResponse,
} from '../../../../types/competitions';
import { Token } from '../../../../db/db';
//GET All compitions

export const getAllCompetitions = async (
  contestType?: string | undefined | null,
  maxEntryStart?: string | null | undefined,
  maxEntryEnd?: string | null | undefined,
  prizeStart?: string | null | undefined,
  prizeEnd?: string | null | undefined,
  status?: string | null | undefined,
  compType?: string | null | undefined,
  sport_id?: string | null | undefined,
  teamId?: string | null | undefined,
  tournamentId?: string | null | undefined,
  startDate?: string | null | undefined,
  page: number = 1,
  limit: number = 10,
): Promise<EventResponse> => {
  let statusData = '';

  if (status === '1') {
    statusData = 'upcoming';
  } else if (status === '2') {
    statusData = 'inprogress';
  } else if (status === '3') {
    statusData = 'finished';
  }
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const params = new URLSearchParams({
    perPage: limit.toString(),
    page: page.toString(),
    startTime: moment(new Date()).format('YYYY-MM-DD'),
    prizeStart: prizeStart?.toString() || '',
    prizeEnd: prizeEnd?.toString() || '',
    maxEntryStart: maxEntryStart?.toString() || '',
    maxEntryEnd: maxEntryEnd?.toString() || '',
    eventType: contestType?.toString() || '',
    status: statusData?.toString() || '',
    compType: compType === 'my' ? 'my' : 'all',
  });

  if (teamId) {
    params.append('teamId', teamId.toString());
  }

  if (tournamentId) {
    params.append('tournamentId', tournamentId);
  }

  if (startDate) {
    params.append('startDate', startDate.toString());
  }

  // Only append sportId if it's defined
  if (sport_id) {
    params.append('SportId', sport_id.toString());
  }

  const res = await axiosInstance.get<EventResponse>(
    `${Config.fantasyURL}/events/competition-list?${params.toString()}&timezone=${timezone}`,
  );
  const sortedData =
    res.data.result?.sort((a, b) => {
      const today = moment(); // Current date and time
      const startTimeA = moment(a?.startTime);
      const startTimeB = moment(b?.startTime);

      // Check if the events are in the future
      const isFutureA = startTimeA.isAfter(today);
      const isFutureB = startTimeB.isAfter(today);

      // If both events are in the future, sort by closest to current date
      if (isFutureA && isFutureB) {
        return startTimeA.diff(today) - startTimeB.diff(today);
      }

      // If only one event is in the future, prioritize that event
      if (isFutureA) return -1;
      if (isFutureB) return 1;

      // Both events are in the past, sort by most recently completed
      return startTimeB.diff(today) - startTimeA.diff(today);
    }) || [];

  return { ...res.data, result: sortedData };
};

// GET Compititon by ID
export const getCompetition = async (
  eventId: string | null,
  sportId: string | null,
  competitionId: string | null,
): Promise<CompetitionResponse> => {
  const url = `/events/competition-details/${competitionId}?eventId=${eventId}&sportId=${sportId}`;
  const res = await axiosInstance.get<CompetitionResponse>(
    Config.fantasyURL + url,
  );
  return res.data;
};

// API function type
export const getPlayers = async (
  tournamentId: string | null,
  sportId: string | null,
  eventId: string | null,
  competitionId: string | null,
  dreamTeamId?: number | null,
  seasonId?: string | null,
): Promise<GetPlayersResponse> => {
  const url =
    Config.fantasyURL +
    `/events/player-list/${tournamentId}?SportId=${sportId}&eventId=${eventId}` +
    (dreamTeamId ? `&dreamTeamId=${dreamTeamId}` : '') +
    (seasonId ? `&seasonId=${seasonId}` : '');

  const res = await axiosInstance.get<GetPlayersResponse>(url);
  return res.data;
};

export const getPlayerList = async <T>(
  tournamentId: string | null,
  sportId: string | null,
  eventId: string | null,
  dreamTeamId?: number | null,
  seasonId?: string | null,
): Promise<GetPlayerListAPIResponse<T>> => {
  const url =
    Config.fantasyURL +
    `/events/player-list/${tournamentId}?SportId=${sportId}&eventId=${eventId}` +
    (dreamTeamId ? `&dreamTeamId=${dreamTeamId}` : '') +
    (seasonId ? `&seasonId=${seasonId}` : '');

  const res = await axiosInstance.get<GetPlayerListAPIResponse<T>>(url);
  return res.data;
};
// Create Team
export const createTeam = async (
  payload: CreateTeamPayload,
  dreamTeamId?: string | null,
) => {
  try {
    const url = dreamTeamId
      ? `${Config.fantasyURL}/events/update-team/${dreamTeamId}`
      : `${Config.fantasyURL}/events/create-team`;

    const options: RequestInit = {
      method: dreamTeamId ? 'PUT' : 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Token}`,
      },
      body: JSON.stringify(
        dreamTeamId ? { playerData: payload.playerData } : payload,
      ),
    };

    const response = await fetch(url, options);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(`${data?.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error creating/updating team:', error);
    throw error;
  }
};

export const createRugbyTeam = async (
  payload: CreateRugbyTeamPayload,
  dreamTeamId?: string | null,
) => {
  try {
    const url = dreamTeamId
      ? `${Config.fantasyURL}/events/update-team/${dreamTeamId}`
      : `${Config.fantasyURL}/events/create-team`;

    const options: RequestInit = {
      method: dreamTeamId ? 'PUT' : 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Token}`,
      },
      body: JSON.stringify(
        dreamTeamId ? { playerData: payload.playerData } : payload,
      ),
    };

    const response = await fetch(url, options);
    const data = await response.json();
    if (!response.ok) {
      throw new Error(`${data?.message}`);
    }

    return data;
  } catch (error) {
    throw error;
  }
};

export const createAFLTeam = async (
  payload: CreateAFLTeamPayload,
  dreamTeamId?: string | null,
) => {
  try {
    const url = dreamTeamId
      ? `${Config.fantasyURL}/events/update-team/${dreamTeamId}`
      : `${Config.fantasyURL}/events/create-team`;

    const options: RequestInit = {
      method: dreamTeamId ? 'PUT' : 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Token}`,
      },
      body: JSON.stringify(
        dreamTeamId ? { playerData: payload.playerData } : payload,
      ),
    };

    const response = await fetch(url, options);
    const data = await response.json();
    if (!response.ok) {
      throw new Error(`${data?.message}`);
    }

    return data;
  } catch (error) {
    throw error;
  }
};

export const createSoccerTeam = async (
  payload: CreateSoccerTeamPayload,
  dreamTeamId?: string | null,
) => {
  try {
    const url = dreamTeamId
      ? `${Config.fantasyURL}/events/update-team/${dreamTeamId}`
      : `${Config.fantasyURL}/events/create-team`;

    const options: RequestInit = {
      method: dreamTeamId ? 'PUT' : 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Token}`,
      },
      body: JSON.stringify(
        dreamTeamId ? { playerData: payload.playerData } : payload,
      ),
    };

    const response = await fetch(url, options);
    const data = await response.json();
    if (!response.ok) {
      throw new Error(`${data?.message}`);
    }

    return data;
  } catch (error) {
    throw error;
  }
};

// Create favourite team

export const getFavouriteTeam = async (
  eventId: string | null,
  sportId: string | null,
  competitionId: string | null,
): Promise<GetFavouriteTeamResponse> => {
  const res = await axiosInstance.get<GetFavouriteTeamResponse>(
    Config.fantasyURL +
      `/events/favorite/team?eventId=${eventId}&SportId=${sportId}&competitionId=${competitionId}`,
  );
  return res.data;
};

// Get Dream
export const getDreamTeam = async (
  dreamTeamId: number,
  tournamentId: string | null,
  seasonId: string | null,
): Promise<FantasyTeamResponse> => {
  const res = await axiosInstance.get<FantasyTeamResponse>(
    Config.fantasyURL +
      `/events/dream-team/${dreamTeamId}?tournamentId=${tournamentId}` +
      (seasonId ? `&seasonId=${seasonId}` : ''),
  );

  return res.data;
};

// Get Biggest value
export const getBiggestValue = async (
  dreamTeamId: number,
): Promise<BiggestValueApiResponse> => {
  const res = await axiosInstance.get<BiggestValueApiResponse>(
    Config.fantasyURL + `/events/biggest-value-rise/${dreamTeamId}`,
  );
  return res.data;
};
