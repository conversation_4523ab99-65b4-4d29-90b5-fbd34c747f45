'use client';

import React from 'react';

const DropDownArrowIcon = ({ position = 'down' }) => {
  const rotation = position === 'up' ? 'rotate(180deg)' : 'rotate(0deg)';

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="11.176"
      height="6.586"
      viewBox="0 0 11.176 6.586"
      style={{ transform: rotation, transition: 'transform 0.2s ease' }}
    >
      <path
        id="Path_20868"
        data-name="Path 20868"
        d="M6.293.707a1,1,0,0,1,1.414,0l4.586,4.586A1,1,0,0,1,11.586,7H2.414a1,1,0,0,1-.707-1.707Z"
        transform="translate(-1.412 -0.414)"
        fill="#fff"
      />
    </svg>
  );
};

export default DropDownArrowIcon;
