import axiosInstance from '@/helpers/axios/axiosInstance';
import { Config } from '@/helpers/context/config';
import { AFLSmartPlayStatsResponse } from '@/lib/types/afl';
import aflSmartPlayStats from '../../../../data/aflSmartPlaySats.json';
import Soccer from '@/assets/images/sportIcon/graySportIcon/Soccer.svg';

interface AFLPlayerStatsPlayer {
  id: number;
  name: string;
  image: string | null;
}

interface AFLPlayerStatsTeam {
  id: number;
  name: string;
  flag: string | null;
}

export interface AFLPlayerStats {
  rowNumber: number;
  player: AFLPlayerStatsPlayer;
  team: AFLPlayerStatsTeam;
  position: string;
  role: string | null;
  totalMatches: number;
  totalScore: number;
  scoreComponents: ScoreComponents;
  avgScore: number;
  lastScore: number;
  lastThreeMatch: number;
  marks: number;
  contested_marks: number;
  tackles: number;
  kicks: number;
  frees_against: number;
  fifty_metre_penalties: number;
  hitouts: number;
  hitouts_to_advantage: number;
  disposals: number;
  clearances: number;
  freesFor: number;
  errors: number;
  goals: number;
  behinds: number;
  goal_assists: number;
  inside_fifty: number;
  handballs: number;
}

type ScoreComponent = {
  value: number;
  weight: number;
  points: number;
};

type ScoreComponents = {
  marks: ScoreComponent;
  contested_marks: ScoreComponent;
  tackles: ScoreComponent;
  freesFor: ScoreComponent;
  freesAgainst: ScoreComponent;
  fifty_metre_penalties: ScoreComponent;
  hitouts: ScoreComponent;
  hitouts_to_advantage: ScoreComponent;
  errors: ScoreComponent;
  goals: ScoreComponent;
  behinds: ScoreComponent;
  goal_assists: ScoreComponent;
  inside_fifty: ScoreComponent;
  kicks: ScoreComponent;
  handballs: ScoreComponent;
  effective_disposals: ScoreComponent;
  clearances: ScoreComponent;
  center_clearances: ScoreComponent;
};

export interface AFLPlayerStatsResponse {
  status: boolean;
  result: {
    count: number;
    rows: AFLPlayerStats[];
  };
}

export interface AFLTeamStatsType {
  goals: [number, number];
  behinds: [number, number];
  total_score: [number, number];
  disposals: [number, number];
  contested_possessions: [number, number];
  uncontested_possessions: [number, number];
  total_possessions: [number, number];
  kicks: [number, number];
  handballs: [number, number];
  marks: [number, number];
  contested_marks: [number, number];
  uncontested_marks: [number, number];
  inside_fifty: [number, number];
  inside_fifty_marks: [number, number];
  clearances: [number, number];
  hitouts: [number, number];
  hitouts_to_advantage: [number, number];
  hitout_efficiency: [number, number];
  tackles: [number, number];
  freesFor: [number, number];
  freesAgainst: [number, number];
  errors: [number, number];
  fifty_metre_penalties: [number, number];
  effective_kicks: [number, number];
  ineffective_kicks: [number, number];
  kick_efficiency: [number, number];
  backward_kicks: [number, number];
  goal_assists: [number, number];
  score_involvements: [number, number];
}

export type AFLStatsResponse = {
  status: boolean;
  result: {
    success: boolean;
    data: {
      event: AFLEvent;
      homeTeam: AFLTeam;
      stats: AFLTeamStatsType;
    };
  };
};

type AFLEvent = {
  id: number;
  name: string;
  date: string; // ISO string format
};

type AFLTeam = {
  id: number;
  name: string;
  flag: string | null;
  stats: AFLTeamStats;
  players: AFLPlayer[];
};

type AFLTeamStats = {
  backward_kicks: number;
  behinds: number;
  clearances: number;
  contested_marks: number;
  contested_possessions: number;
  disposals: number;
  effective_kicks: number;
  errors: number;
  fifty_metre_penalties: number;
  freesAgainst: number;
  freesFor: number;
  goal_assists: number;
  goals: number;
  handballs: number;
  hitouts: number;
  hitouts_to_advantage: number;
  ineffective_kicks: number;
  inside_fifty: number;
  inside_fifty_marks: number;
  kick_errors: number;
  kicks: number;
  marks: number;
  points: number;
  score_involvements: number;
  tackles: number;
  uncontested_marks: number;
  uncontested_possessions: number;
  total_score: number;
  total_possessions: number;
  kick_efficiency: number;
  hitout_efficiency: number;
};

type AFLPlayer = {
  id: number;
  teamId: number;
  stats: AFLTeamStats;
};

type SoccerTeam = {
  id: number;
  name: string;
  flag: string;
};

type SoccerTeamStats = {
  teamId: number;
  played: number;
  win: number;
  loss: number;
  draw: number;
  goalsFor: number;
  goalsAgainst: number;
  goalDifference: number;
  points: number;
  yellowCards: number;
  redCards: number;
  name: string;
  flag: string;
  nameCode: string | null;
};

export type SoccerPlayerStatsResponse = {
  status: boolean;
  result: {
    count: number;
    rows: SoccerPlayerStat[];
  };
};

type ScoreBreakdown = {
  goalScore: number;
  assistScore: number;
  shotOnTargetScore: number;
  cleanSheetScore: number;
  tackleScore: number;
  interceptionScore: number;
  blockScore: number;
  saveScore: number;
  penSaveScore: number;
  passAccuracyScore: number;
  keyPassScore: number;
  dribbleScore: number;
  minutesScore: number;
  earlySubScore: number;
  yellowCardScore: number;
  redCardScore: number;
  penConcededScore: number;
  penMissScore: number;
  hatTrickScore: number;
  tacklesBonusScore: number;
  savesBonusScore: number;
};

export type SoccerPlayerStat = {
  player: {
    id: number;
    name: string;
    image: string | null;
  };
  team: {
    id: number;
    name: string;
    flag: string;
  };
  position: string;
  role: string;
  isCaptain: boolean;
  totalMatches: number;
  totalScore: number;
  avgScore: number;
  lastScore: number;
  lastThreeMatch: number;
  goals: number;
  assists: number;
  passes: number;
  passesAcc: number;
  shots: number;
  shotsOnGoal: number;
  tackles: number;
  interceptions: number;
  clearances: number;
  blocks: number;
  foulsCommited: number;
  foulsDrawn: number;
  minutesPlayed: number;
  yellowCards: number;
  redCards: number;
  saves: number;
  savesInsideBox: number;
  keyPasses: number;
  dribbleSucc: number;
  dribbleAttempts: number;
  penScored: number;
  penMissed: number;
  goalsConceded: number;
  cleanSheet: number;
  hatTrick: number;
  tacklesBonus: number;
  savesBonus: number;
  hit_woodwork: number;
  offsides: number;
  total_crosses: number;
  appearances: number;
  pen_save: number;
  pen_miss: number;
  pen_score: number;
  pen_won: number;
  pen_committed: number;
  baseScore: number;
  calculatedTotalScore: number;
  playerCurrentSalary: number;
  playerLastSalary: number;
  salaryBaseValue: number;
  year: string;
  seasonName: string;
};

export type SoccerTeamStatsResponse = {
  status: boolean;
  result: {
    count: number;
    rows: SoccerTeamStats[];
  };
};

export type PlayerStats = {
  player: {
    id: number;
    name: string;
    image: string | null;
  };
  team: {
    id: number;
    name: string;
    flag: string;
  };
  position: string;
  role: string;
  isCaptain: boolean;
  totalMatches: number;
  totalScore: number;
  avgScore: number;
  lastScore: number;
  lastThreeMatch: number;
  goals: number;
  assists: number;
  passes: number;
  passesAcc: number;
  shots: number;
  shotsOnGoal: number;
  tackles: number;
  interceptions: number;
  clearances: number;
  blocks: number;
  foulsCommited: number;
  foulsDrawn: number;
  minutesPlayed: number;
  yellowCards: number;
  redCards: number;
  saves: number;
  savesInsideBox: number;
  keyPasses: number;
  dribbleSucc: number;
  dribbleAttempts: number;
  penScored: number;
  penMissed: number;
  goalsConceded: number;
  cleanSheet: number;
  hatTrick: number;
  tacklesBonus: number;
  savesBonus: number;
  hit_woodwork: number;
  offsides: number;
  total_crosses: number;
  appearances: number;
  pen_save: number;
  pen_miss: number;
  pen_score: number;
  pen_won: number;
  pen_committed: number;
  baseScore: number;
  calculatedTotalScore: number;
  scoreBreakdown: {
    goalScore: number;
    assistScore: number;
    shotOnTargetScore: number;
    cleanSheetScore: number;
    tackleScore: number;
    interceptionScore: number;
    blockScore: number;
    saveScore: number;
    penSaveScore: number;
    passAccuracyScore: number;
    keyPassScore: number;
    dribbleScore: number;
    minutesScore: number;
    earlySubScore: number;
    yellowCardScore: number;
    redCardScore: number;
    penConcededScore: number;
    penMissScore: number;
    hatTrickScore: number;
    tacklesBonusScore: number;
    savesBonusScore: number;
  };
  playerCurrentSalary: number;
  playerLastSalary: number;
  salaryBaseValue: number;
  year: string;
  seasonName: string;
};

export type SmartPlayStatsResponse = {
  status: boolean;
  result: {
    count: number;
    rows: PlayerStats[];
  };
};

export const getAFLPlayerStats = async ({
  eventId,
}: {
  eventId: number;
}): Promise<AFLPlayerStatsResponse> => {
  const response = await axiosInstance.get<AFLPlayerStatsResponse>(
    `${Config.baseURL}public/ar/player-states?SportId=9&eventId=${eventId}`,
  );
  return response.data;
};

export const getAFLSmartPlayStats = async ({
  eventId,
}: {
  eventId: number;
}): Promise<AFLSmartPlayStatsResponse> => {
  const response = await axiosInstance.get<AFLSmartPlayStatsResponse>(
    `${Config.baseURL}public/ar/player-fantasy-score-breakdown?eventId=${eventId}`,
  );
  return response.data;
};

export const getAFLTeamStats = async ({
  eventId,
}: {
  eventId: number;
}): Promise<AFLStatsResponse> => {
  const response = await axiosInstance.get<AFLStatsResponse>(
    `${Config.baseURL}public/ar/player-team-stats-breakdown?eventId=${eventId}`,
  );
  return response.data;
};

export const getSoccerTeamStats = async ({
  eventId,
}: {
  eventId: number;
}): Promise<SoccerTeamStatsResponse> => {
  const response = await axiosInstance.get<SoccerTeamStatsResponse>(
    `${Config.baseURL}public/soccer/team-states?SportId=8&eventId=${eventId}`,
  );
  return response.data;
};

export const getSoccerPlayerStats = async ({
  eventId,
}: {
  eventId: number;
}): Promise<SoccerPlayerStatsResponse> => {
  const response = await axiosInstance.get<SoccerPlayerStatsResponse>(
    `${Config.baseURL}public/soccer/player-states?SportId=8&eventId=${eventId}`,
  );
  return response.data;
};

export const getSoccerSmartPlayStats = async ({
  eventId,
}: {
  eventId: number;
}): Promise<SmartPlayStatsResponse> => {
  const response = await axiosInstance.get<SmartPlayStatsResponse>(
    `${Config.baseURL}public/soccer/player-score-breakdown?SportId=8&eventId=${eventId}`,
  );
  return response.data;
};
