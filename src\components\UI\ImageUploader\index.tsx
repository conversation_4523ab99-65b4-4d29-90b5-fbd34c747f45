'use client';

import { Plus, Upload } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import React, { useCallback } from 'react';
import type { FileWithPath } from 'react-dropzone';
import { useDropzone } from 'react-dropzone';

import { Avatar, AvatarImage } from '@/components/UI/avatar';
import { Button } from '@/components/UI/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/UI/dialog';
import { Config } from '@/helpers/context/config';
import { useUserProfileContext } from '@/helpers/context/userContext';

import EditIcon from '../Icons/EditIcon';
import ImageCropper from '../ImageCropper';

export type FileWithPreview = FileWithPath & {
  preview: string;
};

const accept = {
  'image/*': [],
};

interface ImageUploaderProps {
  isDialogOpen: boolean;
  setDialogOpen: Dispatch<SetStateAction<boolean>>;
  selectedFile: FileWithPreview | null;
  setSelectedFile: (file: FileWithPreview | null) => void;
  handleUploadSuccess?: (response: any) => void;
  handleUploadError?: (error: Error) => void;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  isDialogOpen,
  setDialogOpen,
  selectedFile,
  setSelectedFile,
  handleUploadError,
  handleUploadSuccess,
}) => {
  const avatarSrc = '/fantasy/images/userIcon.png'; // Default avatar source

  // Handle the cropped image
  const handleCroppedImage = (croppedImageUrl: string) => {
    if (selectedFile) {
      const updatedFileWithPreview = {
        ...selectedFile,
        preview: croppedImageUrl, // Use cropped image URL as preview
      };
      setSelectedFile(updatedFileWithPreview); // Set the cropped image as the avatar
    }
    setDialogOpen(false); // Close dialog after cropping
  };

  // Handle file drop
  const onDrop = useCallback(
    (acceptedFiles: FileWithPath[]) => {
      const file = acceptedFiles[0];
      if (!file) {
        alert('Selected image is too large!');
        return;
      }

      const fileWithPreview = Object.assign(file, {
        preview: URL.createObjectURL(file),
      });

      setSelectedFile(fileWithPreview); // Set selected file with preview
      setDialogOpen(true); // Open the cropper dialog
    },
    [setSelectedFile, setDialogOpen],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept,
  });

  const { user } = useUserProfileContext();

  let userImage: string;

  if (user?.Media) {
    userImage = `${Config.mediaURL}${user?.Media?.filePath}`;
  } else {
    userImage = avatarSrc;
  }

  return (
    <div className="relative">
      {/* Avatar Image (click to open the file dialog) */}
      <button
        className="relative cursor-pointer"
        onClick={() => setDialogOpen(true)}
      >
        <Avatar className="size-20 cursor-pointer  relative">
          <AvatarImage
            src={userImage}
            alt="Avatar"
            style={{
              objectFit: 'cover',
            }}
          />
        </Avatar>
        <div className="absolute w-[28px] h-[28px] bg-[#4455C7] text-white rounded-full bottom-0 right-0 flex flex-col justify-center items-center">
          {userImage ? <EditIcon /> : <Plus size={20} />}
        </div>
      </button>

      {/* Image Upload Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Upload Image</DialogTitle>
          </DialogHeader>

          <div
            {...getRootProps()}
            className={`p-10 border-2 border-dashed rounded-lg text-center cursor-pointer ${
              isDragActive ? 'border-primary' : 'border-gray-300'
            }`}
          >
            <input {...getInputProps()} />
            {isDragActive ? (
              <p>Drop the image here ...</p>
            ) : (
              <div>
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <p>
                  Drag &apos;n&apos; drop an image here, or click to select one
                </p>
              </div>
            )}
          </div>

          {/* Cancel button */}
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
        </DialogContent>
      </Dialog>

      {/* Image Cropper dialog (only show if a file is selected) */}
      {selectedFile && (
        <ImageCropper
          dialogOpen={isDialogOpen}
          setDialogOpen={setDialogOpen}
          selectedFile={selectedFile}
          // @ts-expect-error
          setSelectedFile={setSelectedFile}
          onCroppedImage={handleCroppedImage}
          uploadEndpoint={`${Config.baseURL}public/media`} // Replace with your upload endpoint
          onUploadSuccess={handleUploadSuccess}
          onUploadError={handleUploadError}
        />
      )}
    </div>
  );
};

export default ImageUploader;
