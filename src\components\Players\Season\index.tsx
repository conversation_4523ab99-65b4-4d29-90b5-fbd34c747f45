'use client';
import moment from 'moment';
import React, { useState } from 'react';
import Select, { components } from 'react-select';

import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import SortingDown from '@/assets/images/icons/sortingDown.svg';
import SortingUp from '@/assets/images/icons/sortingUp.svg';

import {
  fantasyStatsStateData,
  seasonSummaryStateData,
  upcomingMatch,
} from '../../../../db/db';
import FantasyStatsTabPage from './FantasyStatsTab';
import SummaryTabPage from './SummaryTab';
import { generateUniqueId } from '@/lib/utils';

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const seasonOption = [
  { value: '2024', label: 'Season Stats - 2024' },
  { value: '2023', label: 'Season Stats - 2023' },
  { value: '2022', label: 'Season Stats - 2022' },
  { value: '2021', label: 'Season Stats - 2021' },
];

const SeasonDetails = () => {
  const [activeTab, setActiveTab] = useState<string>('Upcoming matches');
  const [seasonStatsTab, setSeasonStatsTab] = useState<string>('Summary');
  const [selectedSeason, setSelectedSeason] = useState<string>('2024');

  const tabs = ['Upcoming matches'];

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  const seasonTabs = ['Summary', 'Fantasy Stats'];

  const handleSeasonTabClick = (tab: string) => {
    setSeasonStatsTab(tab);
  };

  const statsData = [
    { abbreviation: 'RUN', description: 'Runs Scored' },
    { abbreviation: 'MO', description: 'Maiden Overs Bowled' },
    { abbreviation: 'RO', description: 'Run outs' },
    { abbreviation: 'SR', description: 'Strike Rate Bonus' },
    { abbreviation: 'DB', description: 'Dot Balls' },
    { abbreviation: 'ST', description: 'Stumping' },
    { abbreviation: 'RB', description: 'Runs Bonus' },
    { abbreviation: 'ER', description: 'Economy Rate Bonus' },
    { abbreviation: 'DH', description: 'Direct Hit Run Out' },
    { abbreviation: 'WIC', description: 'Wickets' },
    { abbreviation: 'CA', description: 'Catches' },
    { abbreviation: 'AR', description: 'All Rounder Bonus' },
  ];

  return (
    <div className="">
      <h6 className="text-[16px] leading-[19px] font-inter font-semibold text-black-100 mt-[18px]">
        Upcoming matches
      </h6>
      <div className="mt-[9px] bg-white rounded-lg">
        <div className="flex items-center gap-[3px] rounded-lg w-fit p-[9px]">
          {tabs?.map((tab, index) => (
            <button
              key={generateUniqueId()}
              className={`px-[9px] py-[6px] cursor-pointer ${
                activeTab === tab ? 'bg-primary-200 rounded-[6px]' : ''
              }`}
              onClick={() => handleTabClick(tab)}
            >
              <p
                className={`text-[12px] leading-[15px] font-inter font-medium ${
                  activeTab === tab ? 'text-white' : 'text-primary-200'
                }`}
              >
                {tab}
              </p>
            </button>
          ))}
        </div>
        <div>
          <div className="upcoming-matches-table overflow-x-auto">
            <table className="w-full min-w-max table-auto ">
              <thead>
                <tr className="bg-primary-200 ">
                  <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] text-white text-left max-799:sticky max-799:left-0 max-799:z-[9] max-799:bg-primary-200">
                    <div className="flex items-center gap-1  ">
                      Opponent{' '}
                      <div className="flex items-center flex-col">
                        <span>
                          <SortingUp />
                        </span>
                        <span className="mt-[1.3px]">
                          <SortingDown />
                        </span>
                      </div>
                    </div>
                  </th>
                  <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                    <div className="flex items-center gap-1  ">
                      Venue{' '}
                      <div className="flex items-center flex-col">
                        <span>
                          <SortingUp />
                        </span>
                        <span className="mt-[1.3px]">
                          <SortingDown />
                        </span>
                      </div>
                    </div>
                  </th>
                  <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                    <div className="flex items-center gap-1  ">
                      Round{' '}
                      <div className="flex items-center flex-col">
                        <span>
                          <SortingUp />
                        </span>
                        <span className="mt-[1.3px]">
                          <SortingDown />
                        </span>
                      </div>
                    </div>
                  </th>
                  <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                    <div className="flex items-center gap-1  ">
                      Date/Time{' '}
                      <div className="flex items-center flex-col">
                        <span>
                          <SortingUp />
                        </span>
                        <span className="mt-[1.3px]">
                          <SortingDown />
                        </span>
                      </div>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {upcomingMatch.map((match, index) => {
                  return (
                    <tr
                      key={generateUniqueId()}
                      className={`${index === upcomingMatch?.length - 1 ? '' : 'border-b-[1px] border-black-300'}`}
                    >
                      <td className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-medium text-black-100 text-left max-799:sticky max-799:left-0 max-799:z-[9] max-799:bg-white">
                        <div className="flex items-center gap-2">
                          <div className="bg-primary-200 w-[26px] h-[26px] rounded-full"></div>
                          <div>
                            <p>{match?.opponent}</p>
                          </div>
                        </div>
                      </td>
                      <td className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-left">
                        {match?.venue}
                      </td>
                      <td className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-left">
                        {match?.rounds}
                      </td>
                      <td className="px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-left">
                        {moment(match?.date).format('DD/MM/YYYY hh:mm A')}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div className="min-w-[230px] max-w-[230px] mt-[15px]">
        <Select
          className="React season-select"
          value={seasonOption?.find((item: any) => {
            return item?.value === selectedSeason;
          })}
          onChange={(e: any) => setSelectedSeason(e?.value)}
          options={seasonOption}
          classNamePrefix="select"
          placeholder="season stats"
          isSearchable={false}
          components={{ DropdownIndicator }}
        />
      </div>
      <div className="mt-[9px] bg-white rounded-lg">
        <div className="flex items-center gap-[3px] rounded-lg w-fit p-[9px]">
          {seasonTabs?.map((tab, index) => (
            <button
              key={generateUniqueId()}
              className={`px-[9px] py-[6px] cursor-pointer ${
                seasonStatsTab === tab ? 'bg-primary-200 rounded-[6px]' : ''
              }`}
              onClick={() => handleSeasonTabClick(tab)}
            >
              <p
                className={`text-[12px] leading-[15px] font-inter font-medium ${
                  seasonStatsTab === tab ? 'text-white' : 'text-primary-200'
                }`}
              >
                {tab}
              </p>
            </button>
          ))}
        </div>
        {seasonStatsTab === 'Summary' ? (
          <SummaryTabPage seasonSummaryStateData={seasonSummaryStateData} />
        ) : (
          <FantasyStatsTabPage fantasyStatsStateData={fantasyStatsStateData} />
        )}
      </div>
      {seasonStatsTab !== 'Summary' && (
        <div className="mt-[18px]">
          <div className="grid grid-cols-3 max-799:grid-cols-2 gap-y-2">
            {statsData?.map((stat, index) => (
              <div key={generateUniqueId()} className="flex items-center">
                <span className="text-[11.42px] leading-[14px] font-semibold font-inter text-black-1000 mr-1">
                  {stat?.abbreviation}
                </span>
                <span className="text-[11.42px] leading-[14px] font-normal font-inter text-black-1000">
                  - {stat?.description}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SeasonDetails;
