'use client';

import { Input } from '@material-tailwind/react';
import RoundDeleteIcon from '@/components/UI/Icons/RoundDeleteIcon';
import {
  FieldErrors,
  UseFormHandleSubmit,
  UseFormRegister,
} from 'react-hook-form';
import { Button } from '@/components/UI/button';
type withdrawAmountData = {
  enterPoint: number;
};

interface WithdrawFormProps {
  withdrawAmountRegister: UseFormRegister<any>;
  withdrawAmountErrors: FieldErrors<withdrawAmountData>;
  withdrawAmounthandleSubmit: UseFormHandleSubmit<any>;
  withdrawAmountSubmit: (
    withdrawAmountData: withdrawAmountData,
  ) => Promise<void>;
  bankDetails?: {
    accountHolderName: string;
    accountNumber: string;
    bsb: string;
    bankName: string;
    id?: number;
  };
  enterPoint: number | null;
  setIsWithdraw: React.Dispatch<React.SetStateAction<boolean>>;
  deleteBank: (id: number) => void;
  withDrawLoading: boolean;
}

const WithdrawForm: React.FC<WithdrawFormProps> = ({
  withdrawAmountRegister,
  withdrawAmountErrors,
  withdrawAmounthandleSubmit,
  withdrawAmountSubmit,
  bankDetails,
  withDrawLoading,
  enterPoint,
  deleteBank,
  setIsWithdraw,
}) => {
  const rewardAmount = enterPoint && enterPoint >= 1000 
  ? '$' + (enterPoint * 0.05).toFixed(2) 
  : '$0';
  return (
    <div className="mt-4">
    {/* Withdrawal Amount Input */}
    <div className="flex items-baseline gap-9 max-799:gap-2 max-479:flex-col max-479:row-gap-2  justify-between">
      <div className="mb-[18px] w-1/2 max-479:w-full common-input-wrap">
        <p className="mb-3 text-[16px] max-799:text-[12px] leading-[19px] max-799:leading-[15px] font-inter font-semibold text-black-100">
          Enter coins to withdraw
        </p>
        {/* @ts-expect-error */}
        <Input
          variant="outlined"
          label="Enter points"
          placeholder="Enter points"
          color="indigo"
          {...withdrawAmountRegister('enterPoint')}
          error={!!withdrawAmountErrors.enterPoint?.message}
        />
        {withdrawAmountErrors.enterPoint?.message && (
          <p className="text-red-400 text-sm mt-[2px]">
            {withdrawAmountErrors.enterPoint.message}
          </p>
        )}
      </div>

      <div className="mb-[18px] w-1/2 common-input-wrap max-479:w-full">
        <p className="mb-3 text-[16px] max-799:text-[12px] leading-[19px] max-799:leading-[15px] font-inter font-semibold text-black-100">
          Withdrawal amount
        </p>
        <div className="bg-white rounded-lg text-[16px] leading-[19px] font-inter font-normal text-black-900 pt-2.5 px-3 pb-[9px] border-black-100 border">
          {rewardAmount}
        </div>
      </div>
    </div>

    {/* Bank Account Details */}
    <div>
      <p className="mb-3 text-[16px] font-inter font-semibold text-black-100">
        Bank account details
      </p>
      <div className="p-3 bg-white rounded-lg shadow-sm">
        <p>Account name: {bankDetails?.accountHolderName}</p>
        <p>BSB: {bankDetails?.bsb}</p>
        <p>Account number: {bankDetails?.accountNumber}</p>
        <p>Bank name: {bankDetails?.bankName}</p>
      </div>
    </div>

    <div className="mt-[19px] max-799:mt-0 flex justify-start items-center space-x-2">
      <button
        onClick={() => {
          deleteBank(bankDetails?.id ?? 0);
        }}
      >
        <RoundDeleteIcon />
      </button>
      <Button
        variant="link"
        className="text-secondary-100 underline p-0"
        onClick={() => setIsWithdraw(false)}
      >
        Update bank details
      </Button>
    </div>

    {/* Withdrawal Button */}
    <div className="mt-[90px] max-799:mt-[40px] text-center">
      <Button
        className="w-full"
        onClick={withdrawAmounthandleSubmit(withdrawAmountSubmit)}
        disabled={withDrawLoading}
      >
        Withdraw
      </Button>
      <p className="text-[14px] max-799:text-[11.42px] leading-[16px] max-799:leading-[14px] font-inter font-normal mt-[9px] text-gray-200">
        Please allow 2 to 5 business days for processing your request.
      </p>
    </div>
  </div>
  )
}

export default WithdrawForm;
