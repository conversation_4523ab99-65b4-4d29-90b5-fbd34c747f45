import type {
  UseMutationOptions,
  UseQueryOptions,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { AxiosError, AxiosResponse } from 'axios';

import axiosInstance from './axios/axiosInstance';

export function useApiQuery<T>(
  url: string,
  options?: UseQueryOptions<AxiosResponse<T>, AxiosError>,
) {
  return useQuery<AxiosResponse<T>, AxiosError>({
    queryKey: [url],
    queryFn: () => axiosInstance.get<T>(url),
    ...options,
  });
}

export function useApiMutation<T, U>(
  url: string,
  method: 'post' | 'put' | 'patch' | 'delete' = 'post',
  options?: UseMutationOptions<AxiosResponse<T>, AxiosError, U>,
) {
  return useMutation<AxiosResponse<T>, AxiosError, U>({
    mutationFn: (data) => axiosInstance[method]<T>(url, data),
    ...options,
  });
}
