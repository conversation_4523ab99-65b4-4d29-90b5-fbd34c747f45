'use client';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { useCompetition } from '@/helpers/context/competitionContext';
import PreLoader from '@/components/UI/PreLoader';
import CompetitionDetailsHeader from '@/components/Competition/CompetitionDetailsHeader';
import { Button } from '@/components/UI/button';
import { PlayerCaptainViewIcon } from '@/components/images';
import { RugbyLeaguePlayersByRole } from '../../../../../../types/rugby-league';
import { getAllAFLPlayers, getAllRugbyPlayers } from '@/lib/utils';
import DataTable from '@/components/UI/DataTabel';
import useScreen from '@/hooks/useScreen';
import {
  completedCricketPlayerListColumn,
  liveCricketPlayerListColumn,
} from '@/components/UI/DataTabel/columns/createTeamColumn';
import { upcomingPlayerListColumn } from '@/components/UI/DataTabel/upcomingPlayerlistColumns';
import { ColumnDef } from '@tanstack/react-table';
import { Player } from '../../../../../../types/competitions';
import FootballPlayerSelectionUI from './FootballPlayerSelectionUI';
import FootballFantasyUI from './FootballFantasyUI';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import SharePopup from '@/components/PopUp/Share/SharePopup';
import ShareIcon from '@/components/Icons/Share/ShareIcon';
import DreamTeamLoader from '@/components/Loading/DreamTeamLoader';

const PreviewTeam = () => {
  const {
    eventDetailsResponse,
    dreamTeamResponse,
    activeTeam,
    setActiveTeam,
    isDreamTeamResponseLoading,
    eventTimeStatus: { isLockout, isLive, isCompleted },
    AFLPlayersByRoles,
  } = useCompetition();

  const {
    activeTabPlayer,
    setActiveTabPlayer,
    clearTeam,
    openReserveModal,
    state: {
      remainingBudget,
      playerByRoleLimit: {
        BL: BLLIMIT,
        HBL: HBLLIMIT,
        MID: MIDLIMIT,
        HFL: HFLLIMIT,
        FL: FLLIMIT,
        FOL: FOLLIMIT,
        IC: ICLIMIT,
      },

      playersByRole: { BL, HBL, MID, HFL, FL, FOL, IC },
      reserveState: { reservePlayers },
    },
  } = useFootballContext();

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const event_id = searchParams.get('event_id');
  const sport_id = searchParams.get('sport_id');
  const tournament_id = searchParams.get('tournament_id');
  const seasonId = searchParams.get('seasonId');
  const competition_id = searchParams.get('competition_id');
  const comp_Type = searchParams.get('comp_Type');
  const dreamTeamId = searchParams.get('dreamTeamId');
  const role = searchParams.get('role') as keyof RugbyLeaguePlayersByRole;
  const playerId = searchParams.get('playerId');
  const [activeTab, setActiveTab] = useState<string | number>(1);
  const [showSharePopup, setShowSharePopup] = useState(false);
  const allLiveScore = dreamTeamResponse?.result?.totalLivePoints;
  const playersByRole =
    dreamTeamResponse && getAllAFLPlayers(dreamTeamResponse);
  const [captain] = dreamTeamResponse?.result?.captain! || [];
  const [viceCaptain] = dreamTeamResponse?.result?.viceCaptain! || [];
  const flattenedPlayerByRole = Object.values(playersByRole || {})
    ?.map((player) => {
      if (player?.playerId === captain?.playerId) {
        return { ...player, positionType: 'captain' };
      }

      if (player?.playerId === viceCaptain?.playerId) {
        return { ...player, positionType: 'viceCaptain' };
      }

      return player;
    })
    ?.sort((a, b) => a.name.localeCompare(b.name));
  const router = useRouter();
  const { width } = useScreen();
  const query = {
    event_id,
    sport_id,
    tournament_id,
    seasonId,
    competition_id,
    dreamTeamId: `${activeTeam?.id}`,
    comp_Type,
  };

  let playerColumn: ColumnDef<Player>[] = [];
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  switch (eventStatus) {
    case 'finished':
      playerColumn = completedCricketPlayerListColumn;
      break;
    case 'innings break':
    case 'inprogress':
    case 'drink':
      playerColumn = liveCricketPlayerListColumn;
      break;
    case 'upcoming':
      playerColumn = upcomingPlayerListColumn;
      break;
  }

  useEffect(() => {
    if (dreamTeamResponse && dreamTeamId) {
      // dispatchDreamTeam(dreamTeamResponse, +playerId!, role);
    }
    return () => {
      clearTeam();
    };
  }, [dreamTeamResponse]);

  // useEffect(() => {
  //   const searchParams = new URLSearchParams(window.location.search);
  //   if (activeTeam?.id) {
  //     searchParams.set('dreamTeamId', JSON.stringify(activeTeam?.id));
  //     router.push(`${pathname}?${searchParams.toString()}`);
  //   }
  // }, [activeTeam?.id]);

  useEffect(() => {
    if (activeTeam?.id && comp_Type === 'my') {
      // @ts-expect-error
      router.push(`${pathname}?${new URLSearchParams(query).toString()}`);
    }
  }, [activeTeam]);

  const dreamTeams = eventDetailsResponse?.result?.dreamTeams;

  useEffect(() => {
    if (dreamTeams && !activeTeam?.id && dreamTeamId) {
      setActiveTeam(() => dreamTeams.find((team) => team.id === +dreamTeamId));
    }

    if (dreamTeams && !activeTeam?.id && !dreamTeamId) {
      setActiveTeam(() => dreamTeams[0]);
    }
  }, [dreamTeamId, dreamTeams]);


  const headerStats = {
    selectedPlayer: `Player ${BL?.length + HBL?.length + MID?.length + HFL?.length + FL?.length + FOL?.length + IC?.length}/${BLLIMIT + HBLLIMIT + MIDLIMIT + HFLLIMIT + FLLIMIT + FOLLIMIT + ICLIMIT}`,
    remainingSalary: remainingBudget,
    totalScore: 200,
  };
  return (
    <div className="bg-white px-0">
      <div>
        <div className="px-[33px] max-799:px-0 bg-off-white-200 pb-[40px]">
          {/* Competition Header */}
          <div>
            <CompetitionDetailsHeader
              stats={{
                selectedPlayer: '22',
                remainingSalary: dreamTeamResponse?.result?.totalSpendValue!,

                allLiveScore: allLiveScore,
              }}
              status={eventDetailsResponse?.result?.eventDetails?.status}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              dreamTeamResponse={dreamTeamResponse}
            />
          </div>

          <div className="flex space-x-2 mt-2 flex-col lg:flex-row w-full">
            <div className="w-full lg:w-1/2">
              {activeTab === 1 ? (
                <>
                  {isDreamTeamResponseLoading ? <DreamTeamLoader /> : <FootballFantasyUI />}
                </>
              ) : (
                <div className="shadow-[0px_1px_9px_0px_#0000002e]">
                  <DataTable
                    columns={playerColumn}
                    // @ts-expect-error
                    data={flattenedPlayerByRole || []}
                    stickyColumns={width <= 700 ? [0] : []}
                    isLoading={isDreamTeamResponseLoading}
                  />
                  {/* <div className="w-full text-center bg-secondary-100 text-white px-3 py-1 text-sm text-black">
                    RESERVES
                  </div>
                  <DataTable
                    columns={playerColumn}
                    data={reservePlayers as any || []}
                    stickyColumns={width <= 700 ? [0] : []}
                    hideHeader={true}
                    hideCellVisibility={true}
                    isLoading={isDreamTeamResponseLoading}
                  /> */}
                </div>
              )}
            </div>
            <div className="w-full lg:w-1/2">
              <FootballPlayerSelectionUI
                activeTab={activeTabPlayer}
                setActiveTab={setActiveTabPlayer}
                playerByRole={AFLPlayersByRoles}
                stats={headerStats}
              />
            </div>
          </div>
        </div>
      </div>

      {eventDetailsResponse?.result?.eventDetails?.status === 'upcoming' &&
        !isLockout &&
        !isLive &&
        !isCompleted && !openReserveModal && (
          <div className="bg-gray-50 z-[1200] fixed bottom-0 left-0 right-0 rounded-t-xl shadow-xl">
            <div className="absolute -top-[25px] left-1/2 transform -translate-x-1/2 hidden"></div>
            <div className="flex w-full space-x-2 pb-4 md:space-x-0 md:mt-0 justify-around items-center">
              <div className="space-x-2 mt-5 flex flex-wrap gap-2 justify-center">
                <Button
                  size="sm"
                  disabled={
                    eventDetailsResponse?.result?.eventConfiguration
                      ?.eventType === 'free' &&
                    eventDetailsResponse?.result?.dreamTeams?.length > 0
                  }
                  variant="ghost"
                  className="!bg-[#335F83] text-white w-40 !disabled:cursor-not-allowed"
                  onClick={() => {
                    clearTeam();
                    router.push(
                      `/competitions/football/${eventDetailsResponse?.result?.eventConfiguration?.eventId}?event_id=${eventDetailsResponse?.result?.eventConfiguration?.eventId}&sport_id=12&tournament_id=${eventDetailsResponse?.result?.eventDetails?.ARTournamentId}&seasonId=${eventDetailsResponse?.result?.eventDetails?.ARSeasonId}&competition_id=${eventDetailsResponse?.result?.eventConfiguration?.id}&add_more=true`,
                    );
                  }}
                >
                  {eventDetailsResponse?.result?.eventConfiguration
                    ?.eventType === 'free' &&
                    eventDetailsResponse?.result?.dreamTeams?.length > 0
                    ? 'Team Entered'
                    : 'Add one more team'}
                </Button>
                <SharePopup
                  isOpen={showSharePopup}
                  onClose={() => setShowSharePopup(false)}
                >
                  <button
                    type="button"
                    className="cursor-pointer border-none bg-transparent p-0 flex items-center justify-center"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setShowSharePopup(true);
                    }}
                  >
                    <ShareIcon width={35} height={35} />
                  </button>
                </SharePopup>
              </div>

              <div className="fixed bottom-[71px] left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center ">
                <PlayerCaptainViewIcon className="md:hidden block" />
              </div>
            </div>
          </div>
        )}

      {eventDetailsResponse?.result?.eventDetails?.status !== 'upcoming' && (
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center ">
          <PlayerCaptainViewIcon className="md:hidden block" />
        </div>
      )}
    </div>
  );
};

export default PreviewTeam;
