'use client';

import type { Column, ColumnDef } from '@tanstack/react-table';
import moment from 'moment';

import type { seasonStatsUpcomingMatches } from '../../../../types';
import SortingDownIcon from '../Icons/SortingDownIcon';
import SortingUpIcon from '../Icons/SortingUpIcon';

const renderSortHeader = (
  column: Column<seasonStatsUpcomingMatches, unknown>,
  label: string,
) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);
export const SeasonStatsUpcomingMatches: ColumnDef<seasonStatsUpcomingMatches>[] =
  [
    {
      accessorKey: 'opponent',
      header: ({ column }) => (
        <div className="md:w-[210px] w-[100px]">
          {renderSortHeader(column, 'Opponent')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-center w-full">{row.original.opponent}</div>
      ),
    },
    {
      accessorKey: 'venue',
      header: ({ column }) => (
        <div className="md:w-[210px] w-[100px]">
          {renderSortHeader(column, 'Venue')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-center w-full">{row.original.venue}</div>
      ),
    },
    {
      accessorKey: 'rounds',
      header: ({ column }) => (
        <div className="md:w-[210px] w-[100px]">
          {renderSortHeader(column, 'Rounds')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-center w-full">{row.original.rounds}</div>
      ),
    },
    {
      accessorKey: 'date',
      header: ({ column }) => (
        <div className="md:w-[210px] w-[100px]">
          {renderSortHeader(column, 'Date')}
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-center w-full">
          {moment(row.original.date).format('DD/MM/YYYY')}
        </div>
      ),
    },
  ];
