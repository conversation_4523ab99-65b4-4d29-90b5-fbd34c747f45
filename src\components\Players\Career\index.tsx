import React, { useState } from 'react';

import MyBarChart from '@/components/UI/BarChart/MyBarChart';

import {
  careerStatsOpponentsData,
  careerStatsSSLData,
  careerStatsVenuesData,
} from '../../../../db/db';
import SSLpage from './SSL';
import VenueStatsPage from './VenueStats';
import VsOpponentsPage from './VsOpponents';
import { generateUniqueId } from '@/lib/utils';

const CareerDetails = () => {
  const [activeTab, setActiveTab] = useState<string>('Vs opponents');
  const [activeCareerTab, setActiveCareerTab] = useState<string>('General');

  const tabs = ['Vs opponents', 'Venue stats', 'SSL'];

  const CareerTab = ['General', 'Average by rounds'];

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  const handleCareerTabClick = (tab: string) => {
    setActiveCareerTab(tab);
  };

  const TotalGamesData = [
    { name: '2020', price: 26 },
    { name: '2021', price: 13 },
    { name: '2022', price: 21 },
    { name: '2023', price: 15 },
    { name: '2024', price: 23 },
  ];

  const TotalGamesConfig = [
    { dataKey: 'price', color: '#4455C7', label: 'Total Games per year' },
  ];

  const ToatlPointData = [
    { name: '2020', price: 1277 },
    { name: '2021', price: 690 },
    { name: '2022', price: 1515 },
    { name: '2023', price: 1298 },
    { name: '2024', price: 1617 },
  ];

  const ToatlPointConfig = [
    { dataKey: 'price', color: '#FDA289', label: 'Total points per year' },
  ];

  const AverageScoreData = [
    { name: '2020', price: 60 },
    { name: '2021', price: 75 },
    { name: '2022', price: 102 },
    { name: '2023', price: 95 },
    { name: '2024', price: 110 },
  ];

  const AverageScoreConfig = [
    { dataKey: 'price', color: '#5D5D5D', label: 'Average score per game' },
  ];

  const AveragePointsData = [
    { name: '2020', price: 0.7 },
    { name: '2021', price: 0.6 },
    { name: '2022', price: 0.8 },
    { name: '2023', price: 0.3 },
    { name: '2024', price: 0.5 },
  ];

  const AveragePointsConfig = [
    {
      dataKey: 'price',
      color: '#D6D9F3',
      label: 'Average points per min by year',
    },
  ];

  const scoreChartData = [
    { name: 'Rd 1-3', 2019: 82, 2020: 75, 2021: 102, 2022: 38, 2023: 79 },
    { name: 'Rd 4-6', 2019: 95, 2020: 33, 2021: 76, 2022: 115, 2023: 75 },
    { name: 'Rd 7-9', 2019: 109, 2020: 46, 2021: 30, 2022: 79, 2023: 59 },
  ];

  const multipleBarsConfig = [
    { dataKey: '2019', color: '#4455C7', label: '2019' },
    { dataKey: '2020', color: '#5D5D5D', label: '2020' },
    { dataKey: '2021', color: '#FDA289', label: '2021' },
    { dataKey: '2022', color: '#D6D9F3', label: '2022' },
    { dataKey: '2023', color: '#78C2A7', label: '2023' },
  ];

  let content = null;

  if (activeTab === 'Vs opponents') {
    content = (
      <VsOpponentsPage careerStatsOpponentsData={careerStatsOpponentsData} />
    );
  } else if (activeTab === 'Venue stats') {
    content = <VenueStatsPage careerStatsVenuesData={careerStatsVenuesData} />;
  } else if (activeTab === 'SSL') {
    content = <SSLpage careerStatsSSLData={careerStatsSSLData} />;
  }

  let tabContent = null;

  if (activeCareerTab === 'General') {
    tabContent = (
      <div className="grid grid-cols-2 max-799:grid-cols-1 gap-y-3">
        <div>
          <MyBarChart
            data={TotalGamesData}
            bars={TotalGamesConfig}
            xAxisKey="name"
          />
        </div>
        <div>
          <MyBarChart
            data={ToatlPointData}
            bars={ToatlPointConfig}
            xAxisKey="name"
          />
        </div>
        <div>
          <MyBarChart
            data={AverageScoreData}
            bars={AverageScoreConfig}
            xAxisKey="name"
          />
        </div>
        <div>
          <MyBarChart
            data={AveragePointsData}
            bars={AveragePointsConfig}
            xAxisKey="name"
          />
        </div>
      </div>
    );
  } else if (activeCareerTab === 'Average by rounds') {
    tabContent = (
      <div>
        <MyBarChart
          data={scoreChartData}
          bars={multipleBarsConfig}
          xAxisKey="name"
        />
      </div>
    );
  }

  return (
    <div className="">
      <h6 className="text-[16px] leading-[19px] font-inter font-semibold text-black-100 mt-[18px]">
        Career Stats
      </h6>
      <div className="mt-[9px] bg-white rounded-lg">
        <div className="flex items-center gap-[3px] rounded-lg w-fit p-[9px]">
          {tabs?.map((tab, index) => (
            <button
              key={generateUniqueId()}
              className={`px-[9px] py-[6px] cursor-pointer ${
                activeTab === tab ? 'bg-primary-200 rounded-[6px]' : ''
              }`}
              onClick={() => handleTabClick(tab)}
            >
              <p
                className={`text-[12px] leading-[15px] font-inter font-medium ${
                  activeTab === tab ? 'text-white' : 'text-primary-200'
                }`}
              >
                {tab}
              </p>
            </button>
          ))}
        </div>
        {content}
      </div>
      <div className="mt-1.5 bg-white rounded-lg pr-[30px] max-799:pr-3">
        <div className="py-[9px] pl-[30px] max-799:pl-3">
          <div className="flex items-center gap-[3px] rounded-lg bg-black-300 p-[3px] w-fit">
            {CareerTab?.map((tab, index) => (
              <button
                key={generateUniqueId()}
                className={`px-[9px] py-[6px] cursor-pointer ${
                  activeCareerTab === tab ? 'bg-primary-200 rounded-[6px]' : ''
                }`}
                onClick={() => handleCareerTabClick(tab)}
              >
                <p
                  className={`text-[12px] leading-[15px] font-inter font-medium ${
                    activeCareerTab === tab ? 'text-white' : 'text-primary-200'
                  }`}
                >
                  {tab}
                </p>
              </button>
            ))}
          </div>
        </div>
        {tabContent}
      </div>
    </div>
  );
};

export default CareerDetails;
