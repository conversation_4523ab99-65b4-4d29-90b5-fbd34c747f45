import { usePlanContext } from '@/helpers/context/userPlanContext';
import { Input } from '@material-tailwind/react';
import React, { useEffect, useState } from 'react';
import type {
  FieldErrors,
  UseFormRegister,
  UseFormReset,
} from 'react-hook-form';

// Define the form data type
export interface CardFormData {
  cardNumber: string;
  expirationDate: string;
  cvv: string;
  cardHolderName: string;
  isFeatured?: boolean;
  acceptTermsandConditions?: boolean;
}

// Define the props type to include the register function, errors, and setValue
interface CardInputProps {
  register: UseFormRegister<CardFormData>;
  errors: FieldErrors<CardFormData>;
  setValue: (name: keyof CardFormData, value: string) => void;
  reset: UseFormReset<CardFormData>;
  price?: number;
  selectedPlan?: string;
  onPlanSelect?: (plan: string) => void;
}

const CardInput: React.FC<CardInputProps> = ({
  register,
  errors,
  setValue,
  price,
  selectedPlan,
  onPlanSelect,
}) => {
  const [formValues, setFormValues] = useState({
    cardHolderName: '',
    cardNumber: '',
    expirationDate: '',
    cvv: '',
  });

  // Add useEffect for default plan selection
  useEffect(() => {
    if (!selectedPlan && onPlanSelect) {
      onPlanSelect('basic'); // or whatever your default plan value is
    }
  }, [selectedPlan, onPlanSelect]);

  // Format the card number as 1234 5678 9012 3456
  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\s+/g, ''); // Remove spaces
    value = value.replace(/\D/g, ''); // Allow only digits
    if (value.length > 16) value = value.slice(0, 16); // Limit to 16 digits
    value = value.replace(/(\d{4})(?=\d)/g, '$1 '); // Add space every 4 digits
    setValue('cardNumber', value.trim()); // Update form state
    setFormValues((prev) => ({ ...prev, cardNumber: value.trim() }));
  };

  // Format expiration date as MM/YYYY
  const handleExpirationDateChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    let value = e.target.value;

    value = value
      .replace(/\D/g, '') // Allow only numbers
      .replace(/^([2-9])$/, '0$1') // Pad single-digit months (2-9) with a leading zero
      .replace(/^(1[3-9])$/, '01/$1') // Convert invalid months like '13' to '01/3'
      .replace(/^0+/, '0') // Ensure only one leading zero
      .replace(/^([01]\d)(\d{0,4}).*/, '$1/$2'); // Format as MM/YYYY

    setValue('expirationDate', value); // Update the value in the form state
    setFormValues((prev) => ({ ...prev, expirationDate: value }));
  };

  const handleCVVChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    value = value.replace(/\D/g, '');
    setValue('cvv', value); // Update the value in the form state
    setFormValues((prev) => ({ ...prev, cvv: value }));
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormValues((prev) => ({ ...prev, cardHolderName: value }));
  };

  const { validateUserCard } = usePlanContext();

  useEffect(() => {
    const { cardHolderName, cardNumber, expirationDate, cvv } = formValues;

    // Check if all fields are filled and no errors exist
    if (
      cardHolderName &&
      cardNumber.length >= 16 &&
      expirationDate.length === 7 &&
      cvv.length === 3 &&
      !errors.cardHolderName &&
      !errors.cardNumber &&
      !errors.expirationDate &&
      !errors.cvv
    ) {
      validateUserCard({
        cardHolderName,
        cardNumber: cardNumber.replace(/\s/g, ''),
        cardExp: expirationDate.replace('/', '-'),
        cvv,
        amount: price ?? 0,
      });
    }
  }, [formValues, errors, validateUserCard, price, selectedPlan]);

  return (
    <div className="w-full my-2">
      <div className="flex flex-col space-y-2">
        <div>
          {/* @ts-expect-error */}
          <Input
            placeholder="Enter Name"
            maxLength={19}
            className="min-w-0"
            color="indigo"
            label="Card Holder Name"
            {...register('cardHolderName', {
              onChange: handleNameChange,
            })}
          />
          {errors.cardHolderName && (
            <p className="text-red-500 text-sm">
              {errors.cardHolderName.message}
            </p>
          )}
        </div>
        <div>
          {/* @ts-expect-error */}
          <Input
            placeholder="1234 5678 9012 3456"
            maxLength={19}
            className="min-w-0"
            color="indigo"
            label="Card Number"
            {...register('cardNumber', {
              onChange: handleCardNumberChange,
            })}
          />
          {errors.cardNumber && (
            <p className="text-red-500 text-sm">{errors.cardNumber.message}</p>
          )}
        </div>

        <div className="grid grid-cols-2 gap-x-2">
          <div>
            {/* @ts-expect-error */}
            <Input
              placeholder="MM/YYYY"
              maxLength={7}
              color="indigo"
              label="Expiration Date"
              containerProps={{ className: '!min-w-0' }}
              {...register('expirationDate', {
                onChange: handleExpirationDateChange,
              })}
            />
            {errors.expirationDate && (
              <p className="text-red-500 text-sm">
                {errors.expirationDate.message}
              </p>
            )}
          </div>

          <div>
            {/* @ts-expect-error */}
            <Input
              placeholder="CVV"
              type="password"
              maxLength={3}
              color="indigo"
              containerProps={{ className: '!min-w-0' }}
              label="CVV"
              {...register('cvv', {
                onChange: handleCVVChange,
              })}
            />
            {errors.cvv && (
              <p className="text-red-500 text-sm">{errors.cvv.message}</p>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2 mt-2">
          <input type="checkbox" id="save-card" {...register('isFeatured')} />
          <label
            htmlFor="save-card"
            className="text-[11.42px] leading-[14px] font-normal text-black-600 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Save the card for future use
          </label>
        </div>
      </div>
    </div>
  );
};

export default CardInput;
