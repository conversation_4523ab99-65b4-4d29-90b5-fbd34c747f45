'use client';
import type { Column, ColumnDef } from '@tanstack/react-table';
import Image from 'next/image';

import DefaultTeam from '@/assets/images/icons/defaultTeam.png';
import { Config } from '@/helpers/context/config';

import type { Player } from '../../../../types/competitions';
import SortingDownIcon from '../Icons/SortingDownIcon';
import SortingUpIcon from '../Icons/SortingUpIcon';

const renderSortHeader = (column: Column<Player, unknown>, label: string) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

const playerLogo = (playerImage: any) => {
  let flagUrl = DefaultTeam;

  if (playerImage) {
    if (playerImage.includes('uploads')) {
      flagUrl = Config?.mediaURL + playerImage;
    } else {
      flagUrl = playerImage;
    }
  }

  return (
    <Image
      src={flagUrl}
      alt="icon"
      width={26}
      height={26}
      className="object-cover rounded-full"
      unoptimized={true}
    />
  );
};

export const teamTrackColumns: ColumnDef<Player>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center w-fit">
        {renderSortHeader(column, 'Name')}
      </div>
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center space-x-2 px-2 py-1">
          {playerLogo(row.original.image)}
          <p>{row.original.name}</p>
          <div>
            {row.original?.positionType?.includes('viceCaptain') && (
              <div className="text-[10px] leading-3 font-semibold w-[18px] h-[18px] text-primary-200 bg-primary-200/15 py-[3px] px-[2px] rounded-[50px]">
                VC
              </div>
            )}
            {row.original?.positionType?.includes('captain') && (
              <div className="text-[10px] leading-3 font-semibold w-[18px] h-[18px] text-orange-500 bg-orange-500/15 py-[3px] px-[2px] rounded-[50px]">
                C
              </div>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'role',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Role')}
      </div>
    ),
  },
  {
    accessorKey: 'scoreData.lastPoint',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Score')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="">
        <p>{row.original?.scoreData?.lastPoint}</p>
      </div>
    ),
  },
];

export const upcomingTeamTrackColumns: ColumnDef<Player>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center w-fit">
        {renderSortHeader(column, 'Name')}
      </div>
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center space-x-2 px-2 py-1">
          {playerLogo(row.original.image)}
          <p>{row.original.name}</p>
          <div>
            {row.original?.positionType?.includes('viceCaptain') && (
              <div className="text-[10px] leading-3 font-semibold w-[18px] h-[18px] text-primary-200 bg-primary-200/15 py-[3px] px-[2px] rounded-[50px]">
                VC
              </div>
            )}
            {row.original?.positionType?.includes('captain') && (
              <div className="text-[10px] leading-3 font-semibold w-[18px] h-[18px] text-orange-500 bg-orange-500/15 py-[3px] px-[2px] rounded-[50px]">
                C
              </div>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'role',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Role')}
      </div>
    ),
  },
  {
    accessorKey: 'scoreData.lastScore',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Score')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="">
        <p>{row.original?.scoreData?.lastScore}</p>
      </div>
    ),
  },
];
