import React from 'react';
import TableScoring from './tableScoring';
import Loader from '@/components/Loader';

interface ScoringProps {
  sportScoring: Record<string, any> | undefined; // This will be a dynamic object for each sport
}

const Scoring: React.FC<ScoringProps> = ({ sportScoring }) => {
  if (!sportScoring) {
    return (
      <div>
        <Loader />
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-[22.4px] leading-[27px] font-semibold mb-[21px] font-inter">
        Points Scoring
      </h1>
      <div className="grid grid-cols-1 max-799:grid-cols-1 gap-6">
        {Object.keys(sportScoring).map((key) => (
          <TableScoring
            key={key}
            headerData={sportScoring[key].header}
            bodyData={sportScoring[key].body}
          />
        ))}
      </div>
    </div>
  );
};

export default Scoring;
