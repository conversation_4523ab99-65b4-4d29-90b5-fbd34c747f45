'use client';

import { ButtonProps, useChatContext } from '@copilotkit/react-ui';
import '@copilotkit/react-ui/styles.css';
import SmartPlayButton from '../SmartPlayAI';
function AISmartICon({}: ButtonProps) {
  const { open, setOpen } = useChatContext();

  return (
    <div onClick={() => setOpen(!open)} className="cursor-pointer">
      <SmartPlayButton />
    </div>
  );
}

export default AISmartICon;
