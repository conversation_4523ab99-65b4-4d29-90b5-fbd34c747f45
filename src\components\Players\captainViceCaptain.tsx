import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';

import SortingDown from '@/assets/images/icons/sortingDown.svg';
import SortingUp from '@/assets/images/icons/sortingUp.svg';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { Config } from '@/helpers/context/config';
import { quyerKeys } from '@/lib/queryKeys';

import { getDefaultProfileImage } from '../../../db/db';
import type { CaptainApiResponse, CaptainPlayer } from '../../../types';
import Loader from '../Loader';
import PlayerAvatar from '../UI/PlayerAvatar/indext';
import { generateUniqueId } from '@/lib/utils';

interface CaptainProfileDetailsModal {
  selectedLeague: number | null;
  selectedSeason: number | null;
}

interface SortingType {
  captainsIdShort: boolean | null;
  captainsScoreShort: boolean | null;
  viceCaptainsScoreShort: boolean | null;
}

const fetchLeagueCaptainDetails = async (
  selectedLeague: any,
  selectedSeason: any,
): Promise<CaptainApiResponse> => {
  let url = `${Config.fantasyURL}/player-stats/captain-list/${selectedLeague}?SportId=4`;

  if (selectedSeason) {
    url += `&seasonId=${selectedSeason}`;
  }

  const res = await axiosInstance.get<CaptainApiResponse>(url);
  return res?.data;
};

const CaptainViceCaptain = ({
  selectedLeague,
  selectedSeason,
}: CaptainProfileDetailsModal) => {
  const { data: CaptainPlayerList, isLoading } = useQuery({
    queryFn: () => fetchLeagueCaptainDetails(selectedLeague, selectedSeason),
    queryKey: [quyerKeys.getCaptainPlayerList, selectedLeague, selectedSeason],
    enabled: !!selectedLeague,
  });

  const captainList = CaptainPlayerList?.result;

  const [captainsData, setCaptainsData] = useState<CaptainPlayer[]>([]);
  const [sortingType, setSortingType] = useState<SortingType>({
    captainsIdShort: null,
    captainsScoreShort: null,
    viceCaptainsScoreShort: null,
  });

  useEffect(() => {
    if (captainList) {
      setCaptainsData(captainList);
    }
  }, [captainList]);

  const onSortByCaptainsId = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...captainsData];
      data?.sort((a: any, b: any) => a?.id - b?.id);
      setCaptainsData(data);
      setSortingType({
        captainsIdShort: true,
        captainsScoreShort: null,
        viceCaptainsScoreShort: null,
      });
    } else {
      const data = [...captainsData];
      data?.sort((a: any, b: any) => b?.id - a?.id);
      setCaptainsData(data);
      setSortingType({
        captainsIdShort: false,
        captainsScoreShort: null,
        viceCaptainsScoreShort: null,
      });
    }
  };

  // sort function for last score
  const onSortByCaptainsScore = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...captainsData];
      data?.sort(
        (a: any, b: any) =>
          a?.averageValue?.captainAverage - b?.averageValue?.captainAverage,
      );
      setCaptainsData(data);
      setSortingType({
        captainsIdShort: null,
        captainsScoreShort: true,
        viceCaptainsScoreShort: null,
      });
    } else {
      const data = [...captainsData];
      data?.sort(
        (a: any, b: any) =>
          b?.averageValue?.captainAverage - a?.averageValue?.captainAverage,
      );
      setCaptainsData(data);
      setSortingType({
        captainsIdShort: null,
        captainsScoreShort: false,
        viceCaptainsScoreShort: null,
      });
    }
  };

  // sort function total score

  const onSortByViceCaptainsScore = (isShorting: SortingType) => {
    // @ts-expect-error
    if (isShorting === null || isShorting === false) {
      const data = [...captainsData];
      data?.sort(
        (a: any, b: any) =>
          a?.averageValue?.viceCaptainAverage -
          b?.averageValue?.viceCaptainAverage,
      );
      setCaptainsData(data);
      setSortingType({
        captainsIdShort: null,
        captainsScoreShort: null,
        viceCaptainsScoreShort: true,
      });
    } else {
      const data = [...captainsData];
      data?.sort(
        (a: any, b: any) =>
          b?.averageValue?.viceCaptainAverage -
          a?.averageValue?.viceCaptainAverage,
      );
      setCaptainsData(data);
      setSortingType({
        captainsIdShort: null,
        captainsScoreShort: null,
        viceCaptainsScoreShort: false,
      });
    }
  };

  const getPlayerRoleType = (role: any) => {
    if (role === 'Batter') {
      return 'BAT';
    } else if (role === 'Bowler') {
      return 'BOW';
    } else if (role === 'WK-Batter') {
      return 'WKP / BAT';
    } else if (role === 'Bowling Allrounder' || role === 'Batting Allrounder') {
      return 'AR';
    } else {
      return '';
    }
  };

  // Extract logic for SortingUp class
  let sortingUpClass;
  if (sortingType?.captainsIdShort === null) {
    sortingUpClass = '';
  } else if (sortingType?.captainsIdShort) {
    sortingUpClass = 'asc-sort';
  }

  // Extract logic for SortingDown class
  let sortingDownClass;
  if (sortingType?.captainsIdShort === null) {
    sortingDownClass = '';
  } else if (!sortingType?.captainsIdShort) {
    sortingDownClass = 'desc-sort';
  }

  return (
    <div className="bg-white rounded-lg max-799:rounded-none py-[13px] px-[9px] max-799:px-0 shadow-[0px_1px_3px_0px_#0000002b] max-799:shadow-none">
      <h6 className="text-[16px] leading-[19px] font-inter font-semibold text-black-100 mb-3 max-799:mt-[31px] max-799:mx-3">
        Selected as Captain and Vice Captain
      </h6>
      <div className="upcoming-matches-table overflow-x-auto rounded-lg max-799:rounded-none border border-black-300 max-h-[900px] overflow-y-auto no-scrollbar">
        <table className="w-full table-auto border-collapse ">
          <thead className="sticky top-0 bg-primary-200 z-10">
            <tr className="bg-primary-200 ">
              <th className="px-[15px]  max-799:px-3 py-1.5 max-799:py-[10px] text-[11.42px] leading-[14px] text-white text-left max-799:sticky max-799:left-0 max-799:z-[9] max-799:bg-primary-200">
                <button
                  className="flex items-center gap-1"
                  onClick={async () =>
                    // @ts-expect-error
                    onSortByCaptainsId(sortingType?.captainsIdShort)
                  }
                >
                  #{' '}
                  <div className="flex items-center flex-col">
                    <span>
                      <SortingUp className={sortingUpClass} />
                    </span>
                    <span className="mt-[1.3px]">
                      <SortingDown className={sortingDownClass} />
                    </span>
                  </div>
                </button>
              </th>
              <th className="px-[15px]  max-799:px-3 py-1.5 max-799:py-[10px] text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                <button
                  className="flex items-center gap-1"
                  onClick={async () =>
                    // @ts-expect-error
                    onSortByCaptainsScore(sortingType?.captainsScoreShort)
                  }
                >
                  Captain %{' '}
                  <div className="flex items-center flex-col">
                    <span>
                      <SortingUp className={sortingUpClass} />
                    </span>
                    <span className="mt-[1.3px]">
                      <SortingDown className={sortingDownClass} />
                    </span>
                  </div>
                </button>
              </th>
              <th className="px-[15px]  max-799:px-3 py-1.5 max-799:py-[10px] text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
                <button
                  className="flex items-center gap-1"
                  onClick={async () =>
                    onSortByViceCaptainsScore(
                      // @ts-expect-error
                      sortingType?.viceCaptainsScoreShort,
                    )
                  }
                >
                  Vice-Captain %{' '}
                  <div className="flex items-center flex-col">
                    <span>
                      <SortingUp className={sortingUpClass} />
                    </span>
                    <span className="mt-[1.3px]">
                      <SortingDown className={sortingDownClass} />
                    </span>
                  </div>
                </button>
              </th>
            </tr>
          </thead>
          <tbody>
            {isLoading && (
              <tr>
                <td colSpan={3} className="py-2">
                  {' '}
                  <Loader />
                </td>
              </tr>
            )}
            {!isLoading && captainsData?.length > 0 ? (
              captainsData?.map((match, index) => {
                return (
                  <tr
                    key={generateUniqueId()}
                    className={`${index === captainsData?.length - 1 ? '' : 'border-b-[1px] border-black-300'}`}
                  >
                    <td className=" px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-medium text-black-100 text-left max-799:sticky max-799:left-0 max-799:z-[9] max-799:bg-white">
                      <div className="flex items-center gap-2">
                        <div className="w-[15px]">{match?.id}</div>
                        <div className="flex items-center gap-1.5">
                          <PlayerAvatar
                            avatarUrl={match?.image ?? getDefaultProfileImage()}
                            className="!ml-2 !w-[26px] !h-[26px]"
                          />
                          <div>
                            <p className="mb-1 text-[12px] leading-[14px] font-inter font-semibold text-black-100">
                              {match?.name}{' '}
                              <span className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100">
                                {getPlayerRoleType(match?.role)}
                              </span>
                            </p>
                            <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100">
                              {match?.teamName}
                            </p>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className=" px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-center">
                      {match?.averageValue?.captainAverage
                        ? match?.averageValue?.captainAverage + '%'
                        : '-'}
                    </td>
                    <td className=" px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-center">
                      {match?.averageValue?.viceCaptainAverage
                        ? match?.averageValue?.viceCaptainAverage + '%'
                        : '-'}
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan={3}>
                  <div className="mt-2 p-2 text-center">
                    <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter text-black-100">
                      {!isLoading ? 'No Data Available' : ''}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CaptainViceCaptain;
