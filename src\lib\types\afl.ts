export interface AflMatchData {
  status: boolean;
  result: AflMatch;
}

export interface AflMatch {
  id: string;
  date: string;
  time: string;
  localteam: EnhancedTeam;
  visitorteam: EnhancedTeam;
  status: {
    time: string;
    value: string;
  };
  events: {
    event: AflMatchEvent[] | AflMatchEvent;
  };
  venue?: string;
  quarters?: {
    quarter: QuarterScore[];
  };
  lineups?: {
    lineup: TeamLineup[];
  };
  timer?: string;
}

export interface EnhancedTeam {
  id: string;
  name: string;
  score: string;
  flag?: string;
  nameCode?: string;
  goals?: string;
  behinds?: string;
}

export interface AflMatchEvent {
  id?: string;
  minute: string;
  period: string;
  player: string;
  team: 'hometeam' | 'visitorteam';
  type: 'goal' | 'behind' | 'rushed';
}

export interface QuarterScore {
  name: string;
  homeBehinds: string;
  homeGoals: string;
  homePoints: string;
  awayBehinds: string;
  awayGoals: string;
  awayPoints: string;
}

export interface TeamLineup {
  team: 'localteam' | 'visitorteam';
  player: AflPlayer[];
}

export interface AflPlayer {
  id: string;
  name: string;
  number: string;
  disposals: string;
  kicks: string;
  marks: string;
  handballs: string;
  goals: string;
  behinds: string;
  tackles: string;
}

export interface AflCommentaryFilters {
  type: 'Full Commentary' | 'Goal' | 'Behind';
}

export type AFLSmartPlayStatsResponse = {
  status: boolean;
  result: {
    success: boolean;
    data: AFLMatchPlayerStats[];
  };
};

export type AFLMatchPlayerStats = {
  matchId: number;
  matchDetails: AFLMatchDetails;
  player: AFLPlayer;
  position: string;
  team: AFLTeam;
  baseScore: number;
  bonusScore: number;
  totalScore: number;
  scoreComponents: Record<string, AFLScoreComponent>;
  rawStats: Record<string, number>;
};

export type AFLMatchDetails = {
  name: string;
  date: string; // ISO string
};

export type AFLPlayer = {
  id: number;
  name: string;
  image: string | null;
};

export type AFLTeam = {
  id: number;
  name: string;
  flag: string | null;
};

export type AFLScoreComponent = {
  value: number;
  weight: number;
  points: number;
};
