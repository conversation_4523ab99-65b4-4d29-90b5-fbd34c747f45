'use client';

import { useState } from 'react';
import Image from 'next/image';
import PlayerAvatar from '@/components/UI/PlayerAvatar/indext';
import { getDefaultTeamImage } from '../../../../db/db';
import { AFLTeamStatsType } from '@/helpers/fetchers/stats';

// Interfaces for type safety
interface StatItem {
  label: string;
  team1Value: number;
  team2Value: number;
  isPercentage?: boolean;
}

interface AFLTeamStatsProps {
  teamNames: [string, string];
  teamColors: [string, string];
  stats: AFLTeamStatsType;
  homeTeamLogo: string;
  awayTeamLogo: string;
}

export default function AFLTeamStats({
  teamNames,
  teamColors,
  stats,
  homeTeamLogo,
  awayTeamLogo,
}: AFLTeamStatsProps) {
  // Map the comprehensive stats to display-friendly stats
  const displayStats: StatItem[] = [
    {
      label: 'GOALS',
      team1Value: stats?.goals[0],
      team2Value: stats?.goals[1],
    },
    {
      label: 'BEHINDS',
      team1Value: stats?.behinds[0],
      team2Value: stats?.behinds[1],
    },
    {
      label: 'TOTAL SCORE',
      team1Value: stats?.total_score[0],
      team2Value: stats?.total_score[1],
    },
    {
      label: 'DISPOSALS',
      team1Value: stats?.disposals[0],
      team2Value: stats?.disposals[1],
    },
    {
      label: 'CONTESTED POSSESSIONS',
      team1Value: stats?.contested_possessions[0],
      team2Value: stats?.contested_possessions[1],
    },
    {
      label: 'UNCONTESTED POSSESSIONS',
      team1Value: stats?.uncontested_possessions[0],
      team2Value: stats?.uncontested_possessions[1],
    },
    {
      label: 'TOTAL POSSESSIONS',
      team1Value: stats?.total_possessions[0],
      team2Value: stats?.total_possessions[1],
    },
    {
      label: 'KICKS',
      team1Value: stats?.kicks[0],
      team2Value: stats?.kicks[1],
    },
    {
      label: 'HANDBALLS',
      team1Value: stats?.handballs[0],
      team2Value: stats?.handballs[1],
    },
    {
      label: 'MARKS',
      team1Value: stats?.marks[0],
      team2Value: stats?.marks[1],
    },
    {
      label: 'CONTESTED MARKS',
      team1Value: stats?.contested_marks[0],
      team2Value: stats?.contested_marks[1],
    },
    {
      label: 'UNCONTESTED MARKS',
      team1Value: stats?.uncontested_marks[0],
      team2Value: stats?.uncontested_marks[1],
    },
    {
      label: 'INSIDE 50S',
      team1Value: stats?.inside_fifty[0],
      team2Value: stats?.inside_fifty[1],
    },
    {
      label: 'INSIDE 50 MARKS',
      team1Value: stats?.inside_fifty_marks[0],
      team2Value: stats?.inside_fifty_marks[1],
    },
    {
      label: 'CLEARANCES',
      team1Value: stats?.clearances[0],
      team2Value: stats?.clearances[1],
    },
    {
      label: 'HITOUTS',
      team1Value: stats?.hitouts[0],
      team2Value: stats?.hitouts[1],
    },
    {
      label: 'HITOUTS TO ADVANTAGE',
      team1Value: stats?.hitouts_to_advantage[0],
      team2Value: stats?.hitouts_to_advantage[1],
    },
    {
      label: 'HITOUT EFFICIENCY (%)',
      team1Value: stats?.hitout_efficiency[0] * 100,
      team2Value: stats?.hitout_efficiency[1] * 100,
      isPercentage: true,
    },
    {
      label: 'TACKLES',
      team1Value: stats?.tackles[0],
      team2Value: stats?.tackles[1],
    },
    {
      label: 'FREES FOR',
      team1Value: stats?.freesFor[0],
      team2Value: stats?.freesFor[1],
    },
    {
      label: 'FREES AGAINST',
      team1Value: stats?.freesAgainst[0],
      team2Value: stats?.freesAgainst[1],
    },
    {
      label: 'ERRORS',
      team1Value: stats?.errors[0],
      team2Value: stats?.errors[1],
    },
    {
      label: '50M PENALTIES',
      team1Value: stats?.fifty_metre_penalties[0],
      team2Value: stats?.fifty_metre_penalties[1],
    },
    {
      label: 'EFFECTIVE KICKS',
      team1Value: stats?.effective_kicks[0],
      team2Value: stats?.effective_kicks[1],
    },
    {
      label: 'INEFFECTIVE KICKS',
      team1Value: stats?.ineffective_kicks[0],
      team2Value: stats?.ineffective_kicks[1],
    },
    {
      label: 'KICK EFFICIENCY (%)',
      team1Value: stats?.kick_efficiency[0] * 100,
      team2Value: stats?.kick_efficiency[1] * 100,
      isPercentage: true,
    },
    {
      label: 'BACKWARD KICKS',
      team1Value: stats?.backward_kicks[0],
      team2Value: stats?.backward_kicks[1],
    },
    {
      label: 'GOAL ASSISTS',
      team1Value: stats?.goal_assists[0],
      team2Value: stats?.goal_assists[1],
    },
    {
      label: 'SCORE INVOLVEMENTS',
      team1Value: stats?.score_involvements[0],
      team2Value: stats?.score_involvements[1],
    },
  ];

  // Function to calculate bar widths
  const calculateBarWidth = (team1Value: number, team2Value: number) => {
    // If both values are 0, return equal 50% width
    if (team1Value === 0 && team2Value === 0) {
      return {
        team1Width: '50%',
        team2Width: '50%',
      };
    }

    // Calculate proportional widths
    const total = team1Value + team2Value;
    return {
      team1Width: `${(team1Value / total) * 100}%`,
      team2Width: `${(team2Value / total) * 100}%`,
    };
  };

  return (
    <div className="max-w-full mx-auto bg-off-white-100 p-6 shadow-md mt-5">
      <div className="grid grid-cols-3 gap-4 place-items-center mb-8 max-w-2xl mx-auto">
        <div className="flex items-center">
          <span className="text-lg font-bold mr-2">{teamNames[0]}</span>
          <div className="relative w-8 h-8">
            <PlayerAvatar avatarUrl={homeTeamLogo ?? getDefaultTeamImage()} />
          </div>
        </div>

        <div className="text-xl font-bold">VS</div>

        <div className="flex items-center">
          <div className="relative w-8 h-8 mr-2">
            <PlayerAvatar avatarUrl={awayTeamLogo ?? getDefaultTeamImage()} />
          </div>
          <span className="text-lg font-bold">{teamNames[1]}</span>
        </div>
      </div>

      <div className="space-y-6">
        {displayStats.map((stat, index) => {
          const { team1Width, team2Width } = calculateBarWidth(
            stat.team1Value,
            stat.team2Value,
          );

          return (
            <div key={index} className="w-full">
              <div className="text-center text-sm font-medium text-gray-600 mb-2">
                {stat.label}
              </div>
              <div className="flex items-center justify-between">
                <div
                  className="font-bold text-xl w-12 text-right mr-2"
                  style={{ color: teamColors[0] }}
                >
                  {stat.isPercentage
                    ? stat.team1Value.toFixed(1)
                    : Math.round(stat.team1Value)}
                </div>

                <div className="flex-1 h-4 flex">
                  <div
                    className="h-full rounded-l-sm"
                    style={{
                      backgroundColor: teamColors[0],
                      width: team1Width,
                      minWidth: '1px',
                    }}
                  ></div>
                  <div
                    className="h-full rounded-r-sm"
                    style={{
                      backgroundColor: teamColors[1],
                      width: team2Width,
                      minWidth: '1px',
                    }}
                  ></div>
                </div>

                <div
                  className="font-bold text-xl w-12 text-left ml-2"
                  style={{ color: teamColors[1] }}
                >
                  {stat.isPercentage
                    ? stat.team2Value.toFixed(1)
                    : Math.round(stat.team2Value)}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
