import { generateUniqueId } from '@/lib/utils';
import React from 'react';

interface TableProps {
  headerData: string[];
  bodyData: { [key: string]: string | number | React.ReactNode }[];
}

const TableScoring: React.FC<TableProps> = ({ headerData, bodyData }) => {
  return (
    <div className="rounded-lg shadow-[0px_1px_3px_0px_#0000002b] overflow-hidden bg-white h-fit">
      <table className="min-w-full text-left text-gray-700">
        <thead className="bg-primary-200 text-white">
          <tr>
            {headerData?.map((header, index) => (
              <th
                key={generateUniqueId()}
                className={`${index === 0 ? 'w-[50%]' : 'w-[20%] text-center'} px-[45px] max-799:px-3 py-1.5 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter`}
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {bodyData?.map((row, rowIndex) => (
            <tr key={generateUniqueId()} className="even:bg-black-300/35">
              {headerData?.map((key, cellIndex) => (
                <td
                  key={generateUniqueId()}
                  className={`${cellIndex === 0 ? 'w-[50%]' : 'w-[20%] text-center'} px-[45px] max-799:px-3 py-3 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-normal font-inter text-black-100`}
                >
                  {row[key]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TableScoring;
