import { StoryObj, Meta } from '@storybook/react';
import { Dialog } from '../dialog';

const meta: Meta<typeof Dialog> = {
  title: 'Components/Dialog',
  component: Dialog,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
  },

  decorators: [
    (Story) => (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Story />
      </div>
    ),
  ],

  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Dialog>;

export const Open: Story = {
  args: {
    open: true,
    children: <></>,
  },
};
