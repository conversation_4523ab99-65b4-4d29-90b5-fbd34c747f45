import React from 'react';

import SortingDown from '@/assets/images/icons/sortingDown.svg';
import SortingUp from '@/assets/images/icons/sortingUp.svg';
import { generateUniqueId } from '@/lib/utils';

interface MatchData {
  rd?: number;
  opponent?: string;
  run?: number;
  sr?: number;
  rb?: number;
  wickets?: number;
  maiden?: number;
  dot?: number;
  economy?: number;
  catches?: number;
  runOut?: number;
  stumping?: number;
  directHit?: number;
  allRounder?: number;
}

interface FantasyStatsStateDataData {
  fantasyStatsStateData: MatchData[];
}

const HeaderCell = ({ label }: any) => (
  <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
    <div className="flex items-center gap-1">
      {label}
      <div className="flex items-center flex-col">
        <span>
          <SortingUp />
        </span>
        <span className="mt-[1.3px]">
          <SortingDown />
        </span>
      </div>
    </div>
  </th>
);

const StickyHeaderCell = ({ label, left, width }: any) => (
  <th
    className={`px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left sticky bg-primary-200 z-[9]`}
    style={{ left, width }}
  >
    <div className="flex items-center gap-1">
      {label}
      <div className="flex items-center flex-col">
        <span>
          <SortingUp />
        </span>
        <span className="mt-[1.3px]">
          <SortingDown />
        </span>
      </div>
    </div>
  </th>
);

const TableCell = ({ content, isSticky, left, width, textCenter }: any) => (
  <td
    className={`px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-left ${isSticky ? 'sticky bg-white z-[9]' : ''}`}
    style={{ width, textAlign: textCenter, ...(isSticky ? { left } : {}) }}
  >
    {content}
  </td>
);

const FantasyStatsTabPage = ({
  fantasyStatsStateData,
}: FantasyStatsStateDataData) => {
  return (
    <div>
      <div className="upcoming-matches-table overflow-x-auto no-scrollbar rounded-b-lg">
        <table className="w-full min-w-max table-auto">
          <thead>
            <tr className="bg-primary-200">
              <StickyHeaderCell label="Rd" left="0px" width="60px" />
              <StickyHeaderCell label="Opponent" left="60px" />
              <HeaderCell label="RUN" />
              <HeaderCell label="SR" />
              <HeaderCell label="RB" />
              <HeaderCell label="WIC" />
              <HeaderCell label="MO" />
              <HeaderCell label="DB" />
              <HeaderCell label="ER" />
              <HeaderCell label="CA" />
              <HeaderCell label="RO" />
              <HeaderCell label="ST" />
              <HeaderCell label="DH" />
              <HeaderCell label="AR" />
            </tr>
          </thead>
          <tbody>
            {fantasyStatsStateData?.map((match, index) => (
              <tr
                key={generateUniqueId()}
                className={`${index === fantasyStatsStateData?.length - 1 ? '' : 'border-b-[1px] border-black-300'}`}
              >
                <TableCell
                  content={match?.rd}
                  isSticky={true}
                  left="0px"
                  width="60px"
                />
                <TableCell
                  content={
                    <div className="flex items-center gap-2">
                      <div className="bg-primary-200 w-[26px] h-[26px] rounded-full"></div>
                      <div>
                        <p>{match?.opponent}</p>
                      </div>
                    </div>
                  }
                  isSticky={true}
                  left="60px"
                />
                <TableCell content={match?.run} textCenter="center" />
                <TableCell content={match?.sr} textCenter="center" />
                <TableCell content={match?.rb} textCenter="center" />
                <TableCell content={match?.wickets} textCenter="center" />
                <TableCell content={match?.maiden} textCenter="center" />
                <TableCell content={match?.dot} textCenter="center" />
                <TableCell content={match?.economy} textCenter="center" />
                <TableCell content={match?.catches} textCenter="center" />
                <TableCell content={match?.runOut} textCenter="center" />
                <TableCell content={match?.stumping} textCenter="center" />
                <TableCell content={match?.directHit} textCenter="center" />
                <TableCell content={match?.allRounder} textCenter="center" />
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default FantasyStatsTabPage;
