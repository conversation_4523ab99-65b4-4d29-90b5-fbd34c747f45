'use client';

import type { Dispatch, ReactNode, SetStateAction } from 'react';
import { createContext, useContext, useMemo, useState } from 'react';

import { Token } from '../../../db/db';

type AuthContextType = {
  token: string | null;
  LoginPopUp: boolean;
  setLoginPopUp: Dispatch<SetStateAction<boolean>>;
};

const authContext = createContext<AuthContextType | undefined>(undefined);
const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [open, setOpen] = useState<boolean>(false);

  const authState = useMemo(
    () => ({
      token: Token!,
      LoginPopUp: open,
      setLoginPopUp: setOpen,
    }),
    [Token, open, setOpen],
  );
  return (
    <authContext.Provider value={authState}>{children}</authContext.Provider>
  );
};

export const useAuthContext = () => {
  const context = useContext(authContext);
  if (!context) {
    throw new Error('useAuthContext must be used within a AuthProvider');
  }
  return context;
};

export default AuthProvider;
