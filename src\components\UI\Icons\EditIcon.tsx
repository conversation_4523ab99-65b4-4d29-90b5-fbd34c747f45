'use client';

const EditIcon = () => {
  return (
    <svg
      id="Group_29154"
      data-name="Group 29154"
      xmlns="http://www.w3.org/2000/svg"
      width="19"
      height="19"
      viewBox="0 0 19 19"
      className="add-button"
    >
      <rect
        id="Rectangle_2827"
        data-name="Rectangle 2827"
        width="19"
        height="19"
        rx="9.5"
        fill="#4455c7"
      ></rect>
      <g
        id="Group_23948"
        data-name="Group 23948"
        transform="translate(4.702 4.703)"
      >
        <path
          id="Path_12078"
          data-name="Path 12078"
          d="M3.714,10.039,10.273,3.48a1.372,1.372,0,0,1,1.94,1.94L5.653,11.979a1.072,1.072,0,0,1-.548.293L3,12.693l.421-2.106A1.072,1.072,0,0,1,3.714,10.039Z"
          transform="translate(-3 -3.1)"
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        ></path>
        <path
          id="Path_12079"
          data-name="Path 12079"
          d="M14.5,6.5l1.608,1.608"
          transform="translate(-8.337 -4.678)"
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        ></path>
      </g>
    </svg>
  );
};

export default EditIcon;
