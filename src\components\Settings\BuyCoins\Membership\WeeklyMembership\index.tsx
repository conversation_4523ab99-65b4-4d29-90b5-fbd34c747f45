import { yupResolver } from '@hookform/resolvers/yup';
import { Input, Radio, Spinner } from '@material-tailwind/react';
import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import Select, { components } from 'react-select';
import * as Yup from 'yup';

import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { Button } from '@/components/UI/button';
import { Card, CardContent } from '@/components/UI/card';
import type { CardFormData } from '@/components/UI/CardInput';
import CardInput from '@/components/UI/CardInput';
import CardListing from '@/components/UI/CardListing';
import CustomDialog from '@/components/UI/CustomDialog';
import AmericanExpressIcon from '@/components/UI/Icons/AmericanExpressIcon';
import MasterCardIcon from '@/components/UI/Icons/MasterCardIcon';
import VisaIcon from '@/components/UI/Icons/VisaIcon';
import { Label } from '@/components/UI/label';
import PlanCard from '@/components/UI/PlanCard';
import { RadioGroup, RadioGroupItem } from '@/components/UI/radio-group';
import { setApiMessage } from '@/helpers/commonFunctions';
import { Config } from '@/helpers/context/config';
import { useUserProfileContext } from '@/helpers/context/userContext';
import { useUserMemeberShip } from '@/helpers/context/userMembershipContext';
import { usePlanContext } from '@/helpers/context/userPlanContext';
import { quyerKeys } from '@/lib/queryKeys';
import {
  calculateTotalWithCardFee,
  capitalize,
  generateUniqueId,
} from '@/lib/utils';

import { Token } from '../../../../../../db/db';
import type { CardType, GetCardsResponse } from '../../../../../../types';
import DiscountDetails from '../Coupon/DiscountDetails';

// Yup validation schema
const cardSchema = Yup.object().shape({
  cardNumber: Yup.string()
    .min(19, 'Card number must be at least 16 characters') // Corrected to "at least"
    .max(19, 'Card number must be at most 19 characters')
    .required('Card number is required'),
  expirationDate: Yup.string()
    .required('Expiration date is required')
    .typeError('Not a valid expiration date. Example: MM/YYYY')
    .max(7, 'Not a valid expiration date. Example: MM/YYYY')
    .matches(/\d{2}\/\d{4}/, 'Not a valid expiration date. Example: MM/YYYY')
    .test(
      'is-future',
      'Card expiration date should be in the future',
      function (value) {
        if (!value) return false;
        const currentDate = new Date();
        const [month, year] = value.split('/');
        const cardExpirationDate = new Date(
          parseInt(year),
          parseInt(month) - 1,
        );
        return cardExpirationDate > currentDate;
      },
    )
    .test(
      'not-over-100',
      'Card expiration date should not exceed 100 years in the future',
      function (value) {
        if (!value) return false;
        const currentDate = new Date();
        const [month, year] = value.split('/');
        const cardExpirationDate = new Date(
          parseInt(year),
          parseInt(month) - 1,
        );
        return (
          cardExpirationDate.getFullYear() <= currentDate.getFullYear() + 100
        );
      },
    ),
  cvv: Yup.string()
    .min(3, 'CVV must be exactly 3 characters')
    .max(3, 'CVV must be exactly 3 characters')
    .required('CVV number is required'),
  cardHolderName: Yup.string().required('Card holder name is required'),
  isFeatured: Yup.boolean().optional(),
  acceptTermsandConditions: Yup.boolean().optional(),
});

// Function to fetch cards
const getCards = async (): Promise<CardType[]> => {
  const res = await fetch(`${Config.baseURL}subscription/get-card`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Token}`,
    },
  });

  const data: GetCardsResponse = await res.json();
  return data.card; // Return only the card array
};

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

export default function WeeklyMemberShip() {
  const {
    buyPlan,
    allPlans,
    currentPlan,
    updatePlan,
    isPendingUpdatePlan,
    isSuccessUpdatePlan,
    setCouponeCode,
    couponData,
    applyCouponCode,
    isApplyinCouponCode,
    isPendingBuyPlan,

    validateUserCardData,
    setValidateUserCardData,
    validateUserCard,
    selectedPlan,
    setSelectedPlan,
    selectedOptions,
    setSelectedOptions,
  } = usePlanContext();

  const selectedPlanStyle = 'bg-gradient-to-b from-[#DAE2E9] to-white';
  const [open, setOpen] = useState<boolean>(false);
  const [acceptTermsandConditions, setAcceptTermsandConditions] =
    useState<boolean>(false);

  const { selectedMembership, handleMembershipChanges } = useUserMemeberShip();

  const { data: cards } = useQuery({
    queryFn: getCards,
    queryKey: [quyerKeys.getCards],
  });

  const selectedCard = cards?.find((card) => card.id === +selectedOptions);
  const plans = allPlans || [];
  const selectedPlanData = plans.find((plan) => plan.name === selectedPlan);

  const seletedPlanFeatures = plans?.find(
    (plan) => plan?.name == selectedPlan,
  )?.featured;

  const {
    register,
    formState: { errors },
    handleSubmit,
    setValue,
    reset,
  } = useForm<CardFormData>({
    resolver: selectedOptions === 'new' ? yupResolver(cardSchema) : undefined,
  });

  const {
    register: addCardRegister,
    watch,
    formState: { errors: formErrors }, // Renaming `errors` to `formErrors`
    handleSubmit: addCardHandelSubmit,
    setValue: addCardSetvalue,
  } = useForm({ resolver: yupResolver(cardSchema) });

  const [couponInput, setCouponInput] = useState<string>('');
  const [isCouponApplied, setIsCouponApplied] = useState<boolean>(false);

  const saveCardHandelSubmit = (data: CardFormData, isUpgrade?: boolean) => {
    const newCardData = {
      card_holder_name: data?.cardHolderName,
      card_number: data.cardNumber.replace(/\s+/g, ''),
      card_exp: data.expirationDate.split('/').reverse().join('-'),
      cvv: data.cvv,
    };

    const payload = {
      plan_id: selectedPlanData?.id,
      plateform: 'web',
      card: newCardData,
      category: 'fantasy',
      isFeatured: data.isFeatured,
      couponCode: couponData?.data?.data?.code || undefined,
    };

    if (isUpgrade) {
      updatePlan(payload);
    } else {
      buyPlan(payload);
    }
  };

  const handleJoinSmartB = (e: any, isUpgrade?: boolean) => {
    if (selectedOptions && selectedOptions !== 'new') {
      const payload = {
        plan_id: selectedPlanData?.id,
        plateform: 'web',
        card_id: selectedCard?.id,
        category: 'fantasy',
        couponCode: couponData?.data?.data?.code || undefined,
      };
      if (isUpgrade) {
        updatePlan(payload);
      } else {
        buyPlan(payload);
      }
    } else {
      const isUpgrade = currentPlan?.status === 'active';
      addCardHandelSubmit((formData) =>
        saveCardHandelSubmit(formData, isUpgrade),
      )(e);
    }
  };

  const { user } = useUserProfileContext();

  const onSubmit = async () => { };

  useEffect(() => {
    if (isSuccessUpdatePlan) {
      setSelectedOptions('new');
    }
  }, [isSuccessUpdatePlan]);

  const handlePlanChange = (value: string) => {
    setSelectedPlan(value);
    setCouponeCode(''); // Clear context coupon code
    setCouponInput(''); // Clear input
    setIsCouponApplied(false); // Reset coupon applied state
    setValidateUserCardData(undefined); // Reset validation data

    // Get the new plan's amount
    const newPlanAmount = Number(
      plans.find((plan) => plan.name === value)?.amount ?? 0,
    );

    if (selectedCard?.id) {
      validateUserCard({
        cardId: selectedCard.id,
        cardHolderName: '',
        cardExp: '',
        cardNumber: '',
        cvv: '',
        amount: newPlanAmount,
      });
    }
  };

  const handleCouponApply = () => {
    setCouponeCode(couponInput);
    setIsCouponApplied(true);
    applyCouponCode();
  };

  // Calculate total based on selected plan amount
  const planAmount = selectedPlanData?.amount
    ? Number(selectedPlanData.amount)
    : 0;
  let todaysTotal = planAmount;

  // Only apply discount if we have a valid coupon and it's for the current plan
  if (couponData?.data?.discountAmountPrize && isCouponApplied) {
    const discountAmount = Number(couponData?.data?.discountAmountPrize);
    todaysTotal = Math.max(0, planAmount - discountAmount);
  }

  type SelectOption = {
    value: string;
    label: string;
  };

  const memberOption: SelectOption[] = [
    { value: 'ManageMembership', label: 'Manage membership' },
    { value: 'HoldMembership', label: 'Hold membership' },
    { value: 'UpgradeMembership', label: 'Upgrade membership' },
    { value: 'CancelMembership', label: 'Cancel membership' },
  ];

  const cancelledMembershipOption: SelectOption[] = [
    { value: 'ManageMembership', label: 'Manage membership' },
    { value: 'UpgradeMembership', label: 'Upgrade membership' },
  ];

  const result = calculateTotalWithCardFee(todaysTotal || 0);
  const cardNumber = watch('cardNumber');

  useEffect(() => {
    const validateCardNumber = async () => {
      try {
        const trimmedCardNumber = cardNumber.replace(/\s+/g, ''); // Remove all whitespace
        if (trimmedCardNumber.length < 16) {
        }
      } catch (err) { }
    };

    if (cardNumber) {
      validateCardNumber();
    }
  }, [cardNumber]);

  // Reseting card state

  useEffect(() => {
    return () => setValidateUserCardData(undefined);
  }, []);

  const planOptions =
    currentPlan?.status === 'cancelled' || !currentPlan?.planQueueStatus
      ? cancelledMembershipOption
      : memberOption;

  return (
    <div className="container mx-auto p-4 px-0 max-w-full ">
      <div className="flex flex-col md:flex-row justify-between mb-2">
        <div className="text-xl mb-2 md:mb-0">Membership</div>
        <div>
          {currentPlan?.id && (
            <Select
              className="React membership-select"
              value={planOptions?.find((item: any) => {
                return item?.value === selectedMembership;
              })}
              onChange={(e: any) => handleMembershipChanges(e?.value)}
              options={planOptions}
              classNamePrefix="select"
              isSearchable={false}
              components={{ DropdownIndicator }}
            />
          )}
        </div>
      </div>

      <div className="mb-4 block md:hidden">
        <h2 className="font-bold text-lg my-2">Membership</h2>

        {seletedPlanFeatures?.map((feature, index) => (
          <div
            className="flex items-center mb-[14px] space-x-2"
            key={generateUniqueId()}
          >
            <div className="bg-[#D6D9F3] rounded-full h-5 w-5 flex justify-center items-center flex-shrink-0">
              <svg
                className="w-4 h-4 text-indigo-500"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M5 13l4 4L19 7"></path>
              </svg>
            </div>

            <p className="truncate">{feature}</p>
          </div>
        ))}
      </div>
      <div className="grid md:grid-cols-2 gap-8 bg-white p-4 rounded-lg shadow-[0px_1px_9px_0px_#0000002e] mt-[14px] overflow-hidden  ">
        <div className="space-y-4">
          <RadioGroup value={selectedPlan} onValueChange={handlePlanChange}>
            <div className="flex flex-row w-full md:flex-col md:space-y-5 space-x-2 md:space-x-0  overflow-x-auto pt-5 md:overflow-visible h-full md:w-full">
              {plans.map((plan) => (
                <PlanCard
                  key={plan.id}
                  planName={plan.name}
                  price={plan.amount}
                  billingFrequency={plan.duration}
                  features={plan.featured}
                  badge={
                    plan.purchasedPlan
                      ? capitalize(plan?.purchasedStatus ?? '')
                      : plan.subTitle!
                  }
                  selectedPlan={selectedPlan!}
                  value={plan.name}
                  selectedPlanStyle={selectedPlanStyle}
                  variant={plan.name}
                  isActive={plan.purchasedPlan}
                  isPopularPlan={plan.subTitle === 'Most popular'}
                  purchasedStatus={plan?.purchasedStatus}
                />
              ))}
            </div>
          </RadioGroup>
        </div>
        <div className="hidden md:block absolute left-1/2 top-[4rem] bottom-4 w-px bg-gray-100 transform -translate-x-1/2" />

        <div className="space-y-6">
          <h2 className="text-base font-semibold md:hidden block">
            Payment details
          </h2>

          <Input
            type="email"
            placeholder="<EMAIL>"
            label="Email"
            color="indigo"
            {...({} as any)}
            value={user?.username}
          />
          <div className="bg-white border p-2 pb-4 rounded border-gray-100">
            <h3 className="text-base font-medium mb-2">Apply Coupon code</h3>
            <div className="flex space-x-2">
              <Input
                placeholder="Coupon code (optional)"
                className="flex-grow"
                label="Coupon code (optional)"
                color="indigo"
                value={couponInput}
                onChange={(e) => {
                  setCouponInput(e.target.value);
                }}
                onPointerEnterCapture={() => { }}
                onPointerLeaveCapture={() => { }}
                crossOrigin="anonymous" onResize={undefined} onResizeCapture={undefined} />
              <Button
                disabled={!selectedPlanData?.id || isApplyinCouponCode}
                onClick={handleCouponApply}
              >
                Apply
              </Button>
            </div>
          </div>

          <div>
            <Card>
              <CardContent className="p-4">
                <RadioGroup
                  defaultValue="new"
                  onValueChange={(value: string) => {
                    setSelectedOptions(value);
                    if (value !== 'new') {
                      validateUserCard({
                        cardId: Number(value),
                        cardHolderName: '',
                        cardExp: '',
                        cardNumber: '',
                        cvv: '',
                        amount: todaysTotal,
                      });
                    }

                    if (value === 'new') {
                      setValidateUserCardData(undefined);
                    }
                  }}
                >
                  <h3 className="text-[16px] font-semibold">Payment method</h3>
                  <div className="flex text-base items-center space-x-2 ">
                    <span>Credit or debit card</span>
                    <div className="flex space-x-1">
                      <MasterCardIcon />
                      <VisaIcon />
                      <AmericanExpressIcon />
                    </div>
                  </div>

                  {cards?.map((card) => {
                    return (
                      <CardListing
                        key={card.id}
                        value={card.id}
                        id={card.id}
                        cardNumber={card.last4}
                        expiry={card.cardExp}
                      />
                    );
                  })}

                  <div className="w-full space-y-2">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="new" id="new" />
                      <Label htmlFor="new">Use a new card</Label>
                    </div>
                    {selectedOptions === 'new' && (
                      <div className="flex space-x-2">
                        <CardInput
                          register={addCardRegister}
                          errors={formErrors}
                          setValue={addCardSetvalue}
                          reset={reset}
                          price={todaysTotal}
                        />
                      </div>
                    )}
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>

            <Card className="mt-2">
              <CardContent className="p-4">
                <h3 className="text-base font-medium mb-2">Summary</h3>
                <div className="flex justify-between pb-2 border-b border-gray-400 text-base font-normal">
                  <span>Smartplay - {selectedPlanData?.name}</span>
                  <span>${todaysTotal}/week</span>
                </div>
                <div className="my-2">
                  {couponData && isCouponApplied && (
                    <DiscountDetails response={couponData} />
                  )}
                </div>

                <div className="flex flex-col text-base justify-between items-end pt-[10px] border-b pb-[10px] border-secondary-100">
                  <div className="flex gap-x-2 w-full justify-between">
                    <p>Sub Total:</p>
                    <p>{todaysTotal ? '$' + todaysTotal : '-'}</p>
                  </div>
                  <div className="flex gap-x-2 w-full justify-between">
                    <p>Credit Card Fees:</p>
                    <p>
                      {todaysTotal
                        ? `$${validateUserCardData?.data?.cardFee || 0}`
                        : '-'}
                    </p>
                  </div>
                </div>
                <div className="flex justify-between text-base font-semibold pt-[10px]">
                  <span>Today&apos; Total</span>

                  <span>
                    {todaysTotal
                      ? `$${validateUserCardData?.data?.totalAmount || todaysTotal}`
                      : '-'}
                  </span>
                </div>
              </CardContent>
            </Card>

            <div className="flex items-center space-x-2 mt-2">
              <input
                type="checkbox"
                id="save-card"
                checked={acceptTermsandConditions}
                onChange={(e) => {
                  setAcceptTermsandConditions(e.target.checked);
                }}
              />

              <label
                htmlFor="save-card"
                className="text-[11.42px] leading-[14px] font-normal  peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                I have read
                <Link
                  href="/terms-conditions"
                  className="text-secondary-100 underline mx-1"
                >
                  Terms and Conditions
                </Link>
                for Smartplay (SSL) memberships.
              </label>
            </div>
          </div>
          {currentPlan?.status === 'active' ||
            currentPlan?.status === 'cancelled' ||
            currentPlan?.status === 'hold' ? (
            <Button
              className="w-full flex justify-center items-center space-x-2"
              onClick={(e: any) => handleJoinSmartB(e, true)}
              disabled={
                isPendingUpdatePlan ||
                !selectedOptions ||
                !acceptTermsandConditions
              }
            >
              {isPendingUpdatePlan && <Spinner {...({} as any)} />}
              <span>
                {isPendingUpdatePlan ? 'Upgrading...' : 'Upgrade Now!'}
              </span>
            </Button>
          ) : (
            <Button
              className="w-full"
              onClick={(e: any) => handleJoinSmartB(e)}
              disabled={isPendingBuyPlan || !selectedOptions || !acceptTermsandConditions}
            >
              Join Smartplay
            </Button>
          )}
        </div>
      </div>

      <CustomDialog
        isOpen={open}
        onClose={() => {
          setOpen(false);
        }}
        title="Add Payment Method"
        maxWidth={570}
        className="add-payment-modal"
        outerClickClose={true}
      >
        <div className="card-input-content">
          <div className="px-2">
            <div>
              <div className="flex space-x-2 w-full">
                <Radio
                  name="type"
                  label="Credit Or Debit Card"
                  {...({} as any)}
                  color="indigo"
                />
                <div className="flex space-x-1">
                  <MasterCardIcon />
                  <VisaIcon />
                  <AmericanExpressIcon />
                </div>
              </div>
            </div>
            <CardInput
              register={register}
              errors={errors}
              setValue={setValue}
              reset={reset}
            />
          </div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="570"
            height="1"
            viewBox="0 0 570 1"
            className="mt-5"
          >
            <rect
              id="Rectangle_14915"
              data-name="Rectangle 14915"
              width="1"
              height="570"
              transform="translate(0 1) rotate(-90)"
              fill="#d4d6d8"
            />
          </svg>

          <div className="w-full flex flex-col justify-center items-center mt-4">
            <Button onClick={handleSubmit(onSubmit)}>Add Cards</Button>

            <Button
              variant={'link'}
              className="underline"
              onClick={() => setOpen(false)}
            >
              Close
            </Button>
          </div>
        </div>
      </CustomDialog>
    </div>
  );
}
