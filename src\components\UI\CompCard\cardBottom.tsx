import Image from 'next/image';
import React from 'react';
import Coins from '@/assets/images/settings/smartBCoins.png';
import FirstStPrices from '@/assets/images/icons/1stPrice.png';
import Entries from '@/assets/images/icons/Entries.png';
import { EventObject } from '.';
import useScreen from '@/hooks/useScreen';

const CardBottom = ({
  compData,
  buttonContent,
  finalEventPrice,
}: {
  compData: EventObject;
  buttonContent: JSX.Element;
  finalEventPrice: number;
}) => {
  const { width } = useScreen();

  const renderEntryCoinsSection = () => {
    return (
      <>
        <div className="flex items-center justify-center">
          <span className="text-[16px] max-1024:text-[11.42px] leading-[19px] max-1024:leading-[14px] font-inter font-normal text-black-700 mb-1.5 uppercase">
            Entry coins{' '}
          </span>
        </div>
        {compData?.eventConfiguration ? (
          <div className="flex items-center justify-center">
            {compData?.eventConfiguration?.eventType === 'paid' && (
              <Image
                src={Coins}
                alt="coin icon"
                unoptimized={true}
                width={18.37}
                height={18.37}
              />
            )}
            <span className="text-[22.4px] max-1024:text-[14px] leading-[27px] max-1024:leading-[16px] font-inter font-semibold text-secondary-100 ml-1">
              {compData?.eventConfiguration?.eventType === 'paid'
                ? compData?.eventConfiguration?.entryCoin
                : 'Free'}
            </span>
          </div>
        ) : (
          <div>-</div>
        )}
      </>
    );
  };

  const renderRankSection = () => {
    return (
      <div className="flex items-center justify-center flex-col">
        <span className="text-[16px] max-1024:text-[11.42px] leading-[19px] max-1024:leading-[14px] font-inter font-normal text-black-700 mb-1.5 uppercase">
          Rank
        </span>
        <span className="text-[22.4px] max-1024:text-[14px] leading-[27px] max-1024:leading-[16px] font-inter font-semibold text-secondary-100 ml-1 capitalize">
          {compData?.currentRank +
            (compData?.name ? ' (' + compData?.name + ')' : '')}
        </span>
      </div>
    );
  };

  const renderFirstPriceSection = () => {
    return (
      <>
        <div className="flex items-center justify-center">
          <span className="text-[16px] leading-[19px] font-inter font-normal text-black-700 mb-1.5 uppercase">
            1st Prize
          </span>
        </div>
        <div className="flex items-center justify-center">
          <Image
            src={FirstStPrices}
            alt="coin icon"
            unoptimized={true}
            width={18.37}
            height={18.37}
          />
          <span className="text-[22.4px] leading-[27px] font-inter font-semibold text-secondary-100 ml-1">
            {finalEventPrice}
          </span>
        </div>
      </>
    );
  };

  const renderLiveScoreSection = () => {
    return (
      <div className="text-center">
        <div className="flex items-center justify-center">
          <span className="text-[16px] leading-[19px] font-inter font-normal text-black-700 mb-1.5 uppercase">
            {compData?.status === 'inprogress' ? 'Live Score' : 'Total Score'}
          </span>
        </div>
        <span className="text-[22.4px] leading-[27px] font-inter font-semibold text-secondary-100 ">
          {compData?.liveScore}
        </span>
      </div>
    );
  };

  return (
    <div className="bg-black-300 rounded-[0px_0px_8px_8px] max-1024:rounded-[0px_0px_15px_15px] p-4 flex items-center justify-between">
      <div className="w-[223px] max-639:w-[85px]">
        <div className="flex items-center justify-center">
          <span className="text-[16px] max-1024:text-[11.42px] leading-[19px] max-1024:leading-[14px] font-inter font-normal text-black-700 mb-1.5 uppercase">
            Prize pool
          </span>
        </div>
        {compData?.eventConfiguration ? (
          <div className="flex items-center justify-center">
            <Image
              src={Coins}
              alt="coin icon"
              unoptimized={true}
              width={18.37}
              height={18.37}
            />
            <span className="text-[22.4px] max-1024:text-[14px] leading-[27px] max-1024:leading-[16px] font-inter font-semibold text-secondary-100 ml-1">
              {compData?.eventConfiguration?.prizePool}
            </span>
          </div>
        ) : (
          <div>-</div>
        )}
      </div>
      {width > 1024 && (
        <div className="w-[223px]">
          {' '}
          {compData?.status === 'upcoming'
            ? renderEntryCoinsSection()
            : compData?.isDreamTeamExist > 0
              ? renderRankSection()
              : renderEntryCoinsSection()}
        </div>
      )}
      <div className="">{buttonContent}</div>
      {width > 1024 ? (
        <>
          <div className="w-[223px]">
            {' '}
            <div className="flex items-center justify-center">
              <span className="text-[16px] leading-[19px] font-inter font-normal text-black-700 mb-1.5 uppercase">
                Entries
              </span>
            </div>
            <div className="flex items-center justify-center">
              <Image
                src={Entries}
                alt="coin icon"
                unoptimized={true}
                width={18.37}
                height={18.37}
              />
              <span className="text-[22.4px] leading-[27px] font-inter font-semibold text-secondary-100 ml-1">
                {compData?.userEntry}
              </span>
            </div>
          </div>
          <div className="w-[223px]">
            {' '}
            {compData?.status === 'upcoming'
              ? renderFirstPriceSection()
              : compData?.isDreamTeamExist > 0
                ? renderLiveScoreSection()
                : renderFirstPriceSection()}{' '}
          </div>
        </>
      ) : (
        <div className="w-[223px] max-639:w-[85px]">
          {' '}
          {compData?.status === 'upcoming'
            ? renderEntryCoinsSection()
            : compData?.isDreamTeamExist > 0
              ? renderRankSection()
              : renderEntryCoinsSection()}
        </div>
      )}
    </div>
  );
};

export default CardBottom;
