'use client';
import { Tooltip } from '@material-tailwind/react';
import type { Column, Row } from '@tanstack/react-table';
import { Minus, Plus } from 'lucide-react';

import PlayerValueChange from '@/components/Competition/PlayerValueChange';
import {
  cn,
  formatNumberWithCommas,
  getShortName,
  NRLplayersRollName,
} from '@/lib/utils';
import { FootballPlayer, Player } from '../../../../../../types/competitions';
import { footballPlayersByRole } from '../../../../../../types/football';
import SortingDownIcon from '@/components/UI/Icons/SortingDownIcon';
import SortingUpIcon from '@/components/UI/Icons/SortingUpIcon';
import PlayerAvatar from '@/components/UI/PlayerAvatar/indext';
import { getDefaultProfileImage } from '../../../../../../db/db';
import {
  RugbyLeaguePlayersByRole,
  RugbyPlayer,
} from '../../../../../../types/rugby-league';
import { PlayersByRole } from '@/helpers/context/createTeamContext';
import { Team } from '../../../../../../types';
import DoneArrow from '@/components/UI/Icons/DoneArrow';
import { getPlayerRole } from '@/components/Common/Player/PlayerCardContent';

import { useSearchParams } from 'next/navigation';
import { useCompetition } from '@/helpers/context/competitionContext';
import {
  SoccerPlayer,
  SoccerPlayerRole,
  SoccerPlayersByRole,
} from '@/lib/types/soccer';
import { CricketPlayer, CricketPlayerRole } from '@/lib/types/cricket';
import { PUBLIC_IMAGES } from '@/lib/constants/constants';
import Image from 'next/image';
import { Config } from '@/helpers/context/config';

export const renderFootballPlayerCell = ({
  player,
  activeTab,
  removePlayer,
  addPlayer,
  playersByRole,
  playerTypeLimits,
  playerRank,
  isPlayerSelected,
  readOnly,
  teamCreated,
  isLive,
  row,
  openReserveModal,
  addReservePlayer,
  removeReservePlayer,
  activePlayerPosition,
  setActivePlayerPosition,
  reservePlayersLimit,
  reservePlayers
}: {
  player: FootballPlayer;
  activeTab: keyof footballPlayersByRole;
  removePlayer: (id: number, tab: keyof footballPlayersByRole) => void;
  addPlayer: (player: FootballPlayer, tab: keyof footballPlayersByRole) => void;
  playersByRole: Record<string, FootballPlayer[]>;
  playerTypeLimits: Record<string, number>;
  playerRank: number;
  isPlayerSelected: boolean;
  readOnly?: boolean;
  teamCreated?: boolean;
  isLive?: boolean;
  row?: Row<FootballPlayer>;
  openReserveModal: boolean;
  addReservePlayer: (player: FootballPlayer, position: number) => void;
  removeReservePlayer: (playerId: number, position: number) => void;
  activePlayerPosition: number;
  setActivePlayerPosition: (position: number) => void;
  reservePlayersLimit: number;
  reservePlayers: (FootballPlayer | null)[];
}) => {
  let button;
  const isPlayerAdded = playersByRole[activeTab]?.some(
    (statePlayer) => statePlayer?.playerId === player?.playerId,
  );

  // Check if player is selected as reserve
  const isReservePlayerSelected = reservePlayers.some(
    (p) => p?.playerId === player?.playerId
  );

  // Check if reserve limit is reached
  const isReservePlayerLimitReached = reservePlayers.filter((p) => p !== null).length >= reservePlayersLimit;

  // Check if we're in reserve mode
  const isReserve = openReserveModal;

  const handleAddPlayer = () => {
    if (isReserve) {
      // Find the first empty position from the beginning
      const firstEmptyPosition = reservePlayers.findIndex((p) => p === null);

      // If no empty position found, don't add player
      if (firstEmptyPosition === -1) return;

      // Add player at the first empty position
      addReservePlayer(player, firstEmptyPosition);

      // Find the next empty position for future additions
      const nextEmptyPosition = reservePlayers.findIndex((p, i) => i > firstEmptyPosition && p === null);

      if (nextEmptyPosition !== -1) {
        setActivePlayerPosition(nextEmptyPosition);
      } else {
        // No more empty positions, reset to 0
        setActivePlayerPosition(0);
      }
    } else {
      addPlayer(player, activeTab);
    }
  };

  const handleRemovePlayer = () => {
    if (isReserve) {
      // Find the index of the removed player
      const removedIndex = reservePlayers.findIndex(
        (p) => p?.playerId === player?.playerId
      );

      // Remove player with position
      removeReservePlayer(player?.playerId, removedIndex !== -1 ? removedIndex : 0);

      // After removal, set the active position to the position that was just vacated
      if (removedIndex !== -1) {
        setActivePlayerPosition(removedIndex);
      } else {
        // Fallback: find the first empty position
        const firstEmptyPosition = reservePlayers.findIndex((p) => p === null);
        if (firstEmptyPosition !== -1) {
          setActivePlayerPosition(firstEmptyPosition);
        } else {
          setActivePlayerPosition(0);
        }
      }
    } else {
      removePlayer(player?.playerId, activeTab);
    }
  };

  if (isLive) {
    // button = (
    //   <button
    //     className="flex flex-col justify-center items-center h-full"
    //     onClick={() => row?.toggleExpanded()} // Only called when clicked
    //     disabled
    //   >
    //     {row?.getIsExpanded() ? <UpArrow /> : <DownArrow />}
    //   </button>
    // );
  } else if ((isPlayerAdded || isReservePlayerSelected) && teamCreated && !isReserve) {
    button = (
      <button
        className="bg-[#1C9A6C] w-6 h-full text-white cursor-default flex flex-col justify-center items-center bg-[#B5DBCD]"
        disabled
      >
        <DoneArrow />
      </button>
    );
  } else if (isPlayerAdded || isReservePlayerSelected) {
    button = (
      <button
        onClick={handleRemovePlayer}
        className="bg-[#D84727] w-6 h-full text-white"
        disabled={readOnly}
      >
        <Minus />
      </button>
    );
  } else {
    const isButtonDisabled = isReserve
      ? isReservePlayerLimitReached
      : playersByRole[activeTab]?.length === playerTypeLimits[activeTab] ||
      isPlayerSelected ||
      readOnly;

    button = (
      <button
        onClick={handleAddPlayer}
        className="bg-[#1C9A6C] w-6 min-h-full text-white disabled:cursor-not-allowed disabled:bg-[#C9C9C9]"
        disabled={isButtonDisabled}
      >
        <Plus />
      </button>
    );
  }

  return (
    <div className="grid md:grid-cols-[10%_30%_70%] grid-cols-[auto_auto_auto] w-full gap-x-2">
      {!isLive && (
        <div className="h-full w-6 justify-start cursor-pointer relative">
          {button}
        </div>
      )}
      <div className="flex justify-center items-center flex-col">
        <div>{playerRank}</div>
        <PlayerAvatar avatarUrl={player?.image ?? getDefaultProfileImage()} />
      </div>
      <div className="flex justify-start space-x-1">
        <div className="flex flex-col justify-start items-start">
          <Tooltip
            content={player?.name}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            <div className="flex items-center gap-2">
              <p className="truncate">{getShortName(player?.name)}</p>
              <span className="text-[9px] text-gray-500">
                {player.role === 'Defender' && 'DEF'}
                {player.role === 'Forward' && 'FW'}
                {player.role === 'Goalkeeper' && 'GOAL-KEEPER'}
                {player.role === 'Midfielder' && 'MID'}
                {player.role === 'Ruck' && 'RUCK'}
              </span>
            </div>
          </Tooltip>
          <Tooltip
            content={player?.teamName}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            <div className="text-xs text-gray-500 truncate max-w-[80px]">
              {player?.teamName}
            </div>
          </Tooltip>
          <PlayerValueChange
            formatToCustomStyle={formatNumberWithCommas}
            playerCurrentSalary={player?.scoreData?.playerCurrentSalary}
            playerLastSalary={player?.scoreData?.playerLastSalary}
            showCalculatedDifference
          />
        </div>
      </div>
    </div>
  );
};

export const renderSoccerPlayerCell = ({
  player,
  activeTab,
  removePlayer,
  addPlayer,
  playerTypeLimits,
  playerRank,
  isPlayerSelected,
  readOnly,
  teamCreated,
  isLive,
  row,
  isValidTeam,
  soccerPlayersByRole,
  canAdd,
}: {
  player: SoccerPlayer;
  activeTab: SoccerPlayerRole;

  playerTypeLimits: Record<SoccerPlayerRole, number>;
  playerRank: number;
  isPlayerSelected: boolean;
  readOnly?: boolean;
  teamCreated?: boolean;
  isLive?: boolean;
  row?: Row<SoccerPlayer>;
  isValidTeam?: boolean;
  addPlayer: (player: SoccerPlayer, role: SoccerPlayerRole) => void;
  removePlayer: (player: SoccerPlayer, role: SoccerPlayerRole) => void;
  soccerPlayersByRole: SoccerPlayersByRole;
  canAdd: boolean;
}) => {
  let button;
  const isPlayerAdded = soccerPlayersByRole[activeTab]?.some(
    (statePlayer) => statePlayer?.playerId === player?.playerId,
  );

  if (isLive) {
    // button = (
    //   <button
    //     className="flex flex-col justify-center items-center h-full"
    //     onClick={() => row?.toggleExpanded()} // Only called when clicked
    //     disabled
    //   >
    //     {row?.getIsExpanded() ? <UpArrow /> : <DownArrow />}
    //   </button>
    // );
  } else if (isPlayerAdded && teamCreated) {
    button = (
      <button
        className="bg-[#1C9A6C] w-6 h-full text-white cursor-default flex flex-col justify-center items-center bg-[#B5DBCD]"
        disabled
      >
        <DoneArrow />
      </button>
    );
  } else if (isPlayerAdded && !isLive) {
    button = (
      <button
        onClick={() => removePlayer(player, activeTab)}
        className="bg-[#D84727] w-6 h-full text-white"
        disabled={readOnly}
      >
        <Minus />
      </button>
    );
  } else {
    button = (
      <button
        onClick={() => addPlayer(player, activeTab)}
        className="bg-[#1C9A6C] w-6 min-h-full text-white disabled:cursor-not-allowed disabled:bg-[#C9C9C9]"
        disabled={
          soccerPlayersByRole[activeTab]?.length ===
          playerTypeLimits[activeTab] ||
          isPlayerSelected ||
          readOnly ||
          isValidTeam ||
          !canAdd
        }
      >
        <Plus />
      </button>
    );
  }

  return (
    <div
      className="grid w-full gap-x-2"
      style={{ gridTemplateColumns: '30px 30px auto' }}
    >
      {!isLive && (
        <div className="h-full w-6 justify-start cursor-pointer relative">
          {button}
        </div>
      )}
      <div className="flex justify-center items-center flex-col">
        <div>{playerRank}</div>
        <PlayerAvatar avatarUrl={player?.image ?? getDefaultProfileImage()} />
      </div>
      <div className="flex justify-start space-x-1">
        <div className="flex flex-col justify-start items-start">
          <Tooltip
            content={player?.name}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            <div className="flex items-center gap-2">
              <p className="truncate w-20 md:w-full">{player?.name}</p>
              <span className="text-[9px] text-gray-500">
                {player.role === 'DEF' && 'DEF'}
                {player.role === 'FWD' && 'FW'}
                {player.role === 'GKP' && 'GKP'}
                {player.role === 'MID' && 'MID'}
              </span>
            </div>
          </Tooltip>
          <Tooltip
            content={player?.teamName}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            <div className="text-xs text-gray-500 truncate max-w-[80px]">
              {player?.teamName}
            </div>
          </Tooltip>
          <PlayerValueChange
            formatToCustomStyle={formatNumberWithCommas}
            playerCurrentSalary={player?.scoreData?.playerCurrentSalary}
            playerLastSalary={player?.scoreData?.playerLastSalary}
            showCalculatedDifference
          />
        </div>
      </div>
    </div>
  );
};

export const renderRugbyPlayerCell = ({
  player,
  activeTab,
  removePlayer,
  addPlayer,
  playersByRole,
  playerTypeLimits,
  playerRank,
  isPlayerSelected,
  readOnly,
  isLive,
  teamCreated,
  row,
  reservePlayersLimit,
  reservePlayers,
  isReserve,
  addReservePlayer,
  removeReservePlayer,
  activePlayerPosition,
  setActivePlayerPosition
}: {
  player: RugbyPlayer;
  activeTab: keyof RugbyLeaguePlayersByRole;
  removePlayer: (id: number, tab: keyof RugbyLeaguePlayersByRole) => void;
  addPlayer: (player: RugbyPlayer, tab: keyof RugbyLeaguePlayersByRole) => void;
  playersByRole: Record<string, RugbyPlayer[]>;
  playerTypeLimits: Record<string, number>;
  playerRank: number;
  isPlayerSelected: boolean;
  readOnly?: boolean;
  isLive?: boolean;
  teamCreated?: boolean;
  row?: Row<RugbyPlayer>;
  reservePlayersLimit: number;
  reservePlayers: (RugbyPlayer | null)[];
  isReserve?: boolean;
  addReservePlayer: (player: RugbyPlayer, position: number) => void;
  removeReservePlayer: (playerId: number, position: number) => void;
  activePlayerPosition: number;
  setActivePlayerPosition: (position: number) => void;
}) => {
  let button;
  const isPlayerAdded = playersByRole[activeTab]?.some(
    (statePlayer) => statePlayer?.playerId === player?.playerId,
  );

  const isReservePlayerLimitReached = reservePlayers.filter((player) => player !== null).length === reservePlayersLimit;

  const isReservePlayerAdded = reservePlayers.some((reservePlayer) => reservePlayer?.playerId === player?.playerId);

  const handleAddPlayer = () => {
    if (isReserve) {
      // Find the first empty position from the beginning
      const firstEmptyPosition = reservePlayers.findIndex((p) => p === null);

      // If no empty position found, don't add player
      if (firstEmptyPosition === -1) return;

      // Add player at the first empty position
      addReservePlayer(player, firstEmptyPosition);

      // Find the next empty position for future additions
      const nextEmptyPosition = reservePlayers.findIndex((p, i) => i > firstEmptyPosition && p === null);

      if (nextEmptyPosition !== -1) {
        setActivePlayerPosition(nextEmptyPosition);
      } else {
        // No more empty positions, reset to 0
        setActivePlayerPosition(0);
      }
    } else {
      addPlayer(player, activeTab);
    }
  };

  const handleRemovePlayer = () => {
    if (isReserve) {
      // Find the index of the removed player
      const removedIndex = reservePlayers.findIndex(
        (p) => p?.playerId === player?.playerId
      );

      // Remove player with position
      removeReservePlayer(player?.playerId, removedIndex !== -1 ? removedIndex : 0);

      // After removal, set the active position to the position that was just vacated
      if (removedIndex !== -1) {
        setActivePlayerPosition(removedIndex);
      } else {
        // Fallback: find the first empty position
        const firstEmptyPosition = reservePlayers.findIndex((p) => p === null);
        if (firstEmptyPosition !== -1) {
          setActivePlayerPosition(firstEmptyPosition);
        } else {
          setActivePlayerPosition(0);
        }
      }
    } else {
      removePlayer(player?.playerId, activeTab);
    }
  };


  if (isLive) {
    // button = (
    //   <button
    //     className="flex flex-col justify-center items-center h-full"
    //     onClick={() => row?.toggleExpanded()} // Only called when clicked
    //   >
    //     {row?.getIsExpanded() ? <UpArrow /> : <DownArrow />}{' '}
    //     {/* Check state instead of triggering toggle */}
    //   </button>
    // );
  } else if (isPlayerAdded && teamCreated) {
    button = (
      <button
        className={cn(
          'bg-[#1C9A6C] w-6 h-full text-white cursor-default flex flex-col justify-center items-center ',
          'bg-[#B5DBCD]',
        )}
        disabled
      >
        <DoneArrow />
      </button>
    );
  } else if (isPlayerAdded || isReservePlayerAdded) {
    button = (
      <button
        onClick={handleRemovePlayer}
        className="bg-[#D84727] w-6 h-full text-white"
        disabled={readOnly}
      >
        <Minus />
      </button>
    );
  } else {

    const isButtonDisabled = isReserve
      ? isReservePlayerLimitReached
      : playersByRole[activeTab]?.length === playerTypeLimits[activeTab] ||
      isPlayerSelected ||
      readOnly;
    button = (
      <button
        onClick={handleAddPlayer}
        className={cn(
          'bg-[#1C9A6C] w-6 min-h-full text-white disabled:cursor-not-allowed disabled:bg-[#C9C9C9]',
        )}
        disabled={isButtonDisabled}
      >
        <Plus />
      </button>
    );
  }

  return (
    <div className="grid md:grid-cols-[10%_30%_70%] grid-cols-[auto_auto_auto] w-full gap-x-2">
      {!isLive && (
        <div className="h-full w-6 justify-start cursor-pointer relative">
          {button}
        </div>
      )}
      <div className="flex justify-center items-center flex-col">
        <div>{playerRank}</div>
        <PlayerAvatar avatarUrl={player?.image ?? getDefaultProfileImage()} />
      </div>
      <div className="flex justify-start space-x-1">
        <div className="flex flex-col justify-start items-start">
          <div className="flex space-x-2">
            <Tooltip
              content={player?.name}
              placement="bottom"
              className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
            >
              <p className="truncate">{getShortName(player?.name)}</p>
            </Tooltip>
            <span className="text-[9px] text-gray-500">
              {NRLplayersRollName(player?.role)}
            </span>
          </div>
          <Tooltip
            content={player?.teamName}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            <div className="text-xs text-gray-500 truncate max-w-[80px]">
              {player?.teamName}
            </div>
          </Tooltip>
          <PlayerValueChange
            formatToCustomStyle={formatNumberWithCommas}
            playerCurrentSalary={player?.scoreData?.playerCurrentSalary}
            playerLastSalary={player?.scoreData?.playerLastSalary}
            showCalculatedDifference
          />
        </div>
      </div>
    </div>
  );
};

export const renderCricketReservedPlayerCell = ({
  player,
  activeTab,
  removePlayer,
  addPlayer,
  playersByRole,
  playerTypeLimits,
  playerRank,
  isPlayerSelected,
  readOnly,
  isLive,
  teamCreated,
  row,
}: {
  player: CricketPlayer;
  activeTab: CricketPlayerRole;
  removePlayer: (id: number) => void;
  addPlayer: (player: CricketPlayer) => void;
  playersByRole: CricketPlayer[];
  playerTypeLimits: number;
  playerRank: number;
  isPlayerSelected: boolean;
  readOnly?: boolean;
  isLive?: boolean;
  teamCreated?: boolean;
  row?: Row<CricketPlayer>;
}) => {
  let button;
  const isPlayerAdded = playersByRole.some(
    (statePlayer) => statePlayer?.playerId === player?.playerId,
  );

  if (isLive) {
    // button = (
    //   <button
    //     className="flex flex-col justify-center items-center h-full"
    //     onClick={() => row?.toggleExpanded()} // Only called when clicked
    //   >
    //     {row?.getIsExpanded() ? <UpArrow /> : <DownArrow />}{' '}
    //     {/* Check state instead of triggering toggle */}
    //   </button>
    // );
  } else if (isPlayerAdded && teamCreated) {
    button = (
      <button
        className={cn(
          'bg-[#1C9A6C] w-6 h-full text-white cursor-default flex flex-col justify-center items-center ',
          'bg-[#B5DBCD]',
        )}
        disabled
      >
        <DoneArrow />
      </button>
    );
  } else if (isPlayerAdded) {
    button = (
      <button
        onClick={() => removePlayer(player?.playerId)}
        className="bg-[#D84727] w-6 h-full text-white"
        disabled={readOnly}
      >
        <Minus />
      </button>
    );
  } else {


    button = (
      <button
        onClick={() => addPlayer(player)}
        className={cn(
          'bg-[#1C9A6C] w-6 min-h-full text-white disabled:cursor-not-allowed disabled:bg-[#C9C9C9]',
        )}
        disabled={
          playersByRole.length === playerTypeLimits ||
          isPlayerSelected ||
          readOnly
        }
      >
        <Plus />
      </button>
    );
  }

  const playerImageSrc = Config.mediaURL! + player?.image;

  return (
    <div
      className="grid w-full gap-x-2"
      style={{ gridTemplateColumns: '30px 60px auto' }}
    >
      {!isLive && (
        <div className="h-full w-6 justify-start cursor-pointer relative">
          {button}
        </div>
      )}
      <div className="flex justify-center items-center flex-col px-2">
        <Image
          src={
            player.image ? playerImageSrc : PUBLIC_IMAGES.DEFAULT_PLAYER_IMAGE
          }
          alt="default player image"
          width={100}
          height={100}
        />
      </div>
      <div className="flex justify-start space-x-1">
        <div className="flex flex-col justify-start items-start">
          <Tooltip
            content={player?.name}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            <div className="flex items-center gap-2">
              <p className="truncate">{getShortName(player?.name)}</p>
              <span className="text-[9px] text-gray-500">
                {player.role === 'Batter' && 'BAT'}
                {player.role === 'WK-Batter' && 'WKP'}
                {player.role === 'Bowler' && 'BOW'}
                {player.role === 'Batting Allrounder' && 'ALL'}
              </span>
            </div>
          </Tooltip>
          <Tooltip
            content={player?.teamName}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            <div className="text-xs text-gray-500 truncate max-w-[80px]">
              {player?.teamName}
            </div>
          </Tooltip>
          <PlayerValueChange
            formatToCustomStyle={formatNumberWithCommas}
            playerCurrentSalary={player?.scoreData?.playerCurrentSalary}
            playerLastSalary={player?.scoreData?.playerLastSalary}
            showCalculatedDifference
          />
        </div>
      </div>
    </div>
  );
};

export const renderCricketPlayerCell = ({
  player,
  activeTab,
  removePlayer,
  addPlayer,
  playersByRole,
  playerTypeLimits,
  playerRank,
  isPlayerSelected,
  readOnly,
  isLive,
  teamCreated,
  row,
  isReserve = false,
  addReservePlayer,
  removeReservePlayer,
  isReservePlayerSelected,
  isReservePlayerLimitReached,
  activePlayerPosition,
  setActivePlayerPosition,
  reservePlayersLimit,
  reservePlayers
}: {
  player: Player;
  activeTab: keyof PlayersByRole;
  removePlayer: (id: number, tab: keyof PlayersByRole) => void;
  addPlayer: (player: Player, tab: keyof PlayersByRole) => void;
  playersByRole: Record<string, Player[]>;
  playerTypeLimits: Record<string, number>;
  playerRank: number;
  isPlayerSelected: boolean;
  readOnly?: boolean;
  isLive?: boolean;
  teamCreated?: boolean;
  row?: Row<Player>;
  isReserve?: boolean;
  addReservePlayer: (player: Player, position: number) => void;
  removeReservePlayer: (playerId: number, position: number) => void;
  isReservePlayerSelected: boolean;
  isReservePlayerLimitReached: boolean;
  activePlayerPosition: number;
  setActivePlayerPosition: (position: number) => void;
  reservePlayersLimit: number;
  reservePlayers: (Player | null)[];
}) => {
  let button;
  const isPlayerAdded = playersByRole[activeTab]?.some(
    (statePlayer) => statePlayer?.playerId === player?.playerId,
  );

  const handleAddPlayer = () => {
    if (isReserve) {
      // Find the first empty position from the beginning
      const firstEmptyPosition = reservePlayers.findIndex((p) => p === null);

      // If no empty position found, don't add player
      if (firstEmptyPosition === -1) return;

      // Add player at the first empty position
      addReservePlayer(player, firstEmptyPosition);

      // Find the next empty position for future additions
      const nextEmptyPosition = reservePlayers.findIndex((p, i) => i > firstEmptyPosition && p === null);

      if (nextEmptyPosition !== -1) {
        setActivePlayerPosition(nextEmptyPosition);
      } else {
        // No more empty positions, reset to 0
        setActivePlayerPosition(0);
      }
    } else {
      addPlayer(player, activeTab);
    }
  };

  const handleRemovePlayer = () => {
    if (isReserve) {
      // Find the index of the removed player
      const removedIndex = reservePlayers.findIndex(
        (p) => p?.playerId === player?.playerId
      );

      // Remove player with position
      removeReservePlayer(player?.playerId, removedIndex !== -1 ? removedIndex : 0);

      // After removal, set the active position to the position that was just vacated
      if (removedIndex !== -1) {
        setActivePlayerPosition(removedIndex);
      } else {
        // Fallback: find the first empty position
        const firstEmptyPosition = reservePlayers.findIndex((p) => p === null);
        if (firstEmptyPosition !== -1) {
          setActivePlayerPosition(firstEmptyPosition);
        } else {
          setActivePlayerPosition(0);
        }
      }
    } else {
      removePlayer(player?.playerId, activeTab);
    }
  };

  if (isLive) {
    // button = (
    //   <button
    //     className="flex flex-col justify-center items-center h-full"
    //     onClick={() => row?.toggleExpanded()} // Only called when clicked
    //   >
    //     {row?.getIsExpanded() ? <UpArrow /> : <DownArrow />}{' '}
    //     {/* Check state instead of triggering toggle */}
    //   </button>
    // );
  } else if ((isPlayerAdded || isReservePlayerSelected) && teamCreated && !isReserve) {
    button = (
      <button
        className={cn(
          'bg-[#1C9A6C] w-6 h-full text-white cursor-default flex flex-col justify-center items-center ',
          'bg-[#B5DBCD]',
        )}
        disabled
      >
        <DoneArrow />
      </button>
    );
  } else if (isPlayerAdded || isReservePlayerSelected) {
    button = (
      <button
        onClick={handleRemovePlayer}
        className="bg-[#D84727] w-6 h-full text-white"
        disabled={readOnly}
      >
        <Minus />
      </button>
    );
  } else {
    const isButtonDisabled = isReserve
      ? isReservePlayerLimitReached
      : playersByRole[activeTab]?.length === playerTypeLimits[activeTab] ||
      isPlayerSelected ||
      readOnly;

    button = (
      <button
        onClick={handleAddPlayer}
        className={cn(
          'bg-[#1C9A6C] w-6 min-h-full text-white disabled:cursor-not-allowed disabled:bg-[#C9C9C9]',
        )}
        disabled={isButtonDisabled}
      >
        <Plus />
      </button>
    );
  }

  return (
    <div
      className="grid w-full gap-x-2"
      style={{ gridTemplateColumns: '30px 30px auto' }}
    >
      {!isLive && (
        <div className="h-full w-6 justify-start cursor-pointer relative">
          {button}
        </div>
      )}
      <div className="flex justify-center items-center flex-col px-2">
        <div>{playerRank}</div>
        <PlayerAvatar avatarUrl={player?.image ?? getDefaultProfileImage()} />
      </div>
      <div className="flex justify-start space-x-1">
        <div className="flex flex-col justify-start items-start">
          <Tooltip
            content={player?.name}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            <div className="flex items-center gap-2">
              <p className="truncate">{getShortName(player?.name)}</p>
              <span className="text-[9px] text-gray-500">
                {player.role === 'Batter' && 'BAT'}
                {player.role === 'WK-Batter' && 'WKP'}
                {player.role === 'Bowler' && 'BOW'}
                {player.role === 'Batting Allrounder' && 'ALL'}
              </span>
            </div>
          </Tooltip>
          <Tooltip
            content={player?.teamName}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            <div className="text-xs text-gray-500 truncate max-w-[80px]">
              {player?.teamName}
            </div>
          </Tooltip>
          <PlayerValueChange
            formatToCustomStyle={formatNumberWithCommas}
            playerCurrentSalary={player?.scoreData?.playerCurrentSalary}
            playerLastSalary={player?.scoreData?.playerLastSalary}
            showCalculatedDifference
          />
        </div>
      </div>
    </div>
  );
};

export const renderCricketLivePlayerCell = (player: Player) => {
  const searchParams = useSearchParams();
  const sport_id = searchParams.get('sport_id');
  const { eventDetailsResponse } = useCompetition();
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  return (
    <div className="grid md:grid-cols-[30%_30%_40%] grid-cols-[20%_60%_20%] w-full gap-x-2">
      <div className="flex justify-center items-center">
        <PlayerAvatar avatarUrl={player?.image ?? getDefaultProfileImage()} />
      </div>

      <div className="flex justify-start">
        <div className="flex flex-col justify-start items-start">
          <p className="truncate ... w-[100px] text-left">{player?.name}</p>
          <div className="text-xs text-gray-500 truncate ... w-[70x]">
            {player?.teamName}
          </div>

          <PlayerValueChange
            formatToCustomStyle={formatNumberWithCommas}
            playerCurrentSalary={
              eventStatus === 'upcoming'
                ? (player?.scoreData?.playerCurrentSalary ?? 0)
                : (player?.playerValue ?? 0)
            }
            playerLastSalary={
              eventStatus === 'upcoming'
                ? (player?.scoreData?.playerLastSalary ?? 0)
                : (player?.scoreData?.playerCurrentSalary ?? 0)
            }
          />
        </div>
        <span className="text-[9px] text-gray-500">
          {sport_id === '12'
            ? getPlayerRole(player?.role)
            : (player?.role === 'Batter' && 'BAT') ||
            (player?.role === 'WK-Batter' && 'WKP') ||
            (player?.role === 'Bowler' && 'BOW') ||
            (player?.role === 'Batting Allrounder' && 'ALL')}
        </span>

        {player?.positionType === 'captain' && (
          <div className="ml-2 p-3 w-5 h-5 bg-[#FC4714] flex flex-col justify-center items-center text-white rounded-full">
            C
          </div>
        )}

        {player?.positionType === 'viceCaptain' && (
          <div className="ml-2 w-5 h-5 p-3 bg-[#003764] flex flex-col justify-center items-center text-white rounded-full">
            VC
          </div>
        )}
      </div>
    </div>
  );
};

const SortHeader = ({
  column,
  label,
  align = 'center'
}: {
  column: Column<any, unknown>;
  label: string;
  align?: 'center' | 'start' | 'end';
}) => (
  <button
    className={cn(`w-full h-full flex items-center justify-${align} text-xs font-bold space-x-1`)}
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white leading-tight">{label}</span>
    <div className="flex flex-col items-center justify-center">
      <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
    </div>
  </button>
);

export const renderFootballSortHeader = (
  column: Column<FootballPlayer, unknown>,
  label: string,
  align?: 'center' | 'start' | 'end',
) => <SortHeader column={column} label={label} align={align} />;

export const renderRugbySortHeader = (
  column: Column<RugbyPlayer, unknown>,
  label: string,
  align?: 'center' | 'start' | 'end',
) => <SortHeader column={column} label={label} align={align} />;

export const renderCricketSortHeader = (
  column: Column<Player, unknown>,
  label: string,
  align?: 'center' | 'start' | 'end',
) => <SortHeader column={column} label={label} align={align} />;

export const renderCricketColumnSortHeader = (
  column: Column<CricketPlayer, unknown>,
  label: string,
) => <SortHeader column={column} label={label} />;

export const renderTeamsSortHeader = (
  column: Column<Team, unknown>,
  label: string,
  align?: 'center' | 'start' | 'end',
) => <SortHeader column={column} label={label} align={align} />;
