import React, { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import MenuArrow from '@/assets/images/icons/menuArrow.svg';
import { identifiers } from '@/helpers/constants/identifier';
import { Token } from '../../../db/db';
import SmartPlayLogo from '../UI/Icons/SmartPlayLogo';
import { generateUniqueId } from '@/lib/utils';
import LiveIcon from '../UI/Icons/LiveIcon';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { useUserProfileContext } from '@/helpers/context/userContext';
import { useAuthContext } from '@/helpers/context/authContext';

type MenuItem = {
  name: string;
  submenu?: MenuItem[];
  url?: string;
};

type SubMenuListProps = {
  items: MenuItem[];
  parentIndex: number;
  openMenuIndex: number | null;
  handleMenuEnter: (index: number) => void;
  handleMenuLeave: () => void;
  handleNavigate: (item: MenuItem) => void;
  isActiveMenuItem: (item: MenuItem) => boolean;
};

const SubMenuList: React.FC<SubMenuListProps> = ({
  items,
  parentIndex,
  openMenuIndex,
  handleMenuEnter,
  handleMenuLeave,
  handleNavigate,
  isActiveMenuItem,
}) => {
  return (
    <button
      className={`absolute left-5 top-full ${openMenuIndex === parentIndex ? 'block' : 'hidden'} bg-white rounded-lg shadow-menu_shedow z-10 min-w-[200px] mt-4 p-[9px]`}
      onMouseEnter={() => handleMenuEnter(parentIndex)}
      onMouseLeave={handleMenuLeave}
    >
      {items?.map((sub, i) => (
        <div key={generateUniqueId()} className="relative group/submenu">
          <button
            className={`flex justify-start items-center text-lg leading-[21px] max-1024:text-[11.42px] max-1024:leading-4 font-semibold p-[9px] max-1024:p-2 text-primary-200 rounded-lg cursor-pointer hover:bg-menu_active_bg ${isActiveMenuItem(sub) ? 'bg-menu_active_bg' : ''}`}
            onClick={() => handleNavigate(sub)}
          >
            {sub?.name}
            {sub?.submenu && <MenuArrow className="h-4 w-4" />}
          </button>
          {sub?.submenu && (
            <SubMenuList
              items={sub?.submenu}
              parentIndex={parentIndex}
              openMenuIndex={openMenuIndex}
              handleMenuEnter={handleMenuEnter}
              handleMenuLeave={handleMenuLeave}
              handleNavigate={handleNavigate}
              isActiveMenuItem={isActiveMenuItem}
            />
          )}
        </div>
      ))}
    </button>
  );
};

type HeaderMenuListProps = {
  handleNavigate: (item: MenuItem) => void;
  isActiveMenuItem: (item: MenuItem) => boolean;
};

const HeaderMenuList: React.FC<HeaderMenuListProps> = ({
  handleNavigate,
  isActiveMenuItem,
}) => {
  const [menuItems, setMenuItems] = useState<MenuItem[]>(
    identifiers?.publicSportsFantasyMenu,
  );
  const [openMenuIndex, setOpenMenuIndex] = useState<number | null>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { user } = useUserProfileContext();
  const { setLoginPopUp } = useAuthContext();

  const handleMenuEnter = (index: number) => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
    }
    setOpenMenuIndex(index);
  };

  const handleLogin = () => {
    setLoginPopUp(true);
  };

  const handleMenuLeave = () => {
    closeTimeoutRef.current = setTimeout(() => {
      setOpenMenuIndex(null);
    }, 300);
  };

  const token = Token;

  useEffect(() => {
    const filterMenu = token
      ? identifiers?.privateSportsFantasyMenu
      : identifiers?.publicSportsFantasyMenu;
    setMenuItems(filterMenu);
  }, [token]);

  return (
    <div className="flex justify-center items-center flex-wrap">
      <div className="flex items-center w-max">
        <Link href={'/'}>
          <SmartPlayLogo />
        </Link>
      </div>

      {menuItems?.map((item, index) => (
        <div key={generateUniqueId()} className="relative group w-fit">
          <div className="flex justify-center items-center">
            <button
              className={`flex justify-start items-center w-max text-lg leading-[21px] max-1600:text-sm max-1024:text-[11.42px] max-1024:leading-4 font-semibold ml-[18px] max-1600:ml-2 p-[9px] max-1024:p-2 text-primary-200 rounded-lg cursor-pointer hover:bg-menu_active_bg ${isActiveMenuItem(item) ? 'bg-menu_active_bg' : ''}`}
              onClick={() => {
                return router.push(item?.url ?? '');
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  const params = new URLSearchParams(searchParams.toString());
                  params.set('status', '2');
                  router.push(`${pathname}?${params.toString()}`);
                }
              }}
              tabIndex={0} // Ensure the button is focusable
            >
              {item?.name}
              {item?.submenu && item?.submenu?.length > 0 && (
                <MenuArrow className="ml-1" />
              )}
            </button>
            {item?.submenu && item?.submenu?.length > 0 && (
              <SubMenuList
                items={item.submenu}
                parentIndex={index}
                openMenuIndex={openMenuIndex}
                handleMenuEnter={handleMenuEnter}
                handleMenuLeave={handleMenuLeave}
                handleNavigate={handleNavigate}
                isActiveMenuItem={isActiveMenuItem}
              />
            )}
          </div>
        </div>
      ))}
      <button
        onClick={() => {
          if (user) {
            const url = '/my-competitions?status=2&compType=my';
            router.push(url);
          } else {
            handleLogin();
          }
        }}
        className="px-4"
      >
        <LiveIcon />
      </button>
    </div>
  );
};

export default HeaderMenuList;
