'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import type { Control } from 'react-hook-form';
import { Controller } from 'react-hook-form';
import AsyncSelect from 'react-select/async';
import { useDebounce } from 'use-debounce';
import { components } from 'react-select';
import { Check } from 'lucide-react';

import { Config } from '@/helpers/context/config';

interface Team {
  id: string | number;
  name: string;
}

interface Option {
  id: string | number;
  label: string;
  value?: string | number;
}

const fetchTeams = async (
  offset: number,
  search: string,
  sportId: number | null,
  tournamentId?: string,
  status?: string,
): Promise<Option[]> => {
  if (!sportId) {
    return [];
  }

  try {
    const res = await fetch(
      Config.fantasyURL +
      `/team?sportId=${sportId}&limit=20&offset=${offset}&search=${search}&tournamentId=${tournamentId}&status=${status}`,
    );
    if (!res.ok) {
      throw new Error('Network response was not ok');
    }
    const data = await res.json();

    return (
      data?.result?.map((team: Team) => ({
        id: team.id,
        label: team.name,
        value: team.id,
      })) || []
    );
  } catch (error) {
    console.error('Error fetching teams:', error);
    throw error;
  }
};

interface TeamDropDownProps {
  name: string;
  control: Control<any>;
  sportId: number;
  placeholder: string;
  className?: string;
  value: Option[] | null;
  setValue: Dispatch<SetStateAction<Option[] | null>>;
  styles?: any;
  tournamentId?: string;
  status: string | null;
}

const TeamDropDown = ({
  name,
  control,
  sportId,
  placeholder,
  className,
  value,
  setValue,
  styles,
  tournamentId,
  status,
}: TeamDropDownProps) => {
  const [inputValue, setInputValue] = useState('');
  const [debouncedSearch] = useDebounce(inputValue, 300);
  const [offset, setOffset] = useState(0);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isPending,
    isLoading,
  } = useInfiniteQuery({
    queryKey: ['async-teams', debouncedSearch, sportId, tournamentId, status],
    queryFn: ({ pageParam = 0 }) => {
      let stringStatus = '';
      if (status === '1') {
        stringStatus = 'upcoming';
      } else if (status === '2') {
        stringStatus = 'inprogress';
      } else if (status === '3') {
        stringStatus = 'finished';
      }
      return fetchTeams(
        pageParam,
        debouncedSearch,
        sportId,
        tournamentId,
        stringStatus,
      );
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage || lastPage.length < 20) return undefined;
      return allPages.length * 20;
    },
    enabled: !!sportId && !!tournamentId,
    staleTime: 30000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  useEffect(() => {
    setOffset(0);
  }, [debouncedSearch]);

  const loadOptions = async (inputValue: string): Promise<Option[]> => {
    let stringStatus = '';
    if (status === '1') {
      stringStatus = 'upcoming';
    } else if (status === '2') {
      stringStatus = 'inprogress';
    } else if (status === '3') {
      stringStatus = 'completed';
    }
    const result = await fetchTeams(
      0,
      inputValue,
      sportId,
      tournamentId,
      stringStatus,
    );
    return result;
  };

  const customStyles = {
    ...styles,
    valueContainer: (base: any) => ({
      ...base,
      padding: '2px 8px',
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected ? '#78C2A7' : provided.backgroundColor,
      color: state.isSelected ? '#FFFFFF !important' : '#000000',
      '&:hover': {
        backgroundColor: '#78C2A7',
        color: '#FFFFFF !important',
      },
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
    }),
    control: (base: any) => ({
      ...base,
      minHeight: '42px',
    }),
  };

  const Option = (props: any) => (
    <components.Option {...props}>
      <span>{props.label}</span>
      {props.isSelected && <Check size={16} className="text-white" />}
    </components.Option>
  );

  return (
    <div className="w-full">
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <AsyncSelect
            isMulti={true}
            isDisabled={!sportId}
            className={className}
            classNamePrefix="react-select"
            isLoading={isLoading || isFetchingNextPage}
            {...field}
            styles={customStyles}
            components={{ Option }}
            placeholder={placeholder}
            onInputChange={(value) => setInputValue(value)}
            loadOptions={loadOptions}
            defaultOptions={(sportId && data?.pages?.flat()) || []}
            onChange={(selectedTeams) => {
              const selectedIds = selectedTeams
                ?.map((team) => team.id)
                .join(',');
              field.onChange(selectedIds || null);
              setValue(selectedTeams as any);
            }}
            value={value}
            onMenuScrollToBottom={() => {
              if (hasNextPage && !isFetchingNextPage) {
                fetchNextPage();
              }
            }}
            hideSelectedOptions={false}
            isClearable={false}
          />
        )}
      />
    </div>
  );
};

export default TeamDropDown;
