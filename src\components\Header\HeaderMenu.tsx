'use client';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import MenuSelectArrowIcon from '@/assets/images/icons/menu_selected_arow.svg';
import SmartBCoins from '@/assets/images/settings/singleCoins.png';
import CoinsPlus from '@/assets/images/icons/coinsPlus.png';
import MenuIcon from '@/assets/images/icons/white_menu_icon.svg';
import Logo from '@/assets/images/logo.svg';
import sportsLeagueLogo from '@/assets/images/mobileSmartPlayLogo.png';
import { identifiers } from '@/helpers/constants/identifier';
import { Config } from '@/helpers/context/config';
import { useUserProfileContext } from '@/helpers/context/userContext';
import { generateUniqueId, LocalStorage } from '@/lib/utils';

import { Button } from '../UI/button';
import HeaderMenuList from './HeaderMenuList';
import MobileHeaderMenu from './MobileHeaderMenu';
import ProfileIconMenu from './profileIconMenu';
import LiveIcon from '../UI/Icons/LiveIcon';
import { useAuthContext } from '@/helpers/context/authContext';
import { useFantasyUser } from '@/helpers/context/fantasyUserContext';

const HeaderMenu = () => {
  const pathname = usePathname();
  const router = useRouter();
  const [menuItems, setMenuItems] = useState<any>(
    identifiers?.publicSportsFantasyMenu,
  );
  const [isOpen, setIsOpen] = useState(false);

  // toggle menu
  const toggleMenu = () => setIsOpen(!isOpen);

  // Check active menu
  const isActiveMenuItem = (item: any) => {
    // For My Competitions, check if we're on any competition-related page
    if (item?.name === 'My Competitions') {
      return pathname.startsWith('/my-competitions');
    }
    // For other menu items, check exact URL match
    return item?.url === pathname;
  };

  // Navigate menu
  const handleNavigate = (item: any) => {
    router.push(item?.url);
    setIsOpen(false);
  };

  const { user } = useUserProfileContext();
  const { user: fantasyUser } = useFantasyUser();
  const { setLoginPopUp } = useAuthContext();

  const handleLogin = () => {
    setLoginPopUp(true);
  };

  useEffect(() => {
    const filterMenu = user?.username
      ? identifiers?.privateSportsFantasyMenu
      : identifiers?.publicSportsFantasyMenu;
    setMenuItems(filterMenu);
  }, [user]);
  console.log("user", user, fantasyUser)
  return (
    <>
      {/* Desktop Menu */}
      <div
        className={`${user ? 'py-[5px]' : 'py-[10px]'} bg-white flex justify-between  items-center flex-wrap  px-[45px] max-1600:px-5 gap-4 max-1280:order-1 max-799:hidden`}
      >
        <div className="max-1280:w-1/2">
          <Link href={Config.siteBaseURL! ?? ''}>
            <Logo />
          </Link>
        </div>
        <div className="max-1280:order-3 max-1280:w-full">
          <HeaderMenuList
            handleNavigate={handleNavigate}
            isActiveMenuItem={isActiveMenuItem}
          />
        </div>
        <div className="flex items-center gap-x-[18px] max-1280:order-2">
          {user ? (
            <>
              <div className='flex items-center gap-2 bg-primary-200 rounded-[8px] py-[7px] px-[9px] text-white cursor-pointer' onClick={() => router.push('/settings?buy_smart_b_coins=true')}>
                <Image
                  src={SmartBCoins}
                  alt="coins"
                  unoptimized={true}
                  className="w-[25px] h-[25px]"
                />
                <p className="text-[20.8px] leading-[25px] font-inter font-semibold text-white  flex flex-col justify-center items-center">
                  {fantasyUser?.coins
                    ? fantasyUser?.coins - (fantasyUser?.holdCoins ?? 0)
                    : 0}
                </p>
                <Image
                  src={CoinsPlus}
                  alt="coins"
                  unoptimized={true}
                  className="w-[30px] h-[30px]"
                />
              </div>
              <ProfileIconMenu />
            </>
          ) : (
            <div className="flex justify-start items-center gap-[9px]">
              <Button
                className="p-3 px-5 rounded-lg h-[44px] text-[16px] w-[91.69px]"
                style={{
                  fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
                  fontWeight: 'normal',
                }}
                onClick={() => {
                  const currentPath = window.location.href;
                  LocalStorage.setItem('redirect', { url: currentPath });
                  router.push(Config.siteBaseURL + 'sign-up');
                }}
              >
                Sign Up
              </Button>
              <Button
                onClick={() => {
                  const currentPath = window.location.href;
                  LocalStorage.setItem('redirect', { url: currentPath });
                  router.push(Config.siteBaseURL + 'sign-in');
                }}
                style={{ fontFamily: 'roboto', fontWeight: 'normal' }}
                className="bg-transparent border-secondary-100 w-[79px] text-secondary-100 border p-3 px-5 rounded-lg h-[44px] text-[16px]"
              >
                Log In
              </Button>
            </div>
          )}
        </div>
      </div >
      {/* Mobile Header Menu */}
      < MobileHeaderMenu />
      {/* Mobile Menu */}
      < div className="hidden max-799:block" >
        <div className="w-full mx-auto ">
          <header
            className={`bg-primary-200 py-1 px-4 flex justify-between items-center gap-[9px]`}
          >
            <div className='flex justify-start items-center gap-[9px] '>

              <button onClick={toggleMenu} className="text-white">
                <MenuIcon size={24} />
              </button>
              <div className="flex items-center w-max">
                <Link href={'/'} onClick={() => setIsOpen(false)}>
                  <Image
                    src={sportsLeagueLogo}
                    alt="sport league logo"
                    className="h-[25px] w-full"
                    priority
                    unoptimized={true}
                  />
                </Link>
              </div>
            </div>
            <div className='flex items-center gap-2 bg-[#335F83] rounded-[8px] py-[7px] px-[9px] text-white' onClick={() => router.push('/settings?buy_smart_b_coins=true')}>
              <Image
                src={SmartBCoins}
                alt="coins"
                unoptimized={true}
                className="w-[15px] h-[15px]"
              />
              <p className="text-[14px] leading-[17px] font-inter font-semibold text-white  flex flex-col justify-center items-center">
                {fantasyUser?.coins
                  ? fantasyUser?.coins - (fantasyUser?.holdCoins ?? 0)
                  : 0}
              </p>
              <Image
                src={CoinsPlus}
                alt="coins"
                unoptimized={true}
                className="w-[17px] h-[17px]"
              />
            </div>
          </header>
          <nav
            className={`absolute z-[99] w-full bg-blue-gradient overflow-hidden transition-all duration-300 ease-in-out rounded-b-md ${isOpen ? 'max-h-screen' : 'max-h-0'
              }`}
          >
            <ul className="py-2 px-[6px]">
              {/* @ts-expect-error */}
              {menuItems?.map((item, index) => {
                return (
                  <button
                    className={`px-[9px] py-2 text-base leading-4 flex justify-between items-center rounded-md ${isActiveMenuItem(item) ? 'bg-white text-orange-500 font-semibold' : 'text-white'}`}
                    key={generateUniqueId()}
                    onClick={() => handleNavigate(item)}
                  >
                    {item?.name}
                    {isActiveMenuItem(item) && (
                      <MenuSelectArrowIcon
                        size={20}
                      // className="text-orange-500"
                      />
                    )}
                  </button>
                );
              })}
              <button
                onClick={() => {
                  if (user) {
                    const url = '/my-competitions?status=2&compType=my';
                    router.push(url);
                  } else {
                    handleLogin();
                  }
                }}
              >
                <LiveIcon />
              </button>
            </ul>
          </nav>
        </div>
      </div >
    </>
  );
};

export default HeaderMenu;
