import React, { useState } from 'react';
import { SoccerAPIPlayer } from '@/helpers/context/soccer/createSoccerTeamContext';
import Select, { components } from 'react-select';
import { cn } from '@/lib/utils';
import { useDataTable } from '@/hooks/useDataTable';
import { soccerPlayerColumns } from '@/components/UI/DataTabel/columns/soccerPlayerColumn';
import DataTable from '@/components/UI/DataTabel';
import useScreen from '@/hooks/useScreen';
import SimpleTabs from '@/components/UI/SimpleTabs';
import { Search, SlidersHorizontal, X } from 'lucide-react';
import { Input } from '@/components/UI/input';
import { SortingState } from '@tanstack/react-table';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { IconButton } from '@material-tailwind/react';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Card } from '@/components/UI/card';
import { formatNumberWithCommas } from '@/lib/utils';
import soccerStyle from './soccer.module.scss';
import SoccerPlayerFilter, { SoccerFiltersState } from './SoccerPlayerFilter';
import MobileSoccerPlayerFilter from './MobileSoccerPlayerFilter';
import { useSearchParams } from 'next/navigation';
import { usePlayerList } from '@/hooks/players/usePlayerList';
import { useSoccerStore } from '@/store/soccer/useSoccerStore';
import {
  SoccerPlayer,
  SoccerPlayerRole,
  SoccerPlayersByRole,
} from '@/lib/types/soccer';

const SoccerPlayerSelectionUI = () => {
  // Search Params
  const searchParams = useSearchParams();
  const add_more = searchParams.get('add_more');
  const eventId = searchParams.get('event_id');
  const tournamentId = searchParams.get('tournament_id');
  const sportId = searchParams.get('sport_id');
  const seasonId = searchParams.get('seasonId');

  const {
    setActiveTabPlayer,
    activeTabPlayer,
    soccerPlayersByRole,
    addPlayer,
    removePlayer,
    resetTeam,
    remainingBudget,
    totalBudget,
    playerByRoleLimit,
    showPlayerTable,
    setShowPlayerTable,
    showFilter,
    setShowFilter,
    showMobileFilter,
    setShowMobileFilter,
  } = useSoccerStore();

  const [filters, setFilters] = useState<SoccerFiltersState>({
    selectedTeam: 'all',
  });

  const { data: playerList } = usePlayerList<SoccerAPIPlayer>({
    eventId,
    tournamentId,
    sportId,
    seasonId,
  });

  // All Soccer Source Players
  const allSoccerSourcePlayers: SoccerPlayer[] =
    playerList?.result.map((player) => ({
      ...player,

      role:
        player.role === 'G'
          ? 'GKP'
          : player.role === 'D'
            ? 'DEF'
            : player.role === 'M'
              ? 'MID'
              : 'FWD',
    })) || [];

  // All Soccer Players By Role
  const allSoccerPlayersByRole: SoccerPlayersByRole = {
    GKP: allSoccerSourcePlayers.filter((player) => player.role === 'GKP'),
    DEF: allSoccerSourcePlayers.filter((player) => player.role === 'DEF'),
    MID: allSoccerSourcePlayers.filter((player) => player.role === 'MID'),
    FWD: allSoccerSourcePlayers.filter((player) => player.role === 'FWD'),
  };

  // Table
  const { table } = useDataTable({
    columns: soccerPlayerColumns,
    data: allSoccerPlayersByRole[activeTabPlayer],
  });



  const { isPlayerListResponseLoading, isDreamTeamResponseLoading } = useCompetition()

  const handleSearchInputChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const column = table.getColumn('name');
      if (column) {
        column.setFilterValue(e.target.value);
      }
    },
    [table],
  );

  const handleSortChange = React.useCallback(
    (option: string): void => {
      const sorting: SortingState = [];
      switch (option) {
        case 'player_asc':
          sorting.push({ id: 'name', desc: false });
          break;
        case 'player_desc':
          sorting.push({ id: 'name', desc: true });
          break;
        case 'team_asc':
          sorting.push({ id: 'teamName', desc: false });
          break;
        case 'team_desc':
          sorting.push({ id: 'teamName', desc: true });
          break;
        default:
          break;
      }
      table.setSorting(sorting);
    },
    [table],
  );

  const sortedData = React.useMemo(() => {
    return table.getSortedRowModel().rows.map((row) => row.original);
  }, [table.getSortedRowModel().rows]);

  const SOCCERTABDATA = [
    {
      count: soccerPlayersByRole.GKP.length,
      id: 'GKP',
      name: 'GKP',
      seq: 0,
      maxCount: playerByRoleLimit.GKP.max,
    },
    {
      count: soccerPlayersByRole.DEF.length,
      id: 'DEF',
      name: 'DEF',
      maxCount: playerByRoleLimit.DEF.max,
      seq: 2,
    },
    {
      count: soccerPlayersByRole.MID.length,
      id: 'MID',
      name: 'MID',
      maxCount: playerByRoleLimit.MID.max,
      seq: 3,
    },
    {
      count: soccerPlayersByRole.FWD.length,
      id: 'FWD',
      name: 'FWD',
      maxCount: playerByRoleLimit.FWD.max,
      seq: 1,
    },
  ];

  const { width } = useScreen();
  const sortOptions = [
    { value: 'player_asc', label: 'Sort Players A to Z' },
    { value: 'player_desc', label: 'Sort Players Z to A' },
    { value: 'team_asc', label: 'Sort Teams A to Z' },
    { value: 'team_desc', label: 'Sort Teams Z to A' },
  ];

  const DropdownIndicator = (props: any) => {
    return (
      <components.DropdownIndicator {...props}>
        <SelectIndicator />
      </components.DropdownIndicator>
    );
  };

  const stats = {
    remainingSalary: remainingBudget,
    selectedPlayer: Object.values(soccerPlayersByRole).flat().length,
  };

  const validTeamSelection =
    Object.values(soccerPlayersByRole).flat().length === 11;

  const getPlayerSelectionInstruction = (role: SoccerPlayerRole): string => {
    switch (role) {
      case 'GKP':
        return 'Select 1 Goalkeeper';
      case 'DEF':
        return 'Select 3 - 5 Defenders';
      case 'MID':
        return 'Select 3 - 5 Midfielders';
      case 'FWD':
        return 'Select 1 - 3 Forwards';
      default:
        return '';
    }
  };

  return (
    <>
      <div className="p-4 hidden md:block">
        <div className="flex justify-between items-center space-x-2">
          <div className="relative w-[60%] hidden md:block">
            <Search
              className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={16}
            />
            <Input
              placeholder="Search (by player name)"
              className="border-gray-100 pl-10"
              onChange={handleSearchInputChange}
            />
          </div>
          <div className="flex items-center gap-2 w-full md:w-fit justify-between md:justify-start sort-container">
            <Select
              className="React desktop-odds-select min-w-[200px]"
              onChange={(e) => e?.value && handleSortChange(e?.value)}
              options={sortOptions}
              classNamePrefix="select"
              placeholder="Sort"
              isSearchable={false}
              components={{ DropdownIndicator }}
            />
            <div>
              <IconButton
                {...({} as any)}
                className="bg-secondary-100 hidden md:block"
                onClick={() => setShowFilter(!showFilter)}
              >
                <SlidersHorizontal />
              </IconButton>
              <IconButton
                {...({} as any)}
                className="bg-secondary-100 md:hidden block"
                onClick={() => setShowPlayerTable(true)}
              >
                <SlidersHorizontal />
              </IconButton>
            </div>
          </div>
        </div>

        {showFilter && (
          <SoccerPlayerFilter
            playerTable={table}
            filters={filters}
            setFilters={setFilters}
            setShowFilter={setShowFilter}
          />
        )}

        <div className="mt-2">
          <SimpleTabs
            tabs={SOCCERTABDATA}
            activeTab={activeTabPlayer}
            setActiveTab={setActiveTabPlayer}
          />
          {add_more && (
            <div className="text-sm text-slate-500 my-2 text-base">
              {getPlayerSelectionInstruction(activeTabPlayer)}
            </div>
          )}
          <div className='mt-2'>
            <DataTable
              columns={soccerPlayerColumns}
              data={sortedData}
              stickyColumns={width > 390 ? [] : [0]}
              initialColumnVisibility={{
                name: false,
                teamName: false,
              }}
              isLoading={isPlayerListResponseLoading || isDreamTeamResponseLoading}

            />
          </div>
        </div>
      </div>

      <Drawer open={showPlayerTable} onOpenChange={setShowPlayerTable}>
        <DrawerContent className="bg-white mb-[70px]">
          <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md flex justify-between items-center">
            <div>SELECT PLAYERS</div>
            <div
              className="cursor-pointer"
              onClick={() => setShowPlayerTable(false)}
            >
              <X className="text-white" />
            </div>
          </div>
          <div className="p-2 space-y-2 mt-10 h-full">
            <div className="flex justify-between items-center space-x-2">
              <Card className="p-3 shadow-sm w-1/2 bg-dark-card-gradient text-white">
                <div className="text-center">
                  <div className="text-sm text-slate-500 mb-1">
                    Remaining salary:
                  </div>
                  <div className="font-semibold text-slate-900">
                    ${formatNumberWithCommas(stats?.remainingSalary)}
                  </div>
                </div>
              </Card>

              <Card
                className={cn(
                  'p-3 shadow-sm text-white w-1/2',
                  validTeamSelection
                    ? 'bg-[#D1E7DF] border border-[#B4DBCD]'
                    : 'bg-dark-card-gradient',
                )}
              >
                <div className="text-center">
                  <div
                    className={cn(
                      'text-sm mb-1',
                      validTeamSelection ? 'text-black-700' : 'text-slate-500',
                    )}
                  >
                    Selected Player:
                  </div>
                  <div
                    className={cn(
                      'font-semibold',
                      validTeamSelection ? 'text-[#1C9A6C]' : 'text-slate-900',
                    )}
                  >
                    {stats.selectedPlayer}
                  </div>
                </div>
              </Card>
            </div>

            <div className="grid grid-cols-[60%_40%] gap-x-1 place-items-center">
              <div className="relative w-full">
                <Search
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400"
                  size={16}
                />
                <Input
                  placeholder="Search (by player name)"
                  className="pl-8 border-gray-100 !py-4"
                  onChange={handleSearchInputChange}
                />
              </div>
              <div
                className={cn(
                  'flex items-center gap-2 w-full lg:w-fit px-1 lg:px-0 justify-end lg:justify-start sort-container',
                )}
              >
                <Select
                  className={cn(
                    'React desktop-odds-select',
                    soccerStyle.selectMenu,
                  )}
                  onChange={(e) => e?.value && handleSortChange(e?.value)}
                  options={sortOptions}
                  classNamePrefix="select"
                  placeholder="Sort"
                  isSearchable={false}
                  components={{ DropdownIndicator }}
                />
                <div>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 hidden lg:block"
                    onClick={() => setShowFilter(!showFilter)}
                  >
                    <SlidersHorizontal />
                  </IconButton>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 lg:hidden block"
                    onClick={() => setShowMobileFilter(!showMobileFilter)}
                  >
                    <SlidersHorizontal />
                  </IconButton>
                </div>
              </div>
            </div>

            <div className="w-full">
              <SimpleTabs
                tabs={SOCCERTABDATA}
                activeTab={activeTabPlayer}
                setActiveTab={setActiveTabPlayer}
                size="small"
              />
              {add_more && (
                <div className="text-sm text-slate-500 my-2 text-base">
                  {getPlayerSelectionInstruction(activeTabPlayer)}
                </div>
              )}
            </div>

            <div>
              <DataTable
                columns={soccerPlayerColumns}
                data={sortedData}
                stickyColumns={[0]}
                initialColumnVisibility={{ name: false, teamName: false }}
              />
            </div>
          </div>
        </DrawerContent>
      </Drawer>

      <MobileSoccerPlayerFilter
        open={showMobileFilter}
        setOpen={setShowMobileFilter}
        playerTable={table}
        filters={filters}
        setFilters={setFilters}
      />
    </>
  );
};

export default SoccerPlayerSelectionUI;
