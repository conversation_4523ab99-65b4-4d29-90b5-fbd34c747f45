'use client';

import ActiveCricketIcon from '@/assets/images/sportTabIcon/activeCricketIcon.svg';
import ActiveFeatureIcon from '@/assets/images/sportTabIcon/activeFeaturedIcon.svg';
import ActiveNFLIcon from '@/assets/images/sportTabIcon/activeNFLIcon.svg';
import ActiveNRLIcon from '@/assets/images/sportTabIcon/activeNRLIcon.svg';
import ActiveSoccerIcon from '@/assets/images/sportTabIcon/activeSoccerIcon.svg';
import CricketIcon from '@/assets/images/sportTabIcon/cricketIcon.svg';
import FeatureIcon from '@/assets/images/sportTabIcon/featuredIcon.svg';
import MobileActiveCricketIcon from '@/assets/images/sportTabIcon/mobileActiveCricketIcon.svg';
import MobileActiveFeaturedIcon from '@/assets/images/sportTabIcon/mobileActiveFeaturedIcon.svg';
import MobileActiveNFLIcon from '@/assets/images/sportTabIcon/mobileActiveNFLIcon.svg';
import MobileActiveNRLIcon from '@/assets/images/sportTabIcon/mobileActiveNRLIcon.svg';
import MobileActiveSoccerIcon from '@/assets/images/sportTabIcon/mobileActiveSoccerIcon.svg';
import MobileCricketIcon from '@/assets/images/sportTabIcon/mobileCricketIcon.svg';
import MobileFeaturedIcon from '@/assets/images/sportTabIcon/mobileFeaturedIcon.svg';
import MobileNFLIcon from '@/assets/images/sportTabIcon/mobileNFLIcon.svg';
import MobileNRLIcon from '@/assets/images/sportTabIcon/mobileNRLIcon.svg';
import MobileSoccerIcon from '@/assets/images/sportTabIcon/mobileSoccerIcon.svg';
import NFLIcon from '@/assets/images/sportTabIcon/NFLIcon.svg';
import NRLIcon from '@/assets/images/sportTabIcon/NRLIcon.svg';
import BasketballIcon from '@/assets/images/sportTabIcon/basketBall.svg';
import ActiveBasketballIcon from '@/assets/images/sportTabIcon/activeBasketBall.svg';
import MobileBasketballIcon from '@/assets/images/sportTabIcon/basketBallMobile.svg';
import MobileActiveBasketballIcon from '@/assets/images/sportTabIcon/activeBasketBallMobile.svg';
import SoccerIcon from '@/assets/images/sportTabIcon/soccerIcon.svg';
// Rename imports to Aussie Rules
import ActiveAussieRulesIcon from '@/assets/images/sportTabIcon/activeAFLIcon.svg';
import AussieRulesIcon from '@/assets/images/sportTabIcon/AFLIcon.svg';
import MobileActiveAussieRulesIcon from '@/assets/images/sportTabIcon/mobileActiveAFLIcon.svg';
import MobileAussieRulesIcon from '@/assets/images/sportTabIcon/mobileAFLIcon.svg';
import BaseBallIcon from '@/assets/images/sportTabIcon/baseBall.svg';
import MobileBaseBallIcon from '@/assets/images/sportTabIcon/baseBallMobile.svg';
import MobileActiveBaseBallIcon from '@/assets/images/sportTabIcon/activeBaseBallMobile.svg';
import ActiveBaseBallIcon from '@/assets/images/sportTabIcon/activeBaseBall.svg';
import IceHockeyIcon from '@/assets/images/sportTabIcon/iceHockey.svg';
import MobileIceHockeyIcon from '@/assets/images/sportTabIcon/iceHockeyMobile.svg';
import MobileActiveIceHockeyIcon from '@/assets/images/sportTabIcon/activeIceHockeyMobile.svg';
import ActiveIceHockeyIcon from '@/assets/images/sportTabIcon/activeIceHockey.svg';

const tabs = [
  {
    label: 'ALL',
    labelId: 1,
    icon: <FeatureIcon />,
    mobileIcon: <MobileFeaturedIcon />,
    activeIcon: <ActiveFeatureIcon />,
    mobileActiveIcon: <MobileActiveFeaturedIcon />,
    comingSoon: false,
    title: 'Coming Soon',
  },
  {
    label: 'Cricket',
    labelId: 2,
    icon: <CricketIcon />,
    mobileIcon: <MobileCricketIcon />,
    activeIcon: <ActiveCricketIcon />,
    mobileActiveIcon: <MobileActiveCricketIcon />,
    comingSoon: false,
    title: 'Coming Soon',
  },
  {
    label: 'AUSSIE RULES',
    labelId: 3,
    icon: <AussieRulesIcon />,
    mobileIcon: <MobileAussieRulesIcon />,
    activeIcon: <ActiveAussieRulesIcon />,
    mobileActiveIcon: <MobileActiveAussieRulesIcon />,
    comingSoon: false,
    title: 'Coming Soon',
  },
  {
    label: 'RUGBY LEAGUE',
    labelId: 4,
    icon: <NRLIcon />,
    mobileIcon: <MobileNRLIcon />,
    activeIcon: <ActiveNRLIcon />,
    mobileActiveIcon: <MobileActiveNRLIcon />,
    comingSoon: false,
    title: 'Coming Soon',
  },
  {
    label: 'FOOTBALL',
    labelId: 5,
    icon: <SoccerIcon />,
    mobileIcon: <MobileSoccerIcon />,
    activeIcon: <ActiveSoccerIcon />,
    mobileActiveIcon: <MobileActiveSoccerIcon />,
    comingSoon: false,
    title: 'Coming Soon',
  },
  {
    label: 'BASKETBALL',
    labelId: 7,
    icon: <BasketballIcon />,
    mobileIcon: <MobileBasketballIcon />,
    activeIcon: <ActiveBasketballIcon />,
    mobileActiveIcon: <MobileActiveBasketballIcon />,
    comingSoon: true,
    title: 'Coming Soon',
  },
  {
    label: 'BASEBALL',
    labelId: 8,
    icon: <BaseBallIcon />,
    mobileIcon: <MobileBaseBallIcon />,
    activeIcon: <ActiveBaseBallIcon />,
    mobileActiveIcon: <MobileActiveBaseBallIcon />,
    comingSoon: true,
    title: 'Coming Soon',
  },
  {
    label: 'AMERICAN FOOTBALL',
    labelId: 6,
    icon: <NFLIcon />,
    mobileIcon: <MobileNFLIcon />,
    activeIcon: <ActiveNFLIcon />,
    mobileActiveIcon: <MobileActiveNFLIcon />,
    comingSoon: true,
    title: 'Coming Soon',
  },
  {
    label: 'ICE HOCKEY',
    labelId: 9,
    icon: <IceHockeyIcon />,
    mobileIcon: <MobileIceHockeyIcon />,
    activeIcon: <ActiveIceHockeyIcon />,
    mobileActiveIcon: <MobileActiveIceHockeyIcon />,
    comingSoon: true,
    title: 'Coming Soon',
  },
];

export const useHeader = () => {
  return {
    tabs,
  };
};
