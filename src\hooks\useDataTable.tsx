import type {
  ColumnDef,
  ColumnFiltersState,
  ExpandedState,
  SortingState,
  VisibilityState,
  OnChangeFn,
} from '@tanstack/react-table';
import {
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useState, useEffect, useMemo, useCallback } from 'react';

interface UseDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  initialColumnVisibility?: VisibilityState;
  columnVisibility?: VisibilityState; // For external control
  onColumnVisibilityChange?: (visibility: VisibilityState) => void; // Callback
}

export function useDataTable<TData, TValue>({
  columns,
  data,
  initialColumnVisibility = {},
  columnVisibility: externalColumnVisibility,
  onColumnVisibilityChange,
}: UseDataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    initialColumnVisibility,
  );
  const [expanded, setExpanded] = useState<ExpandedState>({});

  // Memoize the data with proper dependency
  const memoizedData = useMemo(() => data, [JSON.stringify(data)]);

  // Memoize the columns to prevent unnecessary recalculations
  const memoizedColumns = useMemo(() => columns, [columns]);

  // Use external visibility state if provided
  useEffect(() => {
    if (externalColumnVisibility !== undefined) {
      setColumnVisibility(externalColumnVisibility);
    }
  }, [externalColumnVisibility]);

  // Memoize the visibility change handler
  const handleColumnVisibilityChange = useCallback<OnChangeFn<VisibilityState>>(
    (updater) => {
      const newState =
        typeof updater === 'function' ? updater(columnVisibility) : updater;

      setColumnVisibility(newState);

      if (onColumnVisibilityChange) {
        onColumnVisibilityChange(newState);
      }
    },
    [columnVisibility, onColumnVisibilityChange],
  );

  const table = useReactTable({
    data: memoizedData,
    columns: memoizedColumns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: handleColumnVisibilityChange,
    // Important for filtering
    getExpandedRowModel: getExpandedRowModel(),
    onExpandedChange: setExpanded,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      expanded,
    },
  });

  return {
    table,
    sorting,
    columnVisibility,
    setColumnVisibility: handleColumnVisibilityChange,
  };
}
