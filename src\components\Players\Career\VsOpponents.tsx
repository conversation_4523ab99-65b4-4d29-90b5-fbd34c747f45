import React from 'react';

import SortingDown from '@/assets/images/icons/sortingDown.svg';
import SortingUp from '@/assets/images/icons/sortingUp.svg';
import { generateUniqueId } from '@/lib/utils';

interface MatchData {
  opponent?: string;
  timesPlayed?: number;
  highScore?: number;
  lowScore?: number;
  avg?: number;
}

interface CareerStatsOpponentsData {
  careerStatsOpponentsData: MatchData[];
}

const HeaderCell = ({ label, textCenter }: any) => (
  <th
    className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left"
    style={{ textAlign: textCenter }}
  >
    <div
      className="flex items-center gap-1"
      style={{ justifyContent: textCenter }}
    >
      {label}
      <div className="flex items-center flex-col">
        <span>
          <SortingUp />
        </span>
        <span className="mt-[1.3px]">
          <SortingDown />
        </span>
      </div>
    </div>
  </th>
);

const StickyHeaderCell = ({ label, left, width }: any) => (
  <th
    className={`px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left sticky bg-primary-200 z-[9]`}
    style={{ left, width }}
  >
    <div className="flex items-center gap-1">
      {label}
      <div className="flex items-center flex-col">
        <span>
          <SortingUp />
        </span>
        <span className="mt-[1.3px]">
          <SortingDown />
        </span>
      </div>
    </div>
  </th>
);

const TableCell = ({ content, isSticky, left, width, textCenter }: any) => (
  <td
    className={`px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-left ${isSticky ? 'sticky bg-white z-[9]' : ''}`}
    style={{ width, textAlign: textCenter, ...(isSticky ? { left } : {}) }}
  >
    {content}
  </td>
);

const SummaryTabPage = ({
  careerStatsOpponentsData,
}: CareerStatsOpponentsData) => {
  return (
    <div>
      <div className="upcoming-matches-table overflow-x-auto no-scrollbar  rounded-b-lg">
        <table className="w-full min-w-max table-auto">
          <thead>
            <tr className="bg-primary-200">
              <StickyHeaderCell label="Opponent" left="0px" />
              <HeaderCell label="Times played" textCenter="center" />
              <HeaderCell label="High Score" textCenter="center" />
              <HeaderCell label="Total Score" textCenter="center" />
              <HeaderCell label="Avg" textCenter="center" />
            </tr>
          </thead>
          <tbody>
            {careerStatsOpponentsData?.map((match, index) => (
              <tr
                key={generateUniqueId()}
                className={`${index === careerStatsOpponentsData?.length - 1 ? '' : 'border-b-[1px] border-black-300'}`}
              >
                <TableCell
                  content={
                    <div className="flex items-center gap-2">
                      <div className="bg-primary-200 w-[26px] h-[26px] rounded-full"></div>
                      <div>
                        <p>{match?.opponent}</p>
                      </div>
                    </div>
                  }
                  isSticky={true}
                  left="0px"
                />
                <TableCell content={match?.timesPlayed} textCenter="center" />
                <TableCell content={match?.highScore} textCenter="center" />
                <TableCell content={match?.lowScore} textCenter="center" />
                <TableCell content={match?.avg} textCenter="center" />
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SummaryTabPage;
