import { Column, ColumnDef, Row } from '@tanstack/react-table';
import { Checkbox } from '@/components/UI/checkbox';
import { AFLMatchPlayerStats } from '@/lib/types/afl';
import PlayerAvatar from '@/components/UI/PlayerAvatar/indext';
import PlayerValueChange from '@/components/Competition/PlayerValueChange';
import { formatNumberWithCommas } from '@/lib/utils';
import { getDefaultProfileImage } from '../../../../../db/db';
import SortingDownIcon from '../../Icons/SortingDownIcon';
import SortingUpIcon from '../../Icons/SortingUpIcon';

const renderSortHeader = (
  column: Column<AFLMatchPlayerStats, unknown>,
  label: string,
) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

export const aflPlayerStatsColumn: ColumnDef<AFLMatchPlayerStats>[] = [
  {
    id: 'rank',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, '#')}
      </div>
    ),
    cell: ({ row, table }) => {
      const sortedRows = table.getRowModel().rows;
      const rowIndex = sortedRows.findIndex((r) => r.id === row.id);
      return <div className="text-center">{rowIndex + 1}</div>;
    },
    sortingFn: (rowA, rowB) => rowA.index - rowB.index,
  },
  {
    id: 'player',
    accessorKey: 'player',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'Player')}
      </div>
    ),
    cell: ({ row }: { row: Row<AFLMatchPlayerStats> }) => {
      const player = row.original;
      const image = player?.player?.image ?? getDefaultProfileImage();
      return (
        <div className="text-center">
          <div className="flex space-x-2 max-w-[250px]">
            <div className="flex justify-start items-center">
              <PlayerAvatar avatarUrl={image} />
            </div>
            <div className="flex justify-start">
              <div className="flex flex-col justify-start items-start">
                <p className="truncate ... w-[100px] text-left">
                  {player?.player?.name}
                </p>
                <div className="text-xs text-gray-500 truncate ... w-[70x]">
                  {player?.team?.name}
                </div>
                <PlayerValueChange
                  formatToCustomStyle={formatNumberWithCommas}
                  playerCurrentSalary={0}
                  playerLastSalary={0}
                />
              </div>
              <span className="text-[9px] text-gray-500">
                {player?.scoreComponents?.position?.value ?? '-'}
              </span>
            </div>
          </div>
        </div>
      );
    },
  },
  {
    id: 'position',
    accessorKey: 'position',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'POS')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.position?.value ?? '-'}
      </div>
    ),
  },
  // Scoring
  {
    id: 'goals',
    accessorKey: 'scoreComponents.goals',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'GLS')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.goals?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'behinds',
    accessorKey: 'scoreComponents.behinds',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'BE')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.behinds?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'goal_assists',
    accessorKey: 'scoreComponents.goal_assists',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'GA')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.goal_assists?.value ?? '-'}
      </div>
    ),
  },
  // Disposal
  {
    id: 'kicks',
    accessorKey: 'scoreComponents.kicks',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'K')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.kicks?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'handballs',
    accessorKey: 'scoreComponents.handballs',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'HB')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.handballs?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'effective_disposals',
    accessorKey: 'scoreComponents.effective_disposals',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'ED')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.effective_disposals?.value ?? '-'}
      </div>
    ),
  },
  // Marking
  {
    id: 'marks',
    accessorKey: 'scoreComponents.marks',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'M')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.marks?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'contested_marks',
    accessorKey: 'scoreComponents.contested_marks',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'CM')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.contested_marks?.value ?? '-'}
      </div>
    ),
  },
  // Ruck
  {
    id: 'hitouts',
    accessorKey: 'scoreComponents.hitouts.value',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'HO')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.hitouts?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'clearances',
    accessorKey: 'scoreComponents.clearances',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'CLR')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.clearances?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'center_clearances',
    accessorKey: 'scoreComponents.center_clearances',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'CC')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.center_clearances?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'hitouts_to_advantage',
    accessorKey: 'scoreComponents.hitouts_to_advantage',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'HA')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.hitouts_to_advantage?.value ?? '-'}
      </div>
    ),
  },
  // General Play
  {
    id: 'inside_fifty',
    accessorKey: 'scoreComponents.inside_fifty',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'i50s')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.inside_fifty?.value ?? '-'}
      </div>
    ),
  },
  // Penalty
  {
    id: 'errors',
    accessorKey: 'scoreComponents.errors',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'C')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.errors?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'fifty_metre_penalties',
    accessorKey: 'scoreComponents.fifty_metre_penalties',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'P50m')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.fifty_metre_penalties?.value ?? '-'}
      </div>
    ),
  },
  // Tackling and Defensive
  {
    id: 'tackles',
    accessorKey: 'scoreComponents.tackles',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'TAC')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.tackles?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'freesFor',
    accessorKey: 'scoreComponents.freesFor',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'FKF')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.freesFor?.value ?? '-'}
      </div>
    ),
  },
  {
    id: 'freesAgainst',
    accessorKey: 'scoreComponents.freesAgainst',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'FKA')}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        {row.original.scoreComponents?.freesAgainst?.value ?? '-'}
      </div>
    ),
  },
];
