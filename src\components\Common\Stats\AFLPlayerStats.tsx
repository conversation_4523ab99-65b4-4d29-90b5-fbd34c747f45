'use client';

import { useState, useEffect, useMemo } from 'react';
import { FiltersState } from '../Player/PlayerFilter';
import DataTable from '@/components/UI/DataTabel';
import { AFLPlayerStatsColumns } from '@/components/UI/DataTabel/AFLPlayerStatsColumn';
import { useStatsContext } from '@/helpers/context/stats';
import { StatsLegend } from '../Commentary/CricketLegend';
import { AFL_PLAYER_STATS_LEGEND } from '@/helpers/constants/index';
import { Button } from '@/components/UI/button';
import { Search, SlidersHorizontal } from 'lucide-react';
import { Input } from '@/components/UI/input';
import CustomisePanel from '../Commentary/CustomisePanel';
import CustomisePanelMobile from '../Commentary/CustomisePanelMobile';
import { VisibilityState } from '@tanstack/react-table';
import { IconButton } from '@material-tailwind/react';
import SettingsIcon from '@/components/UI/Icons/SettingsIcon';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Card } from '@/components/UI/card';
import { cn } from '@/lib/utils';
import Select, { components } from 'react-select';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { Checkbox } from '@/components/UI/checkbox';
import { useCompetition } from '@/helpers/context/competitionContext';

const positionOptions = [
  {
    value: 1,
    label: 'All Positions',
  },
  {
    value: 2,
    label: 'Back Line',
  },
  {
    value: 3,
    label: 'Half Back Line',
  },
  {
    value: 4,
    label: 'Midfield',
  },
  {
    value: 5,
    label: 'Half Forward Line',
  },
  {
    value: 6,
    label: 'Forward Line',
  },
  {
    value: 7,
    label: 'Followers',
  },
  {
    value: 8,
    label: 'Interchange',
  },
];

const aflCategories = [
  {
    id: 'disposals',
    name: 'Disposals',
    children: [
      { id: 'disposals', name: 'Disposals' },
      { id: 'kicks', name: 'Kicks' },
      { id: 'handballs', name: 'Handballs' },
      { id: 'inside_fifty', name: 'Inside 50s' },
    ],
  },
  {
    id: 'stoppages',
    name: 'Stoppages',
    children: [
      { id: 'hitouts', name: 'Hit Outs' },
      { id: 'clearances', name: 'Clearances' },
      { id: 'hitouts_to_advantage', name: 'Hit outs to Advantage' },
    ],
  },
  {
    id: 'scoring',
    name: 'Scoring',
    children: [
      { id: 'goals', name: 'Goals' },
      { id: 'behinds', name: 'Behinds' },
      { id: 'goal_assists', name: 'Goal Assists' },
    ],
  },
  {
    id: 'defence',
    name: 'Defence',
    children: [{ id: 'tackles', name: 'Tackles' }],
  },
  {
    id: 'marks',
    name: 'Marks',
    children: [
      { id: 'marks', name: 'Marks' },
      { id: 'contested_marks', name: 'Contested Marks' },
    ],
  },
];

export const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const AFLPlayerStats = () => {
  const [filters, setFilters] = useState<FiltersState>({
    matchesPlayed: false,
    priceRange: [0, 100],
    dualPosition: false,
    teams: {
      awayTeam: false,
      homeTeam: false,
    },
    breakeven: [0, 100],
    projectedScore: [0, 100],
    projectedValueChange: [6300, 9300],
    selectionPercentage: [0, 100],
  });

  const [showFilter, setShowFilter] = useState(false);
  const [showTableCustomization, setShowTableCustomization] = useState(false);
  const [showTableCustomizationMobile, setShowTableCustomizationMobile] =
    useState(false);
  const [showMobileFilter, setShowMobileFilter] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showLegends, setShowLegends] = useState(false);

  const initializeSelectedState = () => {
    const state: Record<string, boolean> = {};

    aflCategories.forEach((category) => {
      // Set parent category as checked
      state[category.id] = true;

      // Set all children as checked
      category.children.forEach((child) => {
        state[`${child.id}`] = true;
      });
    });

    return state;
  };

  const [selected, setSelected] = useState<Record<string, boolean>>(
    initializeSelectedState(),
  );
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    ...selected,
  });

  const [selectedPositions, setSelectedPositions] = useState<number[]>([]);
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);

  const handlePositionChange = (value: number) => {
    setSelectedPositions((prev) => {
      if (prev.includes(value)) {
        return prev.filter((pos) => pos !== value);
      }
      return [...prev, value];
    });
  };

  const handleTeamChange = (value: string) => {
    setSelectedTeams((prev) => {
      if (prev.includes(value)) {
        return prev.filter((team) => team !== value);
      }
      return [...prev, value];
    });
  };

  const resetFilters = () => {
    setSelectedPositions([]);
    setSelectedTeams([]);
  };

  useEffect(() => {
    setColumnVisibility(selected);
  }, [selected]);

  const { playerStats } = useStatsContext();
  const { eventDetailsResponse } = useCompetition();
  const homeTeam = eventDetailsResponse?.result?.eventDetails?.homeTeam;
  const awayTeam = eventDetailsResponse?.result?.eventDetails?.awayTeam;
  const filteredData = useMemo(() => {
    if (!searchQuery.trim() || !playerStats?.result?.rows) {
      return playerStats?.result?.rows || [];
    }

    return playerStats.result.rows.filter((player) =>
      player.player.name.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [searchQuery, playerStats?.result?.rows]);

  return (
    <div className="relative">
      <div className="flex items-center w-full space-x-2 my-2 md:justify-end justify-around">
        <div className="flex items-center gap-2">
          <div>
            <Select
              className="React desktop-odds-select !min-w-[200px]"
              options={positionOptions}
              classNamePrefix="select"
              placeholder="Select Position"
              isSearchable={false}
              components={{ DropdownIndicator }}
            />
          </div>
          <div>
            <Select
              className="React desktop-odds-select !min-w-[200px]"
              options={[
                { value: 'all', label: 'All Teams' },
                { value: homeTeam?.name, label: homeTeam?.name },
                { value: awayTeam?.name, label: awayTeam?.name },
              ]}
              classNamePrefix="select"
              placeholder="Select Team"
              isSearchable={false}
              components={{ DropdownIndicator }}
            />
          </div>
          <div className="relative">
            <Button
              variant="outline"
              className="bg-secondary-100 hidden md:block"
              onClick={() => setShowTableCustomization(!showTableCustomization)}
            >
              <div className="flex space-x-2 text-white">
                <SettingsIcon /> <span>Customise</span>
              </div>
            </Button>
            <IconButton
              {...({} as any)}
              className="bg-secondary-100 md:hidden block"
              onClick={() =>
                setShowTableCustomizationMobile(!showTableCustomizationMobile)
              }
            >
              <SettingsIcon />
            </IconButton>
            {showTableCustomization && (
              <div className="absolute top-full right-0 mt-2 z-50">
                <CustomisePanel
                  isOpen={showTableCustomization}
                  setIsOpen={setShowTableCustomization}
                  selected={selected}
                  setSelected={setSelected}
                  categories={aflCategories}
                />
              </div>
            )}
          </div>
          <div className="relative">
            <div
              onMouseEnter={() => setShowLegends(true)}
              onMouseLeave={() => setShowLegends(false)}
            >
              <Button variant="outline" className="hidden md:block">
                Legends
              </Button>
              {showLegends && (
                <>
                  <div className="absolute top-full right-0 h-2 w-full bg-transparent" />
                  <div className="absolute top-[calc(100%+8px)] right-0 z-50 bg-white rounded-md shadow-lg p-4 min-w-[500px]">
                    <StatsLegend stats={AFL_PLAYER_STATS_LEGEND} />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <DataTable
        columns={AFLPlayerStatsColumns}
        data={filteredData}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={setColumnVisibility}
      />

      {showTableCustomizationMobile && (
        <CustomisePanelMobile
          isOpen={showTableCustomizationMobile}
          setIsOpen={setShowTableCustomizationMobile}
          categories={aflCategories}
          selected={selected}
          setSelected={setSelected}
        />
      )}

      <Drawer open={showMobileFilter} onOpenChange={setShowMobileFilter}>
        <DrawerContent className="bg-white mb-[70px]">
          <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md">
            FILTERS
          </div>
          <div className="p-2 space-y-2 mt-10 h-screen">
            <Card className="p-4 shadow-none">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Filters</h3>
                  <Button
                    variant="outline"
                    onClick={resetFilters}
                    className="text-sm"
                  >
                    Reset Filters
                  </Button>
                </div>
                <div>
                  <label className="block text-xl font-medium text-black-100 mb-2">
                    By position
                  </label>
                  <div className="space-y-2">
                    {positionOptions.map((option) => (
                      <div key={option.value} className="flex items-center">
                        <Checkbox
                          id={`position-${option.value}`}
                          checked={selectedPositions.includes(option.value)}
                          onCheckedChange={() =>
                            handlePositionChange(option.value)
                          }
                          className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                        />
                        <label
                          htmlFor={`position-${option.value}`}
                          className="ml-2 text-sm text-gray-700"
                        >
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Team
                  </label>
                  <div className="space-y-2">
                    {[
                      { value: 'all', label: 'All Teams' },
                      { value: homeTeam?.name, label: homeTeam?.name },
                      { value: awayTeam?.name, label: awayTeam?.name },
                    ].map((option) => (
                      <div key={option.value} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`team-${option.value}`}
                          checked={selectedTeams.includes(option.value ?? '')}
                          onChange={() => handleTeamChange(option.value ?? '')}
                          className="h-4 w-4 text-primary-200 focus:ring-primary-200 border-gray-300 rounded"
                        />
                        <label
                          htmlFor={`team-${option.value}`}
                          className="ml-2 text-sm text-gray-700"
                        >
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex justify-end mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowMobileFilter(false)}
                    className="bg-primary-200 text-white hover:bg-primary-300"
                  >
                    Apply Filters
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </DrawerContent>
      </Drawer>

      {showFilter && (
        <div className="absolute top-14 right-0 w-80 z-40">
          <Card className="p-4">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Position
                </label>
                <Select
                  className="React desktop-odds-select"
                  options={positionOptions}
                  classNamePrefix="select"
                  placeholder="Select Position"
                  isSearchable={false}
                  components={{ DropdownIndicator }}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Team
                </label>
                <Select
                  className="React desktop-odds-select"
                  options={[
                    { value: 'all', label: 'All Teams' },
                    { value: homeTeam?.name, label: homeTeam?.name },
                    { value: awayTeam?.name, label: awayTeam?.name },
                  ]}
                  classNamePrefix="select"
                  placeholder="Select Team"
                  isSearchable={false}
                  components={{ DropdownIndicator }}
                />
              </div>
            </div>
          </Card>
        </div>
      )}

      <div className="mt-4">
        <StatsLegend stats={AFL_PLAYER_STATS_LEGEND} />
      </div>
    </div>
  );
};

export default AFLPlayerStats;
