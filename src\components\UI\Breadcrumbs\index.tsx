import { generateUniqueId } from '@/lib/utils';
import Link from 'next/link';
import type { FC } from 'react';

interface BreadcrumbsProps {
  links: { label: string; href: string }[];
}

const Breadcrumbs: FC<BreadcrumbsProps> = ({ links }) => {
  return (
    <nav className="flex items-center overflow-x-auto no-scrollbar whitespace-nowrap text-black-100 max-1024:text-black-100 text-sm">
      {links?.map((link, index) => (
        <div key={generateUniqueId()} className="flex items-center">
          <Link href={link?.href} passHref>
            <span
              className={`font-inter text-black-100 text-[11.42px] uppercase ${index === links?.length - 1 ? 'text-secondary-100' : ''
                }`}
            >
              {link?.label}
            </span>
          </Link>
          {index < links?.length - 1 && (
            <span className="mx-[6px] text-black-100 max-1024:text-black-100">
              /
            </span>
          )}
        </div>
      ))}
    </nav>
  );
};

export default Breadcrumbs;
