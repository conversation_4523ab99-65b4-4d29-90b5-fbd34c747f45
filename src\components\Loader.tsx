import React from 'react';
import <PERSON><PERSON> from 'react-lottie';

import animationData from '../helpers/loadingAnimation.json';

const Loader = ({ width = 30, height = 30 }) => {
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: animationData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };

  return (
    <div className="loader-wrap">
      {/* @ts-ignore */}
      <Lottie options={defaultOptions} height={height} width={width} />
    </div>
  );
};

export default Loader;