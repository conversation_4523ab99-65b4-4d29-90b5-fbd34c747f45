import React from 'react';

const HarnessRacing = () => {
  return (
    <svg
      id="Group_136880"
      data-name="Group 136880"
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="22"
      viewBox="0 0 22 22"
    >
      <rect
        id="Rectangle_6937"
        data-name="Rectangle 6937"
        width="22"
        height="22"
        fill="none"
      />
      <path
        id="Path_1957"
        data-name="Path 1957"
        d="M356.882,258.711a.01.01,0,0,1,.008,0,.01.01,0,0,1,0,.007c.007.052-.067.262-.067.359a1.583,1.583,0,0,1-.008.381c-.03.06-.082.187-.082.187l.486.7s.1.052.075.1a.589.589,0,0,0-.03.067l.583.972.075.2s.165.052.172.1a.417.417,0,0,1-.03.149,4.385,4.385,0,0,0,.3.389c.057.045.239.224.128.359s-.172.038-.172.113a.477.477,0,0,1-.172.329,1.191,1.191,0,0,1-.262.1.966.966,0,0,1-.367.075,3.091,3.091,0,0,1-.441-.494.439.439,0,0,1-.254-.1l-.113-.112-1.151-.045-.494.935-.007-.007a5.437,5.437,0,0,0-.459.722,6.323,6.323,0,0,1-.208,1.011h.009a2.124,2.124,0,0,1-.03.733c-.09.2-.232.262-.239.381a10.684,10.684,0,0,0,.411,1.66,6.683,6.683,0,0,1,.553,1.279c.008.239.008.262.008.262s.635,1.144.7,1.241a3.847,3.847,0,0,0,.478.5,2.232,2.232,0,0,1,.359.546c.023.082-.3.1-.5.1a.721.721,0,0,1-.478-.172.333.333,0,0,1-.008-.254c.03-.06.112-.09.06-.142s-.112-.172-.2-.172a.233.233,0,0,1-.217-.082,15.924,15.924,0,0,0-.692-1.593,2.082,2.082,0,0,1-.411-.6c-.112-.216-.7-1.115-.7-1.115s.4.972.232,1.212-.449.157-.6.239-.905.442-.965.471a1.12,1.12,0,0,1-.486.067,1.519,1.519,0,0,0-.725.3c-.082.044-.329.187-.388.157a1.854,1.854,0,0,1,.18-.905c.127-.2.262-.172.329-.112s.157.224.239.187.239.03.27-.1.023-.172.082-.18a2.471,2.471,0,0,0,.606-.075,3.591,3.591,0,0,0,.845-.4c.052-.09.023-.329-.044-.367s-.65-1.13-.725-1.272-.149-.329-.149-.329a12.552,12.552,0,0,1-2.049-.247,12.25,12.25,0,0,1-1.878-.613h-.652c-.055.407-.157,1.178-.174,1.406-.023.314.023,1.653.023,1.68a5.774,5.774,0,0,0,.262.635c.044.067.673,1,.755,1.077s.254.337.382.374.269.1.277.216.209.314.015.314-.838.06-.889-.044.082-.232.015-.254a.905.905,0,0,1-.471-.3c-.12-.187-.277-.666-.374-.778s-.748-1.3-.868-1.436a.613.613,0,0,1-.157-.509,3.2,3.2,0,0,0-.008-1.2,1.619,1.619,0,0,0-.262-.531s-1.87,1.122-2.012,1.212a.735.735,0,0,0-.1.085,2.055,2.055,0,0,1-2.3,3.084,2.253,2.253,0,1,1-3.185-2.58c-.14-.361-.247-.643-.247-.667,0-.074.2-.211.2-.211l-.032-.079s-1.153.275-1.195.259a1.824,1.824,0,0,1-.262-.445c-.027-.085.1-.227.1-.227a5.344,5.344,0,0,1-.582-.791c-.011-.117.227-.2.227-.2a2.361,2.361,0,0,1-.185-1.341,2.1,2.1,0,0,1,.68-1.4l.021-.117a.4.4,0,0,1-.132-.153,1.339,1.339,0,0,1,.047-.852c.132-.376.666-.444,1.073-.338a.67.67,0,0,1,.439.593.572.572,0,0,1,.211.117c.047.063-.322.148-.322.148a2.156,2.156,0,0,0,.011.233c.011.053.069.191.053.216s-.122.058-.122.058.011.264,0,.317-.18.043-.18.043l-.037.117s.052.032.164.111.714.767.714.767a2.589,2.589,0,0,1,.344-.211,1.548,1.548,0,0,1,.45-.079l.106-.021s.27-.216.418-.306a.275.275,0,0,1,.265-.027.717.717,0,0,1,.281-.09c.122,0-.021.222.011.27s-.207.19-.207.19l2.189-.606a1.05,1.05,0,0,1,.112-.114,1.969,1.969,0,0,1,1.693-1.107,3.139,3.139,0,0,1,.411-.152,5.781,5.781,0,0,1,1.692-.225,2.882,2.882,0,0,1,.325.025,6.44,6.44,0,0,1,.816-.593,1.439,1.439,0,0,1,1.122-.03c.262.126.784.344,1.075.465l.9-.434.126.059,3.2-1.322-.015.018a4.215,4.215,0,0,1,1.2-.547.519.519,0,0,1-.031-.373c.075-.142.18-.306.2-.239a1.469,1.469,0,0,0,.157.367c.052.044.128.1.128.1l.224.023a1.82,1.82,0,0,0,.27-.471.523.523,0,0,1,.242-.308Zm-3.274,2.136-2.7,1.12v.22a3.8,3.8,0,0,0,.826-.166,6.382,6.382,0,0,0,1.878-1.174Zm-4.959.742a1.023,1.023,0,0,0-.362.069,4.2,4.2,0,0,0-.671.381,5.313,5.313,0,0,1,.8.2c.494-.027,1.226.014,1.94-.013v-.05c-.111-.041-1.028-.39-1.113-.433a1.4,1.4,0,0,0-.593-.152Zm-5.7,2.38-2.36.646a.74.74,0,0,0-.064.282c.015.149.2.329.268.524a1.5,1.5,0,0,1,.021.563l.074.012.486-.191s-.042-.434.127-.476.4.265.4.265l.683-.265a3.206,3.206,0,0,0,.113-.379,7.445,7.445,0,0,1,.259-.98Zm-2.626.676a1.982,1.982,0,0,0-.224.147,1.858,1.858,0,0,1-.224.151c.091.17.215.4.246.462.044.09.2.478.2.478l.068.027.155.028c0-.053-.013-.274-.013-.354s-.253-.464-.253-.583a3.225,3.225,0,0,1,.051-.357Zm-.634.408c-.077.044-.153.087-.217.121a3.137,3.137,0,0,1-.719.188l.032.232a4.994,4.994,0,0,1,.719-.063c.148.021.3.233.434.286a1.053,1.053,0,0,0,.122.033c-.043-.107-.157-.4-.207-.5a3.344,3.344,0,0,0-.163-.3Zm4.447.233-.476.2a3.1,3.1,0,0,1-1.179,1.366,2.065,2.065,0,0,1,.244.041l1.436-1.384c-.008-.075-.016-.15-.025-.222Zm.064.684-1.087,1.046q.09.04.176.088a3.8,3.8,0,0,0,.91-.9,1.111,1.111,0,0,0,0-.236Zm-2.005.115-.812.336.579.042c.077-.128.156-.252.233-.378Zm-1.757.728c-.873.365-1.553.653-1.63.7-.232.148-.148.277-.148.277l.158.381a2.209,2.209,0,0,1,1.522.034,2.059,2.059,0,0,1,.869-1.068l-.088-.192Zm1.839.4a1.677,1.677,0,0,0-.368.042c-.09.063-.171.123-.241.177l.146.334.571-.549h-.109Zm.537.09-.847.812.443,1.012h.009a.273.273,0,0,1,.24.143c.087-.582.206-1.568.206-1.729a.333.333,0,0,1,.064-.2q-.056-.024-.116-.043Zm-1.449.18a1.685,1.685,0,0,0-.692.889,2.246,2.246,0,0,1,.3.2l.623-.593Zm2.382.593c-.069.079-.119.139-.119.139s-.2.6-.247.692a5.032,5.032,0,0,0-.149.883.783.783,0,0,1-.157.419,4.235,4.235,0,0,1-.556.354,1.68,1.68,0,0,0,1.228-2.49Zm-2,.25-.523.5.925.376Zm-2.219.141a1.851,1.851,0,0,0-.557.087l.575,1.389.7-.676a1.931,1.931,0,0,1-.011-.656,1.846,1.846,0,0,0-.711-.144Zm-.856.211a1.845,1.845,0,1,0,2.653,2.046,2.057,2.057,0,0,1-.965-1.068l-.626.6a.267.267,0,0,1,.006.058.273.273,0,1,1-.486-.169c-.076-.189-.332-.829-.582-1.466Zm2.255.432-.272.261a1.687,1.687,0,0,0,.718.954v-.011a1.846,1.846,0,0,0-.447-1.2Zm.623.211a2.255,2.255,0,0,1,.2.635l.51-.03a1.047,1.047,0,0,0,.314-.232.5.5,0,0,0,.025-.1.272.272,0,0,1-.407-.041Zm0,0"
        transform="translate(-336.529 -254.637)"
        fill="#003764"
      />
    </svg>
  );
};

export default HarnessRacing;
