import { Meta, StoryObj } from '@storybook/react'
import BottomAction from './BottomAction'

const meta: Meta<typeof BottomAction> = {
  title: 'Layout/Action/BottomAction',
  component: BottomAction,
}

export default meta
type Story = StoryObj<typeof BottomAction>

export const Default: Story = {
  args: {
    onClearAll: () => { },
    onClearLastEntry: () => { },
    onSave: () => { },
    onSubmitTeam: () => { },
    disabledClearAll: false,
    disabledClearLastEntry: false,
    disabledSave: false,
    disabledSubmitTeam: false,
  },
}