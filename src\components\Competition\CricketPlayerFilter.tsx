import type { Table } from '@tanstack/react-table';
import { X } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import { useRef, useState } from 'react';

import { Checkbox } from '@/components/UI/checkbox';
import { Label } from '@/components/UI/label';
import { useCompetition } from '@/helpers/context/competitionContext';
import { useTeam } from '@/helpers/context/createTeamContext';
import { Card } from '@/components/UI/card';
import { Button } from '@/components/UI/button';
import { DualRangeSlider } from '../UI/DualRangeSlider';
import { SmartPlayCricketPlayer } from '../../../types/commentry';
import type { Player } from '../../../types/competitions';

type Team = {
  homeTeam: boolean;
  awayTeam: boolean;
};

export type FiltersState = {
  matchesPlayed: boolean;
  priceRange: [number, number];
  dualPosition: boolean;
  teams: Team;
  breakeven: [number, number];
  projectedScore: [number, number];
  projectedValueChange: [number, number];
  selectionPercentage: [number, number];
};

type TeamKey = keyof FiltersState['teams'];
type FilterKey = keyof Omit<FiltersState, 'teams'>;

type CricketPlayerFilterProps<T> = {
  playerTable: Table<T>;
  filters: FiltersState;
  setFilters: React.Dispatch<React.SetStateAction<FiltersState>>;
  setShowFilter: React.Dispatch<React.SetStateAction<boolean>>;
  selectedTeam: string;
  setSelectedTeam: (team: string) => void;
  homeTeam?: string;
  awayTeam?: string;
};

const CricketPlayerFilter = <T extends { teamName: string }>({
  playerTable,
  filters,
  setFilters,
  setShowFilter,
  selectedTeam,
  setSelectedTeam,
  homeTeam,
  awayTeam,
}: CricketPlayerFilterProps<T>) => {
  const handleCheckboxChange = (
    key: FilterKey | TeamKey,
    isTeam: boolean = false,
  ) => {
    setFilters((prev) => {
      if (isTeam) {
        return {
          ...prev,
          teams: {
            ...prev.teams,
            [key]: !prev.teams[key as TeamKey],
          },
        };
      }
      return {
        ...prev,
        [key]: !prev[key as FilterKey],
      };
    });
  };

  const handleDualRangeChange =
    (key: keyof FiltersState) => (value: [number, number]) => {
      setFilters((prev) => ({ ...prev, [key]: value }));
    };

  const handleClearAll = () => {
    setFilters({
      matchesPlayed: false,
      priceRange: [0, 100],
      dualPosition: false,
      teams: {
        homeTeam: false,
        awayTeam: false,
      },
      breakeven: [0, 100],
      projectedScore: [0, 100],
      projectedValueChange: [6300, 9300],
      selectionPercentage: [0, 100],
    });

    setShowFilter(false);
    playerTable.resetColumnFilters();
    setTeam({ home: false, away: false });
  };

  const { eventDetailsResponse } = useCompetition();
  const circketFilterRef = useRef<HTMLDivElement>(null);
  const { team, setTeam } = useTeam();

  return (
    <Card className="p-4 shadow-none">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Filters</h3>
          <Button
            variant="outline"
            onClick={() => {
              playerTable.resetColumnFilters();
              playerTable.resetSorting();
              setSelectedTeam('all');
            }}
            className="text-sm"
          >
            Reset Filters
          </Button>
        </div>
        <div>
          <label className="block text-xl font-medium text-black-100 mb-2">
            By Team
          </label>
          <div className="space-y-2">
            <div className="flex items-center">
              <Checkbox
                id="all-teams"
                checked={selectedTeam === 'all'}
                onCheckedChange={() => {
                  playerTable.resetColumnFilters();
                  playerTable.resetSorting();
                  setSelectedTeam('all');
                }}
                className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
              />
              <label htmlFor="all-teams" className="ml-2 text-sm text-gray-700">
                All Teams
              </label>
            </div>
            <div className="flex items-center">
              <Checkbox
                id="home-team"
                checked={selectedTeam === homeTeam}
                onCheckedChange={() => {
                  if (homeTeam) {
                    playerTable.getColumn('teamName')?.setFilterValue(homeTeam);
                    setSelectedTeam(homeTeam);
                  }
                }}
                className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
              />
              <label htmlFor="home-team" className="ml-2 text-sm text-gray-700">
                {homeTeam}
              </label>
            </div>
            <div className="flex items-center">
              <Checkbox
                id="away-team"
                checked={selectedTeam === awayTeam}
                onCheckedChange={() => {
                  if (awayTeam) {
                    playerTable.getColumn('teamName')?.setFilterValue(awayTeam);
                    setSelectedTeam(awayTeam);
                  }
                }}
                className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
              />
              <label htmlFor="away-team" className="ml-2 text-sm text-gray-700">
                {awayTeam}
              </label>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CricketPlayerFilter;
