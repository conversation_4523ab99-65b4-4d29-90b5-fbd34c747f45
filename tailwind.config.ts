const withMT = require('@material-tailwind/react/utils/withMT');

module.exports = withMT({
  darkMode: ['class'],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      animation: {
        'spin-slow': 'spin 3s linear infinite',
        'spin-fast': 'spin 0.5s linear infinite',
      },
      screens: {
        'max-1600': {
          max: '1600px',
        },
        'max-1300': {
          max: '1300px',
        },
        'max-1280': {
          max: '1280px',
        },
        'max-1120': {
          max: '1120px',
        },
        'max-1024': {
          max: '1024px',
        },
        'max-999': {
          max: '999px',
        },
        'max-868': {
          max: '868px',
        },
        'max-799': {
          max: '799px',
        },
        'max-767': {
          max: '767px',
        },
        'max-639': {
          max: '639px',
        },
        'max-539': {
          max: '539px',
        },
        'max-479': {
          max: '479px',
        },
        'max-390': {
          max: '390px',
        },
        'max-379': {
          max: '379px',
        },
        'max-359': {
          max: '359px',
        },
      },
      backgroundImage: {
        'blue-gradient': 'linear-gradient(#003764, #090B0D)',
        'half-transparent':
          'linear-gradient(to bottom, transparent 50%, white 50%)',
        'tab-active-gradient':
          'linear-gradient(180deg, rgba(2,56,100,0.21) 0%, rgba(68,85,199,0.21) 33%, rgba(116,81,152,0.21) 60%, rgba(192,75,78,0.21) 81%, rgba(252,71,20,0.21) 100%)',
        'dark-card-gradient':
          'linear-gradient(180deg, rgba(0,55,100,1) 0%, rgba(0,55,100,0.7) 100%);',
        'live-score-gradient':
          'linear-gradient(to bottom, rgba(255, 198, 154, 0.5), rgba(201, 201, 201, 0.5))',
        'white-background': 'linear-gradient(to bottom, white, white)', // Added for white background
        'tipping-gradient': 'linear-gradient(to bottom, #003764, #090B0D)',
      },

      fontFamily: {
        inter: ['Inter', 'sans-serif'],
        arial: 'var(--font-arial)',
        peckhamPressTrial: 'var(--font-peckham-press-trial)',
        sfProTextRegular: 'var(--font-sf-pro-text-regular)',
        sfProTextSemibold: 'var(--font-sf-pro-text-semibold)',
        theStamshonsDemo: 'var(--font-the-stamshons-demo)',
        veneerItalic: 'var(--font-veneer-italic)',
        veneerCleanSoft: 'var(--font-veneer-clean-soft)',
        apotekCompRegular: 'var(--font-apotek-comp-regular)',
      },
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        white: '#FFFFFF',
        'off-white': {
          '100': '#FFFFFF',
          '200': '#F8F8F8',
        },
        lightborder: 'rgba(112, 112, 112, 0.13)',
        primary: {
          '200': '#003764',
          '300': '#7F9AB1',
          '400': '#335F83',
          '500': '#93A0AD',
          '600': '#4C7292',
        },
        secondary: {
          '100': '#4455C7',
          '200': '#0095FF',
          '300': '#4467FD',
        },
        success: {
          '100': '#1C9A6C',
        },
        negative: {
          '200': '#E2662C',
          '300': '#D84727',
        },

        gray: {
          '100': '#E8EAEC',
          '200': '#707070',
          '300': '#F2F3F5',
          '400': '#333333',
        },
        orange: {
          '200': '#FED1C4',
          '300': '#FDA289',
          '400': '#FC6B43',
          '500': '#FC4714',
        },
        blue: {
          '200': '#D0D4F1',
          '300': '#A1A9E3',
          '400': '#6977D2',
          '500': '#4455C7',
        },
        peach: {
          '200': '#FFF1E6',
          '300': '#FFE2CC',
          '400': '#FFD1AE',
          '500': '#FFC69A',
        },
        black: {
          '100': '#191919',
          '200': '#F7F9FB',
          '300': '#E7E9EC',
          '400': '#D4D6D8',
          '500': '#C9C9C9',
          '600': '#989898',
          '700': '#5D5D5D',
          '800': '#000000',
          '900': '#999999',
          '1000': '#080F16',
        },
        'bt-primery': {
          '500': '#4455C7',
        },
        menu_active_bg: '#e5eaef',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      boxShadow: {
        menu_shedow: '0px 3px 9px rgba(0, 0, 0, 0.16)',
      },
      fontSize: {
        xs: ['12px'],
        sm: ['14px', '20px'],
        base: ['14px'],
        regular: ['16px'],
        lg: ['18px', '28px'],
        xl: ['20px', '28px'],
        '2xl': ['24px', '32px'],
        '3xl': ['30px', '36px'],
        '4xl': ['36px', '44px'],
        '5xl': ['48px', '56px'],
        '6xl': ['60px', '72px'],
        '7xl': ['72px', '84px'],
        '8xl': ['96px', '108px'],
        '9xl': ['128px', '144px'],
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
});
