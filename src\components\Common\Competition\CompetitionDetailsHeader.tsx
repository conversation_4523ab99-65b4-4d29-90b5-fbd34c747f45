import Image from 'next/image';
import type { Dispatch, SetStateAction } from 'react';
import React from 'react';

import { Badge } from '@/components/UI/badge';
import { Button } from '@/components/UI/button';
import { Card } from '@/components/UI/card';
import SimpleTabs from '@/components/UI/SimpleTabs';
import { cn, formatNumberWithCommas, isObjectEmpty } from '@/lib/utils';
import SmileIconMotion from '@/components/motions/SmileIcon';
import { getDefaultProfileImage } from '../../../../db/db';
import { useCompetition } from '@/helpers/context/competitionContext';
import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';
import { usePathname, useSearchParams } from 'next/navigation';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import { SoccerAPIPlayer } from '@/helpers/context/soccer/createSoccerTeamContext';
import { CompetitionStatusProps } from '../../../../types';
import { Smile } from 'lucide-react';
import { useSoccerStore } from '@/store/soccer/useSoccerStore';
import { SoccerPlayer, SoccerPlayersByRole } from '@/lib/types/soccer';
import { usePlayerList } from '@/hooks/players/usePlayerList';

export interface ProfileStats {
  selectedPlayer?: string;
  remainingSalary: number;
  totalScore?: number;
}

type Status =
  | 'team-creation'
  | 'upcoming'
  | 'inprogress'
  | 'finished'
  | 'halftime';

interface TeamData {
  favouriteTeam?: any;
  expertTeam?: any;
}

interface EventDetails {
  salaryCap: number;
  eventName: string;
}

interface CompetitionData {
  currentRank?: number;
  totalRank?: number;
  totalScore?: number;
  totalLivePoints?: number;
}

interface TeamCreationActions {
  createFavouriteTeam: (data: any) => void;
  createExpertTeam: (data: any) => void;
  createLuckyTeam: (data: any) => void;
  setBudget: (amount: number) => void;
}

interface ProfileHeaderProps {
  stats: ProfileStats;
  status?: CompetitionStatusProps;
  activeTab?: string | number;
  setActiveTab?: Dispatch<SetStateAction<string | number>>;
  readOnly?: boolean;
  userProfile: {
    nickName: string;
    profilePicUrl?: string;
  };
  teamData?: TeamData;
  eventDetails?: EventDetails;
  teamCreationActions?: TeamCreationActions;
  competitionData?: CompetitionData;
  playerLimits: Record<string, number>;
  playersByRole: Record<string, any[]>;
  sportType: 'football' | 'rugby' | 'soccer';
}

const CompetitionDetailsHeader: React.FC<ProfileHeaderProps> = ({
  stats,
  status,
  activeTab,
  readOnly,
  setActiveTab,
  userProfile,
  teamData,
  eventDetails,
  teamCreationActions,
  competitionData,
  playerLimits,
  playersByRole,

  sportType,
}) => {
  const validTeamSelection =
    sportType === 'soccer'
      ? Object.values(playersByRole).reduce((a, b) => a + b?.length, 0) === 11
      : Object.values(playerLimits).reduce((a, b) => a + b, 0) ===
      Object.values(playersByRole).reduce((a, b) => a + b?.length, 0);

  const totalPlayersLimit = Object.values(playerLimits).reduce(
    (a, b) => a + b,
    0,
  );

  console.log(playerLimits, 'playerLimits');

  // Search Params
  const searchParams = useSearchParams();
  const add_more = searchParams.get('add_more');
  const eventId = searchParams.get('event_id');
  const tournamentId = searchParams.get('tournament_id');
  const sportId = searchParams.get('sport_id');
  const dreamTeamId = searchParams.get('dreamTeamId');
  const event_id = searchParams.get('event_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const seasonId = searchParams.get('seasonId');

  const { data: playerList } = usePlayerList<SoccerAPIPlayer>({
    eventId,
    tournamentId,
    sportId,
    seasonId,
  });

  // All Soccer Source Players
  const allSoccerSourcePlayers: SoccerPlayer[] =
    playerList?.result.map((player) => ({
      ...player,
      role:
        player.role === 'G'
          ? 'GKP'
          : player.role === 'D'
            ? 'DEF'
            : player.role === 'M'
              ? 'MID'
              : 'FWD',
    })) || [];

  // All Soccer Players By Role
  const allSoccerPlayersByRole: SoccerPlayersByRole = {
    GKP: allSoccerSourcePlayers.filter((player) => player.role === 'GKP'),
    DEF: allSoccerSourcePlayers.filter((player) => player.role === 'DEF'),
    MID: allSoccerSourcePlayers.filter((player) => player.role === 'MID'),
    FWD: allSoccerSourcePlayers.filter((player) => player.role === 'FWD'),
  };

  const {
    rugbyLeaguePlayersByRole,
    eventDetailsResponse,
    favouriteTeamResponse,
    AFLPlayersByRoles,
    allSoccerPlayers,
  } = useCompetition();
  const { createLuckyTeam, createFavouriteTeam, setBudget, createExpertTeam } =
    useRugbyLeagueContext();

  const {
    createLuckyTeam: createFootballLuckyTeam,
    setBudget: setFootballBudget,
    createFavouriteTeam: createFootballFavouriteTeam,
    createAFLExpertTeam,
  } = useFootballContext();

  const {
    createLuckyTeam: createSoccerLuckyTeam,
    setTotalBudget: setSoccerBudget,
    setRemainingBudget: setSoccerRemainingBudget,
    createFavoriteTeam: createSoccerFavoriteTeam,
    createExpertTeam: createSoccerExpertTeam,
  } = useSoccerStore();

  const pathname = usePathname();

  const handleCreateLuckyTeam = () => {
    const eventName = eventDetailsResponse?.result?.eventDetails?.eventName;
    const teamCount = eventDetailsResponse?.result?.dreamTeams?.length ?? 1;

    if (pathname.includes('rugby-league') && rugbyLeaguePlayersByRole) {
      setBudget(
        eventDetailsResponse?.result?.eventDetails?.RLSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      createLuckyTeam({
        playersByRole: rugbyLeaguePlayersByRole,
        competitionId: competition_id,
        eventId: event_id,
        eventName: eventName!,
        sportId: '4',
        tournamentId: tournament_id,
        name: `Team ${teamCount}`,
      });
    } else if (pathname.includes('football') && AFLPlayersByRoles) {
      setFootballBudget(
        eventDetailsResponse?.result?.eventDetails?.ARSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      createFootballLuckyTeam({
        playersByRole: AFLPlayersByRoles,
        competitionId: competition_id,
        eventId: event_id,
        eventName: eventName!,
        sportId: '9',
        tournamentId: tournament_id,
        name: `Team ${teamCount}`,
      });
    } else if (pathname.includes('soccer') && allSoccerPlayers) {
      setSoccerBudget(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      setSoccerRemainingBudget(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );

      createSoccerLuckyTeam(allSoccerPlayersByRole);
    }
  };

  const handelCreateFavouriteTeam = () => {
    if (pathname.includes('rugby-league')) {
      setBudget(
        eventDetailsResponse?.result?.eventDetails?.RLSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      createFavouriteTeam({
        // @ts-ignore
        favoriteTeam: favouriteTeamResponse?.result?.favoriteTeam,
        playersByRole: rugbyLeaguePlayersByRole,
      });
    }

    if (pathname.includes('football')) {
      setFootballBudget(
        eventDetailsResponse?.result?.eventDetails?.ARSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      createFootballFavouriteTeam({
        // @ts-ignore
        favoriteTeam: favouriteTeamResponse?.result?.favoriteTeam,
        playersByRole: AFLPlayersByRoles,
      });
    }

    if (pathname.includes('soccer')) {
      setSoccerBudget(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      setSoccerRemainingBudget(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      createSoccerFavoriteTeam(
        // @ts-ignore
        favouriteTeamResponse?.result?.favoriteTeam,
        allSoccerPlayers,
        event_id!,
        eventDetailsResponse?.result?.eventDetails?.eventName!,
        '8',
        tournament_id!,
        competition_id!,
        `Team ${eventDetailsResponse?.result?.dreamTeams?.length}`,
      );
    }
  };

  const handelCreateExpertTeam = () => {
    if (pathname.includes('rugby-league')) {
      setBudget(
        eventDetailsResponse?.result?.eventDetails?.RLSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      createExpertTeam(
        // @ts-ignore
        favouriteTeamResponse?.result?.expertFavorite?.DreamTeamPlayers,
        rugbyLeaguePlayersByRole,
      );
    }

    if (pathname.includes('football')) {
      setFootballBudget(
        eventDetailsResponse?.result?.eventDetails?.ARSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      createAFLExpertTeam(
        // @ts-ignore
        favouriteTeamResponse?.result?.expertFavorite?.DreamTeamPlayers,
        AFLPlayersByRoles,
      );
    }

    if (pathname.includes('soccer')) {
      setSoccerBudget(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      createSoccerExpertTeam(
        // @ts-ignore
        favouriteTeamResponse?.result?.expertFavorite?.DreamTeamPlayers,
        allSoccerPlayers,
      );
    }
  };

  const renderUserProfile = () => (
    <div className="flex items-start flex-col w-full">
      {(status === 'upcoming' ||
        status === 'inprogress' ||
        status === 'finished' ||
        status === 'halftime') && (
          <div className="flex space-x-4 justify-center items-center">
            <div className="relative">
              <Image
                src={userProfile.profilePicUrl || getDefaultProfileImage()}
                height={65}
                width={65}
                style={{
                  width: '65px',
                  height: '65px',
                  objectFit: 'cover',
                }}
                alt="profile"
                className="rounded-full"
                unoptimized={true}
              />
              {status === 'inprogress' && (
                <Badge className="bg-negative-300 text-white rounded absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                  LIVE
                </Badge>
              )}
            </div>
            <div>
              <div className="text-[22.4px] font-semibold text-black-100">
                {userProfile.nickName}
              </div>
              {!readOnly && (
                <SimpleTabs
                  tabs={[
                    { id: 1, name: 'Preview' },
                    { id: 2, name: 'List' },
                  ]}
                  className="mt-[12px]"
                  activeTab={activeTab!}
                  setActiveTab={setActiveTab!}
                  size={'medium'}
                  variant="default"
                />
              )}
            </div>
          </div>
        )}
    </div>
  );

  const renderTeamCreationSection = () => (
    <div className="flex items-start flex-col w-full">
      {status === 'team-creation' && (
        <div>
          <div>
            <h2 className="text-[16px] font-semibold mb-[3px]">
              {dreamTeamId ? 'Update Team' : 'Create Team'}
            </h2>
            <p className="text-[12px] text-black-700 leading-[15px] mb-3">
              Choose your{' '}
              <span className="font-bold text-bt-primery-500">
                {totalPlayersLimit} players
              </span>{' '}
              and submit your team to enter the competition.
            </p>
          </div>
          <div className="grid grid-cols-3 gap-x-1">
            {favouriteTeamResponse?.result?.favoriteTeam &&
              !isObjectEmpty(favouriteTeamResponse?.result?.favoriteTeam) && (
                <Button
                  size={'sm'}
                  className="lg:text-[16px] text-[12px]"
                  onClick={handelCreateFavouriteTeam}
                >
                  Favourites
                </Button>
              )}

            {favouriteTeamResponse?.result?.expertFavorite &&
              !isObjectEmpty(favouriteTeamResponse?.result?.expertFavorite) && (
                <Button
                  size={'sm'}
                  className="lg:text-[16px] text-[12px]"
                  onClick={handelCreateExpertTeam}
                >
                  Expert Favourites
                </Button>
              )}

            <Button
              size={'sm'}
              className="lg:text-[16px] text-[12px] flex items-center gap-2"
              onClick={handleCreateLuckyTeam}
            >
              <Smile className="text-white" size={20} />
              <span>Feeling Lucky</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );

  const renderStatsCards = () => (
    <div className="hidden lg:flex w-full">
      <div className="flex items-end gap-4 justify-end w-full h-full">
        {status === 'inprogress' && (
          <Card className="p-3 shadow-sm w-full bg-live-score-gradient border-[#FC4714]">
            <div className="text-center">
              <div className="text-sm text-slate-500 mb-1 text-[#FDA289]">
                Live Score
              </div>
              <div className="font-semibold text-slate-900 text-[#FC4714]">
                {competitionData?.totalLivePoints ?? 0}
              </div>
            </div>
          </Card>
        )}

        {(status === 'finished' ||
          status === 'upcoming' ||
          status === 'team-creation') && (
            <Card className="p-3 shadow-sm w-full bg-dark-card-gradient">
              <div className="text-center">
                <div className="text-xs text-[#BFCCD8] mb-1">
                  {status === 'upcoming' || status === 'team-creation'
                    ? 'Remaining Salary:'
                    : 'Team Value'}
                </div>
                <div className="font-semibold text-white">
                  ${formatNumberWithCommas(stats.remainingSalary)}
                </div>
              </div>
            </Card>
          )}

        {status === 'finished' && (
          <Card className="p-3 shadow-sm w-full bg-dark-card-gradient text-white">
            <div className="text-center">
              <div className="text-sm text-slate-500 mb-1">Total Score:</div>
              <div className="font-semibold text-slate-900">
                {competitionData?.totalScore ?? 0}
              </div>
            </div>
          </Card>
        )}

        {status === 'team-creation' && (
          <Card
            className={cn(
              'p-3 shadow-sm w-full text-white hidden md:block',
              validTeamSelection
                ? 'bg-[#D1E7DF] border border-[#B4DBCD]'
                : 'bg-dark-card-gradient',
            )}
          >
            <div className="text-center">
              <div
                className={cn(
                  'text-xs text-[#BFCCD8] mb-1',
                  validTeamSelection ? 'text-black-700' : 'text-slate-500',
                )}
              >
                <span className={cn(
                  'text-xs   mb-1',
                  validTeamSelection ? 'text-black-700' : 'text-[#BFCCD8]',
                )}>Selected Players:</span>
              </div>
              <div
                className={cn(
                  'font-semibold',
                  validTeamSelection ? 'text-[#1C9A6C]' : 'text-slate-900',
                )}
              >
                {stats.selectedPlayer}
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );

  return (
    <div className={`flex items-center w-full`}>
      <div className="grid grid-cols-1 md:grid-cols-2 w-full my-2 gap-8">
        {status === 'team-creation'
          ? renderTeamCreationSection()
          : renderUserProfile()}
        {renderStatsCards()}
      </div>
    </div>
  );
};

export default CompetitionDetailsHeader;
