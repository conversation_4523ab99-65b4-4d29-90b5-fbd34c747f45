'use client';

import React from 'react';

const OddsLogo = ({ value }: { value: number }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="70"
      height="52"
      viewBox="0 0 70 52"
    >
      <g
        id="Group_82816"
        data-name="Group 82816"
        transform="translate(0 0.069)"
      >
        <g id="BoomBet" transform="translate(0 27)">
          <path
            id="Rectangle_19866"
            data-name="Rectangle 19866"
            d="M0,0H70a0,0,0,0,1,0,0V19a6,6,0,0,1-6,6H6a6,6,0,0,1-6-6V0A0,0,0,0,1,0,0Z"
            transform="translate(0 -0.069)"
            fill="#027b5b"
          />
          <g
            id="Group_42170"
            data-name="Group 42170"
            transform="translate(10.487 6.992)"
          >
            <path
              id="Path_40584"
              data-name="Path 40584"
              d="M18.974,18.277q1.628,0,3.254,0c-.01,1.428,0,2.855-.007,4.282a2.468,2.468,0,0,1,1.237-1,2.676,2.676,0,0,1,2.759.666,4.307,4.307,0,0,1,.939,2.793,5.747,5.747,0,0,1-.573,2.92,2.5,2.5,0,0,1-1.62,1.267,2.678,2.678,0,0,1-2.092-.283,2.8,2.8,0,0,1-.856-1.179c.014.462-.012.922-.021,1.383h-3.02q0-5.424,0-10.847"
              transform="translate(-18.973 -18.276)"
              fill="#fafcfc"
            />
            <path
              id="Path_40585"
              data-name="Path 40585"
              d="M41.991,20.01c1.087-.35,2.159-.744,3.246-1.094-.009.947,0,1.895,0,2.842h1.483c-.006.716,0,1.432,0,2.149-.494.008-.99-.013-1.484.011.015.744-.008,1.488.011,2.231a.893.893,0,0,0,.48.837,2.019,2.019,0,0,0,1-.091c-.007.718,0,1.438,0,2.156a6.465,6.465,0,0,1-2.822.344,2.055,2.055,0,0,1-1.441-.887,3.51,3.51,0,0,1-.472-1.821c0-.926,0-1.853,0-2.779-.379,0-.757,0-1.136.007-.014-.718,0-1.438,0-2.158.38,0,.76,0,1.14,0,.008-.582-.016-1.166.012-1.747"
              transform="translate(-24.127 -18.428)"
              fill="#fafcfc"
            />
            <path
              id="Path_40586"
              data-name="Path 40586"
              d="M49.52,19.469a9.218,9.218,0,0,1,3.316-.394,4.52,4.52,0,0,1,2.82,1.016,2.437,2.437,0,0,1-.01,3.575,2.8,2.8,0,0,1-.935.5,2.885,2.885,0,0,1,1.316.815,2.615,2.615,0,0,1-.926,3.931,6.669,6.669,0,0,1-3.85.5,10.8,10.8,0,0,1-1.791-.338c-.024-.78,0-1.562-.012-2.342a5.843,5.843,0,0,0,3.163.3.868.868,0,0,0,.111-1.571,3.7,3.7,0,0,0-2.151-.109c0-.737,0-1.474,0-2.212a7.855,7.855,0,0,0,1.647-.034.914.914,0,0,0,.791-.576.985.985,0,0,0-.176-.828,1.271,1.271,0,0,0-.9-.382,6.039,6.039,0,0,0-2.393.382c-.026-.745,0-1.492-.017-2.237"
              transform="translate(-26.157 -18.464)"
              fill="#f9dc1c"
            />
            <path
              id="Path_40587"
              data-name="Path 40587"
              d="M60.5,20.639a4.4,4.4,0,0,1,3.12-1.553,10.883,10.883,0,0,1,3.311.264c-.015.77,0,1.54-.008,2.31a7.129,7.129,0,0,0-2.7-.316,1.859,1.859,0,0,0-1.389.86,2.522,2.522,0,0,0-.271,1.107,3.449,3.449,0,0,1,2.349-.587,3.056,3.056,0,0,1,2.48,1.61,3.734,3.734,0,0,1,.307,2.244,3.116,3.116,0,0,1-1.2,2.041,4.633,4.633,0,0,1-3.428.815,4.015,4.015,0,0,1-2.646-1.495,5.277,5.277,0,0,1-1.11-3.267A6,6,0,0,1,60.5,20.639"
              transform="translate(-28.482 -18.463)"
              fill="#f9dc1c"
            />
            <path
              id="Path_40588"
              data-name="Path 40588"
              d="M71.229,19.3H77.6c0,.773,0,1.547,0,2.32-1.137.009-2.276-.016-3.412.013.011.512.013,1.026,0,1.539a5.348,5.348,0,0,1,1.936.114,2.793,2.793,0,0,1,1.788,2.005,3.694,3.694,0,0,1-.313,2.644,3.248,3.248,0,0,1-2.037,1.4,8.805,8.805,0,0,1-4.439-.229c-.021-.768,0-1.536-.009-2.3a5.192,5.192,0,0,0,2.848.159,1.115,1.115,0,0,0,.28-1.875,2.269,2.269,0,0,0-1.292-.362,6.127,6.127,0,0,0-1.491.173.7.7,0,0,0-.277.141,1.266,1.266,0,0,0,.047-.254q0-2.746,0-5.491"
              transform="translate(-31.265 -18.517)"
              fill="#f9dc1c"
            />
            <path
              id="Path_40589"
              data-name="Path 40589"
              d="M31.392,23.478a4.41,4.41,0,0,1,3.192-1.069,3.393,3.393,0,0,1,2.409.786,3.951,3.951,0,0,1,1.1,2.4,10.2,10.2,0,0,1,.071,1.609H33.643a1.11,1.11,0,0,0,.281.737,2.1,2.1,0,0,0,1.353.417,4.841,4.841,0,0,0,2.411-.475c-.006.647,0,1.3-.005,1.946a9.173,9.173,0,0,1-2.87.434,4.636,4.636,0,0,1-3.139-.911,3.8,3.8,0,0,1-1.2-2.759,4.015,4.015,0,0,1,.919-3.117"
              transform="translate(-21.681 -19.251)"
              fill="#fafcfc"
            />
            <path
              id="Path_40590"
              data-name="Path 40590"
              d="M34.918,24.96a.728.728,0,0,1,1.159-.009,2.234,2.234,0,0,1,.255,1.206h-1.7a2.165,2.165,0,0,1,.285-1.2"
              transform="translate(-22.665 -19.783)"
              fill="#027b5b"
            />
            <path
              id="Path_40591"
              data-name="Path 40591"
              d="M23.746,25.349a.681.681,0,0,1,.8.345,2.9,2.9,0,0,1,.2,1.284,2.418,2.418,0,0,1-.284,1.273.678.678,0,0,1-1.1-.009,2.247,2.247,0,0,1-.244-1.189,3.956,3.956,0,0,1,.115-1.166.749.749,0,0,1,.507-.538"
              transform="translate(-19.952 -19.936)"
              fill="#027b5b"
            />
            <path
              id="Path_40592"
              data-name="Path 40592"
              d="M64.256,26.394a1.091,1.091,0,0,1,1.3.408,1.747,1.747,0,0,1-.041,1.772A1.038,1.038,0,0,1,63.9,28.7a1.692,1.692,0,0,1-.274-1.474,1.065,1.065,0,0,1,.63-.828"
              transform="translate(-29.487 -20.173)"
              fill="#027b5b"
            />
          </g>
        </g>
        <g
          id="Best_odds_-_Mobile"
          data-name="Best odds - Mobile"
          transform="translate(0 -0.069)"
        >
          <path
            id="Path_95141"
            data-name="Path 95141"
            d="M5.25,0h59.5C67.649,0,70,1.909,70,4.263V27H0V4.263C0,1.909,2.351,0,5.25,0Z"
            fill="#d6d9f3"
          />
          <text
            id="_1.73"
            data-name="1.73"
            transform="translate(35 19)"
            fill="#4455c7"
            fontSize="14"
            fontFamily="Inter-Bold, Inter"
            fontWeight="700"
          >
            <tspan x="-13.712" y="0">
              {value}
            </tspan>
          </text>
        </g>
      </g>
    </svg>
  );
};

export default OddsLogo;
