import type { Table } from '@tanstack/react-table';
import { X } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import { useRef } from 'react';

import { Checkbox } from '@/components/UI/checkbox';
import { Label } from '@/components/UI/label';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Card } from '@/components/UI/card';
import { Button } from '@/components/UI/button';
import { SoccerPlayer } from '@/lib/types/soccer';

export type SoccerFiltersState = {
  selectedTeam: string;
};

type SoccerPlayerFilterTypeProps = {
  playerTable: Table<SoccerPlayer>;
  filters: SoccerFiltersState;
  setFilters: Dispatch<SetStateAction<SoccerFiltersState>>;
  setShowFilter: (show: boolean) => void;
};

export default function SoccerPlayerFilter({
  playerTable,
  filters,
  setFilters,
  setShowFilter,
}: Readonly<SoccerPlayerFilterTypeProps>) {
  const { eventDetailsResponse } = useCompetition();
  const homeTeam = eventDetailsResponse?.result?.eventDetails?.homeTeam?.name;
  const awayTeam = eventDetailsResponse?.result?.eventDetails?.awayTeam?.name;

  const handleTeamChange = (team: string) => {
    setFilters((prev) => ({
      ...prev,
      selectedTeam: team,
    }));

    if (team === 'all') {
      playerTable.getColumn('teamName')?.setFilterValue('');
    } else {
      playerTable.getColumn('teamName')?.setFilterValue(team);
    }
  };

  const handleClearAll = () => {
    setFilters({
      selectedTeam: 'all',
    });
    setShowFilter(false);
    playerTable.resetColumnFilters();
  };

  const soccerFilterRef = useRef<HTMLDivElement>(null);

  return (
    <section ref={soccerFilterRef}>
      <Card className="p-4 shadow-none">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Filters</h3>
            <Button
              variant="outline"
              onClick={handleClearAll}
              className="text-sm"
            >
              Reset Filters
            </Button>
          </div>
          <div>
            <label className="block text-xl font-medium text-black-100 mb-2">
              By Team
            </label>
            <div className="space-y-2">
              <div className="flex items-center">
                <Checkbox
                  id="all-teams"
                  checked={filters.selectedTeam === 'all'}
                  onCheckedChange={() => handleTeamChange('all')}
                  className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                />
                <label
                  htmlFor="all-teams"
                  className="ml-2 text-sm text-gray-700"
                >
                  All Teams
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox
                  id="home-team"
                  checked={filters.selectedTeam === homeTeam}
                  onCheckedChange={() => handleTeamChange(homeTeam || '')}
                  className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                />
                <label
                  htmlFor="home-team"
                  className="ml-2 text-sm text-gray-700"
                >
                  {homeTeam}
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox
                  id="away-team"
                  checked={filters.selectedTeam === awayTeam}
                  onCheckedChange={() => handleTeamChange(awayTeam || '')}
                  className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                />
                <label
                  htmlFor="away-team"
                  className="ml-2 text-sm text-gray-700"
                >
                  {awayTeam}
                </label>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </section>
  );
}
