'use client';
import SportsRule from '@/components/Common/SportsRule';
import Scoring from '@/components/RulesScoring/Scoring';
import { useSearchParams } from 'next/navigation';
import sportsData from '../../../../data/sportsData.json';
import { SportsData } from '../../../../types';
export default function RulesScoring() {
  const searchParam = useSearchParams();
  const sport = searchParam.get('sports');
  const data: SportsData = sportsData as unknown as SportsData;
  const rules_scoring = searchParam.get('rules_scoring');
  return (
    <div className="mt-4 rounded-lg bg-off-white-200">
      <div className="bg-off-white-200 pt-[18px] max-799:pt-[9px] pb-[45px] max-799:pb-[22px] px-[27px] max-799:px-0">
        {rules_scoring === 'rules' ? (
          <SportsRule sport={sport} />
        ) : (
          <Scoring sportScoring={data[sport ?? ''].scoring} />
        )}
      </div>
    </div>
  );
}
