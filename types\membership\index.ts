import type { Dispatch, SetStateAction } from 'react';

import type { MembershipFormData } from '@/components/Settings/BuyCoins/Membership/ActiveMembership';

// Types for the Plan data
export interface Plan {
  SubscriptionPlanId: string;
  // Add other plan properties as needed
}

// Types for API payloads
export interface HoldPlanPayload {
  holdType: string;
  startDate: string;
  holdStatus: string;
  planId: string | undefined;
  plateForm: string;
  endDate?: string;
}

export interface CancelPlanPayload {
  SubscriptionPlanId: string | undefined;
  category: string;
}

// Types for handler functions
export type DateChangeHandler = (newValue: Date | null) => void;
export type MembershipChangeHandler = (value: string) => void;
export type ModalHandler = () => void;
export type MembershipActionHandler = () => Promise<void>;
export type MembershipFormActionHandler = (
  formData: MembershipFormData,
) => Promise<void>;

// Context value type (your existing type with better organization)
export interface UserMembershipContextType {
  // Modal states
  cancelMembershipModal: boolean;
  setCancelMembershipModal: Dispatch<SetStateAction<boolean>>;
  resumeMembershipModal: boolean;
  setResumeMembershipModal: Dispatch<SetStateAction<boolean>>;

  // Hold configuration
  holdType: string;
  setHoldType: Dispatch<SetStateAction<string>>;
  dateFrom: string | null;
  setDateFrom: Dispatch<SetStateAction<string | null>>;
  dateTo: string | null;
  setDateTo: Dispatch<SetStateAction<string | null>>;

  // Membership state
  selectedMembership: string;
  setSelectedMembership: Dispatch<SetStateAction<string>>;
  activeMembership: boolean;
  setActiveMembership: Dispatch<SetStateAction<boolean>>;
  startHoldDateRequired: boolean;
  endHoldDateRequired: boolean;
  setStartHoldDateRequired: Dispatch<SetStateAction<boolean>>;
  setEndHoldDateRequired: Dispatch<SetStateAction<boolean>>;

  // Handler functions
  handleFromDateChange: DateChangeHandler;
  handleToDateChange: DateChangeHandler;
  handleMembershipChanges: MembershipChangeHandler;
  handleCancelMembershipModal: ModalHandler;
  handleResumeMembership: ModalHandler;
  handleResumeMembershipClose: ModalHandler;
  handleMembershipHold: MembershipFormActionHandler;
  handelCanceActiveMembership: MembershipActionHandler;
  handleResumeMembershipPlan: MembershipActionHandler;
}

// Provider props type
export interface UserMembershipProviderProps {
  children: React.ReactNode;
}

// Plan context return type
export interface PlanContextType {
  cancelCurrentPlan: (payload: CancelPlanPayload) => void;
  currentPlan: Plan | null;
  resumePlan: (planId: string) => void;
  holdActivePlan: (payload: HoldPlanPayload) => void;
  isResumePlanSuccess: boolean;
  isHoldPlanSuccess: boolean;
  isSuccessUpdatePlan: boolean;
  isCancelPlanSuccess: boolean;
}

type PlayerRole = 'WKP' | 'BAT' | 'BOW' | 'ALL';
export type TeamComposition = {
  [key in PlayerRole]: {
    min: number;
    max: number;
  };
} & {
  TOTAL_PLAYERS: number;
};
