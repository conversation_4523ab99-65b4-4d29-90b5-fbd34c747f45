'use client';
import moment from 'moment';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';

import type { MembershipFormData } from '@/components/Settings/BuyCoins/Membership/ActiveMembership';

import type {
  UserMembershipContextType,
  UserMembershipProviderProps,
} from '../../../types/membership';
import { usePlanContext } from './userPlanContext';

const userMemeberShipContext = createContext<
  UserMembershipContextType | undefined
>(undefined);

const UserMemeberShipProvider = ({ children }: UserMembershipProviderProps) => {
  const [cancelMembershipModal, setCancelMembershipModal] =
    useState<boolean>(false);
  const [resumeMembershipModal, setResumeMembershipModal] =
    useState<boolean>(false);
  const [holdType, setHoldType] = useState('duration');
  const [dateFrom, setDateFrom] = useState<string | null>(null);
  const [dateTo, setDateTo] = useState<string | null>(null);
  const [startHoldDateRequired, setStartHoldDateRequired] =
    useState<boolean>(false);

  const [endHoldDateRequired, setEndHoldDateRequired] =
    useState<boolean>(false);

  const {
    cancelCurrentPlan,
    currentPlan,
    resumePlan,
    holdActivePlan,
    isResumePlanSuccess,
    isHoldPlanSuccess,
    isSuccessUpdatePlan,
    isCancelPlanSuccess,
  } = usePlanContext();

  const [activeMembership, setActiveMembership] = useState<boolean>(false);

  const [selectedMembership, setSelectedMembership] =
    useState<string>('ManageMembership');

  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const handleFromDateChange = (newValue: Date | null) => {
    // Convert Date to string if needed, or handle null value
    setDateFrom(
      newValue ? moment(newValue).tz(timezone).format('YYYY-MM-DD') : null,
    );
    setStartHoldDateRequired(false);
  };

  const handleToDateChange = (newValue: Date | null) => {
    // Convert Date to string if needed, or handle null value
    setDateTo(
      newValue ? moment(newValue).tz(timezone).format('YYYY-MM-DD') : null,
    );
    setEndHoldDateRequired(false);
  };

  const handleMembershipChanges = (value: any) => {
    setSelectedMembership(value);
    if (value === 'CancelMembership') {
      setCancelMembershipModal(true);
    } else if (value === 'UpgradeMembership') {
      setActiveMembership(true);
    }
  };

  const handleCancelMembershipModal = () => {
    setCancelMembershipModal(false);
  };

  const handleResumeMembership = () => {
    setResumeMembershipModal(true);
  };

  const handleResumeMembershipClose = () => {
    setResumeMembershipModal(false);
  };

  const handleMembershipHold = async (formData: MembershipFormData) => {
    const todayDate = moment().format('YYYY-MM-DD');
    const payload = {
      holdType: formData.durationType,
      startDate:
        formData.durationType === 'duration'
          ? moment(formData.startDate).format('YYYY-MM-DD')
          : todayDate,
      holdStatus: 'hold',
      planId: currentPlan?.SubscriptionPlanId,
      plateForm: 'web',
      ...(formData.durationType === 'duration' && {
        endDate: moment(formData.endDate).format('YYYY-MM-DD'),
      }),
    };
    holdActivePlan(payload);
  };

  const handelCanceActiveMembership = async () => {
    cancelCurrentPlan({
      SubscriptionPlanId: currentPlan?.SubscriptionPlanId,
      category: 'fantasy',
    });
  };

  const handleResumeMembershipPlan = async () => {
    resumePlan(currentPlan?.SubscriptionPlanId);
  };

  useEffect(() => {
    if (isResumePlanSuccess) {
      handleResumeMembershipClose();
    }

    if (isHoldPlanSuccess || isSuccessUpdatePlan) {
      setSelectedMembership('ManageMembership');
    }

    if (isCancelPlanSuccess) {
      handleCancelMembershipModal();
    }
  }, [
    isResumePlanSuccess,
    isHoldPlanSuccess,
    isSuccessUpdatePlan,
    isCancelPlanSuccess,
  ]);
  // For initial page show
  useEffect(() => {
    setActiveMembership(
      currentPlan?.status === 'active' ||
        currentPlan?.status === 'hold' ||
        currentPlan?.status === 'cancelled',
    );
  }, [currentPlan]);

  useEffect(() => {
    if (currentPlan?.status === 'cancelled') {
      setSelectedMembership('ManageMembership');
    }
  }, [currentPlan]);

  // Effect for redirction
  useEffect(() => {
    // Handle selected membership logic without directly using the setter with its matching state variable
    if (selectedMembership === 'UpgradeMembership') {
      setActiveMembership(false);
    } else if (selectedMembership === 'CancelMembership') {
      setActiveMembership(true);
      setCancelMembershipModal(true);
    } else if (
      selectedMembership === 'HoldMembership' ||
      selectedMembership === 'ManageMembership'
    ) {
      setActiveMembership(true);
    }
  }, [selectedMembership]);

  const userMemerShipState = useMemo(
    () => ({
      cancelMembershipModal,
      dateFrom,
      dateTo,
      holdType,
      resumeMembershipModal,
      endHoldDateRequired,
      startHoldDateRequired,
      activeMembership,
      selectedMembership,
      setCancelMembershipModal,
      setDateFrom,
      setDateTo,
      setHoldType,
      setResumeMembershipModal,
      setActiveMembership,
      setSelectedMembership,
      handelCanceActiveMembership,
      handleCancelMembershipModal,
      handleFromDateChange,
      handleMembershipChanges,
      handleMembershipHold,
      handleResumeMembership,
      handleResumeMembershipClose,
      handleResumeMembershipPlan,
      handleToDateChange,
      setEndHoldDateRequired,
      setStartHoldDateRequired,
    }),
    [
      cancelMembershipModal,
      dateFrom,
      dateTo,
      holdType,
      resumeMembershipModal,
      endHoldDateRequired,
      startHoldDateRequired,
      activeMembership,
      selectedMembership,
      setCancelMembershipModal,
      setDateFrom,
      setDateTo,
      setHoldType,
      setResumeMembershipModal,
      setActiveMembership,
      setSelectedMembership,
      handelCanceActiveMembership,
      handleCancelMembershipModal,
      handleFromDateChange,
      handleMembershipChanges,
      handleMembershipHold,
      handleResumeMembership,
      handleResumeMembershipClose,
      handleResumeMembershipPlan,
      handleToDateChange,
      setEndHoldDateRequired,
      setStartHoldDateRequired,
    ],
  );

  return (
    <userMemeberShipContext.Provider value={userMemerShipState}>
      {children}
    </userMemeberShipContext.Provider>
  );
};

export const useUserMemeberShip = () => {
  const context = useContext(userMemeberShipContext);
  if (!context) {
    throw new Error(
      'usePlanContext must be used within a UserPlanContextProvider',
    );
  }
  return context;
};

export default UserMemeberShipProvider;
