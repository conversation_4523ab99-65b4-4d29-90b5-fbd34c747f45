'use client';

const RoundDeleteIcon = () => {
  return (
    <svg
      id="Group_119414"
      data-name="Group 119414"
      xmlns="http://www.w3.org/2000/svg"
      width="30"
      height="30"
      viewBox="0 0 30 30"
    >
      <rect
        id="Rectangle_2967"
        data-name="Rectangle 2967"
        width="30"
        height="30"
        rx="15"
        fill="#f4ddd3"
      />
      <g
        id="Group_24052"
        data-name="Group 24052"
        transform="translate(7.15 6.96)"
      >
        <path
          id="Path_12113"
          data-name="Path 12113"
          d="M4,6H16.967L15.687,17.525a1.621,1.621,0,0,1-1.611,1.443H6.892a1.621,1.621,0,0,1-1.611-1.443Z"
          transform="translate(-2.379 -2.758)"
          fill="none"
          stroke="#d84727"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        />
        <path
          id="Path_12114"
          data-name="Path 12114"
          d="M7.09,2.93A1.621,1.621,0,0,1,8.556,2h4.613a1.621,1.621,0,0,1,1.467.93l1.089,2.312H6Z"
          transform="translate(-2.758 -2)"
          fill="none"
          stroke="#d84727"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        />
        <path
          id="Path_12115"
          data-name="Path 12115"
          d="M2,6H18.209"
          transform="translate(-2 -2.758)"
          fill="none"
          stroke="#d84727"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        />
        <path
          id="Path_12116"
          data-name="Path 12116"
          d="M10,11v4.052"
          transform="translate(-3.516 -3.706)"
          fill="none"
          stroke="#d84727"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        />
        <path
          id="Path_12117"
          data-name="Path 12117"
          d="M14,11v4.052"
          transform="translate(-4.274 -3.706)"
          fill="none"
          stroke="#d84727"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        />
      </g>
    </svg>
  );
};

export default RoundDeleteIcon;
