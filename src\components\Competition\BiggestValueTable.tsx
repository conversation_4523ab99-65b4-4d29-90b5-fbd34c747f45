import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import React from 'react';

import { LIVE_POLLIN_TIME } from '@/helpers/constants/index';
import { useCompetition } from '@/helpers/context/competitionContext';
import { quyerKeys } from '@/lib/queryKeys';
import { formatNumberWithCommas } from '@/lib/utils';

import { getDefaultProfileImage } from '../../../db/db';
import { Badge } from '../UI/badge';
import { fetchLeaderDetails } from './LeaderBorad';
import PlayerValueChange from './PlayerValueChange';

const BiggestValueTable = () => {
  const { eventDetailsResponse } = useCompetition();
  const searchParams = useSearchParams();
  const event_id = searchParams.get('event_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const { data: leaderList } = useQuery({
    queryFn: () =>
      fetchLeaderDetails(
        competition_id,
        event_id,
        tournament_id,
        eventDetailsResponse?.result?.eventDetails?.status,
      ),
    refetchInterval:
      eventDetailsResponse?.result?.eventDetails?.status === 'inprogress'
        ? LIVE_POLLIN_TIME
        : false,
    queryKey: [quyerKeys.getLeaderBoardList],
    enabled: !!tournament_id,
  });

  const leaderBoardList = (leaderList?.result || []).filter(
    (user) => user.totalCurrentSalary - user.teamValue > 0,
  );

  if (leaderBoardList.length === 0) {
    return (
      <div className="rounded py-2 bg-primary-200 text-white rounded-t-md flex justify-center items-center">
        No biggest value raised
      </div>
    );
  }

  return (
    <table className="w-full rounded-md shadow-md overflow-hidden">
      <thead className="bg-primary-200 text-white rounded-t-md">
        <tr className="text-base">
          <th className="font-semibold text-center px-2 py-1">Team/Coach</th>
          <th className="font-semibold text-center px-2 py-1">
            Value increased by
          </th>
        </tr>
      </thead>
      <tbody className="mt-2">
        {leaderBoardList.map((data, index) => (
          <tr
            key={data?.id}
            className={`${index % 2 === 1 ? 'bg-gray-50' : ''} text-sm`}
          >
            <td className="text-center px-2 py-1 capitalize">
              <div className="flex justify-between">
                <div className="flex w-full justify-between">
                  <div className="flex gap-2 items-center">
                    <div className="flex justify-start items-center">
                      {index + 1}
                    </div>
                    <div>
                      <Image
                        src={getDefaultProfileImage()}
                        width={36}
                        height={36}
                        alt="team"
                        unoptimized={true}
                      />
                    </div>
                  </div>
                  <div className="flex flex-row space-x-1 w-full justify-between pl-1">
                    <div className="flex justify-start items-center">
                      {data?.firstName + ' ' + data?.lastName}
                    </div>
                    <Badge
                      variant={'default'}
                      className="text-white flex justify-center items-center"
                    >
                      {data?.name}
                    </Badge>
                  </div>
                </div>
              </div>
            </td>
            <td>
              <div className="flex justify-center">
                <PlayerValueChange
                  playerCurrentSalary={data.totalCurrentSalary}
                  playerLastSalary={data.teamValue}
                  formatToCustomStyle={formatNumberWithCommas}
                  showDifference
                />
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default BiggestValueTable;
