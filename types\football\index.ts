import { Dispatch, SetStateAction } from 'react';
import {
  CreateAFLTeamPayload,
  FantasyTeamResponse,
  FootballPlayer,
  LastEntryType,
} from '../competitions';
import { CompetitionStatusProps } from '..';

export type competitionHeaderType =
  | 'MYTEAM'
  | 'LEADERBOARD'
  | 'PRIZES'
  | 'STANDINGS';
export type FootBallRole =
  | 'Defender'
  | 'Key Defender'
  | 'Back Pocket Left'
  | 'Back Pocket Right'
  | 'Full Back'
  | 'Half Back Flank Right'
  | 'Centre Half Back'
  | 'Half Back Flank Left'
  | 'Midfielder'
  | 'Forward'
  | 'Key Forward'
  | 'Half Forward Flank R'
  | 'Half Forward Flank L'
  | 'Wing Right'
  | 'Wing Left'
  | 'Centre Half Forward'
  | 'Forward Pocket Left'
  | 'Forward Pocket Right'
  | 'Full Forward'
  | 'Forward Pocket'
  | 'Ruck'
  | 'Ruck Rover'
  | 'Rover'
  | 'ALL PLAYERS'
  | 'Emergency'
  | 'Half Back Flank Righ'
  | 'Half Back Flank Right'
  | 'Wing Left'
  | 'Wing Right'
  | 'Goalkeeper'
  | 'Half-Back'
  | 'Half-Forward'
  | 'Centre Half-Forward'
  | 'Forward-Pocket'
  | 'Full-Forward'
  | 'NULL'
  | 'Half-Back'
  | 'Centre Half-Back'
  | 'Interchange'
  | 'Centre'
  | 'Back Pocket'
  | 'Fullback'
  | 'Wing';

export type PlayerArray = FootballPlayer[];

export type footballPlayersByRole = {
  BL: FootballPlayer[];
  HBL: FootballPlayer[];
  MID: FootballPlayer[];
  HFL: FootballPlayer[];
  FL: FootballPlayer[];
  FOL: FootballPlayer[];
  IC: FootballPlayer[];
};

export type AFLReservePlayers = FootballPlayer[];

export type FootballPlayersByRoleLimit = {
  BL: number;
  HBL: number;
  MID: number;
  HFL: number;
  FL: number;
  FOL: number;
  IC: number;
};

export type ReservePlayerPayload = {
  playerId: number;
  playerValue: number;
  reserve: boolean;
  reserveRank: number;
};

interface ReserveState {
  reservePlayers: (FootballPlayer | null)[];
  reservePlayersLimit: number;
  reservePlayerPayload?: ReservePlayerPayload[];
}

export type FootballTeamState = {
  playersByRole: footballPlayersByRole;
  playerByRoleLimit: FootballPlayersByRoleLimit;
  remainingBudget: number;
  totalBudget: number;
  lastEntry: LastEntryType;
  createDreamTeamPayload?: CreateAFLTeamPayload;
  reserveState: ReserveState;
};
type AddPlayer = {
  type: 'ADD_PLAYER';
  payload: {
    player: FootballPlayer;
    role: keyof footballPlayersByRole;
  };
};
type RemovePlayer = {
  type: 'REMOVE_PLAYER';
  payload: {
    playerId: number;
    role: keyof footballPlayersByRole;
  };
};

type SetCaptain = {
  type: 'SET_CAPTAIN';
  payload: {
    playerId: number;
    role: keyof footballPlayersByRole;
  };
};

type SetViceCaptain = {
  type: 'SET_VICE_CAPTAIN';
  payload: {
    playerId: number;
    role: keyof footballPlayersByRole;
  };
};

type ClearTeam = {
  type: 'CLEAR_TEAM';
};

type SetPlayerLimits = {
  type: 'SET_PLAYER_LIMITS';
  payload: FootballPlayersByRoleLimit;
};

export type SetTotalBalance = {
  type: 'SET_TOTAL_BALANCE';
  payload: {
    amount: number;
  };
};

export interface AFLPlayerFavouriteType {
  maxSelected: number;
  playerId: number;
  positionType: string;
}

export interface ValidAFLFavoriteTeamSelection {
  backLine: AFLPlayerFavouriteType[];
  followers: AFLPlayerFavouriteType[];
  forwardLine: AFLPlayerFavouriteType[];
  halfBackLine: AFLPlayerFavouriteType[];
  halfForwardLine: AFLPlayerFavouriteType[];
  interchange: AFLPlayerFavouriteType[];
  midfield: AFLPlayerFavouriteType[];
}

export interface AFLTeamSelectionData {
  possibleCaptains: AFLPlayerFavouriteType[];
  possibleViceCaptains: AFLPlayerFavouriteType[];
  validTeamSelection: ValidAFLFavoriteTeamSelection;
}

export interface AFLFavoriteTeam {
  backLine: AFLPlayerFavouriteType[];
  captain: AFLPlayerFavouriteType[];
  followers: AFLPlayerFavouriteType[];
  forwardLine: AFLPlayerFavouriteType[];
  halfBackLine: AFLPlayerFavouriteType[];
  halfForwardLine: AFLPlayerFavouriteType[];
  interchange: AFLPlayerFavouriteType[];
  midfield: AFLPlayerFavouriteType[];
  viceCaptain: AFLPlayerFavouriteType[];
}

export type LuckyCreateTeamPayload = {
  playersByRole: footballPlayersByRole;
  sportId: string | null;
  eventId: string | null;
  tournamentId: string | null;
  eventName: string | null;
  competitionId: string | null;
  name: string;
};

type CreateLuckyTeam = {
  type: 'CREATE_LUCKY_TEAM';
  payload: LuckyCreateTeamPayload;
};

type PlayerRole = 'BL' | 'HBL' | 'MID' | 'HFL' | 'FL' | 'FOL' | 'IC';
export type AFLTeamComposition = {
  [key in PlayerRole]: {
    min: number;
    max: number;
  };
} & {
  TOTAL_PLAYERS: number;
};

type CreateDreamTeam = {
  type: 'CREATE_DREAM_TEAM';
  payload: {
    sportId: string | null;
    eventId: string | null;
    tournamentId: string | null;
    eventName: string | null;
    competitionId: string | null;
    name: string;
    coins?: number | null;
    bonusCoins?: number | null;
  };
};

type GetDreamTeam = {
  type: 'GET_DREAM_TEAM';
  payload: {
    fantasyTeamResponse: FantasyTeamResponse;
    playerId?: number;
    role?: keyof footballPlayersByRole;
  };
};

export type CreateFavoriteTeam = {
  type: 'CREATE_FAVOURITE_TEAM';
  payload: {
    favoriteTeam: AFLFavoriteTeam;
    playersByRole: footballPlayersByRole;
  };
};

export type AFLDreamTeamPlayer = {
  id: number;
  dreamTeamId: number;
  playerId: number;
  positionType:
    | 'backLine'
    | 'followers'
    | 'forwardLine'
    | 'halfBackLine'
    | 'halfForwardLine'
    | 'interchange'
    | 'midfield'
    | 'viceCaptain'
    | 'captain';
};

export type CreateAFLExpertTeam = {
  type: 'CREATE_AFL_EXPERT_TEAM';
  payload: {
    dreamPlayers: AFLDreamTeamPlayer[];
    playerByRole: footballPlayersByRole;
  };
};

export type LastEnterPlayerData = {
  player: FootballPlayer;
  tabSection: keyof footballPlayersByRole;
};

type AddReservePlayer = {
  type: 'ADD_RESERVE_PLAYER';
  payload: { player: FootballPlayer; position: number };
};

type RemoveReservePlayer = {
  type: 'REMOVE_RESERVE_PLAYER';
  payload: { playerId: number; position: number };
};

type CreateReservePlayerPayload = {
  type: 'CREATE_RESERVE_PLAYER_PAYLOAD';
  payload: { reservePlayerPayload: ReservePlayerPayload[] };
};

export type FootballAction =
  | AddPlayer
  | RemovePlayer
  | SetCaptain
  | SetViceCaptain
  | CreateLuckyTeam
  | ClearTeam
  | SetPlayerLimits
  | SetTotalBalance
  | CreateDreamTeam
  | GetDreamTeam
  | CreateFavoriteTeam
  | CreateAFLExpertTeam
  | AddReservePlayer
  | RemoveReservePlayer
  | CreateReservePlayerPayload;

export type footballTeamContextType = {
  state: FootballTeamState;
  activeTabPlayer: keyof footballPlayersByRole;
  setActiveTabPlayer: Dispatch<SetStateAction<keyof footballPlayersByRole>>;
  showPlayerTabel: boolean;
  setShowPlayerTabel: Dispatch<SetStateAction<boolean>>;
  addPlayer: (
    player: FootballPlayer,
    role: keyof footballPlayersByRole,
  ) => void;
  removePlayer: (playerId: number, role: keyof footballPlayersByRole) => void;
  setPlayerRoleToCaptain: (
    playerId: number,
    role: keyof footballPlayersByRole,
  ) => void;
  setPlayerRoleToViceCaiptain: (
    playerId: number,
    role: keyof footballPlayersByRole,
  ) => void;

  clearTeam: () => void;
  setBudget: (amount: number) => void;
  createLuckyTeam: (payload: LuckyCreateTeamPayload) => void;
  createDreamTeam: (payload: {
    sportId: string | null;
    eventId: string | null;
    tournamentId: string | null;
    eventName: string | null;
    competitionId: string | null;
    name: string;
    coins?: number | null;
    bonusCoins?: number | null;
  }) => void;
  getDreamTeam: (
    fantasyTeamResponse: FantasyTeamResponse,
    playerId?: number,
    role?: keyof footballPlayersByRole,
  ) => void;

  createFavouriteTeam: (payload: {
    favoriteTeam: AFLFavoriteTeam;
    playersByRole: footballPlayersByRole;
  }) => void;

  createAFLExpertTeam: (
    payload: AFLDreamTeamPlayer[],
    playerByRole: footballPlayersByRole,
  ) => void;

  // Reserve player functionality
  openReserveModal: boolean;
  setOpenReserveModal: Dispatch<SetStateAction<boolean>>;
  activePlayerPosition: number;
  setActivePlayerPosition: Dispatch<SetStateAction<number>>;
  addReservePlayer: (player: FootballPlayer, position: number) => void;
  removeReservePlayer: (playerId: number, position: number) => void;
  createReservePlayerPayload: (
    reservePlayerPayload: ReservePlayerPayload[],
  ) => void;

  dispatch: (value: FootballAction) => void;
};

export type FootballTournamentHeaderProps = {
  competitionStatus?: CompetitionStatusProps;
  tournamentDetails: {
    prizePoolAmount: number;
    tournamentType: string;
    entryCoin?: string | number;
    minUserEntry?: string | number;
    userEntryCount: number;
    currentRank?: string | number;
    tournamentName?: string;
    winningPrize?: string | number;
    drawPoolAvailable?: boolean;
    startTime: string;
  };
  isTournamentDetailsLoading: boolean;
};

export interface TabItem {
  id: keyof footballPlayersByRole;
  name: keyof footballPlayersByRole;
  count: number;
  maxCount?: number;
  seq: number;
}
