import 'react-toastify/dist/ReactToastify.css';

import { toast } from 'react-toastify';

type ToastType = 'info' | 'success' | 'warning' | 'error';

export const setApiMessage = (
  type: ToastType,
  message: string,
  autoClose?: number,
) => {
  const commonProps = {
    position: 'bottom-center' as const, // Type assertion for better inference
    autoClose: autoClose ?? 4000,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    hideProgressBar: true,
    theme: 'colored' as const,
    style: {
      minHeight: 'unset',
    },
    toastId: 'active',
  };

  switch (type) {
    case 'info':
      toast.info(message, commonProps);
      break;
    case 'success':
      toast.success(message, commonProps);
      break;
    case 'warning':
      toast.warning(message, commonProps);
      break;
    case 'error':
      toast.error(message, commonProps);
      break;
    default:
      break; // This case should theoretically never happen due to type restriction
  }
};
