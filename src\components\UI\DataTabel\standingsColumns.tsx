'use client';
import type { Column, ColumnDef } from '@tanstack/react-table';

import { getDefaultTeamImage } from '../../../../db/db';
import type {
  RLStandingsTeamDetails,
  SoccerTeamStat,
  StandingsTeamDetails,
} from '../../../../types';
import SortingDownIcon from '../Icons/SortingDownIcon';
import SortingUpIcon from '../Icons/SortingUpIcon';
import PlayerAvatar from '../PlayerAvatar/indext';
import { Config } from '@/helpers/context/config';

const renderSortHeader = (
  column: Column<StandingsTeamDetails, unknown>,
  label: string,
) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

const RLrenderSortHeader = (
  column: Column<RLStandingsTeamDetails, unknown>,
  label: string,
) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

const SoccerrenderSortHeader = (
  column: Column<SoccerTeamStat, unknown>,
  label: string,
) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

export const teamStandingsColumns: ColumnDef<StandingsTeamDetails>[] = [
  {
    accessorKey: 'group',

    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(
          column,
          table.getRowModel().rows[0]?.original?.group ?? '',
        )}
      </div>
    ),

    cell: ({ row }) => {
      const teamImage = row?.original?.team?.flag;

      return (
        <div className="flex items-center py-1 px-2">
          <div className="mr-3 rounded-full">
            <PlayerAvatar
              avatarUrl={teamImage ?? getDefaultTeamImage()}
              isTeam={true}
            />
          </div>
          <p>{row?.original?.Tnm}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'pld',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Matches
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'win',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Wins
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'lstn',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Losses
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'drw',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Ties
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'noResults',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          No Results
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'nrr',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Net Run Rate
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'deductions',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Deductions
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'pts',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Points
        </span>
      </div>
    ),
  },
];

export const RLTeamStandingsColumns: ColumnDef<RLStandingsTeamDetails>[] = [
  // {
  //   accessorKey: 'id',
  //   header: () => (
  //     <div className="flex justify-center items-center">
  //       <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
  //         #
  //       </span>
  //     </div>
  //   ),
  //   cell: ({ row }) => (

  //   ),
  // },
  {
    accessorKey: 'name',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {RLrenderSortHeader(column, 'Team')}
      </div>
    ),
    cell: ({ row }) => {
      const teamImage = row?.original?.flag;
      return (
        <div className="flex items-center gap-x-3 py-1 px-2">
          <div>
            <p>{row?.index + 1}</p>
          </div>
          <div className="mr-3 rounded-full">
            <PlayerAvatar
              avatarUrl={teamImage ?? getDefaultTeamImage()}
              isTeam={true}
            />
          </div>
          <p>{row?.original?.name}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'p',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Played
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'win',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Wins
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'loss',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Losses
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'draw',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Ties
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'byes',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Byes
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'dif',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Points Difference
        </span>
      </div>
    ),
  },

  {
    accessorKey: 'points',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Points
        </span>
      </div>
    ),
  },
];

export const ARTeamStandingsColumns: ColumnDef<RLStandingsTeamDetails>[] = [
  // {
  //   accessorKey: 'id',
  //   header: () => (
  //     <div className="flex justify-center items-center">
  //       <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
  //         #
  //       </span>
  //     </div>
  //   ),
  //   cell: ({ row }) => (

  //   ),
  // },
  {
    accessorKey: 'name',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {RLrenderSortHeader(column, 'Team')}
      </div>
    ),
    cell: ({ row }) => {
      const teamImage = row?.original?.flag;
      return (
        <div className="flex items-center gap-x-3 py-1 px-2">
          <div>
            <p>{row?.index + 1}</p>
          </div>
          <div className="mr-3 rounded-full">
            <PlayerAvatar
              avatarUrl={teamImage ?? getDefaultTeamImage()}
              isTeam={true}
            />
          </div>
          <p>{row?.original?.name}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'p',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Played
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'win',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Wins
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'loss',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Losses
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'draw',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Ties
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'byes',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          %
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'points',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Points For
        </span>
      </div>
    ),
  },

  {
    accessorKey: 'against',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Points Against
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'points',
    header: () => (
      <div className="flex justify-center items-center">
        <span className="text-[11.42px] leading-[14px] font-inter flex space-x-1 font-semibold text-white">
          Points
        </span>
      </div>
    ),
  },
];

export const SoccerTeamStandingsColumns: ColumnDef<SoccerTeamStat>[] = [
  {
    accessorKey: 'name',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {SoccerrenderSortHeader(column, 'Team')}
      </div>
    ),
    cell: ({ row }) => {
      const teamImage = row?.original?.flag;
      return (
        <div className="flex items-center gap-x-3 py-1 px-2">
          <div>
            <p>{row?.index + 1}</p>
          </div>
          <div className="mr-3 rounded-full">
            <PlayerAvatar
              avatarUrl={teamImage ?? getDefaultTeamImage()}
              isTeam={true}
            />
          </div>
          <p className='text-left'>{row?.original?.name}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'points',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {SoccerrenderSortHeader(column, 'PTS')}
      </div>
    ),
  },
  {
    accessorKey: 'played',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {SoccerrenderSortHeader(column, 'P')}
      </div>
    ),
  },
  {
    accessorKey: 'win',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {SoccerrenderSortHeader(column, 'W')}
      </div>
    ),
  },
  {
    accessorKey: 'loss',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {SoccerrenderSortHeader(column, 'L')}
      </div>
    ),
  },
  {
    accessorKey: 'draw',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {SoccerrenderSortHeader(column, 'D')}
      </div>
    ),
  },
  {
    accessorKey: 'goalsFor',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {SoccerrenderSortHeader(column, 'GF')}
      </div>
    ),
  },

  {
    accessorKey: 'goalsAgainst',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {SoccerrenderSortHeader(column, 'GA')}
      </div>
    ),
  },
  {
    accessorKey: 'goalDifference',
    header: ({ column, table }) => (
      <div className="flex justify-start ml-5 items-center">
        {SoccerrenderSortHeader(column, 'GD')}
      </div>
    ),
  },
];
