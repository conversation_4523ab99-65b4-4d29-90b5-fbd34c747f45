'use client';
import ContentWrapper from '@/components/Layout/ContentWrapper';
import Breadcrumbs from '@/components/UI/Breadcrumbs';
import { useEffect, useState } from 'react';
import CustomTabs from '@/components/UI/CustomTab';
import { cn, getTournamentName, LocalStorage } from '@/lib/utils';
import { Button } from '@/components/UI/button';
import Prizes from '@/components/Competition/Prizes';
import Standings from '@/components/Competition/Standings';
import CompetitionDetailsHeader from '@/components/Common/Competition/CompetitionDetailsHeader';
import SoccerIcon from '@/components/UI/Icons/SoccerIcon';

import { SoccerPlayer } from '@/helpers/context/soccer/createSoccerTeamContext';
import TournamentHeader from '@/components/Competition/TournamentHeader';
import { useCompetition } from '@/helpers/context/competitionContext';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createSoccerTeam } from '@/helpers/fetchers/competitions';
import { setApiMessage } from '@/helpers/commonFunctions';
import { useRouter, useSearchParams } from 'next/navigation';
import { CheckedState } from '@radix-ui/react-checkbox';
import { quyerKeys } from '@/lib/queryKeys';
import { useFantasyUser } from '@/helpers/context/fantasyUserContext';
import CustomDialog from '@/components/UI/CustomDialog';
import Image from 'next/image';
import { Checkbox } from '@/components/UI/checkbox';
import Link from 'next/link';
import OverlayCelebrations from '@/components/motions/OverlayCelebrations';
import SuccessAction from '@/components/motions/SuccessAction';
import { Spinner } from '@material-tailwind/react';
import Coins from '@/assets/images/settings/smartBCoins.png';
import { useAuthContext } from '@/helpers/context/authContext';
import CreateTeamMode from '../(components)/CreateTeamMode';
import PreviewTeam from '../(components)/PreviewTeam';
import SoccerLeaderBoard from '../(components)/SoccerLeaderBoard';
import { useUserProfileContext } from '@/helpers/context/userContext';
import { Config } from '@/helpers/context/config';
import SoccerStats from '@/components/Common/Stats/SoccerStats';
import moment from 'moment';
import LiveUpdates from '../../football/(components)/LiveUpdates';
import SoccerLineups from '@/components/Common/Scorecard/SoccerLineups';
import SoccerScoreboardHeader from '@/components/Common/Commentary/SoccerScoreboardHeader';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { SoccerLineupData } from '../../../../../../types';
import Loader from '@/components/Loader';
import { useSoccerStore } from '@/store/soccer/useSoccerStore';

import {
  createSoccerDreamTeamAPIPayload,
  getSoccerPlayerByRoleLimit,
} from '@/lib/soccerUtils';
import { CreateSoccerTeamPayload, SoccerPlayerRole } from '@/lib/types/soccer';
import SharePopup from '@/components/PopUp/Share/SharePopup';
import ShareIcon from '@/components/Icons/Share/ShareIcon';
import useScreen from '@/hooks/useScreen';
import SportIcon from '@/components/UI/Icons/SportIcon';
import SoccerReservePlayerModal from '@/components/Players/Reserve/SoccerReservePlayerModal';
import { soccerPlayerColumns } from '@/components/UI/DataTabel/columns/soccerPlayerColumn';
import DataTable from '@/components/UI/DataTabel';
import EventConfirmModal from '@/components/Common/Modals/EventConfirmModal';
import { PaymentMethod } from '../../../../../../types/competitions';

const fetchLineupDetails = async (
  event_Id: string | null,
): Promise<SoccerLineupData> => {
  const res = await axiosInstance.get<SoccerLineupData>(
    `${Config.baseURL}public/sports/player-lineups?SportId=8&eventId=${event_Id}`,
  );
  return res?.data;
};

const SoccerPageContent = () => {
  const { eventDetailsResponse, refetchDreamTeam, dreamTeamResponse } =
    useCompetition();
  const eventDetailsData = eventDetailsResponse?.result;

  const [smartCoinsAmount, setSmartCoinsAmount] = useState(0)
  const [bonusCoinsAmount, setBonusCoinsAmount] = useState(0)
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('smartCoins')

  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  const isCommentaryAvailable = (): boolean => {
    const sportId = eventDetailsResponse?.result?.eventDetails?.SportId;
    const matchStatus = (eventStatus || '').toLowerCase();
    const isLiveOrCompleted =
      matchStatus === 'inprogress' ||
      matchStatus === 'finished' ||
      matchStatus === 'innings break' ||
      matchStatus === 'drink';

    const hasMatchStarted = moment().isAfter(
      moment.utc(eventDetailsData?.eventDetails?.startTime).local(),
    );

    return isLiveOrCompleted && hasMatchStarted;
  };

  const { freeCompAvailable, freeCompId, freeCompExist } =
    eventDetailsResponse?.result?.eventConfiguration ?? {};
  const showFreeCompAction = freeCompAvailable && freeCompId && !freeCompExist;

  const [activeTab, setActiveTab] = useState<number | string>(1);
  const searchParams = useSearchParams();
  const leaderboard = searchParams.get('leaderboard');

  const dreamTeamId = searchParams.get('dreamTeamId');
  const event_id = searchParams.get('event_id');
  const sport_id = searchParams.get('sport_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const add_more = searchParams.get('add_more');
  const playerId = searchParams.get('playerId');
  const role = searchParams.get('role');
  const [acceptTerms, setAcceptTerms] = useState<CheckedState>(false);
  const queryClient = useQueryClient();

  const {
    setPlayerByRoleLimit,
    setTotalBudget,
    resetTeam,
    setRemainingBudget,
    soccerPlayersByRole,
    remainingBudget,
    getDreamTeam,
    reservePlayers,
  } = useSoccerStore();
  const localSoccerPlayersByRole =
    LocalStorage.getItem<SoccerPlayer[]>('soccer_dream_team');
  const tabs = [
    {
      label: 'my team',
      labelId: 1,
      count: 0,
      disabled:
        leaderboard === 'true' &&
        (eventDetailsData?.dreamTeams?.length ?? 0) === 0,
    },
    {
      label: 'leaderboard',
      labelId: 2,
    },
    {
      label: 'lineups',
      labelId: 7,
      disabled:
        eventDetailsResponse?.result?.eventDetails?.status === 'upcoming',
    },
    {
      label: 'stats',
      labelId: 6,
      disabled:
        eventDetailsResponse?.result?.eventDetails?.status === 'upcoming',
    },
    {
      label: 'prizes',
      labelId: 3,
    },
    {
      label: 'standings',
      labelId: 4,
    },
    {
      label: 'live updates',
      labelId: 5,
      disabled: !isCommentaryAvailable(),
    },
  ];

  const { user } = useFantasyUser();
  let previewMode: boolean = false;
  let createTeamMode: boolean = false;

  switch (eventStatus) {
    case 'finished':
    case 'inprogress':
    case 'innings break':
      previewMode = true;
      break;
    case 'team-creation':
    case 'upcoming':
      if (
        add_more &&
        add_more === 'true' &&
        eventDetailsResponse?.result?.dreamTeams?.length! > 0
      ) {
        createTeamMode = true;
      } else if (!eventDetailsResponse?.result?.dreamTeams?.length) {
        createTeamMode = true;
      } else {
        previewMode = true;
      }
      break;
  }

  const handleCloseDialog = () => {
    setTeamSubmitConfirmation(false);
  };

  const handleSubmitedSucessModalClose = () => {
    setSubmitedSuccess(false);
  };

  const handelEnterAgainTeam = () => {
    handleSubmitedSucessModalClose();

    const compData = eventDetailsResponse?.result;
    const soccerUrl = `/competitions/soccer/${compData?.eventConfiguration?.eventId}?event_id=${compData?.eventConfiguration?.eventId}&sport_id=8&tournament_id=${compData?.eventDetails?.SoccerTournamentId}&seasonId=${compData?.eventDetails?.SoccerSeasonId}&competition_id=${compData?.eventConfiguration?.id}&comp_Type=my`;
    const soccerEnterUrl = `/competitions/soccer/${compData?.eventConfiguration?.eventId}?event_id=${compData?.eventConfiguration?.eventId}&sport_id=8&tournament_id=${compData?.eventDetails?.SoccerTournamentId}&seasonId=${compData?.eventDetails?.SoccerSeasonId}&competition_id=${eventDetailsResponse?.result?.eventConfiguration?.id}&add_more=true`;
    const URL =
      compData?.eventConfiguration?.eventType === 'free'
        ? soccerUrl
        : soccerEnterUrl;
    if (compData?.eventDetails?.SportId === 8) {
      return router.push(URL);
    }
  };

  const handleBuySmartBCoins = () => {
    // Safely check localStorage availability
    if (typeof window !== 'undefined') {
      try {
        // Store current path
        const currentPath = window.location.href;
        LocalStorage.setItem('redirect', { url: currentPath });
        LocalStorage.setItem('soccer_dream_team', soccerPlayersByRole);
        router.push('/settings?buy_smart_b_coins=true');
      } catch (error) {
        console.error('Error storing data in localStorage:', error);
      } finally {
        // Close dialog first
        // handleCloseDialog();
      }
    }
    // Use Next.js router for navigation
  };

  const [submitedSuccess, setSubmitedSuccess] = useState(false);
  const [teamSubmitConfirmation, setTeamSubmitConfirmation] =
    useState<boolean>(false);
  const { setLoginPopUp } = useAuthContext();

  const router = useRouter();

  useEffect(() => {
    if (leaderboard === 'true') {
      setActiveTab(2);
    }
  }, [leaderboard]);

  const eventEntryCoin =
    eventDetailsResponse?.result?.eventConfiguration?.entryCoin;
  const userAvailabeCoins = user?.coins - (user?.holdCoins ?? 0);
  const eventConfigurationData =
    eventDetailsResponse?.result?.eventConfiguration;

  const { mutate: mutateCreateTeam, isPending: isCreatingTeamPending } =
    useMutation({
      mutationFn: (createTeamPayload: CreateSoccerTeamPayload) => {
        if (createTeamPayload) {
          return createSoccerTeam(createTeamPayload, dreamTeamId);
        }
        return Promise.resolve();
      },
      onSuccess: (data: { status: boolean; message: string }) => {
        setApiMessage('success', data.message);

        LocalStorage.removeItem('dream_team');
        LocalStorage.removeItem('rugby_league_dream_team');
        LocalStorage.removeItem('afl_dream_team');
        LocalStorage.removeItem('redirect');

        resetTeam(
          eventDetailsResponse?.result?.eventDetails?.SoccerSeason
            ?.fantasy_sport_salary_cap ?? 0,
          eventDetailsResponse?.result?.eventDetails?.SoccerSeason
            ?.fantasy_sport_salary_cap ?? 0,
        );

        setAcceptTerms(false);
        LocalStorage.removeItem('soccer_dream_team');
        queryClient.invalidateQueries({
          queryKey: [
            quyerKeys.getFantasyUser,
            quyerKeys.getAllCompetitons,
            quyerKeys.getCompetiton,
          ],
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getFantasyUser],
          exact: true,
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getAllCompetitons],
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getCompetiton],
          exact: true,
        });

        if (!dreamTeamId) {
          setSubmitedSuccess(true);
        }

        if (dreamTeamId) {
          refetchDreamTeam();
        }

        if (dreamTeamId) {
          router.back();
        }

        setTeamSubmitConfirmation(false);
        setSmartCoinsAmount(0);
        setBonusCoinsAmount(0);
        setPaymentMethod('smartCoins');
      },
      onError: (data: any) => {
        if (data?.message) {
          setApiMessage('error', data?.message);
        } else {
          setApiMessage('error', data?.response?.data?.message);
        }
      },
    });


  const handlePlayForFree = () => {
    const { freeCompAvailable, freeCompId, freeCompExist } =
      eventDetailsResponse?.result?.eventConfiguration ?? {};

    if (freeCompAvailable && freeCompId && !freeCompExist) {
      const dreamTeamPayload = createSoccerDreamTeamAPIPayload({
        soccerPlayersByRole,
        competitionId: freeCompId.toString(),
        eventId: event_id!,
        sportId: sport_id!,
        tournamentId: tournament_id!,
        eventDetails: {
          eventName: eventDetailsResponse?.result?.eventDetails?.eventName!,
          dreamTeams: eventDetailsResponse?.result?.dreamTeams!,
        },
        coins: 0,
        bonusCoins: 0,
      });
      mutateCreateTeam(dreamTeamPayload);
    }
  };

  const handleTeamSubmitConfirmation = async ({
    coins,
    bonusCoins,
  }: {
    coins: number;
    bonusCoins: number;
  }) => {

    let smartCoinsToUse = 0;
    let bonusCoinsToUse = 0;

    switch (paymentMethod) {
      case "both":
        smartCoinsToUse = coins;
        bonusCoinsToUse = bonusCoins;
        break;
      case "smartCoins":
        smartCoinsToUse = eventEntryCoin ?? 0;
        bonusCoinsToUse = 0;
        break;
      case "bonusCoins":
        bonusCoinsToUse = eventEntryCoin ?? 0;
        smartCoinsToUse = 0;
        break;
    }




    const dreamTeamPayload = createSoccerDreamTeamAPIPayload({
      soccerPlayersByRole,
      competitionId: competition_id!,
      eventId: event_id!,
      sportId: sport_id!,
      tournamentId: tournament_id!,
      eventDetails: {
        eventName: eventDetailsResponse?.result?.eventDetails?.eventName!,
        dreamTeams: eventDetailsResponse?.result?.dreamTeams!,
      },
      coins: smartCoinsToUse,
      bonusCoins: bonusCoinsToUse,
    });
    if (dreamTeamPayload) {
      mutateCreateTeam(dreamTeamPayload);
    }
  };

  const soccerStats = {
    selectedPlayer: `Player ${Object.values(soccerPlayersByRole).flat().length}`,
    remainingSalary: remainingBudget,
  };

  const values = useUserProfileContext();

  const testUserProfile = {
    nickName: values?.user?.nickName,
    profilePicUrl: Config.mediaURL! + values?.user?.Media?.filePath,
  };

  const testTeamData = {
    favouriteTeam: null,
    expertTeam: null,
  };

  const testEventDetails = {
    salaryCap: 100000000,
    eventName: 'Premier League Fantasy',
  };

  const testCompetitionData = {
    currentRank: 10,
    totalRank: 100,
    totalScore: 300,
    totalLivePoints: 150,
  };

  useEffect(() => {
    if (localSoccerPlayersByRole) {
      resetTeam(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
    }
    if (add_more && add_more === 'true') {
      resetTeam(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
    }

    if (!eventDetailsResponse?.result?.dreamTeams) {
      resetTeam(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
    }

    return () => {
      resetTeam(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
    };
  }, [eventDetailsResponse]);

  // Setting player by role limit to store
  useEffect(() => {
    const rules = eventDetailsResponse?.result?.sportRule;
    if (rules) {
      // @ts-ignore
      const playerByRoleLimit = getSoccerPlayerByRoleLimit(rules);
      setPlayerByRoleLimit(playerByRoleLimit);
    }
  }, [eventDetailsResponse]);

  // Setting budget

  useEffect(() => {
    if (!localSoccerPlayersByRole) {
      setTotalBudget(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );

      setRemainingBudget(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
    }
  }, [eventDetailsResponse, localSoccerPlayersByRole]);

  // Add this mock data for lineups (replace with actual data from your API)
  // const mockLineupData = {
  //   homeTeam: {
  //     name:
  //       eventDetailsResponse?.result?.eventDetails?.homeTeam?.name ||
  //       'Home Team',
  //     formation: '4-3-3',
  //     players: [
  //       { name: 'Player 1', position: 'Goalkeeper', number: 1, playerId: 1 },
  //       { name: 'Player 2', position: 'Defender', number: 2, playerId: 2 },
  //       { name: 'Player 3', position: 'Defender', number: 3, playerId: 3 },
  //       { name: 'Player 4', position: 'Defender', number: 4, playerId: 4 },
  //       { name: 'Player 5', position: 'Defender', number: 5, playerId: 5 },
  //       { name: 'Player 6', position: 'Midfielder', number: 6, playerId: 6 },
  //       { name: 'Player 7', position: 'Midfielder', number: 7, playerId: 7 },
  //       { name: 'Player 8', position: 'Midfielder', number: 8, playerId: 8 },
  //       { name: 'Player 9', position: 'Forward', number: 9, playerId: 9 },
  //       { name: 'Player 10', position: 'Forward', number: 10, playerId: 10 },
  //       { name: 'Player 11', position: 'Forward', number: 11, playerId: 11 },
  //     ],
  //   },
  //   awayTeam: {
  //     name:
  //       eventDetailsResponse?.result?.eventDetails?.awayTeam?.name ||
  //       'Away Team',
  //     formation: '4-4-2',
  //     players: [
  //       { name: 'Player 1', position: 'Goalkeeper', number: 1, playerId: 12 },
  //       { name: 'Player 2', position: 'Defender', number: 2, playerId: 13 },
  //       { name: 'Player 3', position: 'Defender', number: 3, playerId: 14 },
  //       { name: 'Player 4', position: 'Defender', number: 4, playerId: 15 },
  //       { name: 'Player 5', position: 'Defender', number: 5, playerId: 16 },
  //       { name: 'Player 6', position: 'Midfielder', number: 6, playerId: 17 },
  //       { name: 'Player 7', position: 'Midfielder', number: 7, playerId: 18 },
  //       { name: 'Player 8', position: 'Midfielder', number: 8, playerId: 19 },
  //       { name: 'Player 9', position: 'Midfielder', number: 9, playerId: 20 },
  //       { name: 'Player 10', position: 'Forward', number: 10, playerId: 21 },
  //       { name: 'Player 11', position: 'Forward', number: 11, playerId: 22 },
  //     ],
  //   },
  // };

  const breadcrumbsLinks = [
    { label: 'Home', href: '/' },
    { label: 'SmartPlay', href: '#' },
    {
      label: eventDetailsResponse?.result?.eventDetails?.eventName ?? '',
      href: '#',
    },
  ];

  const { data: soccerLineupList, isLoading } = useQuery({
    queryFn: () => fetchLineupDetails(event_id),
    queryKey: [quyerKeys.getSoccerLineUp],
    enabled: sport_id == '8' && activeTab === 7,
  });

  const homeTeamData = soccerLineupList?.result?.filter((item) => {
    return (
      item?.teamId === eventDetailsResponse?.result?.eventDetails?.homeTeamId
    );
  });

  const awayTeamData = soccerLineupList?.result?.filter((item) => {
    return (
      item?.teamId === eventDetailsResponse?.result?.eventDetails?.awayTeamId
    );
  });

  useEffect(() => {
    if (dreamTeamResponse && dreamTeamId) {
      getDreamTeam(dreamTeamResponse, +playerId!, role as SoccerPlayerRole);
    }
  }, [dreamTeamResponse, dreamTeamId, playerId, role]);

  const [showSharePopup, setShowSharePopup] = useState(false);

  const { width } = useScreen();

  return (
    <div className="min-h-screen">
      <ContentWrapper>
        {/* HEADER  */}
        <div className="mt-3 mb-2 md:p-8 p-0 md:pb-0">
          <Breadcrumbs links={breadcrumbsLinks} />
          <div className="flex justify-between items-center space-x-2">
            <span className="flex items-center max-799:flex-col max-799:items-start md:text-[31.36px] text-[22.4px] font-veneerCleanSoft  text-black-100">
              {eventDetailsResponse?.result?.eventDetails?.eventName}
              <span className="text-[14px] space-x-2 leading-[16px] font-inter font-normal text-black-100 ml-[15px] max-799:ml-0 max-1024:text-black-100 flex items-center ">
                <span>
                  <SportIcon sport_id={8} />
                </span>
                <span>
                  {getTournamentName(
                    eventDetailsResponse?.result?.eventDetails?.SportId ?? 0,
                    eventDetailsResponse?.result?.eventDetails ?? {},
                  )}
                </span>
              </span>
            </span>
            <SharePopup
              isOpen={showSharePopup}
              onClose={() => setShowSharePopup(false)}
            >
              <button
                type="button"
                className="cursor-pointer border-none bg-transparent p-0"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowSharePopup(true);
                }}
              >
                <ShareIcon />
              </button>
            </SharePopup>
          </div>
        </div>
        <div className="bg-off-white-200 md:px-[32px] mt-2 p-0">
          <TournamentHeader
            status={eventDetailsResponse?.result?.eventDetails?.status!}
          />
          <div className="mt-2">
            <CustomTabs<number | string>
              tabs={tabs}
              setActiveTab={setActiveTab}
              activeTab={activeTab}
            />
          </div>
        </div>
        {/* MAIN */}

        {activeTab === 1 && createTeamMode && (
          <div>
            <CreateTeamMode
              handleTeamSubmitConfirmation={handleTeamSubmitConfirmation}
              coins={smartCoinsAmount}
              bonusCoins={bonusCoinsAmount}
              teamSubmitConfirmation={teamSubmitConfirmation}
              setTeamSubmitConfirmation={setTeamSubmitConfirmation}
              showPlayerTabel={true}
            />
          </div>
        )}

        {activeTab === 1 && previewMode && (
          <div>
            <PreviewTeam />
          </div>
        )}

        {activeTab === 2 && (
          <div className="bg-off-white-100 mt-2 py-2 md:px-[32px] px-0">
            <CompetitionDetailsHeader
              stats={soccerStats}
              status="upcoming"
              activeTab="Preview"
              userProfile={testUserProfile}
              teamData={testTeamData}
              eventDetails={testEventDetails}
              competitionData={testCompetitionData}
              playerLimits={{
                GKP: 1,
                DEF: 5,
                MID: 4,
                FWD: 1,
              }}
              playersByRole={{
                GKP: [],
                DEF: [],
                MID: [],
                FWD: [],
                SUB: [],
              }}
              sportType="soccer"
              readOnly
            />
            <SoccerLeaderBoard />
          </div>
        )}

        {activeTab === 3 && (
          <div className="bg-off-white-100 mt-2 py-2">
            <Prizes />
          </div>
        )}
        {activeTab === 4 && (
          <div className="bg-off-white-100 mt-2 py-2">
            <Standings />
          </div>
        )}

        {activeTab === 6 && <SoccerStats />}
        {activeTab === 5 && (
          <LiveUpdates
            matchId={event_id?.toString()}
            sportId={eventDetailsResponse?.result?.eventDetails?.SportId}
          />
        )}

        {activeTab === 7 && (
          <>
            {isLoading ? (
              <Loader />
            ) : (
              <>
                <div className="max-w-[1350px] bg-off-white-200 max-799:mt-0">
                  <SoccerScoreboardHeader />
                </div>
                <div className="bg-off-white-100 py-2 mt-[-15px]">
                  <SoccerLineups
                    homeTeam={homeTeamData}
                    awayTeam={awayTeamData}
                    onPlayerClick={(playerId) => {
                      // Handle player click - you can implement your own logic here
                    }}
                  />
                </div>
              </>
            )}
          </>
        )}

        {
          userAvailabeCoins >= eventEntryCoin! && (
            <EventConfirmModal
              isOpen={teamSubmitConfirmation}
              paymentMethod={paymentMethod}
              setPaymentMethod={setPaymentMethod}
              onClose={handleCloseDialog}
              user={user}
              eventConfigurationData={{ entryCoin: eventEntryCoin ?? 0 }}
              isCreatingTeamPending={isCreatingTeamPending}
              acceptTerms={acceptTerms as boolean}
              setAcceptTerms={setAcceptTerms}
              handleTeamSubmitConfirmation={handleTeamSubmitConfirmation}
              smartCoinsAmount={smartCoinsAmount}
              setSmartCoinsAmount={setSmartCoinsAmount}
              bonusCoinsAmount={bonusCoinsAmount}
              setBonusCoinsAmount={setBonusCoinsAmount}
            />
          )
        }

        {
          userAvailabeCoins < eventEntryCoin! && (
            <CustomDialog
              isOpen={teamSubmitConfirmation}
              onClose={handleCloseDialog}
              title="Confirmation"
              maxWidth={570}
              className="Confirmation-required-modal"
              outerClickClose={true}
            >
              <div>
                <p className="text-center text-[16px] max-799:text-[14px] leading-[22.4px] max-799:leading-[16px] font-inter text-black-100">
                  Please subscribe to continue with your entry. You can stop your
                  subscription at any time.
                </p>
                <div
                  className={cn(
                    'w-full grid gap-4 grid-cols-1 place-items-center mt-8',
                    showFreeCompAction ? 'md:grid-cols-2' : 'md:grid-cols-1',
                  )}
                >
                  <Button onClick={handleBuySmartBCoins} className="w-full">
                    Buy SmartCoins
                  </Button>
                  {showFreeCompAction && (
                    <Button
                      onClick={handlePlayForFree}
                      className="w-full"
                      disabled={isCreatingTeamPending}
                    >
                      Play For Free Instead
                    </Button>
                  )}
                </div>
              </div>
            </CustomDialog>
          )
        }



        <CustomDialog
          isOpen={submitedSuccess}
          onClose={handleSubmitedSucessModalClose}
          maxWidth={570}
          className="submited-success-modal relative"
          outerClickClose={true}
        >
          <div className="text-center z-50 relative">
            <div>
              <SuccessAction />
            </div>
            <p className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-3 mt-[21px] max-799:mb-[23px]">
              Team submitted!
            </p>
            <p className="text-[16px] max-799:text-[14px] leading-[22.4px] max-799:leading-[17px] font-normal font-inter text-black-100 mb-[33px] max-799:mb-[23px]">
              Track your progress on the live leaderboard once the competition
              starts
            </p>
            <div className="mb-[18px] flex items-center justify-between gap-3">
              <div className="w-full">
                <Button
                  className="w-full"
                  onClick={() => {
                    router.push('/');
                  }}
                >
                  All Competitions
                </Button>
              </div>
              <div className="w-full">
                <Button className="w-full" onClick={handelEnterAgainTeam}>
                  {eventDetailsResponse?.result?.eventConfiguration
                    ?.eventType === 'free'
                    ? 'View Comp'
                    : 'Enter Again'}
                </Button>
              </div>
            </div>
          </div>

          <div className="absolute top-0 left-0 w-full h-full">
            <OverlayCelebrations />
          </div>
        </CustomDialog>

        {/* <SoccerReservePlayerModal setTeamSubmitConfirmation={setTeamSubmitConfirmation}>
          {(() => {
            const { soccerPlayersByRole, reservePlayers } = useSoccerStore.getState();
            // All players from source
            const allPlayers = Object.values(soccerPlayersByRole).flat();
            // Exclude those already in main team or reserves
            const selectedIds = new Set([
              ...Object.values(soccerPlayersByRole).flat().map(p => p.playerId),
              ...(reservePlayers || []).filter(Boolean).map(p => p!.playerId),
            ]);
            const availableReservePlayers = allPlayers.filter(p => !selectedIds.has(p.playerId));
            return (
              <DataTable
                columns={soccerPlayerColumns}
                data={availableReservePlayers}
                stickyColumns={[]}
                initialColumnVisibility={{
                  name: false,
                  team: false,
                  teamName: false,
                }}
              />
            );
          })()}
        </SoccerReservePlayerModal> */}
      </ContentWrapper>
    </div>
  );
};

const Page = () => {
  return <SoccerPageContent />;
};

export default Page;
