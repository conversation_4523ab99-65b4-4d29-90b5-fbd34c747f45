import { Column, ColumnDef } from '@tanstack/react-table';
import { useCompetition } from '@/helpers/context/competitionContext';
import { useSearchParams } from 'next/navigation';
import { renderSoccerPlayerCell } from './createTeamColumn/utils';
import SortingDownIcon from '../../Icons/SortingDownIcon';
import SortingUpIcon from '../../Icons/SortingUpIcon';
import { useSoccerStore } from '@/store/soccer/useSoccerStore';
import { canAddPlayer } from '@/lib/soccerUtils';
import { SoccerPlayer } from '@/lib/types/soccer';
import { cn } from '@/lib/utils';

const renderSoccerSortHeader = (
  column: Column<SoccerPlayer, unknown>,
  label: string,
  align?: 'left' | 'center' | 'right',
) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className={`text-white ${align}`}>{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

export const soccerPlayerColumns: ColumnDef<SoccerPlayer>[] = [
  {
    accessorKey: 'id',
    header: ({ column }) => (
      <div className="md:w-[210px] w-[100px]">
        {renderSoccerSortHeader(column, 'NO.', 'left')}
      </div>
    ),
    sortingFn: (rowA, rowB, columnId) => {
      const valueA = rowA.index;
      const valueB = rowB.index;
      return valueA - valueB;
    },
    cell: ({ row }) => {
      const player = row.original;
      const playerRank = row.index + 1;

      const {
        addPlayer,
        removePlayer,
        activeTabPlayer,
        soccerPlayersByRole,
        playerByRoleLimit,
      } = useSoccerStore();

      const soccerState = useSoccerStore();

      const { canAdd } = canAddPlayer(soccerState, player, player.role);

      const isPlayerSelected = soccerPlayersByRole[player.role].some(
        (p) => p.playerId === player.playerId,
      );
      const { eventDetailsResponse } = useCompetition();
      const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
      const isDreamTeam = eventDetailsResponse?.result?.dreamTeams?.length ?? 0;
      const dreamTeamId = useSearchParams().get('dreamTeamId');
      const playerId = useSearchParams().get('playerId');
      const isValidTeam =
        Object.values(soccerPlayersByRole).reduce(
          (acc, role) => acc + role.length,
          0,
        ) === 11;

      return renderSoccerPlayerCell({
        player,
        activeTab: activeTabPlayer,
        playerRank,
        isPlayerSelected,
        playerTypeLimits: {
          GKP: playerByRoleLimit.GKP.max,
          DEF: playerByRoleLimit.DEF.max,
          MID: playerByRoleLimit.MID.max,
          FWD: playerByRoleLimit.FWD.max,
        },
        isLive: eventStatus === 'inprogress' || eventStatus === 'finished',
        teamCreated:
          eventStatus === 'upcoming' &&
          isDreamTeam > 0 &&
          !!dreamTeamId &&
          !playerId,
        row,
        isValidTeam,
        addPlayer,
        removePlayer,
        soccerPlayersByRole,
        canAdd,
      });
    },
  },


  {
    accessorKey: 'scoreData.lastScore',
    header: ({ column }) => renderSoccerSortHeader(column, 'LS'),
    cell: ({ row }) => <div>{row.original.scoreData.lastScore || '-'}</div>,
  },

  {
    accessorKey: 'scoreData.avg',
    header: ({ column }) => renderSoccerSortHeader(column, 'AVG'),
    cell: ({ row }) => (
      <div className="flex justify-center items-center">
        {row.original.scoreData?.avg?.toFixed(2) || '-'}
      </div>
    ),
  },

  {
    accessorKey: 'scoreData.sel',
    header: ({ column }) => (
      <div className="w-fit">{renderSoccerSortHeader(column, 'SEL%')}</div>
    ),

    cell: ({ row }) => (
      <div>
        {row?.original?.scoreData?.sel ? `${row.original.scoreData.sel}%` : '-'}
      </div>
    ),
  },
  {
    accessorKey: 'playedLastMatch',
    header: ({ column }) => renderSoccerSortHeader(column, 'PLG'),
    cell: ({ row }) => (
      <div className={cn(row.original.playedLastMatch ? 'text-green-500' : 'text-red-500')}>{row.original.playedLastMatch ? 'Y' : 'N'}</div>
    ),
  },
  {
    accessorKey: 'name',
    filterFn: 'includesString',
  },
  {
    accessorKey: 'teamName',
    filterFn: 'includesString',
  }
];
