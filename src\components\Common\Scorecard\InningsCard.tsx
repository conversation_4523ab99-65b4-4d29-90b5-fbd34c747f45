'use client';
// components/InningsCard.tsx
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/UI/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/UI/table';
import { ChevronDown, ChevronUp } from 'lucide-react';
import {
  cn,
  formatCommentary,
  formatScoreCommentary,
  generateUniqueId,
} from '@/lib/utils';
import {
  CricketBatInnings,
  CricketBowlInnings,
} from '../../../../types/commentry';

interface Batter {
  name: string;
  runs: number;
  balls: number;
  fours: number;
  sixes: number;
  strikeRate: number;
  expand: boolean;
  playerId: number;
}

interface Bowler {
  name: string;
  overs: number;
  maidens: number;
  runs: number;
  wickets: number;
  noBalls: number;
  wides: number;
  economy: number;
  expand: boolean;
  playerId: number;
}

interface PowerPlay {
  overs: string;
  runs: number;
  rule: string;
}

interface InningsCardProps {
  title: string;
  batsmen: CricketBatInnings[];
  bowlers: CricketBowlInnings[];
  powerPlays: PowerPlay[];
  extra: React.JSX.Element | null;
  total: string;
  togglePlayerDetails: (
    playerType: 'batsman' | 'bowler',
    playerId: number,
  ) => void;
}

const InningsCard: React.FC<InningsCardProps> = ({
  title,
  batsmen,
  bowlers,
  powerPlays,
  togglePlayerDetails,
  extra,
  total,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <Card className="rounded-none border-0">
      <CardHeader
        className=" text-white cursor-pointer flex flex-row items-center justify-between py-2 px-4 rounded"
        style={{ background: 'linear-gradient(to right, #4455C7, #003764)' }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <CardTitle className="text-[22.4px]  font-veneerCleanSoft font-normal">
          {title}
        </CardTitle>
        {isExpanded ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </CardHeader>
      {isExpanded && (
        <CardContent className="p-0">
          <Table className="p-2">
            <TableHeader>
              <TableRow className="bg-gray-100 hover:bg-gray-100">
                <TableHead className="text-left p-2">Batter</TableHead>
                <TableHead className="text-center">RUN</TableHead>
                <TableHead className="text-center">BALL</TableHead>
                <TableHead className="text-center">4S</TableHead>
                <TableHead className="text-center">6S</TableHead>
                <TableHead className="text-center">SR</TableHead>
                <TableHead className="text-center"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {batsmen?.map((batter, index) => (
                <>
                  <TableRow
                    key={generateUniqueId()}
                    className={cn(index % 2 === 0 ? 'bg-white' : 'bg-gray-100')}
                  >
                    <TableCell
                      // className=" font-medium text-left p-2"

                      className={cn(
                        'font-medium text-left p-2',
                        batter.LpTx === 'not out' &&
                          'border-l-2 border-[#4455C7]',
                      )}
                    >
                      {batter?.CricketPlayer?.name}
                    </TableCell>
                    <TableCell className="text-center">{batter?.R}</TableCell>
                    <TableCell className="text-center">{batter?.B}</TableCell>
                    <TableCell className="text-center">{batter?.four}</TableCell>
                    <TableCell className="text-center">{batter?.six}</TableCell>
                    <TableCell className="text-center">{batter.Sr}</TableCell>
                  </TableRow>
                </>
              ))}
            </TableBody>
          </Table>

          <Table className="mt-4 !mb-0">
            <TableHeader>
              <TableRow className="bg-gray-100 hover:bg-gray-100">
                <TableHead className="text-left w-[300px] p-2">
                  Bowler
                </TableHead>
                <TableHead className="text-center w-[80px]">O</TableHead>
                <TableHead className="text-center w-[80px]">M</TableHead>
                <TableHead className="text-center w-[80px]">R</TableHead>
                <TableHead className="text-center w-[80px]">W</TableHead>
                <TableHead className="text-center w-[80px]">NB</TableHead>
                <TableHead className="text-center w-[80px]">WD</TableHead>
                <TableHead className="text-center w-[100px]">ECO</TableHead>
                <TableHead className="text-center w-[80px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {bowlers?.map((bowler, index) => (
                <>
                  <TableRow
                    key={bowler.id}
                    className={index % 2 === 0 ? 'bg-white' : 'bg-gray-100'}
                  >
                    <TableCell className="text-left p-2">
                      {bowler?.CricketPlayer?.name}
                    </TableCell>
                    <TableCell className="text-center">{bowler?.Ov}</TableCell>
                    <TableCell className="text-center">{bowler?.Md}</TableCell>
                    <TableCell className="text-center">{bowler?.R}</TableCell>
                    <TableCell className="text-center">{bowler?.Wk}</TableCell>
                    <TableCell className="text-center">{bowler?.NB}</TableCell>
                    <TableCell className="text-center">{bowler?.WB}</TableCell>
                    <TableCell className="text-center">{bowler?.Er}</TableCell>
                  </TableRow>
                </>
              ))}
            </TableBody>
          </Table>
          <Table className="mt-4 !mb-0">
            <TableHeader>
              <TableRow className="bg-gray-100">
                <TableHead className="text-left w-[300px] p-2">
                  <div className="grid grid-cols-2">
                    <div>Extra</div>
                    <div>{extra}</div>
                  </div>
                </TableHead>
              </TableRow>
              <TableRow className="bg-white">
                <TableHead className="text-left w-[300px] p-2">
                  <div className="grid grid-cols-2">
                    <div>Total</div>
                    <div>{total}</div>
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
          </Table>
        </CardContent>
      )}
    </Card>
  );
};

export default InningsCard;
