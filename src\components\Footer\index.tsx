'use client';
import './footer.scss';

import moment from 'moment';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

import { Config } from '@/helpers/context/config';
import useScreen from '@/hooks/useScreen';

import AppleStore from '../../assets/images/icons/appstore.png';
import EmailIcon from '../../assets/images/icons/email-icon.svg';
import Facebook from '../../assets/images/icons/facebook.svg';
import Instagram from '../../assets/images/icons/instagram.svg';
import Linkedin from '../../assets/images/icons/linkedin.svg';
import playstore from '../../assets/images/icons/playstore.png';
import FooterLogoWhite from '../../assets/images/icons/smartB-white.png';
import TikTok from '../../assets/images/icons/tiktok.svg';
import TwitterX from '../../assets/images/icons/TwitterX.svg';
import Youtube from '../../assets/images/icons/youtube.svg';

const Footer = () => {
  const { width } = useScreen();

  return (
    <div className="footer-wrap odds-footer-wrap">
      <div>
        <div className="footer-content">
          <Link href="/">
            <Image src={FooterLogoWhite} alt="logo" unoptimized={true} />
          </Link>
          <div className="footer-links-wrap grid grid-cols-4 max-1280:grid-cols-2">
            <div>
              <h4>Contact Us</h4>
              <div className="contact">
                <div className="email-icon-wrap">
                  <EmailIcon />
                </div>
                <span>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </span>
              </div>

              {width > 1279 ? (
                <div className="social-media-wrap">
                  <h4>Follow Us on</h4>
                  <div className="social-icon-wrap">
                    <a
                      href="https://www.instagram.com/smartbapp/"
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="instagram"
                    >
                      <Instagram className="sidebar-icon" />
                    </a>
                    <div className="tiktok">
                      <a
                        href="https://www.tiktok.com/@smartbapp"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="tiktok"
                      >
                        <TikTok />
                      </a>
                    </div>
                    <div className="tiktok youtube">
                      <a
                        href="https://www.youtube.com/@smartbapp"
                        className="youtube"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="youtube"
                      >
                        <Youtube />
                      </a>
                    </div>
                    <div className="tiktok youtube">
                      <a
                        href="https://www.facebook.com/smartbapp"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="facebook"
                      >
                        <Facebook className="sidebar-icon" />
                      </a>
                    </div>
                    {/* 
                    <div className="tiktok linkedin">
                      <a
                        href="https://www.linkedin.com/company/smartbapp/"
                        className="youtube"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="youtube"
                      >
                        <Linkedin />
                      </a>
                    </div> */}
                    <div className="tiktok">
                      <a
                        href="https://twitter.com/backawinneraus"
                        className="twitter"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="twitter"
                      >
                        <TwitterX className="sidebar-icon" />
                      </a>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="link-wrap mobile-policy">
                  <Link
                    href={Config.siteBaseURL + 'privacy-policy'}
                  // className={
                  //   location.pathname === '/privacy-policy' ? 'active' : ''
                  // }
                  >
                    Privacy Policy
                  </Link>
                  <Link
                    href={Config.siteBaseURL + 'responsible-gambling'}
                  // className={
                  //   location.pathname === '/responsible-gambling'
                  //     ? 'active'
                  //     : ''
                  // }
                  >
                    Responsible Gambling
                  </Link>

                  <Link
                    href="/terms-conditions"
                  // className={
                  //   location.pathname === '/terms-and-conditions'
                  //     ? 'active'
                  //     : ''
                  // }
                  >
                    Terms & Conditions
                  </Link>
                  <Link
                    // href="/subscription-terms-and-conditions"
                    href="#"
                  // className={
                  //   location.pathname ===
                  //   '/subscription-terms-and-conditions'
                  //     ? 'active'
                  //     : ''
                  // }
                  >
                    FAQ
                  </Link>
                </div>
              )}
            </div>
            <div>
              {/* <h4>{localesData?.footer?.navigate}</h4> */}
              <div className="link-wrap">
                <Link
                  href={Config.siteBaseURL ?? ''}
                // className={location.pathname === '/' ? 'active' : ''}
                >
                  Home
                </Link>
                <Link
                  href={Config.siteBaseURL + 'racing'}
                // className={location.pathname === '/racing' ? 'active' : ''}
                >
                  Racing
                </Link>
                <Link
                  href={Config.siteBaseURL + 'podcast'}
                // className={location.pathname === '/podcast' ? 'active' : ''}
                >
                  Podcasts
                </Link>
                <Link
                  href={Config.siteBaseURL + 'tipscompetition/public/tips'}
                // className={
                //   location.pathname === '/tipscompetition/public/tips'
                //     ? 'active'
                //     : ''
                // }
                >
                  Tipping Competition
                </Link>
              </div>
            </div>
            <div>
              {/* <h4>{localesData?.MENU?.POLICIES}</h4> */}
              {width > 1279 ? (
                <div className="link-wrap">
                  <Link
                    href={Config.siteBaseURL + 'privacy-policy'}
                  // className={
                  //   location.pathname === '/privacy-policy' ? 'active' : ''
                  // }
                  >
                    Privacy Policy
                  </Link>
                  <Link
                    href={Config.siteBaseURL + 'responsible-gambling'}
                  // className={
                  //   location.pathname === '/responsible-gambling'
                  //     ? 'active'
                  //     : ''
                  // }
                  >
                    Responsible Gambling
                  </Link>

                  <Link
                    href="/terms-conditions"
                  // className={
                  //   location.pathname === '/terms-and-conditions'
                  //     ? 'active'
                  //     : ''
                  // }
                  >
                    Terms & Conditions
                  </Link>
                  <Link
                    // href="/subscription-terms-and-conditions"
                    href="/faqs"
                  // className={
                  //   location.pathname ===
                  //   '/subscription-terms-and-conditions'
                  //     ? 'active'
                  //     : ''
                  // }
                  >
                    FAQ
                  </Link>
                </div>
              ) : (
                <div className="social-media-wrap">
                  <h4>Follow Us on</h4>
                  <div className="social-icon-wrap">
                    <a
                      href="https://www.instagram.com/smartbapp/"
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="instagram"
                    >
                      <Instagram className="sidebar-icon" />
                    </a>
                    <div className="tiktok">
                      <a
                        href="https://www.tiktok.com/@smartbapp"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="tiktok"
                      >
                        <TikTok />
                      </a>
                    </div>
                    <div className="tiktok youtube">
                      <a
                        href="https://www.youtube.com/@smartbapp"
                        className="youtube"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="youtube"
                      >
                        <Youtube />
                      </a>
                    </div>
                    <div className="tiktok youtube">
                      <a
                        href="https://www.facebook.com/smartbapp"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="facebook"
                      >
                        <Facebook className="sidebar-icon" />
                      </a>
                    </div>

                    {/* <div className="tiktok linkedin">
                      <a
                        href="https://www.linkedin.com/company/smartbapp/"
                        className="youtube"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="youtube"
                      >
                        <Linkedin />
                      </a>
                    </div> */}
                    <div className="tiktok">
                      <a
                        href="https://twitter.com/backawinneraus"
                        className="twitter"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="twitter"
                      >
                        <TwitterX className="sidebar-icon" />
                      </a>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div>
              <h4>Our App</h4>
              <div className="app-logo">
                <div className="link-wrap playstore-logo">
                  <a
                    href="https://play.google.com/store/apps/details?id=au.com.sportsleague"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Image
                      src={playstore}
                      alt="playstore"
                      className="playstore-img"
                      unoptimized={true}
                    />
                  </a>
                </div>
                <div className="link-wrap">
                  <a
                    href="https://apps.apple.com/us/app/smart-play/id6737409400"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Image
                      src={AppleStore}
                      alt="Apple-store"
                      className="playstore-img"
                      unoptimized={true}
                    />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <p className="copyright">
          Copyright © SmartB Pty Ltd {moment()?.year()}
          <span style={{ display: 'block', paddingTop: '5px' }}></span>
        </p>
      </div>
    </div>
  );
};

export default Footer;
