'use client';
import type { UseMutateFunction } from '@tanstack/react-query';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import moment from 'moment';
import type { Dispatch, ReactNode, SetStateAction } from 'react';
import { createContext, useContext, useMemo, useState } from 'react';

import { quyerKeys } from '@/lib/queryKeys';

import { Token } from '../../../db/db';
import type {
  GetCouponCodeResponse,
  Plan,
  SubscriptionPlanDetails,
  ValidateCardApiResponse,
} from '../../../types';
import axiosInstance from '../axios/axiosInstance';
import { setApiMessage } from '../commonFunctions';
import { Config } from './config';

type UserPlanContextType = {
  buyPlan: UseMutateFunction<any, Error, any, unknown>;
  allPlans: Plan[] | undefined;
  updatePlan: UseMutateFunction<any, Error, any, unknown>;
  cancelCurrentPlan: UseMutateFunction<any, Error, any, unknown>;
  currentPlan: SubscriptionPlanDetails | undefined;
  holdActivePlan: UseMutateFunction<any, Error, any, unknown>;
  resumePlan: UseMutateFunction<any, Error, any, unknown>;
  isResumePlanSuccess: boolean;
  isHoldPlanSuccess: boolean;
  isPendingUpdatePlan: boolean;
  isSuccessUpdatePlan: boolean;
  setCouponeCode: Dispatch<SetStateAction<string | undefined>>;
  setFetchCouponeCode: Dispatch<SetStateAction<boolean>>;
  fetchCouponCode: boolean;
  couponData: GetCouponCodeResponse | undefined;
  isCancelPlanSuccess: boolean;
  isResumePending: boolean;
  isHoldPending: boolean;
  deleteBank: UseMutateFunction<any, Error, number, unknown>;
  isDeleteBankSuccess: boolean;
  applyCouponCode: UseMutateFunction<
    GetCouponCodeResponse | undefined,
    Error,
    void,
    unknown
  >;

  isApplyinCouponCode: boolean;
  isPendingBuyPlan: boolean;
  validateUserCard: UseMutateFunction<
    any,
    Error,
    {
      cardHolderName: string;
      cardExp: string;
      cardNumber: string;
      cvv: string;
      amount: number;
      cardId?: number;
    },
    unknown
  >;
  validateCardData: ValidateCardApiResponse | undefined;
  validateUserCardData: ValidateCardApiResponse | undefined;
  setValidateUserCardData: Dispatch<
    SetStateAction<ValidateCardApiResponse | undefined>
  >;
  selectedPlan: string;
  setSelectedPlan: Dispatch<SetStateAction<string>>;
  selectedOptions: string;
  setSelectedOptions: Dispatch<SetStateAction<string>>;
  selectedPlanData: Plan | undefined;
};

// Initialize context with the correct type
const userPlanContext = createContext<UserPlanContextType | undefined>(
  undefined,
);

// get cupone code

// First, let's fix the getCouponCode function
const getCouponCode = async (
  couponCode: string,
  planId: number,
): Promise<GetCouponCodeResponse | undefined> => {
  return fetch(
    `${Config.baseURL}subscription/coupon/verify/${couponCode}?plateform=web&plan_id=${planId}&category=fantasy`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Token}`,
      },
    },
  )
    .then((res) => res.json())
    .then((data: GetCouponCodeResponse) => data);
};

const validateCard = async ({
  cardHolderName,
  cardExp,
  cardNumber,
  cvv,
  amount,
  cardId,
}: {
  cardHolderName: string;
  cardExp: string;
  cardNumber: string;
  cvv: string;
  amount: number;
  cardId?: number;
}): Promise<ValidateCardApiResponse> => {
  const res = await axiosInstance.post<ValidateCardApiResponse>(
    `${Config.baseURL}subscription/card/validations`,
    {
      ...(cardId
        ? {
            cardId,
            amount,
          }
        : {
            card: {
              card_holder_name: cardHolderName,
              card_exp: cardExp.split('-').reverse().join('-'),
              card_number: cardNumber,
              cvv: cvv,
            },
            amount,
          }),
    },
  );
  return res.data;
};

// resume plan

const resumePlan = async (planId: number) => {
  const todayDate = moment().format('YYYY-MM-DD');
  const payload = {
    resumeDate: todayDate,
    holdStatus: 'resumed',
    planId: planId,
    plateForm: 'web',
  };
  const res = await fetch(Config.fantasyURL + '/membership/resume/plan', {
    method: 'POST',
    body: JSON.stringify(payload),
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Token}`,
    },
  });

  const data = await res.json();
  return data;
};

// hold plan

const holdPlan = async (payload: any) => {
  const res = await fetch(`${Config.fantasyURL}/membership/hold/plan`, {
    method: 'POST',
    body: JSON.stringify(payload),
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Token}`,
    },
  });

  const data = await res.json();
  return data;
};

// Purchase plan

const purchasePlan = async (payload: any) => {
  const res = await fetch(`${Config.fantasyURL}/membership/purchased-plan`, {
    method: 'POST',
    body: JSON.stringify(payload),
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Token}`,
    },
  });
  const data = await res.json();
  return data;
};

// cance plan

const cancelPlan = async (payload: any) => {
  const res = await fetch(
    `${Config.baseURL}subscription/cancel-plan?plateform=web&type=cancel`,
    {
      method: 'PUT',
      body: JSON.stringify(payload),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Token}`,
      },
    },
  );

  const data = await res.json();
  return data;
};

// Upgrade plan

const upgradePlan = async (payload: any) => {
  const res = await fetch(`${Config.fantasyURL}/membership/upgrade/plan`, {
    method: 'PUT',
    body: JSON.stringify(payload),
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Token}`,
    },
  });
  const data = await res.json();
  return data;
};

interface PlansResponse {
  getPlans: Plan[];
}

// Get All plans

const getAllPlans = async (): Promise<Plan[]> => {
  const res = await fetch(
    `${Config.baseURL}subscription/plan-list?category=fantasy`,
    {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Token}`,
      },
    },
  );
  const data: PlansResponse = await res.json();
  return data.getPlans;
};

// Get Current Plan

const getCurrentPlan = async (): Promise<SubscriptionPlanDetails> => {
  const res = await fetch(
    `${Config.baseURL}subscription/get-purchased-plan?category=fantasy`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${Token}`,
      },
    },
  );

  const data = await res.json();

  return data?.getUserPlanDetails as SubscriptionPlanDetails;
};

// Delete Bank

const deleteBankDetails = async (bankId: number): Promise<any> => {
  const res = await axiosInstance.delete(
    Config.fantasyURL + `/withdraw/delete-bank/${bankId}`,
  );
  return res.data;
};

interface UserPlanContextProviderProps {
  children: ReactNode;
}

const UserPlanContextProvider = ({
  children,
}: UserPlanContextProviderProps) => {
  const queryClient = useQueryClient();
  const [couponCode, setCouponCode] = useState<string | undefined>();
  const [fetchCouponCode, setFetchCouponCode] = useState<boolean>(false);
  const { data: currentPlan } = useQuery({
    queryFn: getCurrentPlan,
    queryKey: [quyerKeys.getCurrentPlan],
  });
  const [selectedPlan, setSelectedPlan] = useState(
    () => currentPlan?.PlanName || 'Bronze',
  );

  const [selectedOptions, setSelectedOptions] = useState<string>('new');
  const [validateUserCardData, setValidateUserCardData] = useState<
    ValidateCardApiResponse | undefined
  >(undefined);
  const { mutate: buyPlan, isPending: isPendingBuyPlan } = useMutation({
    mutationFn: purchasePlan,
    mutationKey: [quyerKeys.purchasePlan],
    onSuccess: (data) => {
      setApiMessage('success', data.message);
      queryClient.invalidateQueries({ queryKey: [quyerKeys.getCurrentPlan] });
      queryClient.refetchQueries({
        queryKey: [quyerKeys.getAllPlans],
        exact: true,
      });
      queryClient.refetchQueries({
        queryKey: [quyerKeys.getCurrentPlan],
        exact: true,
      });

      queryClient.refetchQueries({
        queryKey: [quyerKeys.getFantasyUser],
        exact: true,
      });
    },
  });

  const { mutate: validateUserCard, data: validateCardData } = useMutation({
    mutationFn: validateCard,
    mutationKey: [quyerKeys.validateCard],
    onSuccess: (data) => {
      setValidateUserCardData(data);
    },
  });

  const { data: allPlans } = useQuery({
    queryFn: getAllPlans,
    queryKey: [quyerKeys.getAllPlans],
  });
  const selectedPlanData = allPlans?.find((plan) => plan.name === selectedPlan);

  const {
    mutate: updatePlan,
    isPending: isPendingUpdatePlan,
    isSuccess: isSuccessUpdatePlan,
  } = useMutation({
    mutationFn: upgradePlan,
    mutationKey: [quyerKeys.upgradePlan],
    onSuccess: (data: { status: boolean; message: string }) => {
      if (data.status) {
        setApiMessage('success', data.message);
      }
      queryClient.invalidateQueries({
        queryKey: [quyerKeys.getCurrentPlan, quyerKeys.getAllPlans],
      });
      queryClient.refetchQueries({
        queryKey: [quyerKeys.getCurrentPlan],
        exact: true,
      });
      queryClient.refetchQueries({
        queryKey: [quyerKeys.getAllPlans],
        exact: true,
      });

      queryClient.refetchQueries({
        queryKey: [quyerKeys.getFantasyUser],
        exact: true,
      });
    },
    onSettled: (data) => {
      setApiMessage('error', data?.message ?? '');
    },
    onError: (error) => {
      setApiMessage('error', error.message);
    },
  });

  const { mutate: cancelCurrentPlan, isSuccess: isCancelPlanSuccess } =
    useMutation({
      mutationFn: cancelPlan,
      mutationKey: [quyerKeys.cancelPlan],
      onSuccess: (data: { message: string; status: boolean }) => {
        if (data.status) {
          setApiMessage('success', data.message);
        }
        queryClient.invalidateQueries({
          queryKey: [quyerKeys.getCurrentPlan],
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getCurrentPlan],
          exact: true,
        });
        queryClient.refetchQueries({
          queryKey: [quyerKeys.getAllPlans],
          exact: true,
        });
      },

      onSettled: (data) => {
        setApiMessage('error', data?.message ?? '');
      },
    });

  const {
    mutate: holdActivePlan,
    isSuccess: isHoldPlanSuccess,
    isPending: isHoldPending,
  } = useMutation({
    mutationFn: holdPlan,
    mutationKey: [quyerKeys.holdPlan],
    onSuccess: (data: { status: boolean; message: string }) => {
      if (data.status) {
        setApiMessage('success', data.message);
      }
      queryClient.invalidateQueries({
        queryKey: [quyerKeys.getCurrentPlan],
      });

      queryClient.refetchQueries({
        queryKey: [quyerKeys.getCurrentPlan],
        exact: true,
      });
      queryClient.refetchQueries({
        queryKey: [quyerKeys.getAllPlans],
        exact: true,
      });
    },
    onSettled: (data) => {
      setApiMessage('error', data?.message ?? '');
    },
  });

  const {
    mutate: userResumePlan,
    isSuccess: isResumePlanSuccess,
    isPending: isResumePending,
  } = useMutation({
    mutationFn: resumePlan,
    mutationKey: [quyerKeys.resumePlan],
    onSuccess: (data: { status: boolean; message: string }) => {
      if (data.status) {
        setApiMessage('success', data.message);
      }
      queryClient.invalidateQueries({
        queryKey: [quyerKeys.getCurrentPlan],
      });
      queryClient.refetchQueries({
        queryKey: [quyerKeys.getCurrentPlan],
        exact: true,
      });

      queryClient.refetchQueries({
        queryKey: [quyerKeys.getAllPlans],
        exact: true,
      });
    },
    onSettled: (data) => {
      setApiMessage('error', data?.message ?? '');
    },
  });

  // In your UserPlanContextProvider component, update the useQuery implementation:
  const {
    data: couponData,
    mutate: applyCouponCode,
    isPending: isApplyinCouponCode,
  } = useMutation({
    mutationKey: [quyerKeys.getCouponCode],
    mutationFn: () => {
      if (!couponCode || !selectedPlanData?.id) {
        throw new Error('Missing coupon code or plan ID');
      }
      return getCouponCode(couponCode, selectedPlanData?.id);
    },
    onSuccess: (data) => {
      if (data?.data?.statusCode === 400) {
        setApiMessage('error', data?.data?.message || '');
        setCouponCode(''); // Clear coupon code on error
      } else {
        setApiMessage('success', data?.data?.message || '');
      }
    },
    onError: () => {
      setCouponCode(''); // Clear coupon code on error
    },
  });

  // Delete bankDetails

  const { mutate: deleteBank, isSuccess: isDeleteBankSuccess } = useMutation({
    mutationKey: [quyerKeys.deleteBankDetails],
    mutationFn: deleteBankDetails,
    onSuccess: (data) => {
      setApiMessage('success', data?.message);
      queryClient.refetchQueries({
        queryKey: [quyerKeys.getAllBankDetails],
        exact: true,
      });
    },
    onError: (err) => {
      // @ts-expect-error
      setApiMessage('error', err?.response?.data?.message);
    },
  });

  const planState = useMemo(
    (): UserPlanContextType => ({
      buyPlan,
      allPlans,
      updatePlan,
      cancelCurrentPlan,
      currentPlan,
      holdActivePlan,
      resumePlan: userResumePlan,
      isResumePlanSuccess,
      isHoldPlanSuccess,
      isPendingUpdatePlan,
      isSuccessUpdatePlan,
      setCouponeCode: setCouponCode,
      setFetchCouponeCode: setFetchCouponCode,
      fetchCouponCode,
      couponData,
      isCancelPlanSuccess,
      isResumePending,
      isHoldPending,
      deleteBank,
      isDeleteBankSuccess,
      applyCouponCode,
      isApplyinCouponCode,
      isPendingBuyPlan,
      validateUserCard,
      validateCardData,
      validateUserCardData,
      setValidateUserCardData,
      selectedPlan,
      setSelectedPlan,
      selectedOptions,
      setSelectedOptions,
      selectedPlanData,
    }),
    [
      buyPlan,
      allPlans,
      updatePlan,
      cancelCurrentPlan,
      currentPlan,
      holdActivePlan,
      userResumePlan,
      isResumePlanSuccess,
      isHoldPlanSuccess,
      isPendingUpdatePlan,
      isSuccessUpdatePlan,
      setCouponCode,
      setFetchCouponCode,
      fetchCouponCode,
      couponData,
      isCancelPlanSuccess,
      isResumePending,
      isHoldPending,
      deleteBank,
      isDeleteBankSuccess,
      applyCouponCode,
      isApplyinCouponCode,
      isPendingBuyPlan,
      validateUserCard,
      validateCardData,
      validateUserCardData,
      setValidateUserCardData,
      selectedOptions,
      selectedOptions,
      selectedPlanData,
    ],
  );

  return (
    <userPlanContext.Provider value={planState}>
      {children}
    </userPlanContext.Provider>
  );
};

export const usePlanContext = () => {
  const context = useContext(userPlanContext);
  if (!context) {
    throw new Error(
      'usePlanContext must be used within a UserPlanContextProvider',
    );
  }
  return context;
};

export default UserPlanContextProvider;
