import { Card } from '@/components/UI/card';
import { AnimatePresence, motion } from 'motion/react';
import { Dispatch, SetStateAction } from 'react';
import { cn } from '@/lib/utils';
import { Plus } from 'lucide-react';
import useScreen from '@/hooks/useScreen';
import Image from 'next/image';
import { PUBLIC_IMAGES } from '@/lib/constants/constants';
import { useSearchParams } from 'next/navigation';

// Generic EmptyPlayerCard component
const EmptyPlayerCard = <T,>({
  tabSection,
  setActiveTab,
  isActive,
  setShowPlayerTabel,
  handleEditTeam
}: {
  tabSection: keyof T;
  setActiveTab: (role: keyof T) => void;
  setShowPlayerTabel: (show: boolean) => void;
  isActive?: boolean;
  handleEditTeam: () => void
}) => {
  const { width } = useScreen();
  const searchParams = useSearchParams();
  const dreamTeamId = searchParams.get('dreamTeamId');

  return (
    <AnimatePresence>
      <motion.div
        key="modal"
        initial={{ opacity: 1, scale: 1 }}
        animate={{ opacity: 1, scale: 1 }}
      >
        <Card className="p-4 bg-white shadow-sm border border-gray-100 rounded-lg md:w-[165px] w-[150px] relative h-20">
          <div className="flex justify-center items-centershadow-md rounded-full absolute top-[-15px] left-1/2 transform -translate-x-1/2">
            <Image
              src={PUBLIC_IMAGES.DEFAULT_PLAYER_IMAGE}
              alt="default player image"

            />
          </div>
          <div className="rounded-md absolute bottom-[5px] left-1/2 transform -translate-x-1/2 px-2 pb-1  my-1">
            <button
              className="flex justify-center items-center space-x-1 mt-2"
              onClick={() => {
                if (dreamTeamId) {
                  handleEditTeam();
                  setActiveTab(tabSection);
                } else {
                  if (width <= 959) {
                    setShowPlayerTabel(true);
                  }
                  setActiveTab(tabSection);
                }

              }}
            >
              <div
                className={cn(
                  'text-white rounded-md',
                  isActive ? 'bg-[#1C9A6C]' : 'bg-[#C9C9C9]',
                )}
              >
                <Plus size={30} />
              </div>
            </button>
          </div>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};

export default EmptyPlayerCard;
