'use client';

import { useState, useEffect, useMemo } from 'react';
import { FiltersState } from '../Player/PlayerFilter';
import DataTable from '@/components/UI/DataTabel';
import { aflPlayerStatsColumn } from '@/components/UI/DataTabel/columns/aflPlayerStatsColumn';
import aflPlayerStatsData from '@/data/aflPlayerStatsData.json';
import { Button } from '@/components/UI/button';
import { Input } from '@/components/UI/input';
import { Search, SlidersHorizontal, Filter, Settings } from 'lucide-react';
import { IconButton } from '@material-tailwind/react';
import SettingsIcon from '@/components/UI/Icons/SettingsIcon';
import { VisibilityState } from '@tanstack/react-table';
import CustomisePanel from '../Commentary/CustomisePanel';
import CustomisePanelMobile from '../Commentary/CustomisePanelMobile';
import { StatsLegend } from '../Commentary/CricketLegend';
import { useStatsContext } from '@/helpers/context/stats';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Card } from '@/components/UI/card';
import { cn } from '@/lib/utils';
import Select, { components } from 'react-select';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { Checkbox } from '@/components/UI/checkbox';
import { useCompetition } from '@/helpers/context/competitionContext';
import { AFL_SMARTPLAY_STATS_LEGEND } from '@/helpers/constants/index';

const positionOptions = [
  {
    value: 1,
    label: 'All Positions',
  },
  {
    value: 2,
    label: 'Back Line',
  },
  {
    value: 3,
    label: 'Half Back Line',
  },
  {
    value: 4,
    label: 'Midfield',
  },
  {
    value: 5,
    label: 'Half Forward Line',
  },
  {
    value: 6,
    label: 'Forward Line',
  },
  {
    value: 7,
    label: 'Followers',
  },
  {
    value: 8,
    label: 'Interchange',
  },
];

const aflCategories = [
  {
    id: 'scoring',
    name: 'Scoring',
    children: [
      { id: 'goals', name: 'Goals' },
      { id: 'behinds', name: 'Behinds' },
      { id: 'goal_assists', name: 'Goal Assists' },
    ],
  },
  {
    id: 'ruck',
    name: 'Ruck',
    children: [
      { id: 'hitouts', name: 'Hit Outs' },
      { id: 'clearances', name: 'Clearances' },
      { id: 'centre_clearances', name: 'Centre Clearances' },
      { id: 'hitouts_to_advantage', name: 'Hit outs to Advantage' },
    ],
  },
  {
    id: 'disposal',
    name: 'Disposal',
    children: [
      { id: 'kicks', name: 'Kicks' },
      { id: 'handballs', name: 'Handballs' },
      { id: 'effective_disposals', name: 'Effective Disposals' },
    ],
  },
  {
    id: 'general_play',
    name: 'General Play',
    children: [
      { id: 'inside_fifty', name: 'Inside 50 Entry' },
      { id: 'rebound_50', name: 'Rebound 50' },
      { id: 'meters_gained', name: 'Meters Gained' },
    ],
  },
  {
    id: 'marking',
    name: 'Marking',
    children: [
      { id: 'marks', name: 'Marks' },
      { id: 'marks_inside_50', name: 'Marks Inside 50' },
      { id: 'contested_marks', name: 'Contested Marks' },
    ],
  },
  {
    id: 'penalty',
    name: 'Penalty',
    children: [
      { id: 'clanger', name: 'Clanger' },
      { id: 'fifty_metre_penalties', name: '50m Penalty Conceded' },
    ],
  },
  {
    id: 'tackling_defensive',
    name: 'Tackling & Defensive',
    children: [
      { id: 'tackles', name: 'Tackles' },
      { id: 'intercept_marks', name: 'Intercept Mark' },
      { id: 'spoils', name: 'Spoils' },
      { id: 'frees_for', name: 'Free Kick For' },
      { id: 'frees_against', name: 'Free Kick Against' },
    ],
  },
  {
    id: 'bonus',
    name: 'Bonus',
    children: [
      { id: 'tackles_bonus', name: 'Tackles Bonus (10+ Tackles in a Game)' },
      { id: 'goals_bonus', name: 'Goals Bonus (5+ Goals in a Game)' },
    ],
  },
];

export const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const AFLSmartPlayStats = () => {
  const { smartPlayStats } = useStatsContext();
  const [showFilter, setShowFilter] = useState(false);
  const [showTableCustomization, setShowTableCustomization] = useState(false);
  const [showTableCustomizationMobile, setShowTableCustomizationMobile] =
    useState(false);
  const [showMobileFilter, setShowMobileFilter] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showLegends, setShowLegends] = useState(false);
  const [selectedPositions, setSelectedPositions] = useState<number[]>([]);
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const { eventDetailsResponse } = useCompetition();

  const homeTeam = eventDetailsResponse?.result?.eventDetails?.homeTeam;
  const awayTeam = eventDetailsResponse?.result?.eventDetails?.awayTeam;

  const handlePositionChange = (value: number) => {
    setSelectedPositions((prev) => {
      if (prev.includes(value)) {
        return prev.filter((pos) => pos !== value);
      }
      return [...prev, value];
    });
  };

  const handleTeamChange = (value: string) => {
    setSelectedTeams((prev) => {
      if (prev.includes(value)) {
        return prev.filter((team) => team !== value);
      }
      return [...prev, value];
    });
  };

  const resetFilters = () => {
    setSelectedPositions([]);
    setSelectedTeams([]);
  };

  const initializeSelectedState = () => {
    const state: Record<string, boolean> = {};

    aflCategories.forEach((category) => {
      // Set parent category as checked
      state[category.id] = true;

      // Set all children as checked
      category.children.forEach((child) => {
        state[child.id] = true;
      });
    });

    // Add any missing nested accessors that might not be in categories
    const allColumns = aflPlayerStatsColumn
      .map((col) => ('accessorKey' in col ? col.accessorKey : col.id))
      .filter(Boolean);
    allColumns.forEach((columnKey) => {
      if (columnKey && !state[columnKey]) {
        state[columnKey] = true;
      }
    });

    return state;
  };

  const [selected, setSelected] = useState<Record<string, boolean>>(
    initializeSelectedState(),
  );
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    ...selected,
  });

  useEffect(() => {
    setColumnVisibility(selected);
  }, [selected]);

  const filteredData = useMemo(() => {
    if (!searchQuery.trim() || !smartPlayStats?.result.data) {
      return smartPlayStats?.result.data || [];
    }

    return smartPlayStats.result.data.filter((player) =>
      player.player.name.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [searchQuery, smartPlayStats?.result.data]);

  return (
    <div className="relative">
      <div className="flex items-center w-full space-x-2 my-2 justify-between">
        <div className="relative">
          <Search
            className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={16}
          />
          <Input
            placeholder="Search (by player name)"
            className="border-gray-100 pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <div>
            <Select
              className="React desktop-odds-select hidden md:block !min-w-[200px]"
              options={positionOptions}
              classNamePrefix="select"
              placeholder="Select Position"
              isSearchable={false}
              components={{ DropdownIndicator }}
            />
          </div>
          <div>
            <Select
              className="React desktop-odds-select hidden md:block !min-w-[200px]"
              options={[
                { value: 'all', label: 'All Teams' },
                { value: homeTeam?.name, label: homeTeam?.name },
                { value: awayTeam?.name, label: awayTeam?.name },
              ]}
              classNamePrefix="select"
              placeholder="Select Team"
              isSearchable={false}
              components={{ DropdownIndicator }}
            />
          </div>
          <div className="relative">
            <Button
              variant="outline"
              className="bg-secondary-100 hidden md:block"
              onClick={() => setShowTableCustomization(!showTableCustomization)}
            >
              <div className="flex space-x-2 text-white">
                <SettingsIcon /> <span>Customise</span>
              </div>
            </Button>
            <IconButton
              {...({} as any)}
              className="bg-secondary-100 md:hidden block"
              onClick={() =>
                setShowTableCustomizationMobile(!showTableCustomizationMobile)
              }
            >
              <SettingsIcon />
            </IconButton>
            {showTableCustomization && (
              <div className="absolute top-full right-0 mt-2 z-50">
                <CustomisePanel
                  isOpen={showTableCustomization}
                  setIsOpen={setShowTableCustomization}
                  selected={selected}
                  setSelected={setSelected}
                  categories={aflCategories}
                />
              </div>
            )}
          </div>
          <div>
            <IconButton
              {...({} as any)}
              className="bg-secondary-100 hidden md:block"
              onClick={() => setShowFilter(!showFilter)}
            >
              <SlidersHorizontal />
            </IconButton>
            <IconButton
              {...({} as any)}
              className="bg-secondary-100 md:hidden block"
              onClick={() => setShowMobileFilter(!showMobileFilter)}
            >
              <SlidersHorizontal />
            </IconButton>
          </div>
          <div className="relative">
            <div
              onMouseEnter={() => setShowLegends(true)}
              onMouseLeave={() => setShowLegends(false)}
            >
              <Button variant="outline" className="hidden md:block">
                Legends
              </Button>
              {showLegends && (
                <>
                  <div className="absolute top-full right-0 h-2 w-full bg-transparent" />
                  <div className="absolute top-[calc(100%+8px)] right-0 z-50 bg-white rounded-md shadow-lg p-4 min-w-[500px]">
                    <StatsLegend stats={AFL_SMARTPLAY_STATS_LEGEND} />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <DataTable
        columns={aflPlayerStatsColumn}
        data={filteredData}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={setColumnVisibility}
      />

      {showTableCustomizationMobile && (
        <CustomisePanelMobile
          isOpen={showTableCustomizationMobile}
          setIsOpen={setShowTableCustomizationMobile}
          categories={aflCategories}
          selected={selected}
          setSelected={setSelected}
        />
      )}

      <Drawer open={showMobileFilter} onOpenChange={setShowMobileFilter}>
        <DrawerContent className="bg-white mb-[70px]">
          <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md">
            FILTERS
          </div>
          <div className="p-2 space-y-2 mt-10 h-screen">
            <div className="space-y-4">
              <div>
                <label className="block text-xl font-medium text-black-100 mb-2">
                  By position
                </label>
                <div className="space-y-2">
                  {positionOptions.map((option) => (
                    <div key={option.value} className="flex items-center">
                      <Checkbox
                        id={`position-${option.value}`}
                        checked={selectedPositions.includes(option.value)}
                        onCheckedChange={() =>
                          handlePositionChange(option.value)
                        }
                        className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                      />
                      <label
                        htmlFor={`position-${option.value}`}
                        className="ml-2 text-sm text-gray-700"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <label className="block text-xl font-medium text-black-100 mb-2">
                  By team
                </label>
                <div className="space-y-2">
                  {[
                    { value: 'all', label: 'All Teams' },
                    { value: 'home', label: 'Home Team' },
                    { value: 'away', label: 'Away Team' },
                  ].map((option) => (
                    <div key={option.value} className="flex items-center">
                      <Checkbox
                        id={`team-${option.value}`}
                        className="text-primary-200 focus:ring-primary-200 border-gray-900 rounded"
                      />
                      <label
                        htmlFor={`team-${option.value}`}
                        className="ml-2 text-sm text-gray-700"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              <div className="sticky bottom-0">
                <div className="flex justify-between items-center flex-col gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowMobileFilter(false)}
                    className="bg-primary-200 text-white hover:bg-primary-300 w-full"
                  >
                    Apply Filters
                  </Button>

                  <Button
                    variant="outline"
                    onClick={resetFilters}
                    className="text-sm w-full"
                  >
                    Reset Filters
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </DrawerContent>
      </Drawer>

      <div className="mt-4">
        <StatsLegend stats={AFL_SMARTPLAY_STATS_LEGEND} />
      </div>
    </div>
  );
};

export default AFLSmartPlayStats;
