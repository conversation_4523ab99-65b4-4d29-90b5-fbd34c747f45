import { Config } from '../context/config';

export const identifiers = {
  AUTH_DATA: 'authData',
  publicSportsFantasyMenu: [
    {
      id: 1,
      name: 'All Competitions',
      url: '/',
      submenu: [],
    },
    // {
    //   id: 3,
    //   name: 'Players',
    //   url: '/players',
    //   submenu: [],
    // },
    {
      id: 5,
      name: 'FAQs',
      url: '/faqs',
      submenu: [],
    },
    {
      id: 6,
      name: 'Rules & Scoring',
      url: '/rules-scoring?sports=cricket&rules_scoring=rules',
      submenu: [],
    },
  ],
  privateSportsFantasyMenu: [
    {
      id: 1,
      name: 'All Competitions',
      url: '/',
      submenu: [],
    },
    {
      id: 2,
      name: 'My Competitions',
      url: '/my-competitions?status=1&compType=my',
      submenu: [],
    },
    // {
    //   id: 3,
    //   name: 'Players',
    //   url: '/players',
    //   submenu: [],
    // },
    {
      id: 4,
      name: 'Settings',
      url: '/settings',
      submenu: [],
    },
    {
      id: 5,
      name: 'FAQs',
      url: '/faqs',
      submenu: [],
    },
    {
      id: 6,
      name: 'Rules & Scoring',
      url: '/rules-scoring?sports=cricket&rules_scoring=rules',
      submenu: [],
    },
  ],
  mainMenu: [
    { id: 1, name: 'Home', url: Config.siteBaseURL, submenu: [] },
    {
      id: 101,
      name: 'SmartPlay',
      url: Config.fantasySiteBaseURL,
      raceParentId: 2,
    },
    {
      id: 100,
      name: 'Smart Odds Comparisons',
      url: Config.siteBaseURL + 'odds-comparison',
      raceParentId: 2,
    },
    {
      id: 2,
      name: 'Racing',
      url: Config.siteBaseURL + 'racing',
      submenu: [
        {
          id: 1,
          name: 'Horse Racing',
          url: Config.siteBaseURL + `racing?type=${1}`,
        },
        {
          id: 3,
          name: 'Greyhound Racing',
          url: Config.siteBaseURL + `racing?type=${3}`,
        },
        {
          id: 2,
          name: 'Harness Racing',
          url: Config.siteBaseURL + `racing?type=${2}`,
        },
      ],
    },
    {
      id: 3,
      name: 'Sports',
      url: '',
      submenu: [
        {
          id: 33,
          name: 'Show All Sports',
          url: '',
          submenu: [
            {
              id: 333,
              name: 'Boxing',
              url: '',
              submenu: [],
            },
          ],
        },
      ],
    },
    {
      id: 12,
      name: 'Tipping Competition',
      url: '',
      submenu: [
        {
          id: 2,
          name: 'My Comps',
          url: Config.siteBaseURL + 'tipscompetition/my-comps',
          parentId: 12,
        },
        {
          id: 3,
          name: 'Public Comps',
          url: Config.siteBaseURL + 'tipscompetition/public/tips',
          parentId: 12,
          routeName: Config.siteBaseURL + 'tipscompetition/public/tips',
        },
        {
          id: 1,
          name: 'Create Comps',
          url: Config.siteBaseURL + 'tipscompetition/create-competition',
          parentId: 12,
        },
        {
          id: 4,
          name: 'Rankings',
          url: Config.siteBaseURL + 'tipscompetition/public/rankings',
          parentId: 12,
          routeName: Config.siteBaseURL + 'tipscompetition/public/rankings',
        },
      ],
    },
    {
      id: 10,
      name: 'Smart Info',
      url: '',
      submenu: [
        {
          id: 1,
          name: 'News',
          url: Config.siteBaseURL + 'news/0',
          parentId: 10,
          routeName: 'news',
        },
        {
          id: 4,
          name: 'Bookmakers',
          url: Config.siteBaseURL + 'bookmaker',
          parentId: 10,
        },
        {
          id: 9,
          name: 'Our People',
          url: Config.siteBaseURL + 'our-people',
          parentId: 10,
          routeName: 'our-people',
        },
        {
          id: 8,
          name: 'Recommended Websites',
          url: Config.siteBaseURL + 'recommended-websites',
          parentId: 10,
          routeName: 'recommended-websites',
        },
        {
          id: 12,
          name: 'Manage email Subscriptions',
          url: Config.siteBaseURL + 'manage-email-subscription',
          parentId: 10,
          routeName: 'manage-email-subscription',
        },
        {
          id: 10,
          name: 'Subscription',
          url: Config.siteBaseURL + 'subscription-payment',
          parentId: 10,
          routeName: 'subscription-payment',
        },
        {
          id: 11,
          name: 'My Subscription',
          url: Config.siteBaseURL + 'mySubscription',
          parentId: 10,
          routeName: 'mySubscription',
        },
      ],
    },
    {
      id: 7,
      name: 'Podcasts',
      url: Config.siteBaseURL + 'podcast',
      parentId: 10,
      routeName: 'podcast',
    },
  ],
};
