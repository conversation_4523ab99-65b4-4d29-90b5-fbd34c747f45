'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import Image from 'next/image';
import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import type { Control } from 'react-hook-form';
import { Controller } from 'react-hook-form';
import { components } from 'react-select';
import AsyncSelect from 'react-select/async';
import { useDebounce } from 'use-debounce';

import type { Option } from '@/components/UI/CountryDropDown';
import { fetchCountries } from '@/components/UI/CountryDropDown';
import { Config } from '@/helpers/context/config';

const handleError = (e: React.SyntheticEvent<HTMLImageElement>) => {
  e.currentTarget.onerror = null;
  e.currentTarget.src = '/fantasy/images/defaultImage.svg';
};

const CustomOption = (props: any) => {
  const mediaURL = Config.mediaURL;
  const flagUrl = mediaURL + props.data.country_flag;

  return (
    <components.Option {...props}>
      <div className="flex space-x-2">
        <Image
          src={flagUrl}
          onError={handleError}
          width={0}
          height={0}
          sizes="100vw"
          style={{ width: '20px', height: 'auto' }}
          alt={props.label}
          unoptimized={true}
        />
        <span className="text-sm truncate ...">{props.label}</span>
      </div>
    </components.Option>
  );
};

const CustomSingleValue = (props: any) => {
  const { data } = props;
  const mediaURL = Config.mediaURL;
  const flagUrl = mediaURL + data.country_flag;
  return (
    <div className="absolute">
      <div className="flex justify-center items-center">
        <Image
          src={flagUrl}
          width={0}
          height={0}
          onError={handleError}
          sizes="100vw"
          style={{ width: '30px', height: '44px', padding: '2px' }}
          alt={props.label}
          unoptimized={true}
        />
      </div>
    </div>
  );
};

const PhoneCodeDropdown = ({
  name,
  control,
  placeholder,
  className,
  value,
  setValue,
}: {
  name: string;
  control: Control<any>;
  placeholder?: string;
  className?: string;
  value: Option;
  setValue: Dispatch<SetStateAction<Option | undefined>>;
}) => {
  const [inputValue, setInputValue] = useState('');
  const [debouncedSearch] = useDebounce(inputValue, 300);

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteQuery({
      queryKey: ['async-countries-phone', debouncedSearch],
      queryFn: ({ pageParam = 0 }) =>
        fetchCountries(pageParam, debouncedSearch, '13,230,231,101'),
      initialPageParam: 0,
      getNextPageParam: (lastPage, allPages) => {
        if (!lastPage || lastPage.length < 20) return undefined;
        return allPages.length * 20;
      },
    });

  const loadOptions = async (inputValue: string): Promise<Option[]> => {
    return fetchCountries(0, inputValue, '13,230,231,101');
  };

  // Convert the incoming value to the format expected by react-select
  const getSelectValue = () => {
    if (!value) return null;
    // If value is already in the correct format, return it
    if (typeof value === 'object' && value.value && value.label) {
      return value;
    }
    // If we have the value in our options, use that
    const option = data?.pages
      ?.flat()
      .find((option) => option.id.toString() === value.id.toString());
    if (option) {
      return {
        id: option.id,
        value: option.id,
        label: option.label,
        country_flag: option.country_flag,
      };
    }
    return null;
  };

  return (
    <div className="w-full">
      <Controller
        name={name}
        control={control}
        render={() => (
          <AsyncSelect
            className={className}
            classNamePrefix="select"
            components={{
              Option: CustomOption,
              SingleValue: CustomSingleValue,
            }}
            styles={{
              container: (provided) => ({
                ...provided,
              }),
              valueContainer: (provided) => ({
                ...provided,
                overflow: 'visible',
              }),
              // @ts-expect-error
              placeholder: (provided, state) => ({
                ...provided,
                position: 'absolute',
                top:
                  state.hasValue ||
                  state.selectProps.inputValue ||
                  state.selectProps.menuIsOpen
                    ? -14
                    : 'auto',
                backgroundColor:
                  state.hasValue ||
                  state.selectProps.inputValue ||
                  state.selectProps.menuIsOpen
                    ? 'white'
                    : 'transparent',
                transition: 'top 0.2s, font-size 0.1s !important',
                fontSize:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '12px !important',
                color: state.selectProps.menuIsOpen ? '#4455c7' : '#a4a4a4',
                padding:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '0px 3px',
                paddingLeft:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '1px !important',
                marginLeft:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '7px !important',
                lineHeight:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '8px !important',
              }),
            }}
            placeholder={placeholder}
            onInputChange={(value) => setInputValue(value)}
            loadOptions={loadOptions}
            defaultOptions={data?.pages?.flat() || []}
            onChange={(selectedOption) => {
              setValue(selectedOption || undefined);
            }}
            value={getSelectValue()}
            onMenuScrollToBottom={() => {
              if (hasNextPage && !isFetchingNextPage) {
                fetchNextPage();
              }
            }}
          />
        )}
      />
    </div>
  );
};

export default PhoneCodeDropdown;
