import {
  FacebookShare<PERSON>utton,
  TwitterShareButton,
  LinkedinShareButton,
  WhatsappShareButton,
  FacebookMessengerShareButton,
  TelegramShareButton,
} from 'react-share';
import { useState, useEffect } from 'react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/UI/popover';
import { setApiMessage } from '@/helpers/commonFunctions';
import WhatsApp from '@/components/Icons/Socials/WhatsApp';
import Facebook from '@/components/Icons/Socials/Facebook';
import Messenger from '@/components/Icons/Socials/Messenger';
import Twitter from '@/components/Icons/Socials/Twitter';
import LinkedIn from '@/components/Icons/Socials/LinkedIn';
import Message from '@/components/Icons/Socials/Message';
import CopyLink from '@/components/Icons/Socials/CopyLink';
import { Config } from '@/helpers/context/config';

interface SharePopupProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

export default function SharePopup({
  isOpen,
  onClose,
  children,
}: SharePopupProps) {
  const [shareUrl, setShareUrl] = useState('');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const baseUrl = Config.siteBaseURL?.replace(/\/+$/, ''); // remove trailing slashes
      const currentUrl = window.location.href;
      const url = new URL(currentUrl);
      const pathname = url.pathname;
      const search = url.search;
      const shareUrl = `${baseUrl}${pathname}${search}`;
      setShareUrl(shareUrl);
    }
  }, []);

  console.log(shareUrl);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareUrl);
    setApiMessage('success', 'Link copied to clipboard!');
    onClose();
  };

  return (
    <Popover open={isOpen} onOpenChange={onClose}>
      <PopoverTrigger asChild>
        <div>{children}</div>
      </PopoverTrigger>
      <PopoverContent
        className="w-[309px] p-3 !z-[1500] bg-white border border-gray-100 rounded-lg"
        side="bottom"
        align="end"
      >
        <div className="grid grid-cols-4 gap-2">
          {/* First row */}
          <div className="flex flex-col items-center gap-2">
            <button onClick={handleCopyLink}>
              <CopyLink />
            </button>
            <span className="text-xs text-gray-600">Copy Link</span>
          </div>

          <div className="flex flex-col items-center gap-2">
            <WhatsappShareButton url={shareUrl} onClick={onClose}>
              <WhatsApp />
            </WhatsappShareButton>
            <span className="text-xs text-gray-600">Whatsapp</span>
          </div>

          <div className="flex flex-col items-center gap-2">
            <FacebookShareButton url={shareUrl} onClick={onClose}>
              <Facebook />
            </FacebookShareButton>
            <span className="text-xs text-gray-600">Facebook</span>
          </div>

          <div className="flex flex-col items-center gap-2">
            <FacebookMessengerShareButton
              url={shareUrl}
              onClick={onClose}
              appId={Config.FacebookAppID || ''}
            >
              <Messenger />
            </FacebookMessengerShareButton>
            <span className="text-xs text-gray-600">Messenger</span>
          </div>

          {/* Second row */}
          <div className="flex flex-col items-center gap-2">
            <TwitterShareButton url={shareUrl} onClick={onClose}>
              <Twitter />
            </TwitterShareButton>
            <span className="text-xs text-gray-600">Twitter</span>
          </div>

          <div className="flex flex-col items-center gap-2">
            <LinkedinShareButton url={shareUrl} onClick={onClose}>
              <LinkedIn />
            </LinkedinShareButton>
            <span className="text-xs text-gray-600">LinkedIn</span>
          </div>

          <div className="flex flex-col items-center gap-2">
            <button
              onClick={() => {
                window.open(`sms:?body=${encodeURIComponent(shareUrl)}`);
                onClose();
              }}
            >
              <Message />
            </button>
            <span className="text-xs text-gray-600">Messages</span>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
