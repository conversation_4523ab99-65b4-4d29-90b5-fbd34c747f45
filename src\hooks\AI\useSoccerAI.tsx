'use client';

import { SoccerAPIPlayer } from '@/helpers/context/soccer/createSoccerTeamContext';
import { usePlayerList } from '../players/usePlayerList';
import { useSearchParams } from 'next/navigation';
import {
  SoccerPlayer,
  SoccerPlayerRole,
  SoccerPlayersByRole,
} from '@/lib/types/soccer';
import { useCopilotAction, useCopilotReadable } from '@copilotkit/react-core';
import { CopilotPopup, useCopilotChatSuggestions } from '@copilotkit/react-ui';
import { useSoccerStore } from '@/store/soccer/useSoccerStore';
import { useEffect, useState } from 'react';

const useSoccerAI = () => {
  const searchParams = useSearchParams();
  const eventId = searchParams.get('eventId');
  const tournamentId = searchParams.get('tournamentId');
  const sportId = searchParams.get('sportId');

  const { data: playerList } = usePlayerList<SoccerAPIPlayer>({
    eventId,
    tournamentId,
    sportId,
  });

  const {
    remainingBudget,
    removePlayer,
    resetTeam,
    addPlayer,
    playerByRoleLimit,
    totalBudget,
    soccerPlayersByRole,
    setActiveTabPlayer,
  } = useSoccerStore();

  // All Soccer Source Players
  const allSoccerSourcePlayers: SoccerPlayer[] =
    playerList?.result.map((player) => ({
      ...player,

      role:
        player.role === 'G'
          ? 'GKP'
          : player.role === 'D'
            ? 'DEF'
            : player.role === 'M'
              ? 'MID'
              : 'FWD',
    })) || [];

  // All Soccer Players By Role
  const allSoccerPlayersByRole: SoccerPlayersByRole = {
    GKP: allSoccerSourcePlayers.filter((player) => player.role === 'GKP'),
    DEF: allSoccerSourcePlayers.filter((player) => player.role === 'DEF'),
    MID: allSoccerSourcePlayers.filter((player) => player.role === 'MID'),
    FWD: allSoccerSourcePlayers.filter((player) => player.role === 'FWD'),
  };

  useCopilotReadable({
    description: 'All Available Players for your team',
    value: allSoccerPlayersByRole,
  });

  useCopilotChatSuggestions({
    instructions: `The best players for your team are: ${JSON.stringify(
      allSoccerPlayersByRole,
    )}`,
  });

  useCopilotReadable({
    description: 'Total Budget for your team',
    value: totalBudget,
  });

  useCopilotReadable({
    description: 'Remaining Budget for your team',
    value: remainingBudget,
  });

  useCopilotReadable({
    description: 'Player By Role Limit',
    value: playerByRoleLimit,
  });

  useCopilotReadable({
    description: 'Valid Team Selection',
    value: Object.values(soccerPlayersByRole).flat().length === 11,
  });

  useCopilotAction({
    name: 'addPlayer',
    description: 'Add a new player to the team',
    parameters: [
      {
        name: 'playerId',
        type: 'number',
        description: 'The id of the player to add',
      },
      {
        name: 'role',
        type: 'string',
        description: 'The role of the player to add',
        enum: ['GK', 'DEF', 'MID', 'FWD'],
      },
    ],
    handler: async ({ playerId, role }) => {
      const player = allSoccerPlayersByRole[role as SoccerPlayerRole].find(
        (player) => player.playerId === playerId,
      );

      setActiveTabPlayer(role as SoccerPlayerRole);

      if (player) {
        addPlayer(player, role as SoccerPlayerRole);
      }
    },
  });

  useCopilotAction({
    name: 'removePlayer',
    description: 'Remove a player from the team',
    parameters: [
      {
        name: 'playerId',
        type: 'number',
        description: 'The id of the player to remove',
      },
      {
        name: 'role',
        type: 'string',
        description: 'The role of the player to remove',
        enum: ['GK', 'DEF', 'MID', 'FWD'],
      },
    ],
    handler: async ({ playerId, role }) => {
      const player = allSoccerPlayersByRole[role as SoccerPlayerRole].find(
        (player) => player.playerId === playerId,
      );

      if (player) {
        removePlayer(player, role as SoccerPlayerRole);
      }
    },
  });

  useCopilotAction({
    name: 'resetTeam',
    description: 'Reset the team',
    parameters: [],
    handler: async () => {
      resetTeam(totalBudget ?? 0, totalBudget ?? 0);
    },
  });

  return {
    SoccerCopilot: CopilotPopup,
  };
};

export default useSoccerAI;
