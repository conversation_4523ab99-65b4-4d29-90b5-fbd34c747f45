'use client';
import '../../../../components/Competition/competitions.scss';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import CricketMobileIcon from '@/assets/images/icons/cricketMobileIcon.svg';
import CricketIcon from '@/assets/images/sportIcon/blueSportIcon/Cricket.svg';
import CreateTeam from '@/components/Competition/CreateTeam';
import LeaderBorad from '@/components/Competition/LeaderBorad';
import PreviewTeam from '@/components/Competition/PreviewTeam';
import Prizes from '@/components/Competition/Prizes';
import Standings from '@/components/Competition/Standings';
import TournamentHeader from '@/components/Competition/TournamentHeader';
import Breadcrumbs from '@/components/UI/Breadcrumbs';
import CustomTabs from '@/components/UI/CustomTab';
import PreLoader from '@/components/UI/PreLoader';
import { useCompetition } from '@/helpers/context/competitionContext';
import type { PlayersByRole } from '@/helpers/context/createTeamContext';
import { useTeam } from '@/helpers/context/createTeamContext';
import useScreen from '@/hooks/useScreen';
import { LocalStorage } from '@/lib/utils';
import CricketScoreboard from '@/components/Common/Scorecard';
import LiveUpdates from '../football/(components)/LiveUpdates';
import moment from 'moment';
import ContentWrapper from '@/components/Layout/ContentWrapper';
import ShareIcon from '@/components/Icons/Share/ShareIcon';
import SharePopup from '@/components/PopUp/Share/SharePopup';
import { Button } from '@/components/UI/button';
import ReservePlayerModal from '@/components/Players/Reserve/ReservePlayerModal';
import DataTable from '@/components/UI/DataTabel';
import { cricketCloumns } from '@/components/UI/DataTabel/columns/createTeamColumn';

const Competitions = () => {
  const { width } = useScreen();
  const [activeTab, setActiveTab] = useState<number>(1);
  const [coinsAvailable] = useState<boolean>(false);

  const [teamSubmitConfirmation, setTeamSubmitConfirmation] =
    useState<boolean>(false);
  const { state: { lastEntry },
    resetTeam,
    removePlayer,
    activeTab: plyaerRoleTab,
    setActiveTab: setPlayerRoleTab,
    setBudget,
    openReserveModal,
    setOpenReserveModal,
  } = useTeam();
  const {
    eventDetailsResponse,
    playerByRole,
    eventDetailsResponseLoading,
    isDreamTeamResponseLoading,
    dreamTeamResponse,
  } = useCompetition();
  const { state, getDreamTeam: dispatchDreamTean } = useTeam();
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;

  const eventDetailsData = eventDetailsResponse?.result;
  const isCommentaryAvailable = (): boolean => {
    const sportId = eventDetailsResponse?.result?.eventDetails?.SportId;
    const matchStatus = (eventStatus || '').toLowerCase();
    const isLiveOrCompleted =
      matchStatus === 'inprogress' ||
      matchStatus === 'finished' ||
      matchStatus === 'innings break' ||
      matchStatus === 'drink';

    const hasMatchStarted = moment().isAfter(
      moment.utc(eventDetailsData?.eventDetails?.startTime).local(),
    );

    return isLiveOrCompleted && hasMatchStarted;
  };

  const { ALL, BAT, BOW, WKP } = state.playersByRole;

  const stats = {
    selectedPlayer: `${ALL?.length + BAT?.length + BOW?.length + WKP?.length}/11`,
    remainingSalary: state?.remainingBudget,
  };

  const handleCloseDialog = () => {
    setTeamSubmitConfirmation(false);
  };

  const localPlayerByRole = LocalStorage.getItem<PlayersByRole>('dream_team');
  const searchParams = useSearchParams();
  const add_more = searchParams.get('add_more');
  const compType = searchParams.get('comp_Type');
  const dreamTeamId = searchParams.get('dreamTeamId');
  const playerId = searchParams.get('playerId');
  const event_id = searchParams.get('event_id');
  const leaderboard = searchParams.get('leaderboard');
  const scoreCard = searchParams.get('score_card');
  const role = searchParams.get('role') as keyof PlayersByRole;
  const [showSharePopup, setShowSharePopup] = useState(false);

  const disabledMyTeamTab =
    ((leaderboard === 'true' || scoreCard === 'true') ||
      (eventDetailsData?.dreamTeams?.length ?? 0) === 0) &&
    eventStatus !== 'upcoming';

  const tabs = [
    {
      label: 'my team',
      labelId: 1,
      count: eventDetailsData?.dreamTeams
        ? eventDetailsData?.dreamTeams?.length
        : '',
      disabled: disabledMyTeamTab,
    },
    {
      label: 'leaderboard',
      labelId: 2,
    },
    // {
    //   label: 'Commentary',
    //   labelId: 5,
    // },
    {
      label: 'Scorecard',
      labelId: 6,
      disabled:
        eventDetailsResponse?.result?.eventDetails?.status === 'upcoming',
    },
    {
      label: 'prizes',
      labelId: 3,
    },
    {
      label: 'standings',
      labelId: 4,
    },
    {
      label: 'Live Updates',
      labelId: 5,
      disabled: !isCommentaryAvailable(),
    },
  ];

  const breadcrumbsLinks = [
    { label: 'Home', href: '/' },
    { label: 'SmartPlay', href: '#' },
    {
      label: compType === 'my' ? 'My Competitions' : 'All Competitions',
      href: compType === 'my' ? '/my-competitions?status=1&compType=my' : '/',
    },
    {
      label: eventDetailsResponse?.result?.eventDetails?.eventName!,
      href: '#',
    },
  ];

  useEffect(() => {
    if (localPlayerByRole) {
      resetTeam();
    }
    if (add_more && add_more === 'true') {
      resetTeam();
    }

    if (!eventDetailsData?.dreamTeams) {
      resetTeam();
    }

    return () => {
      resetTeam();
    };
  }, []);

  let previewMode: boolean = false;
  let createTeamMode: boolean = false;

  switch (eventStatus) {
    case 'finished':
    case 'inprogress':
    case 'innings break':
    case 'Strategic Timeout':
    case 'Stumps':
      previewMode = true;
      break;
    case 'team-creation':
    case 'upcoming':
      if (
        add_more &&
        add_more === 'true' &&
        eventDetailsResponse?.result?.dreamTeams?.length! > 0
      ) {
        createTeamMode = true;
      } else if (!eventDetailsResponse?.result?.dreamTeams?.length) {
        createTeamMode = true;
      } else {
        previewMode = true;
      }
      break;
  }

  // Setting budget
  const salaryCap =
    eventDetailsResponse?.result?.eventDetails?.CricketSeason
      ?.fantasy_sport_salary_cap;
  useEffect(() => {
    if (salaryCap) {
      setBudget(salaryCap);
    }
  }, [salaryCap]);

  // Set Dream Team For Edit

  useEffect(() => {
    if (dreamTeamResponse && dreamTeamId) {
      dispatchDreamTean(dreamTeamResponse, +playerId!, role);
    }
  }, [dreamTeamResponse]);

  useEffect(() => {
    if (leaderboard === 'true') {
      setActiveTab(2);
    }
    if (scoreCard === 'true') {
      setActiveTab(6);
    }

    if (eventStatus === 'finished' && disabledMyTeamTab) {
      setActiveTab(2);
    }
  }, [leaderboard, scoreCard, eventStatus]);

  const sourcePlayers = Object.values(playerByRole).flat();
  const selectedPlayers = Object.values(state.playersByRole).flat();
  const selectedPlayerIds = selectedPlayers.map((player) => player?.playerId);
  const reservePlayers = sourcePlayers.filter((player) => !selectedPlayerIds.includes(player?.playerId));
  const uniqueReservePlayers = new Set(reservePlayers);
  const uniqueReservePlayersArray = Array.from(uniqueReservePlayers);

  return (
    <div>
      {(!eventDetailsResponseLoading || !isDreamTeamResponseLoading) && (
        <ContentWrapper>
          <div className="mt-3 mb-2 md:p-8 p-0 md:pb-0">
            <Breadcrumbs links={breadcrumbsLinks} />
            <h1 className="flex  items-end md:items-start justify-between mt-2 space-y-1 text-[22.4px] leading-[28px] text-white font-normal font-veneerCleanSoft
            flex-row md:mt-0 md:text-[31.36px] md:leading-[40px]
            lg:text-black-100"
            >
              <span className="flex items-center max-799:flex-col max-799:items-start text-black-100">
                {eventDetailsResponse?.result?.eventDetails?.eventName}
                <span className="text-[14px] leading-[16px] font-inter font-normal text-black-100 ml-[15px] max-799:ml-0 max-1024:text-black-100 flex items-center ">
                  <span>
                    {width > 1024 ? <CricketIcon /> : <CricketMobileIcon />}
                  </span>
                  <span>
                    {
                      eventDetailsResponse?.result?.eventDetails
                        ?.CricketTournament?.name
                    }
                  </span>
                </span>
              </span>
              <div className="flex items-center gap-2 justify-center ">
                <Button
                  variant="default"
                  size={'lg'}
                  className="h-[48px] bg-tipping-gradient text-lg text-white hidden"
                >
                  Compare Odds
                </Button>
                <div className='hidden md:block'>
                  <SharePopup
                    isOpen={showSharePopup}
                    onClose={() => setShowSharePopup(false)}
                  >
                    <button
                      type="button"
                      className="cursor-pointer border-none bg-transparent p-0 flex items-center justify-center"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setShowSharePopup(true);
                      }}
                    >
                      <ShareIcon />
                    </button>
                  </SharePopup>
                </div>
              </div>
            </h1>
          </div>
          <div>
            <div className="bg-off-white-200 md:px-8 px-0">
              <TournamentHeader
                status={eventDetailsResponse?.result?.eventDetails?.status}
              />
              {/* Tabs */}
              <div
                className={`${eventDetailsResponse?.result?.eventDetails?.status === 'upcoming' ? 'mt-3' : 'mt-2 md:mt-0'}  max-1024:pt-1.5`}
              >
                <CustomTabs<number>
                  tabs={tabs}
                  setActiveTab={setActiveTab}
                  activeTab={activeTab}
                />
              </div>
            </div>
            <div className="bg-off-white-200 pb-10">
              {activeTab === 1 && createTeamMode && (
                <CreateTeam
                  coinsAvailable={coinsAvailable}
                  handleCloseDialog={handleCloseDialog}
                  stats={stats}
                  lastEntery={lastEntry}
                  plyaerRoleTab={plyaerRoleTab}
                  removePlayer={removePlayer}
                  resetTeam={resetTeam}
                  setPlayerRoleTab={setPlayerRoleTab}
                  setTeamSubmitConfirmation={setTeamSubmitConfirmation}
                  teamSubmitConfirmation={teamSubmitConfirmation}
                  playerByRole={playerByRole}

                />
              )}

              {activeTab === 1 && previewMode && <PreviewTeam />}

              {activeTab === 2 && (
                <div className="md:px-8 px-0 mt-2">
                  <LeaderBorad />
                </div>
              )}

              {activeTab === 3 && <Prizes />}
              {activeTab === 4 && <Standings />}
              {/* {activeTab === 5 && <Commentary />} */}
              {activeTab === 6 && <CricketScoreboard />}
              {activeTab === 5 && (
                <LiveUpdates
                  matchId={event_id?.toString()}
                  sportId={eventDetailsResponse?.result?.eventDetails?.SportId}
                />
              )}
            </div>
          </div>

          <ReservePlayerModal setTeamSubmitConfirmation={setTeamSubmitConfirmation}>
            <DataTable
              columns={cricketCloumns}
              data={uniqueReservePlayersArray}
              stickyColumns={[]}
              initialColumnVisibility={{
                name: false,
                team: false,
                teamName: false,
              }}
            />
          </ReservePlayerModal>


        </ContentWrapper>
      )}
    </div>
  );
};

export default Competitions;
