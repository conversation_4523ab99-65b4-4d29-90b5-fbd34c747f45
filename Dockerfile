# syntax=docker.io/docker/dockerfile:1

FROM node:18-alpine AS base

# 1. Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json ./
RUN npm install


# 2. Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
# This will do the trick, use the corresponding env file for each environment.
#COPY .env .env
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_FANTASY_API_URL
ARG NEXT_PUBLIC_MEDIA_URL
ARG NEXT_PUBLIC_COUNTRY_MEDIA_URL
ARG NEXT_PUBLIC_WP_BASE_URL
ARG NEXT_PUBLIC_FANTASY_API_BASE_URL
ARG NEXT_PUBLIC_VERSION
ARG NEXT_PUBLIC_RELEASE
ARG NEXT_PUBLIC_SUBSCRIPTION_USER_ID
ARG NEXT_PUBLIC_FACEBOOK_ID

# Set environment variables
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_FANTASY_API_URL=${NEXT_PUBLIC_FANTASY_API_URL}
ENV NEXT_PUBLIC_MEDIA_URL=${NEXT_PUBLIC_MEDIA_URL}
ENV NEXT_PUBLIC_COUNTRY_MEDIA_URL=${NEXT_PUBLIC_COUNTRY_MEDIA_URL}
ENV NEXT_PUBLIC_WP_BASE_URL=${NEXT_PUBLIC_WP_BASE_URL}
ENV NEXT_PUBLIC_FANTASY_API_BASE_URL=${NEXT_PUBLIC_FANTASY_API_BASE_URL}
ENV NEXT_PUBLIC_VERSION=${NEXT_PUBLIC_VERSION}
ENV NEXT_PUBLIC_RELEASE=${NEXT_PUBLIC_RELEASE}
ENV NEXT_PUBLIC_SUBSCRIPTION_USER_ID=${NEXT_PUBLIC_SUBSCRIPTION_USER_ID}
ENV NEXT_PUBLIC_FACEBOOK_ID=${NEXT_PUBLIC_FACEBOOK_ID}
RUN npm run build

# 3. Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

COPY --from=builder /app/public ./public

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static


USER nextjs

EXPOSE 3000

ENV PORT=3000

CMD HOSTNAME="0.0.0.0" node server.js