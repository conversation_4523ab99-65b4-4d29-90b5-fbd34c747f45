import type { <PERSON>a, StoryObj } from '@storybook/react';
import Header from './index';

const meta: Meta<typeof Header> = {
  title: 'Components/Header',
  component: Header,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;

type Story = StoryObj<typeof Header>;

export const Default: Story = {
  args: {},
};

export const SmartPlay: Story = {
  args: {
    type: 'smartPlay',
  },
};

export const SmartInfo: Story = {
  args: {
    type: 'smartInfo',
  },
};

export const SmartOdds: Story = {
  args: {
    type: 'smartOdds',
  },
};

export const SmartTipping: Story = {
  args: {
    type: 'smartTipping',
  },
};
