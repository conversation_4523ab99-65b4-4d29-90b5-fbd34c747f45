/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  experimental: {
    missingSuspenseWithCSRBailout: false,
  },
  basePath: '/fantasy',
  reactStrictMode: false,
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'media.staging.smartb.au.sydney.digiground.com.au',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'http',
        hostname: 'media.smartb.com.au',
        port: '',
        pathname: '**',
      },
    ],
    domains: [
      'media.staging.smartb.au.sydney.digiground.com.au',
      'media.smartb.com.au',
    ],
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    return config;
  },
};

export default nextConfig;
