import axios from 'axios';
import {
  CommentaryApiResponse,
  CricketCommentaryResponse,
  CricketEvent,
  CricketMatchData,
  MatchLineUpApiResponse,
  MatchResponse,
  SmartPlayCricketApiResponse,
} from '../../../../types/commentry';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { Config } from '@/helpers/context/config';

// Fetcher function
export async function fetchMatchCommentary(
  matchId: number,
  teamId?: number,
): Promise<MatchResponse> {
  let url = `/fantasy/api/commentary?matchId=${matchId}`;
  if (teamId) {
    url += `&teamId=${teamId}`;
  }
  const response = await axios.get(url);
  return response.data;
}

export async function fetchMatchCommentaryByMatchId(
  matchId: number | undefined,
  teamId?: number,
  isPreview?: boolean,
): Promise<CommentaryApiResponse> {
  let url = `${Config.baseURL}sports/statistics/cricket/commentary/${matchId}`;

  // const params = new URLSearchParams();
  // if (teamId !== undefined) params.append('teamId', teamId.toString());
  // if (isPreview) params.append('preview', 'true'); // Only adds preview if true

  // if (params.toString()) {
  //   url += `?${params.toString()}`;
  // }

  const response = await axiosInstance.get(url);
  return response.data;
}

export async function getLiveCricketCommentary({
  matchId,
  teamId,
  eventFilter,
}: {
  matchId: number | undefined;
  teamId?: number;
  eventFilter?: string;
}): Promise<CricketCommentaryResponse> {
  let url = `${Config.baseURL}sports/statistics/cricket/commentary/${matchId}`;
  if (eventFilter) {
    url += `?event=${eventFilter}`;
  }
  const response = await axiosInstance.get(url);
  return response.data;
}

export async function getMatchDetails(matchId: number): Promise<CricketEvent> {
  const url = `${Config.baseURL}sports/statistics/cricket/info/${matchId}`;
  const response = await axiosInstance.get(url);
  return response.data;
}

export async function getMatchLineUp(
  matchId: number,
): Promise<MatchLineUpApiResponse> {
  const url = `${Config.baseURL}sports/statistics/cricket/lineups/${matchId}`;
  const response = await axiosInstance.get(url);
  return response.data;
}

export async function getGameStats(matchId: number): Promise<CricketMatchData> {
  const url = `${Config.baseURL}sports/statistics/cricket/scoreboard/commentary/${matchId}`;
  const response = await axiosInstance.get(url);
  return response.data;
}

export async function getSmartPlayCricketStats({
  tournamentId,
  seasonId,
  eventId,
  sportId,
}: {
  tournamentId: number;
  sportId: number;
  eventId: number;
  seasonId: number;
}): Promise<SmartPlayCricketApiResponse> {
  const url = `${Config.fantasyURL}/events/player-score/${tournamentId}?SportId=${sportId}&eventId=${eventId}&seasonId=${seasonId}`;
  const response = await axiosInstance.get(url);
  return response.data;
}
