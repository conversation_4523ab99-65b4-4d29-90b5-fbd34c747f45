// hooks/useFetchCountries.ts
import { useInfiniteQuery } from '@tanstack/react-query';

const fetchCountries = async (offset: number, search: string) => {
  const response = await fetch(
    `https://smartb.com.au/api/public/country?limit=20&offset=${offset}&search=${search}`,
  );
  if (!response.ok) {
    throw new Error('Network response was not ok');
  }
  return response.json();
};

export const useFetchCountries = ({ search }: { search: string }) => {
  return useInfiniteQuery({
    queryKey: ['countries', search],
    queryFn: ({ pageParam = 0 }) => fetchCountries(pageParam, search), // Default to 0 if pageParam is undefined
    initialPageParam: 0, // Start with offset of 0
    getNextPageParam: (lastPage, allPages) => {
      const totalItem = lastPage?.result?.count;
      const allItems = allPages?.length * 20;
      if (allItems >= totalItem) {
        return undefined;
      }
      return allPages?.length * 20;
    },
  });
};
