'use client';
import { Tooltip } from '@material-tailwind/react';
import moment from 'moment';
import Image from 'next/image';
import Countdown from 'react-countdown';

import DefaultTeam from '@/assets/images/icons/defaultTeam.png';
import Payout from '@/assets/images/icons/payout.svg';
import WinArrow from '@/assets/images/icons/winArrow.svg';
import WinCup from '@/assets/images/icons/wincup.svg';
import Coins from '@/assets/images/settings/smartBCoins.png';
import { Config } from '@/helpers/context/config';
import { EventObject } from '.';
import LeaderboardIcon from '../Icons/LeaderboardIcon';
import CommentaryIcon from '../Icons/CommentaryIcon';
import ScoreCardIcon from '../Icons/ScoreCardIcon';
import { cn } from '@/lib/utils';
import { useSearchParams } from 'next/navigation';

interface CompetitionProps {
  compData: EventObject;
  buttonContent: React.ReactNode;
  finalEventPrice: number;
  topTeamPayOut: number;
  fetchCricketScore: (scoreboard: any, team: string) => JSX.Element;
  otherSportScore: (scoreboard: any, team: string) => JSX.Element;
  renderer: (props: any) => JSX.Element; // Adjust renderer type
  extraActions?: React.ReactNode;
  handleOpenCommentary: () => void;
  isCommentaryAvailable: () => boolean;
  handleLeaderboard: () => void;
  handleScoreCard: () => void;
}

const Competition: React.FC<CompetitionProps> = ({
  compData,
  buttonContent,
  finalEventPrice,
  topTeamPayOut,
  fetchCricketScore,
  otherSportScore,
  renderer,
  handleOpenCommentary,
  isCommentaryAvailable,
  handleLeaderboard,
  handleScoreCard,
  extraActions,
}) => {
  const searchParams = useSearchParams();

  const status = searchParams.get('status');
  // Helper functions to render specific sections
  const renderTeamInfo = (team: 'home' | 'away') => {
    const teamData = team === 'home' ? compData?.homeTeam : compData?.awayTeam;

    let otherSportScoreData;
    if (compData?.SportId === 12) {
      otherSportScoreData = compData?.RLScores;
    } else if (compData?.SportId === 9) {
      otherSportScoreData = compData?.ARScores;
    } else if (compData?.SportId === 8) {
      otherSportScoreData = compData?.ScoreBoard;
    }
    const teamScore =
      compData?.SportId === 4
        ? fetchCricketScore(compData?.ScoreBoard, `${team}team`)
        : otherSportScore(otherSportScoreData, `${team}team`);
    const isWinner = compData?.winnerCode === (team === 'home' ? 1 : 2);
    let teamFlag;
    if (Config?.mediaURL && teamData?.flag) {
      teamFlag = Config?.mediaURL + teamData?.flag;
    }

    return (
      <div className="flex items-center mb-1.5">
        <div className="w-[30px] h-[30px] rounded-full mr-2">
          <Image
            src={teamFlag ?? DefaultTeam}
            alt={`${team} Team Icon`}
            width={30}
            height={30}
            unoptimized={true}
          />
        </div>
        <div className="flex items-center gap-[50px]">
          <span className="text-[16px] leading-[19px] font-inter font-semibold text-black-100 w-[175px]">
            {teamData?.name}
          </span>
          {(compData?.status === 'finished' ||
            compData?.status === 'inprogress') &&
          (compData?.ScoreBoard || compData?.RLScores || compData?.ARScores) ? (
            <div className="flex items-center gap-2">
              <span
                className={isWinner ? 'text-secondary-100' : 'text-black-100'}
              >
                {teamScore}
              </span>
              {isWinner && <WinArrow />}
            </div>
          ) : null}
        </div>
      </div>
    );
  };

  const renderRankSection = () => {
    return (
      <div className="flex items-start flex-col">
        <div className="text-[16px] leading-[19px] font-inter font-normal text-black-700 mb-1.5">
          Rank
        </div>
        <span className="text-[22.4px] leading-[27px] font-inter font-semibold text-secondary-100 ml-1 capitalize">
          {compData?.currentRank +
            (compData?.name ? ' (' + compData?.name + ')' : '')}
        </span>
      </div>
    );
  };

  const renderPrizePoolSection = () => {
    return (
      <div>
        <div className="flex items-center">
          <span className="text-[16px] leading-[19px] font-inter font-normal text-black-700 mb-1.5">
            Prize pool
          </span>
        </div>
        {compData?.eventConfiguration ? (
          <div className="flex items-center">
            <Image
              src={Coins}
              alt="coin icon"
              unoptimized={true}
              width={18.37}
              height={18.37}
            />
            <span className="text-[22.4px] leading-[27px] font-inter font-semibold text-secondary-100 ml-1">
              {compData?.eventConfiguration?.prizePool}
            </span>
          </div>
        ) : (
          <div>-</div>
        )}
      </div>
    );
  };

  const renderWinningsSection = () => {
    return (
      <div>
        <div className="flex items-center">
          <span className="text-[16px] leading-[19px] font-inter font-normal text-black-700 mb-1.5">
            Winnings
          </span>
        </div>
        <div className="flex items-center">
          <Image
            src={Coins}
            alt="coin icon"
            unoptimized={true}
            width={18.37}
            height={18.37}
          />
          <span className="text-[22.4px] leading-[27px] font-inter font-semibold text-secondary-100 ml-1">
            {compData?.winningPrice ?? 0}
          </span>
        </div>
      </div>
    );
  };

  const renderEntryCoinsSection = () => {
    return (
      <div>
        <div className="flex items-center">
          <span className="text-[16px] leading-[19px] font-inter font-normal text-black-700 mb-1.5">
            Entry coins
          </span>
        </div>
        {compData?.eventConfiguration ? (
          <div className="flex items-center">
            {compData?.eventConfiguration?.eventType === 'paid' && (
              <Image
                src={Coins}
                alt="coin icon"
                unoptimized={true}
                height={18.37}
                width={18.37}
              />
            )}
            <span className="text-[22.4px] leading-[27px] font-inter font-semibold text-secondary-100 ml-1">
              {compData?.eventConfiguration?.eventType === 'paid'
                ? compData?.eventConfiguration?.entryCoin
                : 'Free'}
            </span>
          </div>
        ) : (
          <div>-</div>
        )}
      </div>
    );
  };

  const renderEntriesSection = () => {
    return (
      <div>
        <div className="flex items-center">
          <span className="text-[16px] leading-[19px] font-inter font-normal text-black-700 mb-1.5">
            Entries
          </span>
        </div>
        <span className="text-[22.4px] leading-[27px] font-inter font-semibold text-secondary-100 ml-1">
          {compData?.userEntry}
        </span>
      </div>
    );
  };

  const renderLiveScoreSection = () => {
    return (
      <div>
        <div className="flex items-center">
          <span className="text-[16px] leading-[19px] font-inter font-normal text-black-700 mb-1.5">
            Live Score
          </span>
        </div>
        <span className="text-[22.4px] leading-[27px] font-inter font-semibold text-secondary-100 ml-1">
          {compData?.liveScore}
        </span>
      </div>
    );
  };

  const renderTeamRankSection = () => {
    return (
      <div className="flex items-center flex-col">
        <div className="flex items-center">
          <span className="text-[16px] leading-[19px] font-inter font-normal text-black-700 mb-1.5">
            Current Rank
          </span>
        </div>
        <span className="text-[22.4px] leading-[27px] font-inter font-semibold text-secondary-100 ml-1">
          {compData?.currentRank}/{compData?.name}
        </span>
      </div>
    );
  };

  const renderCountdown = () => {
    return (
      <span className="text-[16px] leading-[19px] font-inter font-semibold text-black-100">
        Starting in -{' '}
        <span className="text-[#FC4714]">
          <Countdown
            date={moment.utc(compData?.startTime).local().toDate()}
            renderer={renderer}
          />
        </span>
      </span>
    );
  };

  const renderLiveStatus = () => {
    return (
      <span className="text-[16px] leading-[19px] font-inter font-semibold text-white bg-negative-300 uppercase px-[13px] py-1 rounded-[3px]">
        Live
      </span>
    );
  };

  return (
    <>
      <div className="flex justify-between items-center mb-2 relative">
        <div className="w-[60%] h-full">
          <div>
            {renderTeamInfo('home')}
            {renderTeamInfo('away')}
          </div>
        </div>
        <div className="flex items-start justify-between gap-3 w-full">
          {compData?.status === 'finished'
            ? renderRankSection()
            : renderPrizePoolSection()}
          {compData?.status === 'finished' && renderWinningsSection()}

          {compData?.status === 'upcoming' && renderEntryCoinsSection()}
          {renderEntriesSection()}
          {compData?.currentRank !== 0 &&
            compData.status === 'inprogress' &&
            renderTeamRankSection()}
          {compData?.liveScore && renderLiveScoreSection()}

          <div className="flex flex-col justify-end gap-y-2">
            <div className={cn('flex justify-end')}>
              <div className="flex items-center gap-2">
                {buttonContent}
                {/* {extraActions} */}
              </div>
            </div>
          </div>
        </div>

        {/* <hr
        className={cn(
          'absolute bottom-10 left-0 w-full border-[#E5EAEF] hidden',
          (status === '2' || status === '3') && 'block',
        )}
      /> */}
      </div>
      <div className="flex justify-between items-center p-2 border-t border-[#E5EAEF]">
        {compData?.eventConfiguration && (
          <div className="flex items-end gap-7">
            <Tooltip
              content={`1st prize: ${finalEventPrice} coins`}
              placement="bottom"
              className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
            >
              <div className="flex items-center bg-black-400 rounded-md py-[3px] pl-[3px] pr-[15px] cursor-pointer">
                <WinCup />
                <span className="ml-1.5 text-[14px] leading-[16px] font-inter font-normal text-primary-200">
                  {finalEventPrice}
                </span>
              </div>
            </Tooltip>
            <Tooltip
              content={`Payout: Top ${topTeamPayOut} teams`}
              placement="bottom"
              className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
            >
              <div className="flex items-center bg-black-400 rounded-md py-[3px] pl-[3px] pr-[15px] cursor-pointer">
                <div className="flex justify-center space-x-1 items-center">
                  <Payout />
                  <span className="text-[14px] leading-[16px] font-inter font-normal text-primary-200">
                    Top
                  </span>
                </div>
                <span className="ml-1.5 text-[14px] leading-[16px] font-inter font-normal text-primary-200">
                  {topTeamPayOut}
                </span>
              </div>
            </Tooltip>
          </div>
        )}
        <div className="flex gap-x-2">
          {(compData?.status === 'finished' ||
            compData?.status === 'inprogress') && (
            <div
              className={cn(
                'h-full flex space-x-3 divide-x-[2px] divide-black-400',
              )}
            >
              <div
                className="flex items-center gap-2 justify-center cursor-pointer"
                onClick={handleLeaderboard}
              >
                <LeaderboardIcon />
                <span className="text-secondary-100 text-base">
                  Leaderboard
                </span>
              </div>
              {isCommentaryAvailable() && (
                <div
                  className="flex items-center gap-2 justify-center pl-2 cursor-pointer"
                  onClick={handleOpenCommentary}
                >
                  <CommentaryIcon />
                  <span className="text-secondary-100 text-base">
                    Commentary
                  </span>
                </div>
              )}
              {compData.SportId === 4 && (
                <div
                  className="flex items-center gap-2 justify-center pl-2 cursor-pointer"
                  onClick={handleScoreCard}
                >
                  <ScoreCardIcon />
                  <span className="text-secondary-100 text-base">
                    Scorecard
                  </span>
                </div>
              )}
            </div>
          )}

          {(compData?.status === 'upcoming' ||
            compData?.status === 'inprogress') && (
            <div className="flex justify-end items-center">
              <div className="flex items-center text-sm text-gray-600">
                {compData?.status === 'upcoming'
                  ? renderCountdown()
                  : compData?.status === 'inprogress' && renderLiveStatus()}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Competition;
