{"files.exclude": {}, "editor.tabSize": 2, "editor.detectIndentation": false, "search.exclude": {"yarn.lock": true}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "typescript.tsdk": "node_modules/typescript/lib", "eslint.format.enable": true, "[javascript][typescript][typescriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.codeActionsOnSave": ["source.addMissingImports", "source.fixAll.eslint"]}, "[json][jsonc]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}