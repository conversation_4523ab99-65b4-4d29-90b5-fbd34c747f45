'use client';

import { Column, ColumnDef } from '@tanstack/react-table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/UI/avatar';
import { cn } from '@/lib/utils';
import { PlayerStats } from '@/helpers/fetchers/stats';
import { Config } from '@/helpers/context/config';
import PlayerAvatar from '../PlayerAvatar/indext';
import SortingUpIcon from '../Icons/SortingUpIcon';
import SortingDownIcon from '../Icons/SortingDownIcon';

const renderSortHeader = (
  column: Column<PlayerStats, unknown>,
  label: string,
) => (
  <button
    className={cn(
      'text-xs flex items-center w-full space-x-1 font-bold',
      label === 'Player' ? 'justify-start' : 'justify-center',
    )}
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white text-center">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

export const SoccerSmartPlayStatsColumns: ColumnDef<PlayerStats>[] = [
  {
    accessorKey: 'rank',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, '#')}
      </div>
    ),
    cell: ({ row }) => <div className="text-xs">{row.index + 1}</div>,
    sortingFn: (rowA, rowB) => rowA.index - rowB.index,
  },
  {
    accessorKey: 'player',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'Player')}
      </div>
    ),
    cell: ({ row }) => {
      const player = row.original.player;

      return (
        <div className="text-center">
          <div className="flex space-x-2 max-w-[250px]">
            <div className="flex justify-start items-center">
              <PlayerAvatar avatarUrl={Config.baseURL! + player?.image!} />
            </div>
            <div className="flex justify-start">
              <div className="flex flex-col justify-start items-start">
                <p className="truncate ... w-[100px] text-left">
                  {player?.name}
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    },
  },

  {
    accessorKey: 'totalScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'Total Score')}
        </div>
      );
    },
    cell: ({ row }) => {
      return <div className="text-center">{row.original.totalScore}</div>;
    },
  },
  // Scoring Stats
  {
    id: 'goalScorGsf',
    accessorKey: 'scoreBreakdown.goalScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'GSF')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.goalScore}
        </div>
      );
    },
  },
  {
    id: 'goalScorGsm',
    accessorKey: 'scoreBreakdown.goalScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'GSM')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.goalScore}
        </div>
      );
    },
  },
  {
    id: 'goalScorGsd',
    accessorKey: 'scoreBreakdown.goalScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'GSD')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.goalScore}
        </div>
      );
    },
  },
  {
    id: 'goalScorGsg',
    accessorKey: 'scoreBreakdown.goalScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'GSG')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.goalScore}
        </div>
      );
    },
  },
  {
    id: 'assist',
    accessorKey: 'scoreBreakdown.assistScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'A')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.assistScore}
        </div>
      );
    },
  },
  {
    id: 'shotOnTarget',
    accessorKey: 'scoreBreakdown.shotOnTargetScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'ST')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.shotOnTargetScore}
        </div>
      );
    },
  },

  // Defence Stats
  {
    id: 'cleanSheetDefender',
    accessorKey: 'scoreBreakdown.cleanSheetScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'CSD')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.cleanSheetScore}
        </div>
      );
    },
  },
  {
    id: 'cleanSheetGoalkeeper',
    accessorKey: 'scoreBreakdown.cleanSheetScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'CSG')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.cleanSheetScore}
        </div>
      );
    },
  },
  {
    id: 'tackle',
    accessorKey: 'scoreBreakdown.tackleScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'TAC')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.tackleScore}
        </div>
      );
    },
  },
  {
    id: 'interception',
    accessorKey: 'scoreBreakdown.interceptionScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'INT')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.interceptionScore}
        </div>
      );
    },
  },
  {
    id: 'block',
    accessorKey: 'scoreBreakdown.blockScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'BS')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.blockScore}
        </div>
      );
    },
  },
  {
    id: 'save',
    accessorKey: 'scoreBreakdown.saveScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'SAV')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.saveScore}
        </div>
      );
    },
  },
  {
    id: 'penSave',
    accessorKey: 'scoreBreakdown.penSaveScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'PS')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.penSaveScore}
        </div>
      );
    },
  },

  // General Play Stats

  {
    id: 'passCompletionRate',
    accessorKey: 'scoreBreakdown.penSaveScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'PCR')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.penSaveScore}
        </div>
      );
    },
  },
  {
    id: 'keyPass',
    accessorKey: 'scoreBreakdown.keyPassScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'KP')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.keyPassScore}
        </div>
      );
    },
  },
  {
    id: 'dribble',
    accessorKey: 'scoreBreakdown.dribbleScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'DC')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.dribbleScore}
        </div>
      );
    },
  },
  {
    id: 'minutes10',
    accessorKey: 'scoreBreakdown.minutesScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'MP10')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.minutesScore}
        </div>
      );
    },
  },
  {
    id: 'minutes30',
    accessorKey: 'scoreBreakdown.minutesScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'MP30')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.minutesScore}
        </div>
      );
    },
  },

  // Penalty Stats

  {
    id: 'ownGoal',
    accessorKey: 'scoreBreakdown.ownGoalScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'OG')}
        </div>
      );
    },
    cell: ({ row }) => {
      return <div className="text-center">{'-'}</div>;
    },
  },

  {
    id: 'yellowCard',
    accessorKey: 'yellowCards',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'YC')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">{row.original.yellowCards ?? '-'}</div>
      );
    },
  },
  {
    id: 'redCard',
    accessorKey: 'redCards',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'RC')}
        </div>
      );
    },
    cell: ({ row }) => {
      return <div className="text-center">{row.original.redCards ?? '-'}</div>;
    },
  },

  {
    id: 'penaltyConceded',
    accessorKey: 'pen_committed',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'PC')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">{row.original.pen_committed ?? '-'}</div>
      );
    },
  },

  {
    id: 'penaltyMissed',
    accessorKey: 'penMissed',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'MP')}
        </div>
      );
    },
    cell: ({ row }) => {
      return <div className="text-center">{row.original.penMissed ?? '-'}</div>;
    },
  },

  // Bonus Stats

  {
    id: 'hatTrick',
    accessorKey: 'scoreBreakdown.hatTrickScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'HT')}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">
          {row.original.scoreBreakdown.hatTrickScore}
        </div>
      );
    },
  },
  {
    id: 'matchWinningGoal',
    accessorKey: 'scoreBreakdown.hatTrickScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'MG')}
        </div>
      );
    },
    cell: ({ row }) => {
      return <div className="text-center">{'-'}</div>;
    },
  },
  {
    id: 'tenTackles',
    accessorKey: 'scoreBreakdown.hatTrickScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, '10TAC')}
        </div>
      );
    },
    cell: ({ row }) => {
      return <div className="text-center">{'-'}</div>;
    },
  },
  {
    id: 'fiveSaves',
    accessorKey: 'scoreBreakdown.hatTrickScore',
    header: ({ column }) => {
      return (
        <div className="flex justify-start ml-5 items-center">
          {renderSortHeader(column, 'SSAV')}
        </div>
      );
    },
    cell: ({ row }) => {
      return <div className="text-center">{'-'}</div>;
    },
  },
];
