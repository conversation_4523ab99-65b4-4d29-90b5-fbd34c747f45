'use client';
import React, { useState } from 'react';

import MyBar<PERSON>hart from '@/components/UI/BarChart/MyBarChart';
import { generateUniqueId } from '@/lib/utils';

const SummaryDetails = () => {
  const [activeTab, setActiveTab] = useState<string>('Score');

  const summaryData = [
    { label: 'Last perPoint', value: '48' },
    { label: 'Total score', value: '476' },
    { label: 'Rounds played', value: '10' },
    { label: 'Season Rank (Rd 8)', value: '11 (8)' },
    { label: 'Average score', value: '72.1' },
    { label: '3 Game Avg', value: '47.6' },
    { label: '5 Game Avg', value: '85.2' },
    { label: 'Highest score/Lowest score', value: '125 (35)' },
    { label: 'Selections (% of teams)', value: '21,151 (42%)' },
    { label: 'Points per min ($ per point)', value: '0.4 ($8,696)' },
  ];

  const tabs = ['Score', 'Price', 'point', 'Selections'];

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  const scoreChartData = [
    { name: '1', round: 100, average: 51 },
    { name: '2', round: 100, average: 33 },
    { name: '3', round: 102, average: 51 },
    { name: '4', round: 100, average: 51 },
    { name: '5', round: 73, average: 51 },
    { name: '6', round: 120, average: 51.3 },
    { name: '7', round: 130, average: 58.2 },
    { name: '8', round: 120, average: 60.8 },
    { name: '9', round: 0, average: 59 },
  ];

  const multipleBarsConfig = [
    { dataKey: 'round', color: '#4455C7', label: 'Round' },
    { dataKey: 'average', color: '#D6D9F3', label: 'Average' },
  ];

  const priceData = [
    { name: '1', price: 8200 },
    { name: '2', price: 8200 },
    { name: '3', price: 53000 },
    { name: '4', price: 53000 },
    { name: '5', price: 53000 },
    { name: '6', price: 53000 },
    { name: '7', price: 53000 },
    { name: '8', price: 53000 },
    { name: '9', price: 53000 },
  ];

  const priceBarConfig = [
    { dataKey: 'price', color: '#4455C7', label: 'Price' },
  ];

  const perPointChartData = [
    { name: '1', '$ per point': 100, 'Average $ per point': 51 },
    { name: '2', '$ per point': 100, 'Average $ per point': 33 },
    { name: '3', '$ per point': 102, 'Average $ per point': 51 },
    { name: '4', '$ per point': 100, 'Average $ per point': 51 },
    { name: '5', '$ per point': 73, 'Average $ per point': 51 },
    { name: '6', '$ per point': 120, 'Average $ per point': 51.3 },
    { name: '7', '$ per point': 130, 'Average $ per point': 58.2 },
    { name: '8', '$ per point': 120, 'Average $ per point': 60.8 },
    { name: '9', '$ per point': '', 'Average $ per point': 59 },
  ];

  const perPointmultipleBarsConfig = [
    { dataKey: '$ per point', color: '#4455C7', label: '$ per point' },
    {
      dataKey: 'Average $ per point',
      color: '#D6D9F3',
      label: 'Average $ per point',
    },
  ];

  const chartData = [
    { name: '1', round: 100 },
    { name: '2', round: 100 },
    { name: '3', round: 102 },
    { name: '4', round: 100 },
    { name: '5', round: 73 },
    { name: '6', round: 120 },
    { name: '7', round: 130 },
    { name: '8', round: 120 },
    { name: '9', round: '' },
  ];

  const singleBarConfig = [
    { dataKey: 'round', color: '#4455C7', label: 'Round' },
  ];

  let barChartComponent = null;

  switch (activeTab) {
    case 'Score':
      barChartComponent = (
        <MyBarChart
          data={scoreChartData}
          bars={multipleBarsConfig}
          xAxisKey="name"
        />
      );
      break;
    case 'Price':
      barChartComponent = (
        <MyBarChart data={priceData} bars={priceBarConfig} xAxisKey="name" />
      );
      break;
    case 'point':
      barChartComponent = (
        <MyBarChart
          data={perPointChartData}
          bars={perPointmultipleBarsConfig}
          xAxisKey="name"
        />
      );
      break;
    case 'Selections':
      barChartComponent = (
        <MyBarChart data={chartData} bars={singleBarConfig} xAxisKey="name" />
      );
      break;
    default:
      break;
  }

  return (
    <>
      <div className="grid grid-cols-4 max-868:grid-cols-2 border border-black-300 rounded-lg bg-white mt-[9px]">
        <div className="px-3 py-[9px] border-b border-r border-gray-300">
          <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100 mb-1">
            Breakeven:
          </p>
          <h6 className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100">
            58
          </h6>
        </div>
        <div className="px-3 py-[9px] border-b border-r border-gray-300">
          <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100 mb-1">
            Projected:
          </p>
          <h6 className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100">
            60
          </h6>
        </div>
        <div className="px-3 py-[9px] border-b border-r border-gray-300">
          <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100 mb-1">
            Projected $ change:
          </p>
          <h6 className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100">
            +$3.8k
          </h6>
        </div>
        <div className="px-3 py-[9px] border-b border-gray-300">
          <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100 mb-1">
            Rank (opp. Adelaide Strikers)
          </p>
          <h6 className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100">
            2
          </h6>
        </div>
        <div className="px-3 py-[9px] border-r border-gray-300">
          <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100 mb-1">
            Avg (opp. Adelaide Strikers)
          </p>
          <h6 className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100">
            36
          </h6>
        </div>
        <div className="px-3 py-[9px] border-r border-gray-300">
          <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100 mb-1">
            Selection%:
          </p>
          <h6 className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100">
            60%
          </h6>
        </div>
        <div className="px-3 py-[9px] border-r border-gray-300">
          <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100 mb-1">
            Selected as captain:
          </p>
          <h6 className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100">
            75%
          </h6>
        </div>
        <div className="px-3 py-[9px]">
          <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100 mb-1">
            Selected as vice-captain:
          </p>
          <h6 className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100">
            20%
          </h6>
        </div>
      </div>
      <div className="mt-2.5">
        <h2 className="text-[16px] leading-[19px] font-semibold font-inter mb-[9px]">
          Season Summary
        </h2>
        <div className="grid gap-1.5 grid-cols-5 max-999:grid-cols-3 max-799:grid-cols-2">
          {summaryData?.map((item, index) => (
            <div key={generateUniqueId()} className="bg-white p-1 rounded-lg">
              <p className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100 mb-1">
                {item?.label}:
              </p>
              <p className="text-[16px] leading-[19px] font-inter font-semibold text-black-100">
                {item?.value}
              </p>
            </div>
          ))}
        </div>
      </div>
      <div className="mt-1.5 bg-white rounded-lg pr-[30px] max-799:pr-3">
        <div className="py-[9px] pl-[30px] max-799:pl-3">
          <div className="flex items-center gap-[3px] rounded-lg bg-black-300 p-[3px] w-fit">
            {tabs?.map((tab, index) => (
              <button
                key={generateUniqueId()}
                className={`px-[9px] py-[6px] cursor-pointer ${
                  activeTab === tab ? 'bg-primary-200 rounded-[6px]' : ''
                }`}
                onClick={() => handleTabClick(tab)}
              >
                <p
                  className={`text-[12px] leading-[15px] font-inter font-medium ${
                    activeTab === tab ? 'text-white' : 'text-primary-200'
                  }`}
                >
                  {tab}
                </p>
              </button>
            ))}
          </div>
        </div>
        <div>{barChartComponent}</div>
      </div>
    </>
  );
};

export default SummaryDetails;
