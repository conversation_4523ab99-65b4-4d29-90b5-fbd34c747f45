import React from 'react';

const ShareIcon = ({ width = 48, height = 48 }: { width?: number, height?: number }) => {
  return (
    <svg
      id="Share_comp_-_Mobile"
      data-name="Share comp - Mobile"
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 48 48"
    >
      <rect
        id="Rectangle_40912"
        data-name="Rectangle 40912"
        width="48"
        height="48"
        rx="8"
        fill="#fc4714"
      />
      <g
        id="Icon_feather-share-2"
        data-name="Icon feather-share-2"
        transform="translate(13.277 12.086)"
      >
        <path
          id="Path_20888"
          data-name="Path 20888"
          d="M29.649,6.574A3.574,3.574,0,1,1,26.074,3,3.574,3.574,0,0,1,29.649,6.574Z"
          transform="translate(-8.203 -3)"
          fill="none"
          stroke="#fff"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
        <path
          id="Path_20889"
          data-name="Path 20889"
          d="M11.649,17.074A3.574,3.574,0,1,1,8.074,13.5,3.574,3.574,0,0,1,11.649,17.074Z"
          transform="translate(-4.5 -5.16)"
          fill="none"
          stroke="#fff"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
        <path
          id="Path_20890"
          data-name="Path 20890"
          d="M29.649,27.574A3.574,3.574,0,1,1,26.074,24,3.574,3.574,0,0,1,29.649,27.574Z"
          transform="translate(-8.203 -7.32)"
          fill="none"
          stroke="#fff"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
        <path
          id="Path_20891"
          data-name="Path 20891"
          d="M12.885,20.265l8.137,4.742"
          transform="translate(-6.225 -6.552)"
          fill="none"
          stroke="#fff"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
        <path
          id="Path_20892"
          data-name="Path 20892"
          d="M21.01,9.765l-8.125,4.742"
          transform="translate(-6.225 -4.392)"
          fill="none"
          stroke="#fff"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </g>
    </svg>
  );
};

export default ShareIcon;
