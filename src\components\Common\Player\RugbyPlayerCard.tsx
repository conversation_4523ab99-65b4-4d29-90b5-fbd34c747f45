import { Dispatch, SetStateAction } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>ubarMenu, MenubarTrigger } from '@/components/UI/menubar';
import { AnimatePresence, motion } from 'motion/react';
import PlayerLockIcon from '@/components/UI/Icons/PlayerLockIcon';
import PlayerScore from './PlayerScore';
import PlayerAvatar from '@/components/UI/PlayerAvatar/indext';
import MobileRugbyMenuContent from './MobileMenuContent';
import PlayerCardContent from './PlayerCardContent';

import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';
import PlayerActions from '@/components/Common/PlayerActions';
import {
  RugbyLeaguePlayersByRole,
  RugbyPlayer,
} from '../../../../types/rugby-league';
import { getDefaultProfileImage } from '../../../../db/db';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCompetition } from '@/helpers/context/competitionContext';
import { CompetitionStatusProps } from '../../../../types';

interface RugbyPlayerCardProps {
  player: RugbyPlayer;
  activePlayerTab: keyof RugbyLeaguePlayersByRole;
  setActivePlayerTab: Dispatch<SetStateAction<keyof RugbyLeaguePlayersByRole>>;
  setShowPlayerTabel: Dispatch<SetStateAction<boolean>>;
  isPlayerLocked?: boolean;
  isActive?: boolean;
  isReserveType?: boolean;
  playerIndex?: number;
}

const calculateRubyScore = (
  player: RugbyPlayer,
  isPlayerLocked: boolean,
  eventStatus: CompetitionStatusProps | undefined,
): number => {
  let score = player?.scoreData?.livePoint ?? 0;

  if (isPlayerLocked) {
    return score;
  }

  if (eventStatus === 'finished') {
    score = player?.scoreData?.lastScore ?? 0;
    if (player?.isCaiptain) return score * 2;
    if (player?.isViceCaiptain) return score * 1.5;
  }

  if (eventStatus === 'upcoming') {
    score = player?.scoreData?.lastScore ?? 0;
  }

  return score;
};

const calculateSalary = (player: RugbyPlayer, addMore: string | null) => {
  if (addMore === 'true') {
    return {
      current: player?.scoreData?.playerCurrentSalary ?? 0,
      last: player?.scoreData?.playerLastSalary ?? 0,
    };
  }
  return {
    current: player?.playerValue ?? 0,
    last: player?.scoreData?.playerCurrentSalary ?? 0,
  };
};

const RugbyPlayerCard = ({
  activePlayerTab,
  player,
  setActivePlayerTab,
  setShowPlayerTabel,
  isActive,
  isPlayerLocked,
  isReserveType = false,
  playerIndex,
}: RugbyPlayerCardProps) => {
  const {
    removePlayer,
    setPlayerRoleToCaptain,
    setPlayerRoleToViceCaiptain,
    removeReservePlayer,
    activeTabPlayer,
  } = useRugbyLeagueContext();
  const { eventDetailsResponse, refetchDreamTeam } = useCompetition();

  const searchParams = useSearchParams();
  const dreamTeamId = searchParams.get('dreamTeamId');
  const add_more = searchParams.get('add_more');
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  const readOnly =
    (eventStatus === 'finished' ||
      eventDetailsResponse?.result?.dreamTeams?.length! > 0) &&
    add_more !== 'true';

  const score = calculateRubyScore(player, isPlayerLocked!, eventStatus);
  const salary = calculateSalary(player, add_more);

  const router = useRouter();

  const pathname = usePathname();

  const handleEditTeam = () => {
    const query = {
      event_id: searchParams.get('event_id'),
      sport_id: searchParams.get('sport_id'),
      tournament_id: searchParams.get('tournament_id'),
      dreamTeamId,
      competition_id: searchParams.get('competition_id'),
      add_more: 'true',
      seasonId: searchParams.get('seasonId'),
      playerId: `${player.playerId}`,
      role: activePlayerTab,
    };
    refetchDreamTeam();
    // @ts-expect-error
    router.push(`${pathname}?${new URLSearchParams(query).toString()}`);
  };

  const handleRemovePlayer = () => {
    if (isReserveType) {
      removeReservePlayer(player.playerId, playerIndex ?? 0);
    } else {
      removePlayer(player.playerId, activePlayerTab);
    }
  };

  return (
    <Menubar className="border-0 bg-transparent" asChild>
      <MenubarMenu>
        <MenubarTrigger disabled={isPlayerLocked}>
          <AnimatePresence>
            <motion.div
              className="box relative group"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
            >
              <div className="md:text-xs text-[12px] absolute top-1 left-3">
                {isPlayerLocked ? (
                  <PlayerLockIcon />
                ) : (
                  player?.scoreData?.avg?.toFixed(2) || 0
                )}
              </div>

              <PlayerScore<RugbyPlayer>
                score={score}
                isPlayerLocked={isPlayerLocked ?? false}
                player={player}
                setPlayerRoleToCaptain={setPlayerRoleToCaptain}
                setPlayerRoleToViceCaiptain={setPlayerRoleToViceCaiptain}
                activeTab={activePlayerTab}
              />

              <div className="absolute top-[-20px] left-1/2 -translate-x-1/2">
                <div className="relative flex">
                  <PlayerAvatar
                    avatarUrl={player?.image ?? getDefaultProfileImage()}
                  />
                  <MobileRugbyMenuContent<RugbyPlayer>
                    player={player}
                    dreamTeamIdentifier={dreamTeamId}
                    isAddMore={add_more}
                    handleEditTeam={handleEditTeam}
                    handleSetCaptain={() =>
                      setPlayerRoleToCaptain(player.playerId, activePlayerTab)
                    }
                    handleSetViceCaptain={() =>
                      setPlayerRoleToViceCaiptain(
                        player.playerId,
                        activePlayerTab,
                      )
                    }
                    handleRemovePlayer={handleRemovePlayer}
                    isReserveType={isReserveType}
                  />
                </div>
              </div>
              <PlayerCardContent
                player={player}
                isPlayerLocked={isPlayerLocked ?? false}
                playerCurrentSalary={salary.current}
                playerLastSalary={salary.last}
              />
              {eventDetailsResponse?.result?.eventDetails?.status ===
                'upcoming' && (
                  <PlayerActions
                    showSubstituteButton={!!dreamTeamId && add_more !== 'true'}
                    showRemoveButton={
                      !eventDetailsResponse?.result?.dreamTeams ||
                      add_more === 'true'
                    }
                    dreamTeamId={dreamTeamId}
                    addMore={add_more === 'true'}
                    onSubstitute={handleEditTeam}
                    onSetCaptain={() =>
                      setPlayerRoleToCaptain(player.playerId, activePlayerTab)
                    }
                    onSetViceCaptain={() =>
                      setPlayerRoleToViceCaiptain(
                        player.playerId,
                        activePlayerTab,
                      )
                    }
                    onRemove={handleRemovePlayer}
                    isReserveType={isReserveType}
                    add_more={add_more === 'true'}
                  />
                )}
            </motion.div>
          </AnimatePresence>
        </MenubarTrigger>
      </MenubarMenu>
    </Menubar>
  );
};

export default RugbyPlayerCard;
