'use client';
// components/PlayerValueChange.tsx

import DownPriceArrow from '@/assets/images/icons/downPriceArrow.svg';
import UpPriceArrow from '@/assets/images/icons/upPriceArrow.svg';
import { calculateDifferenceInK, cn } from '@/lib/utils';

interface PlayerValueChangeProps {
  playerLastSalary: number;
  playerCurrentSalary: number;
  formatToCustomStyle: (value: number) => string;
  showDifference?: boolean;
  showCalculatedDifference?: boolean;
}

const PlayerValueChange: React.FC<PlayerValueChangeProps> = ({
  playerCurrentSalary,
  playerLastSalary,
  formatToCustomStyle,
  showDifference,
  showCalculatedDifference,
}) => {
  const difference = playerCurrentSalary - playerLastSalary;
  const isPositive = difference > 0;
  const displayValue = showDifference ? difference : playerCurrentSalary;
  const renderUpDown = isPositive ? <UpPriceArrow /> : <DownPriceArrow />;
  return (
    <div className="flex items-center gap-1 text-[11.42px] leading-[14px] font-inter font-normal text-black-100">
      {difference !== 0 && renderUpDown}
      <span>
        {'$'}
        {formatToCustomStyle(displayValue)}
      </span>
      {showCalculatedDifference &&
        Number(playerCurrentSalary - playerLastSalary) !== 0 && (
          <span
            className={cn(
              'text-mute',
              Number(playerCurrentSalary - playerLastSalary) > 0
                ? 'text-[#1C9A6C]'
                : 'text-[#D84727]',
            )}
          >
            {calculateDifferenceInK(playerCurrentSalary, playerLastSalary)}
          </span>
        )}
    </div>
  );
};

export default PlayerValueChange;
