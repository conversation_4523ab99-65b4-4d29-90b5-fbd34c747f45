'use client';
import { Tooltip } from '@material-tailwind/react';
import moment from 'moment';
import Image, { StaticImageData } from 'next/image';
import Countdown from 'react-countdown';

import DefaultTeam from '@/assets/images/icons/defaultTeam.png';
import Payout from '@/assets/images/icons/payout.svg';
import WinArrow from '@/assets/images/icons/winArrow.svg';
import WinCup from '@/assets/images/icons/wincup.svg';
import Coins from '@/assets/images/settings/smartBCoins.png';

import { EventObject } from '.';
interface CompetitionMobileViewProps {
  compData: EventObject;
  actionButton: React.ReactNode;
  finalEventPrice: number;
  topTeamPayOut: number;
  renderer: (props: any) => JSX.Element;
  homeTeamIcon: string | null;
  awayTeamIcon: string | null;
  fetchCricketScore: (scoreboard: any, team: string) => JSX.Element;
  otherSportScore: (scoreboard: any, team: string) => JSX.Element;
  extraActions?: React.ReactNode;
}

const TeamInfo: React.FC<{
  teamName: string;
  teamIcon: any;
  compData: any;
  winnerCode: number | string;
  teamType: 'hometeam' | 'awayteam';
  fetchScore: (
    scoreboard: any,
    teamType: 'hometeam' | 'awayteam',
  ) => JSX.Element;
  otherSportScore: (
    scoreboard: any,
    teamType: 'hometeam' | 'awayteam',
  ) => JSX.Element;
}> = ({
  teamName,
  teamIcon,
  compData,
  winnerCode,
  teamType,
  fetchScore,
  otherSportScore,
}) => {
  const otherSportScoreData =
    compData?.SportId === 12 ? compData?.RLScores : compData?.ARScores;
  const score =
    compData?.SportId === 4
      ? fetchScore(compData?.ScoreBoard, teamType)
      : otherSportScore(otherSportScoreData, teamType);
  const isWinner = winnerCode === (teamType === 'hometeam' ? 1 : 2);

  type ImageSource = string | StaticImageData;

  const getImageSrc = (image: ImageSource) =>
    typeof image === 'object' ? image.src : image;

  return (
    <div className="flex items-center mb-1.5">
      <div className="w-[30px] h-[30px] rounded-full mr-2">
        <Image
          src={teamIcon}
          alt="icon"
          width={30}
          height={30}
          onError={(e) => (e.currentTarget.src = getImageSrc(DefaultTeam))}
          unoptimized={true}
        />
      </div>
      <div className="flex items-center gap-3">
        <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100">
          {teamName}
        </span>
        {score && (
          <div className="flex items-center gap-2">
            <span
              className={isWinner ? 'text-secondary-100' : 'text-black-100'}
            >
              {score}
            </span>
            {isWinner && <WinArrow />}
          </div>
        )}
      </div>
    </div>
  );
};

const PrizeAndEntry: React.FC<{
  label: string;
  value: any;
  icon?: any;
}> = ({ label, value, icon }) => {
  return (
    <div className="w-[33.33%] flex items-center justify-center flex-col pt-[9px] pb-3 border-r border-black-300">
      <div className="flex items-center">
        <span className="text-[16px] max-799:text-[11.42px] leading-[19px] max-799:leading-[14px] font-inter font-normal text-black-700 mb-[2px]">
          {label}
        </span>
      </div>
      <div className="flex items-center">
        {icon && <Image src={icon} alt="icon" unoptimized={true} />}
        <span className="text-[22.4px] max-799:text-[18px] leading-[27px] max-799:leading-[21px] font-inter font-semibold text-secondary-100 ml-1">
          {value || 0}
        </span>
      </div>
    </div>
  );
};

const CompetitionMobileView: React.FC<CompetitionMobileViewProps> = ({
  compData,
  actionButton,
  finalEventPrice,
  topTeamPayOut,
  renderer,
  homeTeamIcon,
  awayTeamIcon,
  fetchCricketScore,
  otherSportScore,
  extraActions,
}) => {
  const isFinished = compData?.status === 'finished';
  const isUpcoming = compData?.status === 'upcoming';
  const isInProgress = compData?.status === 'inprogress';

  const renderLiveScoreSection = () => {
    if (!compData?.liveScore) return null;

    return (
      <div className="w-[33.33%] flex items-center justify-center flex-col pt-[9px] pb-3">
        <div className="flex items-center">
          <span className="text-[16px] max-799:text-[11.42px] leading-[19px] max-799:leading-[14px] font-inter font-normal text-black-700 mb-[2px]">
            Live Score
          </span>
        </div>
        <span className="text-[22.4px] max-799:text-[18px] leading-[27px] max-799:leading-[21px] font-inter font-semibold text-secondary-100 ml-1">
          {compData?.liveScore}
        </span>
      </div>
    );
  };

  const renderCurrentRankSection = () => {
    if (!compData?.currentRank) return null;

    return (
      <div className="w-[33.33%] flex items-center justify-center flex-col pt-[9px] pb-3">
        <div className="flex items-center">
          <span className="text-[16px] max-799:text-[11.42px] leading-[19px] max-799:leading-[14px] font-inter font-normal text-black-700 mb-[2px]">
            Current Rank
          </span>
        </div>
        <span className="text-[22.4px] max-799:text-[18px] leading-[27px] max-799:leading-[21px] font-inter font-semibold text-secondary-100 ml-1">
          {compData?.currentRank}/{compData?.name}
        </span>
      </div>
    );
  };

  return (
    <>
      <div className="flex justify-between items-center pb-[9px] border-b border-black-300 max-799:px-3">
        <div>
          <TeamInfo
            teamName={compData?.homeTeam?.name || ''}
            teamIcon={homeTeamIcon ?? DefaultTeam}
            compData={compData}
            winnerCode={compData?.winnerCode ?? 0}
            teamType="hometeam"
            fetchScore={fetchCricketScore}
            otherSportScore={otherSportScore}
          />

          <TeamInfo
            teamName={compData?.awayTeam?.name || ''}
            teamIcon={awayTeamIcon ?? DefaultTeam}
            compData={compData}
            winnerCode={compData?.winnerCode ?? 0}
            teamType="awayteam"
            fetchScore={fetchCricketScore}
            otherSportScore={otherSportScore}
          />
        </div>
        <div className="flex items-center gap-2 mt-3 pb-2 flex-wrap w-full justify-end">
          {actionButton}
          {compData.status === 'inprogress' && extraActions}
        </div>
      </div>

      {(isUpcoming || isInProgress || isFinished) && (
        <>
          {compData?.eventConfiguration && (
            <div className="flex items-center justify-between gap-3 w-full">
              {isFinished ? (
                <>
                  <PrizeAndEntry
                    label="Rank"
                    value={
                      compData?.currentRank +
                      (compData?.name ? ` (${compData?.name})` : '')
                    }
                  />
                  <PrizeAndEntry
                    label="Winnings"
                    value={compData?.winningPrice}
                    icon={Coins}
                  />
                </>
              ) : compData?.isDreamTeamExist &&
                compData?.status === 'inprogress' ? (
                <>
                  <PrizeAndEntry
                    label={
                      compData?.eventConfiguration?.eventType === 'free'
                        ? 'Prize'
                        : 'Prize pool'
                    }
                    value={compData?.eventConfiguration?.prizePool}
                    icon={Coins}
                  />
                  {compData?.currentRank !== 0 && (
                    <PrizeAndEntry
                      label="Rank"
                      value={
                        compData?.currentRank +
                        (compData?.name ? ` (${compData?.name})` : '')
                      }
                    />
                  )}
                  {compData?.liveScore && renderLiveScoreSection()}
                </>
              ) : (
                <>
                  <PrizeAndEntry
                    label={
                      compData?.eventConfiguration?.eventType === 'free'
                        ? 'Prize'
                        : 'Prize pool'
                    }
                    value={compData?.eventConfiguration?.prizePool}
                    icon={Coins}
                  />
                  <PrizeAndEntry
                    label="Entry coins"
                    value={
                      compData?.eventConfiguration?.eventType === 'paid'
                        ? compData?.eventConfiguration?.entryCoin
                        : 'Free'
                    }
                  />
                  <div className="w-[33.33%] flex items-center justify-center flex-col pt-[9px] pb-3">
                    <div className="flex items-center">
                      <span className="text-[16px] max-799:text-[11.42px] leading-[19px] max-799:leading-[14px] font-inter font-normal text-black-700 mb-[2px]">
                        Entries
                      </span>
                    </div>
                    <span className="text-[22.4px] max-799:text-[18px] leading-[27px] max-799:leading-[21px] font-inter font-semibold text-secondary-100 ml-1">
                      {compData?.userEntry}
                    </span>
                  </div>
                </>
              )}
            </div>
          )}

          {compData?.status !== 'finished' && (
            <div className="flex justify-between items-center bg-black-400 py-1.5 rounded-bl-lg rounded-br-lg max-799:px-3">
              <div className="flex items-center gap-7">
                <Tooltip
                  content={`1st prize: ${finalEventPrice} coins`}
                  placement="bottom"
                  className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
                >
                  <div className="flex items-center pl-[3px] pr-[15px] cursor-pointer">
                    <WinCup />
                    <span className="ml-1.5 text-[11.42px] leading-[14px] font-inter font-normal text-primary-200">
                      {finalEventPrice}
                    </span>
                  </div>
                </Tooltip>
                <Tooltip
                  content={`Payout: Top ${topTeamPayOut} teams`}
                  placement="bottom"
                  className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
                >
                  <div className="flex items-center pl-[3px] pr-[15px] cursor-pointer">
                    <Payout />
                    <span className="ml-1.5 text-[11.42px] leading-[14px] font-inter font-normal text-primary-200">
                      Top {topTeamPayOut}
                    </span>
                  </div>
                </Tooltip>
              </div>

              <div className="flex items-center text-sm text-gray-600">
                {isUpcoming ? (
                  <span className="text-[11.42px] leading-[14px] font-inter font-semibold text-black-100">
                    <span className="text-orange-400">
                      <Countdown
                        date={moment.utc(compData?.startTime).local().toDate()}
                        renderer={renderer}
                      />
                    </span>
                  </span>
                ) : (
                  <span className="text-[11.42px] leading-[14px] font-inter font-semibold text-white bg-negative-300 uppercase px-[11px] py-1 rounded-[3px]">
                    Live
                  </span>
                )}
              </div>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default CompetitionMobileView;
