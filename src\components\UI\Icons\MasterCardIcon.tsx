'use client';

const MasterCardIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="30"
      height="20"
      viewBox="0 0 30 20"
    >
      <defs>
        <clipPath id="clip-path">
          <rect
            id="Rectangle_9129"
            data-name="Rectangle 9129"
            width="22.802"
            height="14.092"
            fill="none"
          />
        </clipPath>
      </defs>
      <g
        id="Group_32659"
        data-name="Group 32659"
        transform="translate(-0.007 0)"
      >
        <g
          id="Rectangle_9130"
          data-name="Rectangle 9130"
          transform="translate(0.007 0)"
          fill="#fff"
          stroke="#c3c3c3"
          strokeWidth="0.2"
        >
          <rect width="30" height="20" rx="1" stroke="none" />
          <rect
            x="0.1"
            y="0.1"
            width="29.8"
            height="19.8"
            rx="0.9"
            fill="none"
          />
        </g>
        <g
          id="Group_32653"
          data-name="Group 32653"
          transform="translate(4.522 2.954)"
        >
          <g
            id="Group_32652"
            data-name="Group 32652"
            clipPath="url(#clip-path)"
          >
            <path
              id="Path_29450"
              data-name="Path 29450"
              d="M286.329,54.152a7.065,7.065,0,0,0-2.681-5.539h-.02a7.062,7.062,0,0,0,0,11.078h.02a7.006,7.006,0,0,0,2.681-5.539"
              transform="translate(-272.237 -47.106)"
              fill="#ff5f00"
            />
            <path
              id="Path_29451"
              data-name="Path 29451"
              d="M8.71,7.046a7.065,7.065,0,0,1,2.681-5.539A7.043,7.043,0,1,0,7.046,14.092a7,7,0,0,0,4.345-1.507h0A7.035,7.035,0,0,1,8.71,7.046"
              transform="translate(0)"
              fill="#eb001b"
            />
            <path
              id="Path_29452"
              data-name="Path 29452"
              d="M372.416,0a7,7,0,0,0-4.345,1.507,7.062,7.062,0,0,1,0,11.078A7.043,7.043,0,1,0,372.416,0"
              transform="translate(-356.66 0.001)"
              fill="#f79e1b"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};

export default MasterCardIcon;
