import React from 'react';
import { Users, Check } from 'lucide-react';
import availableCoins from "../../../../public/images/availableCoins.png"
import Image from 'next/image';
import RefferIcon from '@/components/Icons/Share/RefferIcon';
import DoneAction from '@/components/Icons/DoneAction';

interface StatsSectionProps {
  totalReferralCoins: number;
  totalReferralTracked: number;
  totalReferralCompleted: number;
}

const StatsSection = ({ totalReferralCoins, totalReferralTracked, totalReferralCompleted }: StatsSectionProps) => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-[21px]">
    <div className="border border-gray-100 rounded-xl p-4 flex items-center">
      <div className="mr-3">
        {/* <CoinStack /> */}
        <Image src={availableCoins} alt="Coin Stack" />
      </div>
      <div>
        <h2 className="text-2xl md:text-3xl">{totalReferralCoins}</h2>
        <p className="text-gray-600 text-sm">Bonus Coins Balance</p>
      </div>
    </div>
    <div className="border border-gray-100 rounded-xl p-4 flex items-center">
      <div className="mr-3 text-yellow-500">
        <RefferIcon />
      </div>
      <div>
        <h2 className="text-2xl md:text-3xl">{totalReferralTracked}</h2>
        <p className="text-gray-600 text-sm">Total Referred</p>
      </div>
    </div>
    <div className="border border-gray-100 rounded-xl p-4 flex items-center">
      <div className="mr-3 text-green-500">
        <DoneAction />
      </div>
      <div>
        <h2 className="text-2xl md:text-3xl">{totalReferralCompleted}</h2>
        <p className="text-gray-600 text-sm">Completed</p>
      </div>
    </div>
  </div>
);

export default StatsSection; 