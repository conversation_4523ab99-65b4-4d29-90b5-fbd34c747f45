'use client';

import Image from 'next/image';

import { Card, CardContent } from '@/components/UI/card';
import BonusIcon from '@/components/UI/Icons/BonusIcon';
import PrizePooIcon from '@/components/UI/Icons/PrizePooIcon';
import SkillBasedIcon from '@/components/UI/Icons/SkillBasedIcon';

import TableScoring from '../Scoring/tableScoring';

export default function GuidePage() {
  const CaptainViceCaptainData = {
    header: ['Player Type', 'Number'],
    body: [
      {
        'Player Type': 'Wicket Keeper (WKP)',
        Number: 1,
      },
      {
        'Player Type': 'Batsman (BAT)',
        Number: 5,
      },
      {
        'Player Type': 'Bowler (BOW)',
        Number: 5,
      },
    ],
  };
  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="">
        <div className="container mx-auto px-4 py-4 space-y-[18px]">
          <h1 className="text-[22.4px] font-bold">
            How to Play & Win with SmartB SmartPlay Fantasy Sports Cricket
            Competitions
          </h1>
          <p className="text-[16px]">
            Welcome to SmartB SmartPlay Fantasy Sports, where your cricket
            knowledge can turn into real winnings! Follow this step-by-step
            guide to get started:
          </p>
        </div>
      </header>

      {/* Main Content */}
      <main className="container  mt-[33px]">
        <div className="max-w-full mx-4 space-y-12">
          {/* Steps */}
          <div className="space-y-12">
            {/* Step 1 */}
            <section className="space-y-4">
              <div className="flex flex-col items-start space-y-[18px] gap-2">
                <h2 className="text-xl font-semibold text-[22.4px]">
                  Step 1: Sign Up
                </h2>
                <p className="text-[16px]">
                  Create your free account on the SmartB platform.{' '}
                </p>
              </div>
              <div className="rounded-lg mt-[18px]">
                <Image
                  src="/fantasy/images/rules/desktopSingup.jpg"
                  alt="Sign up screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg hidden md:block"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />
                <Image
                  src="/fantasy/images/rules/mobileSingup.jpg"
                  alt="Sign up screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg block md:hidden"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />
              </div>
            </section>

            {/* Step 2 */}
            <section className="space-y-2">
              <div
                className="flex flex-col items-start
               gap-2"
              >
                <h2 className="text-xl font-semibold">
                  Step 2: Subscribe or Purchase SmartCoins
                </h2>
                <p className="text-[16px] font-bold">How to Buy Coins</p>
                <p className="text-[16px]">
                  1. Navigate to the &apos;Buy Coins&apos; tab within the
                  'Settings' menu
                </p>
              </div>
              <div className="rounded-lg mt-[18px]">
                <Image
                  src="/fantasy/images/rules/subscribeDesktop.jpg"
                  alt="Purchase coins screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg hidden md:block"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />
                <Image
                  src="/fantasy/images/rules/mobileCoin.jpg"
                  alt="Purchase coins screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg block md:hidden"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />

                <ol className="list-decimal pl-6 space-y-2 my-[18px]" start={2}>
                  <li>Choose a coin package that suits you.</li>
                  <li>
                    You can either buy enough coins for a one off game, or
                    Subscribe to a weekly plan for more entries.
                    <ul className="list-disc gap-y-3 flex flex-col mt-[12px] ml-5">
                      <li>
                        For $15 you can buy 100 coins and play in one game with
                        one team
                      </li>
                      <li>
                        For $30 you can buy 300 coins and enter 3 teams in any
                        of the games
                      </li>
                      <li>
                        For $60 you can buy 700 coins and enter 7 teams in any
                        of the games
                      </li>
                      <li>
                        For $10/week you&apos;ll automatically receive 100 coins
                        in your wallet, allowing you to enter a game each week.
                      </li>
                      <li>
                        For $25/week you&apos;ll automatically receive 300 coins
                        in your wallet, allowing you to enter three teams each
                        week.
                      </li>
                      <li>
                        For $50/week you&apos;ll automatically receive 700 coins
                        in your wallet, allowing you to enter 7 teams each week.
                      </li>
                    </ul>
                  </li>
                </ol>

                <Image
                  src="/fantasy/images/rules/membershiDesktop.jpg"
                  alt="Purchase coins screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg hidden md:block"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />
                <Image
                  src="/fantasy/images/rules/mobileMembership.jpg"
                  alt="Purchase coins screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg block md:hidden"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />
                <p className="text-[16px] mt-[18px]">
                  4. Complete your purchase via secure payment options.{' '}
                </p>
              </div>
            </section>

            {/* Step 3 */}
            <section className="mt-[18px]">
              <div className="flex flex-col items-start">
                <h2 className="text-xl font-semibold mb-[18px]">
                  Step 3: Join a Match
                </h2>

                <p>
                  1.{' '}
                  <span className="font-bold text-[16px] mt-[18px]">
                    Select a Real Match:
                  </span>{' '}
                  Browse available matches and choose one that interests you.{' '}
                </p>
              </div>
              <div className="rounded-lg p-4">
                <Image
                  src="/fantasy/images/rules/joinMatchDesktop.jpg"
                  alt="Purchase coins screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg hidden md:block"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />
                <Image
                  src="/fantasy/images/rules/mobileJoinMatch.jpg"
                  alt="Purchase coins screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg block md:hidden"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />
              </div>

              <ol className="list-decimal text-[16px] pl-6 space-y-2" start={2}>
                <li>
                  <span className="font-bold">Use Coins to Enter:</span> Each
                  match has an entry fee of 100 coins paid with your SmartB
                  Coins.
                </li>
                <li>
                  {' '}
                  <span className="font-bold">
                    Enter as many times as you like:
                  </span>{' '}
                  You can enter as many teams as you like in a single match, or
                  enter teams into different matches. Each entry costs 100
                  SmartCoins
                </li>
                <li>
                  The entry fees from all players in each match are pooled
                  together to create the prize pool for that match.
                </li>
              </ol>
            </section>

            {/* Step 4 */}
            <section className="my-[18px]">
              <div className="flex flex-col items-start gap-2">
                <h2 className="text-xl font-semibold">
                  Step 4: Build Your Fantasy Team
                </h2>
                <p className="text-[16px]">
                  1. Draft a team from the players participating in the
                  real-world match. When playing cricket you’ll need to pick 11
                  players from the two real world teams that are playing. You’ll
                  need to pick
                </p>

                <div className="md:w-[950px] md:p-4 p-0 w-full">
                  <TableScoring
                    headerData={CaptainViceCaptainData?.header}
                    bodyData={CaptainViceCaptainData?.body}
                  />
                </div>
              </div>
              <div className="rounded-lg p-4">
                <Image
                  src="/fantasy/images/rules/teamBuildDesktop.jpg"
                  alt="Purchase coins screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg hidden md:block"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />
                <Image
                  src="/fantasy/images/rules/mobileTeam.jpg"
                  alt="Purchase coins screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg block md:hidden"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />
              </div>

              <ol className="list-decimal text-[16px] pl-6 space-y-2" start={2}>
                <li>
                  Stay within the $2,000,000 budget and choose strategically to
                  maximize your score.
                </li>
                <li>
                  Your players earn points based on their real-world performance
                  during the match.
                </li>
                <li>
                  You aren&apos;t able to edit your team after you've submitted,
                  so make sure you choose wisely. You can also enter as many
                  teams as you like
                </li>
              </ol>
            </section>

            {/* Step 5 */}
            <section className="mt-[33px]">
              <div className="flex items-start flex-col gap-4">
                <h2 className="text-xl font-semibold">Step 5: Compete & Win</h2>
                <p className="text-[16px]">
                  1. Watch your fantasy team rack up points as the real-world
                  match progresses.{' '}
                </p>
              </div>
              <div className="rounded-lg  p-4">
                <Image
                  src="/fantasy/images/rules/winDesktop.jpg"
                  alt="Purchase coins screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg hidden md:block"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />

                <Image
                  src="/fantasy/images/rules/mobileWin.jpg"
                  alt="Purchase coins screenshot"
                  width={925}
                  height={489}
                  className="rounded-lg block md:hidden"
                  unoptimized={true}
                  style={{ mixBlendMode: 'darken' }}
                />
                <ol className="list-decimal pl-6 space-y-2 mt-3" start={2}>
                  <li>
                    Finish in the{' '}
                    <span className="font-bold">Top 20 Percent Standings</span>{' '}
                    to win a share of the prize pool:
                    <ul className="list-disc gap-y-3 flex flex-col mt-[12px] ml-5">
                      <li>Higher rankings earn bigger prizes.</li>
                      <li>
                        Your winnings are paid out in SmartCoins, which you
                        can use for future games or withdraw for real world
                        cash.
                      </li>
                      <li>
                        The play with the highest score wins, if two players
                        have the same score, the player who entered and
                        confirmed their team earliest will win.
                      </li>
                    </ul>
                  </li>
                </ol>
              </div>
            </section>

            {/* Step 6 */}
            <section className="mt-[33px]">
              <div className="flex items-center gap-2">
                <h2 className="text-xl font-semibold">
                  Step 6: Daily Lucky Draw{' '}
                </h2>
              </div>
              <div className="rounded-lg">
                <p className="text-[16px] mt-[18px]">
                  1. <span className="font-bold">Bonus Opportunity:</span> Earn
                  entries into the daily lucky draw just by participating in
                  matches. Each game night we’ll give away a big prize and a
                  little prize to 2 lucky entrants.
                </p>

                <ol className="list-decimal mx-4 space-y-2 mt-3" start={2}>
                  <li>
                    <span className="font-bold">How It Works</span>
                    <ul className="list-disc gap-y-3 flex flex-col mt-[12px] ml-5">
                      <li>
                        The number of entries you get depends on your
                        subscription tier.{' '}
                        <ul
                          className="pl-5 gap-y-3 flex flex-col mt-[12px] ml-5"
                          style={{ listStyle: 'circle' }}
                        >
                          <li>
                            Bronze Subscriptions – Receive 1 entry each week if
                            you have participated in a match that week.
                          </li>
                          <li>
                            Silver Subscriptions – Receive 2 entries each week
                            if you have participated in a match that week.
                          </li>
                          <li>
                            Gold Subscriptions – Receive 3 entries each week if
                            you have participated in a match that week.
                          </li>
                        </ul>
                      </li>

                      <li>
                        Higher-tier subscribers enjoy more entries and better
                        chances to win exclusive prizes.
                      </li>
                    </ul>
                  </li>
                </ol>
              </div>
            </section>
          </div>

          {/* Why Choose Section */}
          <section className="space-y-6">
            <h2 className="text-2xl font-bold ">
              Why Choose SmartPlay Daily Fantasy Sports?
            </h2>
            <div className="grid md:grid-cols-3 gap-6">
              <Card className="bg-gradient-to-b from-[#494949] to-[#191919] text-white">
                <CardContent className="pt-6">
                  <div className="space-y-2">
                    <div className="flex justify-start">
                      <SkillBasedIcon />
                    </div>
                    <h3 className="text-xl font-semibold ">
                      Skill-Based Winnings
                    </h3>
                    <p className="text-muted-foreground">
                      Use your cricket knowledge to outsmart opponents and climb
                      the leaderboard.
                    </p>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-gradient-to-b from-[#4455C7] to-[#181C3D] text-white">
                <CardContent className="pt-6">
                  <div className="space-y-2">
                    <div className="flex justify-start">
                      <PrizePooIcon />
                    </div>
                    <h3 className="text-xl font-semibold ">
                      Dynamic Prize Pools
                    </h3>
                    <p className=" text-muted-foreground">
                      The more players that join, the bigger the prize pool.
                    </p>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-gradient-to-b from-[#003764] to-[#090B0D] text-white">
                <CardContent className="pt-6">
                  <div className="space-y-2">
                    <div className="flex justify-start">
                      <BonusIcon />
                    </div>
                    <h3 className="text-xl font-semibold ">Exciting Bonuses</h3>
                    <p className=" text-muted-foreground">
                      Earn daily rewards and boost your winnings with lucky
                      draws.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <p>
              Get started now and experience the thrill of Get started now and
              experience the thrill of SmartPlay - SmartB&apos;s Daily Fantasy
              Sports Platform. SmartPlay is the ultimate way to score when you
              love to watch and play cricket.
            </p>

            <p>
              Support email:{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-[#1A73E8] underline"
              >
                <EMAIL>
              </a>
            </p>
          </section>
        </div>
      </main>
    </div>
  );
}
