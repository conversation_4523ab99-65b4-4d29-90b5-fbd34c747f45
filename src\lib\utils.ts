import { type ClassValue, clsx } from 'clsx';
import _ from 'lodash';
import moment from 'moment';
import { twMerge } from 'tailwind-merge';

import { setApiMessage } from '@/helpers/commonFunctions';
import type {
  PlayersByRole,
  ReservePlayerPayload,
} from '@/helpers/context/createTeamContext';

import type {
  AFLPlayerData,
  Event,
  FavoriteTeam,
  FootballPlayer,
  Player,
  PlayerData,
  PlayerRoleType,
  PlayersByRoleType,
  RugbyPlayerData,
} from '../../types/competitions';
import type { TeamComposition } from '../../types/membership';
import type {
  DreamTeamPlayer,
  FantasyTeamResponse,
  PlayerFavouriteType,
  PlayersByRoleLimit,
  TeamSelectionData,
} from './../../types/competitions/index';
import {
  AFLDreamTeamPlayer,
  AFLFavoriteTeam,
  AFLPlayerFavouriteType,
  AFLTeamComposition,
  AFLTeamSelectionData,
  footballPlayersByRole,
  FootballPlayersByRoleLimit,
  FootBallRole,
} from '../../types/football';
import {
  RugbyDreamTeamPlayer,
  RugbyFavoriteTeam,
  RugbyLeaguePlayersByRole,
  RugbyLeaguePlayersByRoleLimit,
  RugbyPlayer,
  RugbyPlayerFavouriteType,
  RugbyTeamComposition,
  RugbyTeamSelectionData,
} from '../../types/rugby-league';
import { SportsType } from '../../types';
import { ALL_AFL_ROLES, ALL_RUGBY_ROLES } from '@/helpers/constants/index';
import {
  CommentaryFormat,
  CommentaryItem,
  CommentaryItemAPI,
  CricketCommentary,
  SportEventCricketCommentaryEvent,
} from '../../types/commentry';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const handleImageError = (
  e: React.SyntheticEvent<HTMLImageElement>,
  src: string,
) => {
  e.currentTarget.onerror = null; // Prevent looping
  e.currentTarget.src = src; // Set the default image
};

export const parseJSON = (value: string): string => {
  try {
    const parsed = JSON.parse(value);
    if (Array.isArray(parsed)) {
      return JSON.stringify(parsed); // Convert array to string
    }
    if (parsed === null) {
      return ''; // Return empty string for null
    }
    return String(parsed); // Convert other types to string
  } catch (e) {
    console.error(e);
    return ''; // Return empty string if parsing fails
  }
};

export const transformEvent = (event: Event) => {
  return {
    id: event.id,
    leagueName: event.CricketTournament?.name || 'N/A',
    startTime: event.startTime,
    homeTeam: {
      name: event.homeTeam?.name,
      flag: event.homeTeam?.flag,
    },
    awayTeam: {
      name: event.awayTeam?.name,
      flag: event.awayTeam?.flag,
    },
    prizePool: event.eventConfiguration?.prizePool?.[0]?.coins,
    entryCoins: event.eventConfiguration?.entryCoin,
    entries: event.eventConfiguration?.minUserEntry,
    status: event.status || 'upcoming',
  };
};

// Hook to transfer token from localStorage to cookie
// Place this in a component that runs on initial load
export const transferToken = (token: string) => {
  if (typeof window !== 'undefined') {
    if (token) {
      // Set the cookie
      fetch('/fantasy/api/auth/transfer-token', {
        method: 'POST',
        body: JSON.stringify({ token }),
        headers: {
          'Content-Type': 'application/json',
        },
      });
      // Optionally clear localStorage
      // localStorage.removeItem('auth_token');
    }
  }
};

export function safeJsonParse<T>(
  jsonString: string | undefined,
  defaultValue: T,
): T {
  try {
    if (!jsonString) throw new Error('Invalid input: undefined or empty');
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.error('Error parsing JSON:', error);
    return defaultValue;
  }
}

export interface TimeDifference {
  readonly completed: boolean;
  readonly days: number;
  readonly hours: number;
  readonly minutes: number;
  readonly seconds: number;
}

/**
 * Calculate time difference from the given startTime to now.
 * @param startTime - ISO string representing the start time.
 * @returns An object containing days, hours, minutes, and seconds.
 */
export function getTimeDifference(startTime: string): TimeDifference {
  const startMoment = moment(startTime);
  const currentMoment = moment();
  const duration = moment.duration(currentMoment.diff(startMoment));

  return {
    days: Math.floor(duration.asDays()),
    hours: duration.hours(),
    minutes: duration.minutes(),
    seconds: duration.seconds(),
    completed: duration.asMilliseconds() <= 0, // This checks if the duration has finished
  };
}

export function formatDate(dateString: string): string {
  return moment(dateString).format('ddd DD/MM/YYYY | hh:mma');
}
let playerType: keyof PlayersByRoleType;

export const getPlayerRoleType = (role: PlayerRoleType) => {
  switch (role) {
    case 'Batter':
      playerType = 'BAT';
      break;
    case 'Batting Allrounder':
      playerType = 'ALL';
      break;
    case 'Bowling Allrounder':
      playerType = 'ALL';
      break;
    case 'Bowler':
      playerType = 'BOW';
      break;
    case 'WK-Batter':
      playerType = 'WKP';
      break;
  }
  return playerType;
};

type InputData = {
  WKP: Player[];
  BAT: Player[];
  BOW: Player[];
  ALL: Player[];
};

export const generateTeamPayload = (
  input: InputData,
  captain: Player | undefined,
  viceCaptain: Player | undefined,
  reservePlayers: ReservePlayerPayload[],
): PlayerData => {
  // Initialize an object to hold the team data

  const payload: PlayerData = {
    wicketKeeper: input.WKP.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    bowler: input.BOW.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    batsman: input.BAT.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    allRounder: input.ALL.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    reserve: reservePlayers,
    captain: [],
    viceCaptain: [],
  };

  // Only add the captainId if it's not null
  if (captain?.playerId !== null) {
    payload.captain.push({
      playerId: captain?.playerId!,
      playerValue: captain?.scoreData?.playerCurrentSalary!,
      role: captain?.role || '',
    });
  }

  // Only add the viceCaptainId if it's not null
  if (viceCaptain?.playerId !== null) {
    payload.viceCaptain.push({
      playerId: viceCaptain?.playerId!,
      playerValue: viceCaptain?.scoreData?.playerCurrentSalary!,
      role: viceCaptain?.role || '',
    });
  }

  return payload;
};

type RugbyInputData = {
  BAC: RugbyPlayer[];
  HAL: RugbyPlayer[];
  BR: RugbyPlayer[];
  FRF: RugbyPlayer[];
  IC: RugbyPlayer[];
};

type AFLInputData = {
  BL: FootballPlayer[];
  HBL: FootballPlayer[];
  MID: FootballPlayer[];
  HFL: FootballPlayer[];
  FL: FootballPlayer[];
  FOL: FootballPlayer[];
  IC: FootballPlayer[];
};

export const generateRugbyTeamPayload = (
  input: RugbyInputData,
  captain: RugbyPlayer | undefined,
  viceCaptain: RugbyPlayer | undefined,
  reservePlayers: ReservePlayerPayload[],
): RugbyPlayerData => {
  // Initialize an object to hold the team data

  const payload: RugbyPlayerData = {
    backs: input.BAC.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    halves: input.HAL.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    backRow: input.BR.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    frontRowForwards: input.FRF.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    interchange: input.IC.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    captain: [],
    viceCaptain: [],
    reserve: reservePlayers,
  };

  // Only add the captain if it's not null
  if (captain?.playerId !== null) {
    payload.captain.push({
      playerId: captain?.playerId!,
      playerValue: captain?.scoreData.playerCurrentSalary!,
      role: captain?.role || '',
    });
  }

  // Only add the viceCaptain if it's not null
  if (viceCaptain?.playerId !== null) {
    payload.viceCaptain.push({
      playerId: viceCaptain?.playerId!,
      playerValue: viceCaptain?.scoreData.playerCurrentSalary!,
      role: viceCaptain?.role || '',
    });
  }

  return payload;
};

export const generateAFLTeamPayload = (
  input: AFLInputData,
  captain: FootballPlayer | undefined,
  viceCaptain: FootballPlayer | undefined,
  reservePlayers: ReservePlayerPayload[] = [],
): AFLPlayerData => {
  // Initialize an object to hold the team data

  const payload: AFLPlayerData = {
    backLine: input.BL.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    halfBackLine: input.HBL.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    midfield: input.MID.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    halfForwardLine: input.HFL.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    forwardLine: input.FL.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    followers: input.FOL.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    interchange: input.IC.map((player) => ({
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
      role: player.role,
    })),
    captain: [],
    viceCaptain: [],
    reserve: reservePlayers,
  };

  // Only add the captain if it's not null
  if (captain?.playerId !== null) {
    payload.captain.push({
      playerId: captain?.playerId!,
      playerValue: captain?.scoreData.playerCurrentSalary!,
      role: captain?.role || '',
    });
  }

  // Only add the viceCaptain if it's not null
  if (viceCaptain?.playerId !== null) {
    payload.viceCaptain.push({
      playerId: viceCaptain?.playerId!,
      playerValue: viceCaptain?.scoreData.playerCurrentSalary!,
      role: viceCaptain?.role || '',
    });
  }

  return payload;
};

export const getOriginalRugbyPlayers = (
  role: keyof RugbyFavoriteTeam,
  favouritePlayers: RugbyPlayerFavouriteType[],
  orignalPlayers: RugbyPlayer[],
  captainId: number | undefined,
  viceCaptainId: number | undefined,
): RugbyPlayer[] => {
  const favouritePlayerIds = favouritePlayers.map((player) => player.playerId);

  // Add captainId and viceCaptainId to the list of IDs to consider
  const importantPlayerIds = new Set([...favouritePlayerIds]); // Filter out undefined values

  const originalDataOfFavourites = orignalPlayers.filter((player) =>
    importantPlayerIds.has(player.playerId),
  );

  return originalDataOfFavourites.map((player) => {
    if (player.playerId === captainId) {
      return { ...player, isCaiptain: true };
    }

    if (player.playerId === viceCaptainId) {
      return { ...player, isViceCaiptain: true };
    }

    return player;
  });
};

export const getOriginalAFLPlayers = (
  role: keyof AFLFavoriteTeam,
  favouritePlayers: AFLPlayerFavouriteType[],
  orignalPlayers: FootballPlayer[],
  captainId: number | undefined,
  viceCaptainId: number | undefined,
): FootballPlayer[] => {
  const favouritePlayerIds = favouritePlayers.map((player) => player.playerId);

  // Add captainId and viceCaptainId to the list of IDs to consider
  const importantPlayerIds = new Set([...favouritePlayerIds]); // Filter out undefined values

  const originalDataOfFavourites = orignalPlayers.filter((player) =>
    importantPlayerIds.has(player.playerId),
  );

  return originalDataOfFavourites.map((player) => {
    if (player.playerId === captainId) {
      return { ...player, isCaiptain: true };
    }

    if (player.playerId === viceCaptainId) {
      return { ...player, isViceCaiptain: true };
    }

    return player;
  });
};

export const getOriginalPlayers = (
  role: keyof FavoriteTeam,
  favouritePlayers: PlayerFavouriteType[],
  orignalPlayers: Player[],
  captainId: number | undefined,
  viceCaptainId: number | undefined,
): Player[] => {
  const favouritePlayerIds = favouritePlayers.map((player) => player.playerId);

  // Add captainId and viceCaptainId to the list of IDs to consider
  const importantPlayerIds = new Set([...favouritePlayerIds]); // Filter out undefined values

  const originalDataOfFavourites = orignalPlayers.filter((player) =>
    importantPlayerIds.has(player.playerId),
  );

  return originalDataOfFavourites.map((player) => {
    if (player.playerId === captainId) {
      return { ...player, isCaiptain: true };
    }

    if (player.playerId === viceCaptainId) {
      return { ...player, isViceCaiptain: true };
    }

    return player;
  });
};

function findValidCaptainAndViceCaptain(data: TeamSelectionData): {
  validCaptain?: number;
  validViceCaptain?: number;
} {
  // Extract all player IDs from valid team selection
  const validTeamPlayerIds = Object.values(data.validTeamSelection).flatMap(
    (positions) =>
      positions.map((player: PlayerFavouriteType) => player.playerId),
  );

  // Find a captain that exists in the valid team
  const validCaptain = data.possibleCaptains.find((captain) =>
    validTeamPlayerIds.includes(captain.playerId),
  );

  // Find a vice-captain that exists in the valid team
  const validViceCaptain = data.possibleViceCaptains.find((viceCaptain) =>
    validTeamPlayerIds.includes(viceCaptain.playerId),
  );

  return {
    validCaptain: validCaptain?.playerId,
    validViceCaptain: validViceCaptain?.playerId,
  };
}

function findValidRugbyCaptainAndViceCaptain(data: RugbyTeamSelectionData): {
  validCaptain?: number;
  validViceCaptain?: number;
} {
  // Extract all player IDs from valid team selection
  const validTeamPlayerIds = Object.values(data.validTeamSelection).flatMap(
    (positions) =>
      positions.map((player: RugbyPlayerFavouriteType) => player.playerId),
  );

  // Find a captain that exists in the valid team
  const validCaptain = data.possibleCaptains.find((captain) =>
    validTeamPlayerIds.includes(captain.playerId),
  );

  // Find a vice-captain that exists in the valid team
  const validViceCaptain = data.possibleViceCaptains.find((viceCaptain) =>
    validTeamPlayerIds.includes(viceCaptain.playerId),
  );

  return {
    validCaptain: validCaptain?.playerId,
    validViceCaptain: validViceCaptain?.playerId,
  };
}

function findValidAFLCaptainAndViceCaptain(data: AFLTeamSelectionData): {
  validCaptain?: number;
  validViceCaptain?: number;
} {
  // Extract all player IDs from valid team selection
  const validTeamPlayerIds = Object.values(data.validTeamSelection).flatMap(
    (positions) =>
      positions.map((player: AFLPlayerFavouriteType) => player.playerId),
  );

  // Find a captain that exists in the valid team
  const validCaptain = data.possibleCaptains.find((captain) =>
    validTeamPlayerIds.includes(captain.playerId),
  );

  // Find a vice-captain that exists in the valid team
  const validViceCaptain = data.possibleViceCaptains.find((viceCaptain) =>
    validTeamPlayerIds.includes(viceCaptain.playerId),
  );

  return {
    validCaptain: validCaptain?.playerId,
    validViceCaptain: validViceCaptain?.playerId,
  };
}

export const generateFavouriteTeamPayload = (
  favouriteTeam: FavoriteTeam,
  playersLimit: PlayersByRoleLimit,
  playersByRole: PlayersByRole,
): PlayersByRole => {
  const favouriteRoles: (keyof FavoriteTeam)[] = [
    'allRounder',
    'batsman',
    'bowler',
    'captain',
    'viceCaptain',
    'wicketKeeper',
  ];

  const sortedFavouriteTeam: FavoriteTeam = {
    allRounder: [],
    batsman: [],
    bowler: [],
    captain: [],
    viceCaptain: [],
    wicketKeeper: [],
  };

  const sortedPlayerByeRole: PlayersByRole = {
    ALL: [],
    BAT: [],
    BOW: [],
    WKP: [],
  };

  favouriteRoles?.forEach((role) => {
    const players = favouriteTeam[role];
    // Sorting and limiting
    let limit: number = 0;
    switch (role) {
      case 'allRounder':
        limit = playersLimit.ALL;
        break;
      case 'batsman':
        limit = playersLimit.BAT;
        break;
      case 'bowler':
        limit = playersLimit.BOW;
        break;
      case 'wicketKeeper':
        limit = playersLimit.WKP;
        break;
      case 'captain':
        limit = favouriteTeam.captain.length;
        break;
      case 'viceCaptain':
        limit = favouriteTeam.viceCaptain.length;
        break;
    }

    // Function to check if a player exists in a specific team role
    const isPlayerInRole = (
      teamRole: string,
      player: PlayerFavouriteType,
      favouriteTeam: FavoriteTeam,
    ): boolean => {
      return (
        favouriteTeam[teamRole as keyof FavoriteTeam]?.some(
          (teamPlayer) => teamPlayer?.playerId === player?.playerId,
        ) ?? false
      );
    };

    // Function to check if the player is already in the team for captain/viceCaptain roles
    const isPlayerInFavouriteTeam = (
      player: PlayerFavouriteType,
      favouriteTeam: FavoriteTeam,
    ): boolean => {
      const teamRoles = ['allRounder', 'batsman', 'bowler', 'wicketKeeper'];

      // Check each role to see if the player is part of the team
      return teamRoles.some((teamRole) =>
        isPlayerInRole(teamRole, player, favouriteTeam),
      );
    };

    // Function to sort players by maxSelected in descending order
    const sortPlayers = (
      players: PlayerFavouriteType[],
    ): PlayerFavouriteType[] => {
      return players?.toSorted((a, b) => b.maxSelected - a.maxSelected);
    };

    // Function to filter players based on the role
    const filterByRole = (
      player: PlayerFavouriteType,
      role: string,
      favouriteTeam: FavoriteTeam,
    ): boolean => {
      if (role === 'captain' || role === 'viceCaptain') {
        return isPlayerInFavouriteTeam(player, favouriteTeam);
      }
      return true; // No filtering for other roles
    };

    // Main logic to get sorted and filtered players
    const getSortedPlayers = (
      players: PlayerFavouriteType[],
      role: string,
      favouriteTeam: FavoriteTeam,
      limit: number,
    ): PlayerFavouriteType[] => {
      const sortedPlayers = sortPlayers(players)
        .filter((player) => filterByRole(player, role, favouriteTeam)) // Filter based on role
        .slice(0, limit); // Extract only the top 'limit' records
      return sortedPlayers;
    };

    sortedFavouriteTeam[role] = getSortedPlayers(
      players,
      role,
      favouriteTeam,
      limit,
    );
  });

  const possibleCaptains = sortedFavouriteTeam?.captain;
  const possibleViceCaptains = sortedFavouriteTeam?.viceCaptain;
  const validTeamSelection: TeamSelectionData = {
    possibleCaptains,
    possibleViceCaptains,
    validTeamSelection: {
      allRounder: sortedFavouriteTeam.allRounder,
      wicketKeeper: sortedFavouriteTeam.wicketKeeper,
      batsman: sortedFavouriteTeam.batsman,
      bowler: sortedFavouriteTeam.bowler,
    },
  };

  const { validCaptain, validViceCaptain } =
    findValidCaptainAndViceCaptain(validTeamSelection);

  favouriteRoles?.forEach((role) => {
    switch (role) {
      case 'allRounder': {
        const favouriteAllPlayers = sortedFavouriteTeam[role];
        const orignalAllPlayers = playersByRole['ALL'];
        const allRounders = getOriginalPlayers(
          role,
          favouriteAllPlayers,
          orignalAllPlayers,
          validCaptain,
          validViceCaptain,
        );
        sortedPlayerByeRole['ALL'] = allRounders;
        break;
      }
      case 'batsman': {
        const favouriteBatPlayers = sortedFavouriteTeam[role];
        const orignalBatPlayers = playersByRole['BAT'];
        const batstmaPlayers = getOriginalPlayers(
          role,
          favouriteBatPlayers,
          orignalBatPlayers,
          validCaptain,
          validViceCaptain,
        );
        sortedPlayerByeRole['BAT'] = batstmaPlayers;
        break;
      }

      case 'bowler':
        {
          const favouriteBowPlayers = sortedFavouriteTeam[role];
          const orignalBowPlayers = playersByRole['BOW'];
          const bowlerPlayers = getOriginalPlayers(
            role,
            favouriteBowPlayers,
            orignalBowPlayers,
            validCaptain,
            validViceCaptain,
          );
          sortedPlayerByeRole['BOW'] = bowlerPlayers;
        }
        break;
      case 'wicketKeeper':
        {
          const favouriteWkpPlayers = sortedFavouriteTeam[role];
          const orignalWkpPlayers = playersByRole['WKP'];
          const wkpPlayers = getOriginalPlayers(
            role,
            favouriteWkpPlayers,
            orignalWkpPlayers,
            validCaptain,
            validViceCaptain,
          );
          sortedPlayerByeRole['WKP'] = wkpPlayers;
        }
        break;
    }
  });
  return sortedPlayerByeRole;
};

export const generateRugbyFavouriteTeamPayload = (
  favouriteTeam: RugbyFavoriteTeam,
  playersLimit: RugbyLeaguePlayersByRoleLimit,
  playersByRole: RugbyLeaguePlayersByRole,
): RugbyLeaguePlayersByRole => {
  const favouriteRoles: (keyof RugbyFavoriteTeam)[] = [
    'backRow',
    'backs',
    'frontRowForwards',
    'halves',
    'interchange',
    'captain',
    'viceCaptain',
  ];

  const sortedFavouriteTeam: RugbyFavoriteTeam = {
    backRow: [],
    backs: [],
    captain: [],
    frontRowForwards: [],
    halves: [],
    interchange: [],
    viceCaptain: [],
  };

  const sortedPlayerByeRole: RugbyLeaguePlayersByRole = {
    BR: [],
    HAL: [],
    BAC: [],
    FRF: [],
    IC: [],
  };

  favouriteRoles?.forEach((role) => {
    const players = favouriteTeam[role];
    // Sorting and limiting
    let limit: number = 0;
    switch (role) {
      case 'backRow':
        limit = playersLimit.BR;
        break;
      case 'backs':
        limit = playersLimit.BAC;
        break;
      case 'frontRowForwards':
        limit = playersLimit.FRF;
        break;
      case 'halves':
        limit = playersLimit.HAL;
        break;
      case 'interchange':
        limit = playersLimit.IC;
        break;
      case 'captain':
        limit = 1;
        break;
      case 'viceCaptain':
        limit = 1;
        break;
    }

    // Function to check if a player exists in a specific team role
    const isPlayerInRole = (
      teamRole: string,
      player: RugbyPlayerFavouriteType,
      favouriteTeam: RugbyFavoriteTeam,
    ): boolean => {
      return (
        favouriteTeam[teamRole as keyof RugbyFavoriteTeam]?.some(
          (teamPlayer) => teamPlayer?.playerId === player?.playerId,
        ) ?? false
      );
    };

    // Function to check if the player is already in the team for captain/viceCaptain roles
    const isPlayerInFavouriteTeam = (
      player: RugbyPlayerFavouriteType,
      favouriteTeam: RugbyFavoriteTeam,
    ): boolean => {
      const teamRoles = [
        'backRow',
        'backs',
        'frontRowForwards',
        'halves',
        'interchange',
      ];

      // Check each role to see if the player is part of the team
      return teamRoles.some((teamRole) =>
        isPlayerInRole(teamRole, player, favouriteTeam),
      );
    };

    // Function to sort players by maxSelected in descending order
    const sortPlayers = (
      players: RugbyPlayerFavouriteType[],
    ): RugbyPlayerFavouriteType[] => {
      return players?.toSorted((a, b) => b.maxSelected - a.maxSelected);
    };

    // Function to filter players based on the role
    const filterByRole = (
      player: RugbyPlayerFavouriteType,
      role: string,
      favouriteTeam: RugbyFavoriteTeam,
    ): boolean => {
      if (role === 'captain' || role === 'viceCaptain') {
        return isPlayerInFavouriteTeam(player, favouriteTeam);
      }
      return true; // No filtering for other roles
    };

    // Main logic to get sorted and filtered players
    const getSortedPlayers = (
      players: RugbyPlayerFavouriteType[],
      role: string,
      favouriteTeam: RugbyFavoriteTeam,
      limit: number,
    ): RugbyPlayerFavouriteType[] => {
      const sortedPlayers = sortPlayers(players)
        .filter((player) => filterByRole(player, role, favouriteTeam)) // Filter based on role
        .slice(0, limit);

      // Extract only the top 'limit' records
      return sortedPlayers;
    };

    sortedFavouriteTeam[role] = getSortedPlayers(
      players,
      role,
      favouriteTeam,
      limit,
    );
  });

  const possibleCaptains = favouriteTeam?.captain;
  const possibleViceCaptains = favouriteTeam?.viceCaptain;
  const validTeamSelection: RugbyTeamSelectionData = {
    possibleCaptains,
    possibleViceCaptains,
    validTeamSelection: {
      backRow: sortedFavouriteTeam.backRow,
      backs: sortedFavouriteTeam.backs,
      frontRowForwards: sortedFavouriteTeam.frontRowForwards,
      halves: sortedFavouriteTeam.halves,
      interchange: sortedFavouriteTeam.interchange,
    },
  };
  const { validCaptain, validViceCaptain } =
    findValidRugbyCaptainAndViceCaptain(validTeamSelection);

  favouriteRoles?.forEach((role) => {
    switch (role) {
      case 'backRow': {
        const favouriteBackRowPlayers = sortedFavouriteTeam[role];
        const originalBackRowPlayers = playersByRole['BR'];
        const backRowPlayers = getOriginalRugbyPlayers(
          role,
          favouriteBackRowPlayers,
          originalBackRowPlayers,
          validCaptain,
          validViceCaptain,
        );
        sortedPlayerByeRole['BR'] = backRowPlayers;
        break;
      }
      case 'backs': {
        const favouriteBackPlayers = sortedFavouriteTeam[role];
        const originalBacksPlayers = playersByRole['BAC'];
        const backsPlayers = getOriginalRugbyPlayers(
          role,
          favouriteBackPlayers,
          originalBacksPlayers,
          validCaptain,
          validViceCaptain,
        );
        sortedPlayerByeRole['BAC'] = backsPlayers;
        break;
      }
      case 'halves': {
        const favouriteHalvesPlayers = sortedFavouriteTeam[role];
        const originalHalvesPlayers = playersByRole['HAL'];
        const halvesPlayers = getOriginalRugbyPlayers(
          role,
          favouriteHalvesPlayers,
          originalHalvesPlayers,
          validCaptain,
          validViceCaptain,
        );
        sortedPlayerByeRole['HAL'] = halvesPlayers;
        break;
      }
      case 'interchange': {
        const favouriteInterchangePlayers = sortedFavouriteTeam[role];
        const originalInterchangePlayers = playersByRole['IC'];
        const interchangePlayers = getOriginalRugbyPlayers(
          role,
          favouriteInterchangePlayers,
          originalInterchangePlayers,
          validCaptain,
          validViceCaptain,
        );
        sortedPlayerByeRole['IC'] = interchangePlayers;
        break;
      }

      case 'frontRowForwards': {
        const favouritefrontRowForwardsPlayers = sortedFavouriteTeam[role];
        const originalfrontRowForwardsPlayers = playersByRole['FRF'];
        const frontRowForwardsPlayers = getOriginalRugbyPlayers(
          role,
          favouritefrontRowForwardsPlayers,
          originalfrontRowForwardsPlayers,
          validCaptain,
          validViceCaptain,
        );
        sortedPlayerByeRole['FRF'] = frontRowForwardsPlayers;
        break;
      }
    }
  });

  return sortedPlayerByeRole;
};

export const generateAFLFavouriteTeamPayload = (
  favouriteTeam: AFLFavoriteTeam,
  playersLimit: FootballPlayersByRoleLimit,
  playersByRole: footballPlayersByRole,
): footballPlayersByRole => {
  const favouriteRoles: (keyof AFLFavoriteTeam)[] = [
    'backLine',
    'captain',
    'followers',
    'forwardLine',
    'halfBackLine',
    'halfForwardLine',
    'interchange',
    'midfield',
    'viceCaptain',
  ];

  const sortedFavouriteTeam: AFLFavoriteTeam = {
    backLine: [],
    captain: [],
    followers: [],
    forwardLine: [],
    halfBackLine: [],
    halfForwardLine: [],
    interchange: [],
    midfield: [],
    viceCaptain: [],
  };

  const sortedPlayerByeRole: footballPlayersByRole = {
    BL: [],
    HBL: [],
    MID: [],
    HFL: [],
    FL: [],
    FOL: [],
    IC: [],
  };

  favouriteRoles?.forEach((role) => {
    const players = favouriteTeam[role];
    // Sorting and limiting
    let limit: number = 0;
    switch (role) {
      case 'backLine':
        limit = playersLimit.BL;
        break;
      case 'followers':
        limit = playersLimit.FOL;
        break;
      case 'forwardLine':
        limit = playersLimit.FL;
        break;
      case 'halfBackLine':
        limit = playersLimit.HBL;
        break;
      case 'halfForwardLine':
        limit = playersLimit.HFL;
        break;
      case 'interchange':
        limit = playersLimit.IC;
        break;
      case 'midfield':
        limit = playersLimit.MID;
        break;
      case 'captain':
        limit = 1;
        break;
      case 'viceCaptain':
        limit = 1;
        break;
    }

    // Function to check if a player exists in a specific team role
    const isPlayerInRole = (
      teamRole: string,
      player: AFLPlayerFavouriteType,
      favouriteTeam: AFLFavoriteTeam,
    ): boolean => {
      return (
        favouriteTeam[teamRole as keyof AFLFavoriteTeam]?.some(
          (teamPlayer) => teamPlayer?.playerId === player?.playerId,
        ) ?? false
      );
    };

    // Function to check if the player is already in the team for captain/viceCaptain roles
    const isPlayerInFavouriteTeam = (
      player: AFLPlayerFavouriteType,
      favouriteTeam: AFLFavoriteTeam,
    ): boolean => {
      const teamRoles = [
        'backLine',
        'followers',
        'forwardLine',
        'halfBackLine',
        'halfForwardLine',
        'interchange',
        'midfield',
      ];

      // Check each role to see if the player is part of the team
      return teamRoles.some((teamRole) =>
        isPlayerInRole(teamRole, player, favouriteTeam),
      );
    };

    // Function to sort players by maxSelected in descending order
    const sortPlayers = (
      players: RugbyPlayerFavouriteType[],
    ): RugbyPlayerFavouriteType[] => {
      return players?.toSorted((a, b) => b.maxSelected - a.maxSelected);
    };

    // Function to filter players based on the role
    const filterByRole = (
      player: AFLPlayerFavouriteType,
      role: string,
      favouriteTeam: AFLFavoriteTeam,
    ): boolean => {
      if (role === 'captain' || role === 'viceCaptain') {
        return isPlayerInFavouriteTeam(player, favouriteTeam);
      }
      return true; // No filtering for other roles
    };

    // Main logic to get sorted and filtered players
    const getSortedPlayers = (
      players: AFLPlayerFavouriteType[],
      role: string,
      favouriteTeam: AFLFavoriteTeam,
      limit: number,
      selectedPlayers?: any,
    ): AFLPlayerFavouriteType[] => {
      const sortedPlayers = sortPlayers(players)
        .filter((player) => filterByRole(player, role, favouriteTeam))
        ?.filter((player: any) => !selectedPlayers.has(player.playerId)) // Ensure uniqueness // Filter based on role
        .slice(0, limit);

      // Extract only the top 'limit' records
      return sortedPlayers;
    };

    let selectedPlayers = new Set();

    sortedFavouriteTeam[role] = getSortedPlayers(
      players,
      role,
      favouriteTeam,
      limit,
      selectedPlayers,
    );
  });

  const possibleCaptains = favouriteTeam?.captain;
  const possibleViceCaptains = favouriteTeam?.viceCaptain;
  const validTeamSelection: AFLTeamSelectionData = {
    possibleCaptains,
    possibleViceCaptains,
    validTeamSelection: {
      backLine: sortedFavouriteTeam.backLine,
      followers: sortedFavouriteTeam.followers,
      forwardLine: sortedFavouriteTeam.forwardLine,
      halfBackLine: sortedFavouriteTeam.halfBackLine,
      halfForwardLine: sortedFavouriteTeam.halfForwardLine,
      interchange: sortedFavouriteTeam.interchange,
      midfield: sortedFavouriteTeam.midfield,
    },
  };
  const { validCaptain, validViceCaptain } =
    findValidAFLCaptainAndViceCaptain(validTeamSelection);
  const roleMapping: Record<string, keyof footballPlayersByRole> = {
    backLine: 'BL',
    halfBackLine: 'HBL',
    midfield: 'MID',
    halfForwardLine: 'HFL',
    forwardLine: 'FL',
    followers: 'FOL',
    interchange: 'IC',
  };

  favouriteRoles?.forEach((role) => {
    const mappedRole = roleMapping[role as keyof typeof roleMapping];
    if (mappedRole) {
      const favouritePlayers =
        sortedFavouriteTeam[role as keyof AFLFavoriteTeam] || [];
      const originalPlayers = playersByRole[mappedRole] || [];
      sortedPlayerByeRole[mappedRole] = getOriginalAFLPlayers(
        role,
        favouritePlayers,
        originalPlayers,
        validCaptain,
        validViceCaptain,
      );
    }
  });
  return sortedPlayerByeRole;
};

const getExpertOriginalPlayer = (
  expertPlayers: DreamTeamPlayer,
  playersByRole: PlayersByRole,
): Player | null => {
  const expertPlayerId = expertPlayers.playerId;

  switch (expertPlayers.positionType) {
    case 'allRounder':
      return (
        playersByRole.ALL.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'batsman':
      return (
        playersByRole.BAT.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'bowler':
      return (
        playersByRole.BOW.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'wicketKeeper':
      return (
        playersByRole.WKP.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    default:
      return null;
  }
};

const getRugbyExpertOriginalPlayer = (
  expertPlayers: RugbyDreamTeamPlayer,
  playersByRole: RugbyLeaguePlayersByRole,
): RugbyPlayer | null => {
  const expertPlayerId = expertPlayers.playerId;

  switch (expertPlayers?.positionType) {
    case 'backRow':
      return (
        playersByRole?.BR?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'backs':
      return (
        playersByRole?.BAC?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'interchange':
      return (
        playersByRole?.IC?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'halves':
      return (
        playersByRole?.HAL?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'frontRowForwards':
      return (
        playersByRole?.FRF?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );

    default:
      return null;
  }
};

const getAFLExpertOriginalPlayer = (
  expertPlayers: AFLDreamTeamPlayer,
  playersByRole: footballPlayersByRole,
): FootballPlayer | null => {
  const expertPlayerId = expertPlayers.playerId;

  switch (expertPlayers?.positionType) {
    case 'backLine':
      return (
        playersByRole?.BL?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'halfBackLine':
      return (
        playersByRole?.HBL?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'midfield':
      return (
        playersByRole?.MID?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'halfForwardLine':
      return (
        playersByRole?.HFL?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'forwardLine':
      return (
        playersByRole?.FL?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'followers':
      return (
        playersByRole?.FOL?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    case 'interchange':
      return (
        playersByRole?.IC?.find(
          (player) => player.playerId === expertPlayerId,
        ) || null
      );
    default:
      return null;
  }
};

export const generateExpertTeamPayload = (
  expetTeams: DreamTeamPlayer[],
  playersByRole: PlayersByRole,
): PlayersByRole => {
  // Initialize expertPlayersByRoles with empty arrays
  const expertPlayersByRoles: PlayersByRole = {
    ALL: [],
    BAT: [],
    BOW: [],
    WKP: [],
  };

  // Find captain and vice captain
  const viceCaptain = expetTeams?.find(
    (player) => player.positionType === 'viceCaptain',
  );

  const captain = expetTeams?.find(
    (player) => player.positionType === 'captain',
  );

  // Process each player (excluding captain and vice captain)
  const dreamPlayers = expetTeams.filter(
    (player) =>
      player.positionType !== 'captain' &&
      player.positionType !== 'viceCaptain',
  );

  // Process dream players and add to their respective role arrays
  dreamPlayers?.forEach((player) => {
    const expertPlayer = getExpertOriginalPlayer(player, playersByRole);

    if (expertPlayer) {
      switch (player.positionType) {
        case 'allRounder':
          expertPlayersByRoles.ALL.push(expertPlayer);
          break;
        case 'batsman':
          expertPlayersByRoles.BAT.push(expertPlayer);
          break;
        case 'bowler':
          expertPlayersByRoles.BOW.push(expertPlayer);
          break;
        case 'wicketKeeper':
          expertPlayersByRoles.WKP.push(expertPlayer);
          break;
      }
    }
  });

  // Add captain and vice captain flags
  const allRoles: (keyof PlayersByRole)[] = ['ALL', 'BAT', 'BOW', 'WKP'];

  allRoles?.forEach((role) => {
    const rolePlayers = expertPlayersByRoles[role].map((player) => {
      if (captain && player.playerId === captain.playerId) {
        return { ...player, isCaiptain: true };
      }
      if (viceCaptain && player.playerId === viceCaptain.playerId) {
        return { ...player, isViceCaiptain: true };
      }
      return player;
    });

    expertPlayersByRoles[role] = rolePlayers;
  });

  return expertPlayersByRoles;
};

export const generateRugbyExpertTeamPayload = (
  expetTeams: RugbyDreamTeamPlayer[],
  playersByRole: RugbyLeaguePlayersByRole,
): RugbyLeaguePlayersByRole => {
  // Initialize expertPlayersByRoles with empty arrays
  const expertPlayersByRoles: RugbyLeaguePlayersByRole = {
    BR: [],
    HAL: [],
    BAC: [],
    FRF: [],
    IC: [],
  };

  // Find captain and vice captain
  const viceCaptain = expetTeams?.find(
    (player) => player?.positionType === 'viceCaptain',
  );

  const captain = expetTeams?.find(
    (player) => player?.positionType === 'captain',
  );

  // Process each player (excluding captain and vice captain)
  const dreamPlayers = expetTeams?.filter(
    (player) =>
      player.positionType !== 'captain' &&
      player.positionType !== 'viceCaptain',
  );

  // Process dream players and add to their respective role arrays
  dreamPlayers?.forEach((player) => {
    const expertPlayer = getRugbyExpertOriginalPlayer(player, playersByRole);

    if (expertPlayer) {
      switch (player.positionType) {
        case 'backRow':
          expertPlayersByRoles.BR.push(expertPlayer);
          break;
        case 'backs':
          expertPlayersByRoles.BAC.push(expertPlayer);
          break;
        case 'interchange':
          expertPlayersByRoles.IC.push(expertPlayer);
          break;
        case 'halves':
          expertPlayersByRoles.HAL.push(expertPlayer);
          break;
        case 'frontRowForwards':
          expertPlayersByRoles.FRF.push(expertPlayer);
          break;
      }
    }
  });

  ALL_RUGBY_ROLES?.forEach((role) => {
    const rolePlayers = expertPlayersByRoles[role].map((player) => {
      if (captain && player.playerId === captain.playerId) {
        return { ...player, isCaiptain: true };
      }
      if (viceCaptain && player.playerId === viceCaptain.playerId) {
        return { ...player, isViceCaiptain: true };
      }
      return player;
    });

    expertPlayersByRoles[role] = rolePlayers;
  });

  return expertPlayersByRoles;
};

export const generateAFLExpertTeamPayload = (
  expetTeams: AFLDreamTeamPlayer[],
  playersByRole: footballPlayersByRole,
): footballPlayersByRole => {
  // Initialize expertPlayersByRoles with empty arrays
  const expertPlayersByRoles: footballPlayersByRole = {
    BL: [],
    HBL: [],
    MID: [],
    HFL: [],
    FL: [],
    FOL: [],
    IC: [],
  };

  // Find captain and vice captain
  const viceCaptain = expetTeams?.find(
    (player) => player?.positionType === 'viceCaptain',
  );

  const captain = expetTeams?.find(
    (player) => player?.positionType === 'captain',
  );

  // Process each player (excluding captain and vice captain)
  const dreamPlayers = expetTeams?.filter(
    (player) =>
      player.positionType !== 'captain' &&
      player.positionType !== 'viceCaptain',
  );

  // Process dream players and add to their respective role arrays
  dreamPlayers?.forEach((player) => {
    const expertPlayer = getAFLExpertOriginalPlayer(player, playersByRole);

    if (expertPlayer) {
      switch (player.positionType) {
        case 'backLine':
          expertPlayersByRoles.BL.push(expertPlayer);
          break;
        case 'halfBackLine':
          expertPlayersByRoles.HBL.push(expertPlayer);
          break;
        case 'midfield':
          expertPlayersByRoles.MID.push(expertPlayer);
          break;
        case 'halfForwardLine':
          expertPlayersByRoles.HFL.push(expertPlayer);
          break;
        case 'forwardLine':
          expertPlayersByRoles.FL.push(expertPlayer);
          break;
        case 'followers':
          expertPlayersByRoles.FOL.push(expertPlayer);
          break;
        case 'interchange':
          expertPlayersByRoles.IC.push(expertPlayer);
          break;
      }
    }
  });

  ALL_AFL_ROLES?.forEach((role) => {
    const rolePlayers = expertPlayersByRoles[role].map((player) => {
      if (captain && player.playerId === captain.playerId) {
        return { ...player, isCaiptain: true };
      }
      if (viceCaptain && player.playerId === viceCaptain.playerId) {
        return { ...player, isViceCaiptain: true };
      }
      return player;
    });

    expertPlayersByRoles[role] = rolePlayers;
  });

  return expertPlayersByRoles;
};

export function findCaptainViceCaptain(data: PlayersByRoleType): {
  captain: Player | undefined;
  viceCaptain: Player | undefined;
} {
  let captain: Player | undefined = undefined;
  let viceCaptain: Player | undefined = undefined;

  // Iterate over each category (WKP, BAT, BOW, ALL)
  for (const category of Object.values(data)) {
    for (const player of category) {
      if (player?.isCaiptain) {
        captain = player;
      }
      if (player?.isViceCaiptain) {
        viceCaptain = player;
      }
    }
  }

  return { captain, viceCaptain };
}

export function findRugbyCaptainViceCaptain(data: RugbyLeaguePlayersByRole): {
  captain: RugbyPlayer | undefined;
  viceCaptain: RugbyPlayer | undefined;
} {
  let captain: RugbyPlayer | undefined = undefined;
  let viceCaptain: RugbyPlayer | undefined = undefined;

  // Iterate over each category (BAC, HAL, BR, FRF, IC)
  for (const category of Object.values(data)) {
    for (const player of category) {
      if (player?.isCaiptain) {
        captain = player;
      }
      if (player?.isViceCaiptain) {
        viceCaptain = player;
      }
    }
  }

  return { captain, viceCaptain };
}

export function findAFLCaptainViceCaptain(data: footballPlayersByRole): {
  captain: FootballPlayer | undefined;
  viceCaptain: FootballPlayer | undefined;
} {
  let captain: FootballPlayer | undefined = undefined;
  let viceCaptain: FootballPlayer | undefined = undefined;

  // Iterate over each category (BAC, HAL, BR, FRF, IC)
  for (const category of Object.values(data)) {
    for (const player of category) {
      if (player?.isCaiptain) {
        captain = player;
      }
      if (player?.isViceCaiptain) {
        viceCaptain = player;
      }
    }
  }

  return { captain, viceCaptain };
}

function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

export function generateRandomTeam(
  players: PlayersByRole,
  TEAM_COMPOSITION: TeamComposition,
): PlayersByRole {
  // Validate input
  if (
    !players.WKP?.length ||
    !players.BAT?.length ||
    !players.BOW?.length ||
    !players.ALL?.length
  ) {
    throw new Error('Missing required player');
  }

  let attempts = 0;
  const maxAttempts = 100; // Prevent infinite loops

  while (attempts < maxAttempts) {
    try {
      const result: PlayersByRole = {
        WKP: [],
        BAT: [],
        BOW: [],
        ALL: [],
      };

      // Track all selected players to ensure uniqueness
      const selectedPlayerIds = new Set<string | number>();

      // Helper function to get random unique players for a role
      const getRandomUniquePlayers = (
        availablePlayers: Player[],
        count: number,
      ): Player[] => {
        if (count > availablePlayers.length) {
          throw new Error(
            `Not enough players available for role. Need ${count} but have ${availablePlayers.length}`,
          );
        }

        // Filter out already selected players
        const eligiblePlayers = availablePlayers.filter(
          (player) => !selectedPlayerIds.has(player.playerId),
        );

        if (eligiblePlayers.length < count) {
          throw new Error(
            `Not enough eligible players for role. Need ${count} but have ${eligiblePlayers.length}`,
          );
        }

        // Shuffle eligible players
        const shuffledPlayers = shuffleArray(eligiblePlayers);
        const selectedPlayers = shuffledPlayers.slice(0, count);

        // Add selected players to tracking set
        selectedPlayers.forEach((player) => {
          selectedPlayerIds.add(player.playerId);
        });

        return selectedPlayers;
      };

      // 1. First, select wicket-keeper (mandatory 1)
      result.WKP = getRandomUniquePlayers(
        players.WKP,
        TEAM_COMPOSITION.WKP.min,
      );

      // 2. Select all-rounders (1-2)
      const allRoundersCount =
        Math.floor(
          Math.random() *
            (TEAM_COMPOSITION.ALL.max - TEAM_COMPOSITION.ALL.min + 1),
        ) + TEAM_COMPOSITION.ALL.min;
      result.ALL = getRandomUniquePlayers(players.ALL, allRoundersCount);

      // 3. Calculate remaining slots
      const remainingSlots =
        TEAM_COMPOSITION.TOTAL_PLAYERS - result.WKP.length - result.ALL.length;

      // 4. Calculate valid range for batsmen
      const maxBatsmen = Math.min(
        TEAM_COMPOSITION.BAT.max,
        remainingSlots - TEAM_COMPOSITION.BOW.min,
      );
      const minBatsmen = Math.max(
        TEAM_COMPOSITION.BAT.min,
        remainingSlots - TEAM_COMPOSITION.BOW.max,
      );

      if (maxBatsmen < minBatsmen) {
        throw new Error('Invalid distribution');
      }

      // 5. Select random number of batsmen within valid range
      const batsmenCount =
        Math.floor(Math.random() * (maxBatsmen - minBatsmen + 1)) + minBatsmen;
      result.BAT = getRandomUniquePlayers(players.BAT, batsmenCount);

      // 6. Fill remaining slots with bowlers
      const bowlersCount =
        TEAM_COMPOSITION.TOTAL_PLAYERS -
        result.WKP.length -
        result.ALL.length -
        result.BAT.length;

      if (
        bowlersCount < TEAM_COMPOSITION.BOW.min ||
        bowlersCount > TEAM_COMPOSITION.BOW.max
      ) {
        throw new Error('Invalid bowler count');
      }

      result.BOW = getRandomUniquePlayers(players.BOW, bowlersCount);

      // Verify total players
      const totalPlayers =
        result.WKP.length +
        result.BAT.length +
        result.BOW.length +
        result.ALL.length;

      if (totalPlayers !== TEAM_COMPOSITION.TOTAL_PLAYERS) {
        throw new Error(
          `Invalid total players: ${totalPlayers}. Expected: ${TEAM_COMPOSITION.TOTAL_PLAYERS}`,
        );
      }

      // 7. Randomly assign captain and vice-captain from unique players
      const allPlayers = [
        ...result.WKP,
        ...result.BAT,
        ...result.BOW,
        ...result.ALL,
      ];

      // Reset any existing captain/vice-captain flags
      allPlayers.forEach((player) => {
        player.isCaiptain = false;
        player.isViceCaiptain = false;
      });

      // Select unique captain and vice-captain
      const shuffledPlayers = shuffleArray([...allPlayers]);
      const captain = shuffledPlayers[0];
      const viceCaptain = shuffledPlayers[1];

      // Set new captain and vice-captain
      captain.isCaiptain = true;
      viceCaptain.isViceCaiptain = true;

      return result;
    } catch (error) {
      attempts++;
      if (attempts === maxAttempts) {
        console.error(error);
        throw new Error(
          'Could not generate valid team composition after maximum attempts',
        );
      }
      continue;
    }
  }

  throw new Error('Could not generate valid team composition');
}

export function generateRandomRugbyTeam(
  players: RugbyLeaguePlayersByRole,
  TEAM_COMPOSITION: RugbyTeamComposition,
): RugbyLeaguePlayersByRole {
  // Validate input
  if (
    !players.BAC?.length ||
    !players.HAL?.length ||
    !players.BR?.length ||
    !players.FRF?.length ||
    !players.IC?.length
  ) {
    throw new Error('Missing required player roles');
  }

  let attempts = 0;
  const maxAttempts = 100; // Prevent infinite loops

  while (attempts < maxAttempts) {
    try {
      const result: RugbyLeaguePlayersByRole = {
        BAC: [],
        HAL: [],
        BR: [],
        FRF: [],
        IC: [],
      };

      // Track all selected players to ensure uniqueness
      const selectedPlayerIds = new Set<string | number>();

      // Helper function to get random unique players for a role
      const getRandomUniquePlayers = (
        availablePlayers: RugbyPlayer[],
        count: number,
      ): RugbyPlayer[] => {
        if (count > availablePlayers.length) {
          throw new Error(
            `Not enough players available for role. Need ${count} but have ${availablePlayers.length}`,
          );
        }

        // Filter out already selected players
        const eligiblePlayers = availablePlayers.filter(
          (player) => !selectedPlayerIds.has(player.playerId),
        );

        if (eligiblePlayers.length < count) {
          throw new Error(
            `Not enough eligible players for role. Need ${count} but have ${eligiblePlayers.length}`,
          );
        }

        // Shuffle eligible players
        const shuffledPlayers = shuffleArray(eligiblePlayers);
        const selectedPlayers = shuffledPlayers.slice(0, count);

        // Add selected players to tracking set
        selectedPlayers.forEach((player) => {
          selectedPlayerIds.add(player.playerId);
        });

        return selectedPlayers;
      };

      // Process roles in order of restrictiveness
      const roleOrder: (keyof RugbyLeaguePlayersByRole)[] = [
        'HAL',
        'FRF',
        'BR',
        'BAC',
        'IC',
      ];

      // Select players for each role
      for (const role of roleOrder) {
        const requiredCount = TEAM_COMPOSITION[role].min;
        result[role] = getRandomUniquePlayers(players[role], requiredCount);
      }

      // Verify total players
      const totalPlayers = Object.values(result).reduce(
        (sum, rolePlayers) => sum + rolePlayers.length,
        0,
      );

      if (totalPlayers !== TEAM_COMPOSITION.TOTAL_PLAYERS) {
        throw new Error(
          `Invalid total players: ${totalPlayers}. Expected: ${TEAM_COMPOSITION.TOTAL_PLAYERS}`,
        );
      }

      // Assign captain and vice-captain from unique players
      const allPlayers = Object.values(result).flat();
      const shuffledPlayers = shuffleArray([...allPlayers]);
      const captain = shuffledPlayers[0];
      const viceCaptain = shuffledPlayers[1];

      // Reset any existing captain/vice-captain flags
      allPlayers.forEach((player) => {
        player.isCaiptain = false;
        player.isViceCaiptain = false;
      });

      // Set new captain and vice-captain
      captain.isCaiptain = true;
      viceCaptain.isViceCaiptain = true;

      return result;
    } catch (error) {
      attempts++;
      if (attempts === maxAttempts) {
        console.error(error);
        throw new Error(
          'Could not generate a valid team composition after maximum attempts',
        );
      }
      continue;
    }
  }

  throw new Error('Could not generate a valid team composition');
}

export function generateRandomAFLTeam(
  players: footballPlayersByRole,
  TEAM_COMPOSITION: AFLTeamComposition,
): footballPlayersByRole {
  // Initialize result object
  const result: footballPlayersByRole = {
    BL: [],
    HBL: [],
    MID: [],
    HFL: [],
    FL: [],
    FOL: [],
    IC: [],
  };

  // Validate input
  if (
    !players.BL?.length ||
    !players.HBL?.length ||
    !players.MID?.length ||
    !players.HFL?.length ||
    !players.FL?.length ||
    !players.FOL?.length ||
    !players.IC?.length
  ) {
    console.warn(
      'Missing some required player roles, returning available players',
    );
    // Return whatever players we have in each role
    const roles: (keyof footballPlayersByRole)[] = [
      'BL',
      'HBL',
      'MID',
      'HFL',
      'FL',
      'FOL',
      'IC',
    ];
    roles.forEach((role) => {
      if (players[role]?.length && TEAM_COMPOSITION[role]?.min) {
        result[role] = players[role].slice(0, TEAM_COMPOSITION[role].min);
      }
    });
    return result;
  }

  let attempts = 0;
  const maxAttempts = 100; // Prevent infinite loops

  while (attempts < maxAttempts) {
    try {
      // Reset result for each attempt
      const roles: (keyof footballPlayersByRole)[] = [
        'BL',
        'HBL',
        'MID',
        'HFL',
        'FL',
        'FOL',
        'IC',
      ];
      roles.forEach((role) => {
        result[role] = [];
      });

      // Track all selected players to ensure uniqueness
      const selectedPlayerIds = new Set<string | number>();

      // Helper function to get random unique players for a role
      const getRandomUniquePlayers = (
        availablePlayers: FootballPlayer[],
        count: number,
      ): FootballPlayer[] => {
        // First, remove duplicate players by playerId
        const uniquePlayers = availablePlayers.filter(
          (player, index, self) =>
            index === self.findIndex((p) => p.playerId === player.playerId),
        );

        if (count > uniquePlayers.length) {
          throw new Error(
            `Not enough unique players available for role. Need ${count} but have ${uniquePlayers.length}`,
          );
        }

        // Filter out already selected players
        const eligiblePlayers = uniquePlayers.filter(
          (player) => !selectedPlayerIds.has(player.playerId),
        );

        if (eligiblePlayers.length < count) {
          // If we don't have enough eligible players, try using players from other roles
          const allAvailablePlayers = Object.values(players)
            .flat()
            .filter(
              (player) =>
                !selectedPlayerIds.has(player.playerId) &&
                // Ensure we don't get duplicates
                !eligiblePlayers.some((p) => p.playerId === player.playerId),
            );

          eligiblePlayers.push(...allAvailablePlayers);

          if (eligiblePlayers.length < count) {
            throw new Error(
              `Not enough eligible players for role. Need ${count} but have ${eligiblePlayers.length}`,
            );
          }
        }

        // Shuffle eligible players
        const shuffledPlayers = shuffleArray([...eligiblePlayers]);
        const selectedPlayers = shuffledPlayers.slice(0, count);

        // Add selected players to tracking set
        selectedPlayers.forEach((player) => {
          selectedPlayerIds.add(player.playerId);
        });

        return selectedPlayers;
      };

      // Process roles in order of restrictiveness
      const roleOrder: (keyof footballPlayersByRole)[] = [
        'BL',
        'HBL',
        'MID',
        'HFL',
        'FL',
        'FOL',
        'IC',
      ];

      // Select players for each role
      for (const role of roleOrder) {
        const requiredCount = TEAM_COMPOSITION[role].min;
        result[role] = getRandomUniquePlayers(players[role], requiredCount);
      }

      // Verify total players
      const totalPlayers = Object.values(result).reduce(
        (sum, rolePlayers) => sum + rolePlayers.length,
        0,
      );

      if (totalPlayers !== TEAM_COMPOSITION.TOTAL_PLAYERS) {
        throw new Error(
          `Invalid total players: ${totalPlayers}. Expected: ${TEAM_COMPOSITION.TOTAL_PLAYERS}`,
        );
      }

      // Assign captain and vice-captain from unique players
      const allPlayers = Object.values(result).flat();
      const shuffledPlayers = shuffleArray([...allPlayers]);
      const captain = shuffledPlayers[0];
      const viceCaptain = shuffledPlayers[1];

      // Reset any existing captain/vice-captain flags
      allPlayers.forEach((player) => {
        player.isCaiptain = false;
        player.isViceCaiptain = false;
      });

      // Set new captain and vice-captain
      captain.isCaiptain = true;
      viceCaptain.isViceCaiptain = true;

      return result;
    } catch (error) {
      attempts++;
      if (attempts === maxAttempts) {
        console.error(error);
        throw new Error(
          'Could not generate a valid team composition after maximum attempts',
        );
      }
      continue;
    }
  }

  throw new Error('Could not generate a valid team composition');
}

export function isObjectEmpty(obj: unknown): boolean {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    Object.keys(obj).length === 0 &&
    obj.constructor === Object
  );
}

export const formatToCustomStyle = (number: number): string => {
  const numberString = number?.toString();

  if (numberString?.length <= 3) return numberString; // No formatting needed for small numbers

  const lastThreeDigits = numberString?.slice(-3); // Get the last three digits
  let remainingDigits = numberString?.slice(0, -3); // Get the rest of the digits

  let result = lastThreeDigits;

  // Format the remaining digits into groups of two
  while (remainingDigits?.length > 0) {
    const chunk = remainingDigits?.slice(-2); // Take the last two digits
    remainingDigits = remainingDigits?.slice(0, -2); // Remove the last two digits
    result = `${chunk},${result}`; // Add the chunk to the result
  }

  return result;
};

export const calculateDifferenceInK = (
  currentSalary: number,
  lastSalary: number,
): string => {
  const difference = currentSalary - lastSalary;

  // Format difference in "K" with one decimal place
  const formattedDifference = `${Math.abs(difference / 1000).toFixed(1)}K`;

  // Prefix with "-" if the difference is negative
  return difference < 0 ? `-${formattedDifference}` : formattedDifference;
};

export const LocalStorage = {
  getItem<T>(key: string): T | null {
    if (typeof window === 'undefined') return null;
    const item = window.localStorage.getItem(key);
    return item ? (JSON.parse(item) as T) : null;
  },

  setItem<T>(key: string, value: T): void {
    if (typeof window === 'undefined') return;
    window.localStorage.setItem(key, JSON.stringify(value));
  },

  removeItem(key: string): void {
    if (typeof window === 'undefined') return;
    window.localStorage.removeItem(key);
  },
};

interface SalaryDifferenceResult {
  status: 'positive' | 'negative';
  change: number;
}

export function calculateDifferenceRating(
  pastSalary: number,
  currentSalary: number,
): SalaryDifferenceResult {
  // Calculate the difference and percentage difference
  const difference = currentSalary - pastSalary;
  const percentageDifference = (difference / pastSalary) * 100;

  // Determine if the change is positive or negative
  const status: 'positive' | 'negative' =
    percentageDifference >= 0 ? 'positive' : 'negative';

  // Get the absolute percentage difference for range calculation
  const absPercentage = Math.abs(percentageDifference);

  // Map percentage to a rating
  let change: number;
  if (absPercentage === 0) {
    change = 0;
  } else if (absPercentage <= 25) {
    change = 1;
  } else if (absPercentage <= 50) {
    change = 2;
  } else if (absPercentage <= 75) {
    change = 3;
  } else if (absPercentage <= 100) {
    change = 4;
  } else {
    change = 4;
  }

  // Return the result
  return {
    status,
    change,
  };
}

export const formatNumberWithCommas = (number: number) => {
  if (typeof number !== 'number') return '';
  return new Intl.NumberFormat('en-AU', {
    currency: 'AUD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(number);
};

export const addPlayersWithinBudget = (
  playerData: PlayersByRole,
  remainingBudget: number,
): PlayersByRole => {
  const selectedPlayers: PlayersByRole = { WKP: [], BAT: [], BOW: [], ALL: [] };
  for (const role in playerData) {
    const players = playerData[role as keyof PlayersByRole];
    for (const player of players) {
      if (remainingBudget - player.scoreData.playerCurrentSalary >= 0) {
        selectedPlayers[role as keyof PlayersByRole].push(player);
        remainingBudget -= player.scoreData.playerCurrentSalary;
      } else {
        setApiMessage('error', `Insufficient balance.`);
      }
    }
  }
  return selectedPlayers;
};

export const addRugbyPlayersWithinBudget = (
  playerData: RugbyLeaguePlayersByRole,
  remainingBudget: number,
): RugbyLeaguePlayersByRole => {
  const selectedPlayers: RugbyLeaguePlayersByRole = {
    BAC: [],
    HAL: [],
    BR: [],
    FRF: [],
    IC: [],
  };
  for (const role in playerData) {
    const players = playerData[role as keyof RugbyLeaguePlayersByRole];
    for (const player of players) {
      if (remainingBudget - player.scoreData.playerCurrentSalary >= 0) {
        selectedPlayers[role as keyof RugbyLeaguePlayersByRole].push(player);
        remainingBudget -= player.scoreData.playerCurrentSalary;
      } else {
        setApiMessage('error', `Insufficient balance.`);
      }
    }
  }
  return selectedPlayers;
};

export const addAFLPlayersWithinBudget = (
  playerData: footballPlayersByRole,
  remainingBudget: number,
): footballPlayersByRole => {
  const selectedPlayers: footballPlayersByRole = {
    BL: [],
    HBL: [],
    MID: [],
    HFL: [],
    FL: [],
    FOL: [],
    IC: [],
  };
  for (const role in playerData) {
    const players = playerData[role as keyof footballPlayersByRole];
    for (const player of players) {
      if (remainingBudget - player.scoreData.playerCurrentSalary >= 0) {
        selectedPlayers[role as keyof footballPlayersByRole].push(player);
        remainingBudget -= player.scoreData.playerCurrentSalary;
      } else {
        setApiMessage('error', `Insufficient balance.`);
      }
    }
  }
  return selectedPlayers;
};

export const getAllPlayers = (response: FantasyTeamResponse): Player[] => {
  // Destructure result from response with a default empty object to prevent undefined errors
  const { result } = response || {};

  // If result is undefined, return an empty array
  if (!result) return [];

  // Use optional chaining and provide empty arrays as fallback values
  return [
    ...(result.wicketKeeper || []),
    ...(result.batsman || []),
    ...(result.bowler || []),
    ...(result.allRounder || []),
  ];
};

export const getAllRugbyPlayers = (
  response: FantasyTeamResponse,
): RugbyPlayer[] => {
  // Destructure result from response with a default empty object to prevent undefined errors
  const { result } = response || {};

  // If result is undefined, return an empty array
  if (!result) return [];

  // Return only rugby players
  return [
    ...(result.backRow || []),
    ...(result.backs || []),
    ...(result.halves || []),
    ...(result.interchange || []),
    ...(result.frontRowForwards || []),
  ];
};

export const getAllAFLPlayers = (
  response: FantasyTeamResponse,
): FootballPlayer[] => {
  // Destructure result from response with a default empty object to prevent undefined errors
  const { result } = response || {};

  // If result is undefined, return an empty array
  if (!result) return [];

  // Return only AFL players
  // @ts-ignore
  return [
    ...(result.backLine || []),
    ...(result.halfBackLine || []),
    ...(result.midfield || []),
    ...(result.halfForwardLine || []),
    ...(result.forwardLine || []),
    ...(result.followers || []),
    ...(result.interchange || []),
  ];
};

export const getShortName = (fullName: any): string => {
  if (!fullName || typeof fullName !== 'string') return '';

  const nameParts = fullName.trim().split(/\s+/); // Split by whitespace
  if (nameParts.length === 0) return '';

  const lastName = nameParts.pop() || ''; // Extract last name with empty string fallback

  // Ensure last name is valid (allowing letters, numbers, hyphens, apostrophes, and dots)
  if (!/^[a-zA-Z0-9.'-]+$/.test(lastName)) return '';

  const initials = nameParts
    .filter((name) => /^[a-zA-Z.'-]+$/.test(name)) // Allow letters, dots, apostrophes, and hyphens in initials
    .map((name) => name.charAt(0) + (name.charAt(1) === '.' ? '.' : '')); // Keep dots if they exist

  return initials.length ? `${initials.join('.')} ${lastName}` : lastName;
};

export function capitalize(value: string): string {
  if (!value) return value; // Handle empty or null strings safely
  return value.charAt(0).toUpperCase() + value.slice(1);
}

// Define the type for PAID_WINNER_PERCENTAGE
type WinnerPercentage = {
  [key: number]: number; // Keys are numbers representing positions, values are percentages
};

// Define the type for the result object
type CoinsForWinners = {
  [key: number]: number; // Keys are numbers representing positions, values are calculated coin amounts
};

// Winner Arrange based on paid Competition Percentage
export const PAID_WINNER_PERCENTAGE: WinnerPercentage = {
  1: 50,
  2: 20,
  3: 10,
  4: 1,
  5: 1,
  6: 1,
  7: 1,
  8: 1,
  9: 1,
  10: 1,
  11: 1,
  12: 1,
  13: 1,
  14: 1,
  15: 1,
  16: 1,
  17: 1,
  18: 1,
  19: 1,
  20: 1,
};

// Get the coins values as per winner percentage
export const paidWinCoins = (totalCoins: number): CoinsForWinners => {
  const coinsForWinners: CoinsForWinners = {};

  Object.keys(PAID_WINNER_PERCENTAGE)?.forEach((winner) => {
    const winnerKey = Number(winner); // Convert string keys to numbers
    coinsForWinners[winnerKey] =
      (totalCoins * PAID_WINNER_PERCENTAGE[winnerKey]) / 100;
  });

  return coinsForWinners;
};

// export function calculateTotalWithCardFee(amount: number): {
//   fee: number;
//   total: number;
// } {
//   const percentage = 1.75 / 100; // 1.75% fee
//   const fixedFee = 0.3; // Fixed fee of $0.30

export function calculateTotalWithCardFee(amount: number): {
  fee: number;
  total: number;
} {
  const percentage = 1.75 / 100; // 1.75% fee
  const fixedFee = 0.3; // Fixed fee of $0.30

  // Calculate the fee with precise decimal handling
  const cardFee = Number(percentage * amount + fixedFee);

  // Calculate total with precise decimal handling
  const totalAmount = Number(amount + cardFee);

  return {
    fee: Number(_.round(cardFee, 2).toFixed(2) ?? 0),
    total: Number(_.round(totalAmount, 2).toFixed(2) ?? 0),
  };
}

export function generateUniqueId(): string {
  // Check if running in a Node.js environment
  if (typeof window === 'undefined') {
    const { randomBytes } = require('crypto');
    return randomBytes(16).toString('hex');
  } else {
    // Use browser's crypto API
    const array = new Uint8Array(16);
    window.crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join(
      '',
    );
  }
}

export const groupRugbyPlayersByRole = (
  players: RugbyPlayer[],
): RugbyLeaguePlayersByRole => {
  return players.reduce<RugbyLeaguePlayersByRole>(
    (acc, player) => {
      switch (player.role) {
        case 'BAC':
          acc.BAC.push(player);
          break;
        case 'BR':
          acc.BR.push(player);
          break;
        case 'FRF':
          acc.FRF.push(player);
          break;
        case 'IC':
          acc.IC.push(player);
          break;
        case 'HAL':
          acc.HAL.push(player);
      }
      return acc;
    },
    { BAC: [], BR: [], FRF: [], IC: [], HAL: [] }, // Initialize empty arrays for each role
  );
};

export const getSportsId = (name: SportsType): number | undefined => {
  switch (name) {
    case 'afl':
      return 9;
    case 'nrl':
      return 12;
    case 'cricket':
      return 4;
    case 'soccer':
      return 8;
  }
};

type TournamentItem = {
  CricketTournament?: { name: string };
  RLTournament?: { name: string };
  AFLTournament?: { name: string };
  ARTournament?: { name: string };
  GolfTournament?: { name: string };
  TennisTournament?: { name: string };
  BaseballTournament?: { name: string };
  IceHockeyTournament?: { name: string };
  BoxingTournament?: { name: string };
  MMATournament?: { name: string };
  SoccerTournament?: { name: string };
  NBATournament?: { name: string; NBACategory?: { name: string } };
};

export const getTournamentName = (
  sportId: number,
  item: TournamentItem,
): string | null => {
  switch (sportId) {
    case 4: // Cricket
      return item?.CricketTournament?.name ?? null;

    case 12: // Rugby League
    case 13: // Rugby Union
      return item?.RLTournament?.name ?? null;

    case 10: // Basketball (NBA)
      const { NBATournament } = item;
      return NBATournament?.NBACategory
        ? `${NBATournament.name} ${NBATournament.NBACategory.name}`
        : (NBATournament?.name ?? null);

    case 15: // American Football
      return item?.AFLTournament?.name ?? null;

    case 9: // Australian Rules Football
      return item?.ARTournament?.name ?? null;

    case 16: // Golf
      return item?.GolfTournament?.name ?? null;

    case 7: // Tennis
      return item?.TennisTournament?.name ?? null;

    case 11: // Baseball
      return item?.BaseballTournament?.name ?? null;

    case 17: // Ice Hockey
      return item?.IceHockeyTournament?.name ?? null;

    case 6: // Boxing
      return item?.BoxingTournament?.name ?? null;

    case 5: // MMA
      return item?.MMATournament?.name ?? null;

    case 8: // Soccer
      return item?.SoccerTournament?.name ?? null;

    default:
      return null;
  }
};

export const NRLplayersRollName = (role: string) => {
  let title;
  switch (role) {
    case 'Centres':
      title = 'C';
      break;
    case 'Five Eighth':
      title = 'FE';
      break;
    case 'Back Row':
      title = 'BR';
      break;
    case 'Lock':
      title = 'LOC';
      break;
    case 'Prop Forward':
      title = 'PF';
      break;
    case 'Prop':
      title = 'PF';
      break;
    case 'Interchange':
      title = 'IC';
      break;
    case 'Halfback':
      title = 'HB';
      break;
    case 'Centre':
      title = 'CEN';
      break;
    case 'Wing':
      title = 'WING';
      break;
    case 'Full Back':
      title = 'FB';
      break;
    case 'Hooker':
      title = 'HOK';
      break;
    case 'Fullback':
      title = 'FB';
      break;
    case 'FB':
      title = 'FB';
      break;
    case 'half back':
      title = 'HB';
      break;
    case 'Back':
      title = 'BR';
      break;
    case 'Outside Back':
      title = 'OB';
      break;
    case 'Second Row':
      title = 'SR';
      break;
    case 'Utility':
      title = 'UTILITY';
      break;
  }
  return title;
};

export const resetPlayersCaptaincy = (
  playersData: PlayersByRole,
): PlayersByRole => {
  const resetCategory = (players: Player[]) =>
    players.map((player) => ({
      ...player,
      isCaiptain: false,
      isViceCaiptain: false,
    }));

  return {
    ALL: resetCategory(playersData.ALL),
    BAT: resetCategory(playersData.BAT),
    BOW: resetCategory(playersData.BOW),
    WKP: resetCategory(playersData.WKP),
  };
};

export const getAFLPlayersList = (
  playerList: FootballPlayer[],
): footballPlayersByRole => {
  const filterAndMapPlayers = (
    roles: FootBallRole[],
    subrole: keyof footballPlayersByRole,
  ) =>
    playerList
      .filter((player) => roles.includes(player.role))
      .map((player) => ({ ...player, subrole }));

  return {
    BL: filterAndMapPlayers(
      [
        'Defender',
        'Key Defender',
        'Back Pocket Left',
        'Back Pocket Right',
        'Full Back',
      ],
      'BL',
    ),
    HBL: filterAndMapPlayers(
      [
        'Defender',
        'Key Defender',
        'Half Back Flank Right',
        'Half Back Flank Righ',
        'Centre Half Back',
        'Half Back Flank Left',
      ],
      'HBL',
    ),
    MID: filterAndMapPlayers(['Midfielder'], 'MID'),
    HFL: filterAndMapPlayers(
      [
        'Forward',
        'Key Forward',
        'Half Forward Flank R',
        'Half Forward Flank L',
        'Wing Right',
        'Wing Left',
        'Centre Half Forward',
      ],
      'HFL',
    ),
    FL: filterAndMapPlayers(
      [
        'Forward',
        'Key Forward',
        'Forward Pocket Left',
        'Forward Pocket Right',
        'Full Forward',
        'Forward Pocket',
      ],
      'FL',
    ),
    FOL: filterAndMapPlayers(['Ruck', 'Ruck Rover', 'Rover'], 'FOL'),
    IC: filterAndMapPlayers(
      [
        'Defender',
        'Key Defender',
        'Back Pocket Left',
        'Back Pocket Right',
        'Full Back',
        'Half Back Flank Right',
        'Centre Half Back',
        'Half Back Flank Left',
        'Midfielder',
        'Forward',
        'Key Forward',
        'Half Forward Flank R',
        'Half Forward Flank L',
        'Wing Right',
        'Wing Left',
        'Centre Half Forward',
        'Forward Pocket Left',
        'Forward Pocket Right',
        'Full Forward',
        'Forward Pocket',
        'Ruck',
        'Ruck Rover',
        'Rover',
        'Emergency',
      ],
      'IC',
    ),
  };
};

let data = {
  commText:
    "B0$   \\n73 - IND vs NZ, Dubai, CT 2025 Final   \\n65.1 - IND vs AUS, Dubai, CT 2025 SF   \\n62.3 - IND vs NZ, Dubai, CT 2025   \\n60 - PAK vs WI, Dhaka, QF, 1998   \\n60 - AFG vs PAK, Headingley, CWC 2019   \\n   \\nB1$   \\n2 - Australia (2006, 2009)   \\n2 - India (2013, 2025)   \\n1 - New Zealand (2000)   \\n1 - South Africa (1998)   \\n1 - West Indies (2004)   \\n1 - Pakistan (2017)   \\nI0$  \\n   \\nB2$   \\nMatches: 24   \\nWon: 23   \\nLost: 1   \\nI1$  \\n   \\nB3$   \\nMatches: 34   \\nWins: 23   \\nDefeats: 8   \\nNR: 3   \\nW/L ratio: 2.875   \\nI2$  \\n  \\nB4$   \\n10 - India, Dubai (11 matches, 1 tied)   \\n10 - New Zealand, Dunedin    \\n7 - India, Indore   \\n7 - Pakistan, Hyderabad (Niaz Stadium, Pakistan)   \\n   \\nThis was India's seventh straight ODI win over New Zealand   \\n   \\nB5$ \\nClive Lloyd (West Indies, CWC 1975)   \\nRicky Ponting (Australia, CWC 2003)   \\nMS Dhoni (India, CWC 2011)   \\nRohit Sharma (India, CT 2025)",
  timestamp: 1741540949330,
  ballNbr: 0,
  inningsId: 2,
  event: 'NONE',
  batTeamName: 'IND',
  commentaryFormats: {
    bold: {
      formatId: ['B0$', 'B1$', 'B2$', 'B3$', 'B4$', 'B5$'],
      formatValue: [
        'Most overs of spin in an ICC tournament ODI game',
        'ICC Champions Trophy titles',
        'India in last three ICC white-ball tournaments',
        'India in Champions Trophy',
        'Most ODI wins without a defeat at a venue',
        "Captains with POTM award in Men's ICC tournament finals ",
      ],
    },
    italic: {
      formatId: ['I0$', 'I1$', 'I2$'],
      formatValue: [
        'The 2002 edition was shared between India and Sri Lanka.',
        '*CWC, T20 WC and CT',
        "No other team has more than 15 wins in the competition's history, and the next best win-loss ratio is Australia's 1.444.",
      ],
    },
  },
};

export function formatCommentary(data?: CommentaryItemAPI): string {
  let { T: commText, commentaryFormats } = data || {};

  // Replace \n with <br /> to handle line breaks in HTML
  commText = commText?.replace(/\\n/g, '<br />');

  // Iterate over bold and italic formats
  for (const formatType in commentaryFormats) {
    if (Object.prototype.hasOwnProperty.call(commentaryFormats, formatType)) {
      const { formatId, formatValue } =
        commentaryFormats[formatType as keyof CommentaryFormat];

      formatId.forEach((id, index) => {
        const value =
          formatType === 'bold'
            ? `<b>${formatValue[index]}</b>`
            : formatType === 'italic'
              ? `<i>${formatValue[index]}</i>`
              : `${formatValue[index]}`;
        const regex = new RegExp(id.replace('$', '\\$'), 'g');
        commText = commText?.replace(regex, value);
      });
    }
  }

  return commText || '';
}

export function formatScoreCommentary(data?: CricketCommentary): string {
  if (!data) return '';

  let { T: commText, commentaryFormats } = data;

  // Handle null or undefined commentary text
  if (!commText) return '';

  // Replace \n with <br /> for proper line breaks in HTML
  commText = commText.replace(/\n/g, '<br />');

  // Process formatting for bold and italic
  if (commentaryFormats) {
    Object.entries(commentaryFormats).forEach(
      ([formatType, { formatId, formatValue }]) => {
        formatId.forEach((id, index) => {
          let formattedValue = formatValue[index];

          if (formatType === 'bold') {
            formattedValue = `<b>${formattedValue}</b>`;
          } else if (formatType === 'italic') {
            formattedValue = `<i>${formattedValue}</i>`;
          }

          const regex = new RegExp(id.replace('$', '\\$'), 'g');
          commText = commText.replace(regex, formattedValue);
        });
      },
    );
  }

  return commText;
}

export const getFooterText = (data: any, toss: any, choose: any) => {
  if (toss === 1 && choose === 1) {
    return `${data?.homeTeam?.name} (Batting)`;
  } else if (toss === 1 && choose === 2) {
    return `${data?.homeTeam?.name} (Bowling)`;
  } else if (toss === 2 && choose === 1) {
    return `${data?.awayTeam?.name} (Batting)`;
  } else if (toss === 2 && choose === 2) {
    return `${data?.awayTeam?.name} (Bowling)`;
  } else return '';
};

export const renderCricketCommentaryEventText = (
  event: SportEventCricketCommentaryEvent,
) => {
  let text: string = '';
  switch (event) {
    case 'FOUR':
      text = '4';
      break;
    case 'SIX':
      text = '6';
      break;
    case 'WICKET':
      text = 'W';
      break;
    default:
      text = '0';
  }

  return text;
};

export const formatCricketLineup = (cricketLineups: any[], teamId: number) => {
  const players = cricketLineups
    ?.filter((player) => player?.teamId === teamId)
    ?.map((player) => ({
      name: player?.CricketPlayer?.name,
      role: player?.CricketPlayer?.role,
    }));
  // const captain = 'Rohit Sharma'; // Set the captain manually
  const wicketKeeper = players?.find((player) =>
    player?.role?.includes('WK'),
  )?.name; // Get first WK player
  return players
    ?.map((player) => {
      // if (player.name === captain) return `${player.name} (c)`;
      if (player?.name === wicketKeeper) return `${player?.name} (wk)`;
      return player?.name;
    })
    .join(', ');
};

export function extractSoccerPlayerName(text: string): string | null {
  const match = text.match(/-\s(.+)$/);
  return match ? match[1].trim() : null;
}
