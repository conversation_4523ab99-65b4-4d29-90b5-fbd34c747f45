import type { Meta, StoryObj } from '@storybook/react';

import SharePopup from './SharePopup';
import ShareIcon from '@/components/Icons/Share/ShareIcon';

const meta = {
  title: 'Components/SharePopup',
  component: SharePopup,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
  },
  decorators: [
    (Story) => (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof SharePopup>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    isOpen: false,
    onClose: () => {},
    children: <ShareIcon />,
  },
};
