import { generateUniqueId } from '@/lib/utils';
import './barChart.scss';

import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

interface BarConfig {
  dataKey: string;
  color: string;
  label?: string;
}

interface MyBarChartProps {
  data: Record<string, any>[];
  bars: BarConfig[];
  xAxisKey: string;
}

// Custom tooltip to show dynamic data based on bars
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload?.length) {
    return (
      <div className="bg-white p-1.5 rounded-[6px] shadow-[0px_1px_3px_0px_#0000002b]">
        <p className="text-[11.42px] leading-[14px] font-inter font-medium text-secondary-100">
          {`Rd: ${label}`}
        </p>
        {payload.map((item: any, index: number) => (
          <p
            key={generateUniqueId()}
            className="text-[11.42px] leading-[14px] font-inter font-normal text-black-100"
          >
            {`${item?.name}: ${item?.value}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const CustomLegend = ({ payload }: any) => {
  return (
    <div className="flex items-center justify-center gap-2">
      {payload?.map((entry: any, index: any) => (
        <div key={generateUniqueId()} className="flex items-center space-x-2">
          <span
            className="w-3 h-3 rounded-[3px]"
            style={{ backgroundColor: entry?.color }}
          ></span>
          <span className="text-[11.42px] leading-[14px] text-black-100 font-inter font-normal capitalize">
            {entry?.value}
          </span>
        </div>
      ))}
    </div>
  );
};

const MyBarChart: React.FC<MyBarChartProps> = ({ data, bars, xAxisKey }) => {
  return (
    <div className="bar-chart-wrap flex justify-center items-center mt-3">
      <ResponsiveContainer width="100%" height={250}>
        <BarChart data={data}>
          <CartesianGrid
            strokeDasharray="0"
            stroke="#D4D6D8"
            vertical={false}
          />
          <XAxis
            dataKey={xAxisKey}
            fontSize={12}
            fontFamily="Inter"
            color="#989898"
            padding={{ left: 16, right: 16 }}
          />
          <YAxis fontSize={12} fontFamily="Inter" color="#989898" />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{ fill: 'transparent' }}
          />
          <Legend content={<CustomLegend />} />
          {bars?.map((bar, index) => (
            <Bar
              key={generateUniqueId()}
              dataKey={bar?.dataKey}
              fill={bar?.color}
              name={bar?.label ?? bar?.dataKey}
              radius={8}
              isAnimationActive={false}
              label={{ position: 'top' }}
            />
          ))}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default MyBarChart;
