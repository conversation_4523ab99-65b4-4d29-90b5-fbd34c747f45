import React from 'react';

const CommentaryIcon = () => {
  return (
    <svg
      id="icon_commentary"
      xmlns="http://www.w3.org/2000/svg"
      width="26"
      height="26"
      viewBox="0 0 26 26"
    >
      <rect
        id="Rectangle_73869"
        data-name="Rectangle 73869"
        width="26"
        height="26"
        fill="none"
      />
      <g
        id="Group_143740"
        data-name="Group 143740"
        transform="translate(3.296 3.971)"
      >
        <path
          id="Path_259189"
          data-name="Path 259189"
          d="M13.645,18.589c3.659,0,5.49,0,6.626-1.142s1.137-2.978,1.137-6.652,0-5.512-1.137-6.652S17.3,3,13.645,3H9.763C6.1,3,4.274,3,3.137,4.142S2,7.12,2,10.794s0,5.512,1.137,6.652a3.261,3.261,0,0,0,2.408.985"
          transform="translate(-2 -3)"
          fill="none"
          stroke="#4455c7"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          id="Path_259190"
          data-name="Path 259190"
          d="M12,11v.01"
          transform="translate(-2.296 -3.178)"
          fill="none"
          stroke="#4455c7"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          id="Path_259191"
          data-name="Path 259191"
          d="M8,11v.01"
          transform="translate(-2.178 -3.178)"
          fill="none"
          stroke="#4455c7"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          id="Path_259192"
          data-name="Path 259192"
          d="M16,11v.01"
          transform="translate(-2.414 -3.178)"
          fill="none"
          stroke="#4455c7"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
        <path
          id="Path_259193"
          data-name="Path 259193"
          d="M14,19a8.8,8.8,0,0,0-3.841,1.145c-2,1.037-3,1.556-3.489,1.225s-.4-1.355-.212-3.4L6.5,17.5"
          transform="translate(-2.207 -3.411)"
          fill="none"
          stroke="#4455c7"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
      </g>
    </svg>
  );
};

export default CommentaryIcon;
