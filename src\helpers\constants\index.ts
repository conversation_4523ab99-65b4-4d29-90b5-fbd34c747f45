import { footballPlayersByRole } from '../../../types/football';
import { RugbyLeaguePlayersByRole } from '../../../types/rugby-league';
import type { PlayersByRole } from '../context/createTeamContext';

export const MIN_MAX_NUMBER = 'Number should be 5 to 10 digit';
type RoleKey = keyof PlayersByRole; // 'WKP' | 'BAT' | 'BOW' | 'ALL'

type RugbyRoleKey = keyof RugbyLeaguePlayersByRole;
export const ALL_ROLES: RoleKey[] = ['ALL', 'BAT', 'BOW', 'WKP'];
export const ALL_RUGBY_ROLES: RugbyRoleKey[] = [
  'BAC',
  'BR',
  'FRF',
  'HAL',
  'IC',
];
type AFLRoleKey = keyof footballPlayersByRole;
export const ALL_AFL_ROLES: AFLRoleKey[] = [
  'BL',
  'HBL',
  'MID',
  'HFL',
  'FL',
  'FOL',
  'IC',
];
export const LIVE_POLLIN_TIME = 15000;
export const LOCK_OUT_TIME = 0;
export const SPORTS = {
  cricket: 'cricket',
  afl: 'afl',
  nrl: 'nrl',
};

export const NAVIGATE_COINS_TEXT = 'Choose a coin package that suits you.';
export const USER_COINS_ENTER_TEXT =
  'Use Coins to Enter: Each match has an entry fee of 100 coins paid with your SmartCoins.';
export const USER_STEP_TEXT =
  'Finish in the Top 20 Percent Standings to win a share of the prize pool:';

export const AFL_SMARTPLAY_STATS_LEGEND = [
  { column: 'Scoring', label: 'GLS', text: 'Goals' },
  { column: 'Scoring', label: 'BE', text: 'Behind' },
  { column: 'Scoring', label: 'GA', text: 'Goal Assist' },

  { column: 'Disposal', label: 'K', text: 'Kick' },
  { column: 'Disposal', label: 'HB', text: 'Handball' },
  { column: 'Disposal', label: 'ED', text: 'Effective Disposal' },

  { column: 'Marking', label: 'M', text: 'Mark' },
  { column: 'Marking', label: 'CM', text: 'Contested Mark' },
  { column: 'Marking', label: 'Mi50', text: 'Inside 50 Mark' },

  { column: 'Ruck', label: 'HO', text: 'Hit-Out' },
  { column: 'Ruck', label: 'CLR', text: 'Clearance' },
  { column: 'Ruck', label: 'CC', text: 'Centre Clearance' },
  { column: 'Ruck', label: 'HA', text: 'Hit outs to Advantage' },

  { column: 'General Play', label: 'i50s', text: 'Inside 50 Entry' },
  { column: 'General Play', label: 'R50s', text: 'Rebound 50' },
  { column: 'General Play', label: 'MG', text: 'Meters Gained' },

  { column: 'Penalty', label: 'C', text: 'Clanger (Turnover)' },
  { column: 'Penalty', label: 'P50m', text: '50m Penalty Conceded' },

  { column: 'Tackling and Defensive', label: 'TAC', text: 'Tackles' },
  { column: 'Tackling and Defensive', label: 'IM', text: 'Intercept Mark' },
  { column: 'Tackling and Defensive', label: 'S', text: 'Spoils' },
  { column: 'Tackling and Defensive', label: 'FKF', text: 'Free Kick For' },
  { column: 'Tackling and Defensive', label: 'FKA', text: 'Free Kick Against' },

  {
    column: 'Bonus',
    label: 'TB',
    text: 'Tackles Bonus (10+ Tackles in a Game)',
  },
  { column: 'Bonus', label: 'GB', text: 'Goals Bonus (5+ Goals in a Game)' },
];

export const AFL_PLAYER_STATS_LEGEND = [
  { column: 'Disposals', label: 'D', text: 'Disposals' },
  { column: 'Disposals', label: 'K', text: 'Kicks' },
  { column: 'Disposals', label: 'HB', text: 'Handballs' },
  { column: 'Disposals', label: 'i50s', text: 'Inside 50s' },
  { column: 'Disposals', label: 'DE%', text: 'Disposals Efficiency %' },
  { column: 'Disposals', label: 'C', text: 'Clangers' },
  { column: 'Disposals', label: 'R50s', text: 'Rebound 50s' },
  { column: 'Disposals', label: 'EK', text: 'Effective Kicks' },
  { column: 'Disposals', label: 'KE%', text: 'Kick Efficiency %' },
  { column: 'Disposals', label: 'KHB', text: 'Kick to Handball Ratio' },
  { column: 'Disposals', label: 'ED', text: 'Effective Disposals' },

  { column: 'Defence', label: 'TAC', text: 'Tackles' },
  { column: 'Defence', label: 'Ti50', text: 'Tackles Inside 50' },
  { column: 'Defence', label: 'PA', text: 'Pressure Acts' },
  { column: 'Defence', label: 'DHPA', text: 'Defensive Half Pressure Acts' },
  { column: 'Defence', label: 'S', text: 'Spoils' },
  { column: 'Defence', label: 'CDO', text: 'Contested Defensive One on Ones' },
  { column: 'Defence', label: 'CDL', text: 'Contested Defensive Losses' },
  { column: 'Defence', label: 'CDL%', text: 'Contested Defensive Loss %' },

  { column: 'Possessions', label: 'CP', text: 'Contested Possessions' },
  { column: 'Possessions', label: 'UP', text: 'Uncontested Possessions' },
  { column: 'Possessions', label: 'IP', text: 'Intercept Possessions' },
  { column: 'Possessions', label: 'TU', text: 'Turnovers' },
  { column: 'Possessions', label: 'CPR', text: 'Contested Possession Rate' },
  { column: 'Possessions', label: 'GBG', text: 'Ground Ball Gets' },
  {
    column: 'Possessions',
    label: 'F50GBG',
    text: 'Forward 50 Ground Ball Gets',
  },

  { column: 'Stoppages', label: 'HO', text: 'Hit Outs' },
  { column: 'Stoppages', label: 'CC', text: 'Centre Clearances' },
  { column: 'Stoppages', label: 'SC', text: 'Stoppage Clearances' },
  { column: 'Stoppages', label: 'CLR', text: 'Clearances' },
  { column: 'Stoppages', label: 'HA', text: 'Hit Outs to Advantage' },
  { column: 'Stoppages', label: 'HW%', text: 'Hit Outs Win %' },
  { column: 'Stoppages', label: 'HA%', text: 'Hit Outs to Advantage %' },
  { column: 'Stoppages', label: 'RC', text: 'Ruck Contests' },

  { column: 'Scoring', label: 'G', text: 'Goals' },
  { column: 'Scoring', label: 'BE', text: 'Behinds' },
  { column: 'Scoring', label: 'GA', text: 'Goal Assists' },
  { column: 'Scoring', label: 'GA%', text: 'Shot at Goal Accuracy %' },
  { column: 'Scoring', label: 'SH', text: 'Shots at Goal' },
  { column: 'Scoring', label: 'SI', text: 'Score Involvements' },
  { column: 'Scoring', label: 'SL', text: 'Score Launches' },

  { column: 'Marks', label: 'M', text: 'Marks' },
  { column: 'Marks', label: 'Mi50', text: 'Marks Inside 50' },
  { column: 'Marks', label: 'CM', text: 'Contested Marks' },
  { column: 'Marks', label: 'ML', text: 'Marks on Lead' },
  { column: 'Marks', label: 'IM', text: 'Intercept Marks' },
];

export const SOCCER_PLAYER_STATS_LEGEND = [
  { column: 'Basic', label: 'G', text: 'Goals' },
  { column: 'Basic', label: 'A', text: 'Assists' },
  { column: 'Basic', label: 'M', text: 'Minutes' },
  { column: 'Basic', label: 'YC', text: 'Yellow Cards' },
  { column: 'Basic', label: 'RC', text: 'Red Cards' },

  { column: 'Attacking', label: 'S', text: 'Shots' },
  { column: 'Attacking', label: 'SOT', text: 'Shots on Target' },
  { column: 'Attacking', label: 'P', text: 'Passes' },
  { column: 'Attacking', label: 'KP', text: 'Key Passes' },
  { column: 'Attacking', label: 'C', text: 'Crosses' },
  { column: 'Attacking', label: 'D', text: 'Dribbles' },
  { column: 'Attacking', label: 'F', text: 'Fouls' },
  { column: 'Attacking', label: 'FW', text: 'Fouls Won' },

  { column: 'Defending', label: 'T', text: 'Tackles' },
  { column: 'Defending', label: 'I', text: 'Interceptions' },
  { column: 'Defending', label: 'B', text: 'Blocks' },
  { column: 'Defending', label: 'CL', text: 'Clearances' },
  { column: 'Defending', label: 'H', text: 'Headed Clearances' },

  { column: 'Goalkeeping', label: 'SV', text: 'Saves' },
  { column: 'Goalkeeping', label: 'CS', text: 'Clean Sheets' },
  { column: 'Goalkeeping', label: 'GC', text: 'Goals Conceded' },
  { column: 'Goalkeeping', label: 'PS', text: 'Penalties Saved' },
  { column: 'Goalkeeping', label: 'PC', text: 'Penalties Conceded' },
];
