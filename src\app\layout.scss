.wrapper {
  .Main-Wrap {
    background-image: url('../assets/images/banner/main-bg-1.webp') !important;

    @media (max-width: 1024px) {
      background-image: none !important;
    }

    .container {
      margin: 0 auto;
      position: relative;
      max-width: 1920px;

      .external-wrapper {
        .main-full-layout {
          height: calc(100vh - 64px);
          overflow-y: auto;

          @media (max-width: 799px) {
            height: calc(100vh - 48px);
          }
        }

        .full-layout {
          max-width: 1374px;
          margin: 0px auto;
          // height: calc(100vh - 194px);
          // overflow-y: auto;

          // @media (max-width: 799px) {
          //   height: calc(100vh - 178px);
          // }
        }

        .main-full-layout::-webkit-scrollbar {
          width: 0px;
        }

        .main-full-layout::-webkit-scrollbar-thumb {
          display: none;
        }

        .content {
          padding: 0px 12px;
        }
      }
    }
  }

  .desktop-odds-select {
    .select__control {
      .select__indicator {
        svg {
          path {
            fill: #000000 !important;
          }
        }
      }
    }
  }
}

.login-required-dialog {
  max-width: 1044px;
  width: 100%;
  padding: 12px;
  background: transparent;
  overflow: auto;
  box-shadow: none;

  .login-dialog {
    background-image: url('../assets/images/auth/login-popup-desktopV2.png');
    border-radius: 8px;
    border: 1px solid #fc4714;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    background-color: #ffffff;
    min-height: 553px;

    @media (max-width: 1023px) {
      background-image: url('../assets/images/auth/login-popup-mobileV2.png');
      min-height: 600px;
      background-position: bottom;
    }
  }
}
