'use client';

import { DesktopDatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { parseISO } from 'date-fns';
import moment from 'moment-timezone';
import type { Dispatch, SetStateAction } from 'react';
import React from 'react';
import Select, { components } from 'react-select';

import DatepickerBG from '@/assets/images/icons/date-picker-bg.svg';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { Button } from '@/components/UI/button';

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const monthOption = [
  { value: 'All', label: 'All' },
  { value: '3 months', label: 'Last 3 months' },
  { value: '6 months', label: 'Last 6 months' },
  { value: '1 year', label: 'Last one year' },
];

interface TransactionFilterModal {
  activeTab: string;
  handleClose: () => void;
  setDateFrom: Dispatch<SetStateAction<string | null>>;
  dateFrom: string | null;
  setDateTo: Dispatch<SetStateAction<string | null>>;
  dateTo: string | null;
  setSelectedMonth: Dispatch<SetStateAction<string>>;
  selectedMonth: string;
  fetchHistoryData: (
    offset: any,
    type: any,
    dateFrom: any,
    dateTo: any,
  ) => void;
}
const TransactionFilterModalPage = ({
  activeTab,
  handleClose,
  setDateFrom,
  dateFrom,
  setDateTo,
  dateTo,
  setSelectedMonth,
  selectedMonth,
  fetchHistoryData,
}: TransactionFilterModal) => {
  const handleFromDateChange = (newValue: Date | null) => {
    // Convert Date to string if needed, or handle null value
    setDateFrom(
      newValue ? moment(newValue).tz(timezone).format('YYYY-MM-DD') : null,
    );
    setSelectedMonth('All');
  };

  const handleToDateChange = (newValue: Date | null) => {
    // Convert Date to string if needed, or handle null value

    setDateTo(
      newValue ? moment(newValue).tz(timezone).format('YYYY-MM-DD') : null,
    );
    setSelectedMonth('All');
  };
  const handleClearAll = () => {
    setSelectedMonth('All');
    setDateFrom(null);
    setDateTo(null);
    fetchHistoryData(
      0,
      activeTab === 'SmartCoins Activity' ? 'fantasy' : 'fantasyWithdraw',
      null,
      null,
    );
    handleClose();
  };

  const handleApplyFilter = () => {
    // Call fetchHistoryData function with the selected date range and month
    fetchHistoryData(
      0,
      activeTab === 'SmartCoins Activity' ? 'fantasy' : 'fantasyWithdraw',
      dateFrom,
      dateTo,
    );
    handleClose();
  };
  return (
    <div>
      <div className="p-3 border-b border-primary-200 ">
        <p className="text-[16px] leading-[19px] font-inter font-semibold text-black-100 mb-3">
          Sort by
        </p>
        <div className="mb-7">
          <Select
            className="React sort-select"
            value={monthOption?.find((item: any) => {
              return item?.value === selectedMonth;
            })}
            onChange={(e: any) => setSelectedMonth(e?.value)}
            options={monthOption}
            classNamePrefix="select"
            placeholder="Odds"
            isSearchable={false}
            components={{ DropdownIndicator }}
          />
        </div>
        <div>
          <p className="text-[16px] leading-[19px] font-inter font-semibold text-black-100 mb-3">
            Select date
          </p>
          <div className="mb-[18px]">
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DesktopDatePicker
                clearable
                autoOk
                // disableToolbar
                variant="inline"
                format="dd/MM/yyyy"
                placeholder="All"
                margin="normal"
                id="date-picker-inline"
                inputVariant="outlined"
                value={dateFrom ? parseISO(dateFrom) : null}
                onChange={(newValue) => handleFromDateChange(newValue)} // Ensure newValue is of type Date or null
                slots={{
                  openPickerIcon: DatepickerBG, // Custom icon
                }}
                slotProps={{
                  field: {
                    id: 'date-picker-inline',
                    // @ts-expect-error
                    placeholder: 'Date from',
                  },
                }}
                className="common-date-picker filter-date-picker"
              />
            </LocalizationProvider>
          </div>
          <div>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DesktopDatePicker
                clearable
                autoOk
                // disableToolbar
                variant="inline"
                format="dd/MM/yyyy"
                placeholder="All"
                margin="normal"
                id="date-picker-inline"
                inputVariant="outlined"
                value={dateTo ? parseISO(dateTo) : null}
                onChange={(newValue) => handleToDateChange(newValue)} // Ensure newValue is of type Date or null
                slots={{
                  openPickerIcon: DatepickerBG, // Custom icon
                }}
                slotProps={{
                  field: {
                    id: 'date-picker-inline',
                    // @ts-expect-error
                    placeholder: 'Date to',
                  },
                }}
                className="common-date-picker filter-date-picker"
              />
            </LocalizationProvider>
          </div>
        </div>
      </div>
      <div className="p-3 w-full">
        <Button className="w-full" onClick={() => handleApplyFilter()}>
          Apply
        </Button>
        <Button
          variant="link"
          className="w-full text-white underline mt-2"
          onClick={() => handleClearAll()}
        >
          Clear all
        </Button>
      </div>
    </div>
  );
};

export default TransactionFilterModalPage;
