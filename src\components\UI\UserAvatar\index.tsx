import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import React from 'react';

// Define the response structure from the API
interface RandomUserResponse {
  results: Array<{
    picture: {
      large: string;
      medium: string;
      thumbnail: string;
    };
  }>;
}

// Fetch random male user avatar from the API
const fetchRandomMaleAvatar = async (): Promise<string> => {
  const response = await fetch('https://randomuser.me/api/?gender=male');
  const data: RandomUserResponse = await response.json();
  return data.results[0].picture.thumbnail; // Return the thumbnail image URL
};

type UserAvatarProps = {
  userId: number | string;
  imageWidth?: number; // Optional width for the avatar image
  imageHeight?: number; // Optional height for the avatar image
};

const UserAvatar: React.FC<UserAvatarProps> = ({
  userId,
  imageWidth = 100, // Default width
  imageHeight = 100, // Default height
}) => {
  // Use react-query to fetch data and specify the return type for `data`
  const {
    data: avatarUrl,
    isLoading,
    isError,
  } = useQuery<string>({
    queryKey: ['random-user', userId],
    queryFn: fetchRandomMaleAvatar,
  });

  if (isLoading) {
    return <div className="text-white text-sm">Loading...</div>;
  }

  if (isError) {
    return <div className="text-red-500 text-sm">Error loading avatar</div>;
  }

  return (
    <div className="flex items-center justify-center">
      {avatarUrl && (
        <Image
          src={avatarUrl}
          alt="User Avatar"
          width={imageWidth}
          height={imageHeight}
          className="rounded-full"
          style={{ width: imageWidth, height: imageHeight }}
          unoptimized={true}
        />
      )}
    </div>
  );
};

export default UserAvatar;
