import { Button } from '@/components/UI/button';
import { Card } from '@/components/UI/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/UI/dialog';
import { useSoccerStore } from '@/store/soccer/useSoccerStore';
import { MoveRight, PlusIcon } from 'lucide-react';
import { ReactNode } from 'react';
import { cn, formatNumberWithCommas, LocalStorage } from '@/lib/utils';
import useScreen from '@/hooks/useScreen';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { useAuthContext } from '@/helpers/context/authContext';
import { SoccerPlayer } from '@/lib/types/soccer';
import { PUBLIC_IMAGES } from '@/lib/constants/constants';
import Image from 'next/image';

const generateReserveOrder = (orderLength: number) => {
  return Array.from({ length: orderLength }, (_, index) => ({
    name: `Reserve ${index + 1}`,
  }));
};

const ReserveOrder = ({ orderLength }: { orderLength: number }) => {
  const orders = generateReserveOrder(orderLength);
  return (
    <>
      {orders.map((order, index) => (
        <div key={index} className="flex space-x-2">
          <span>{order.name}</span>
          {index !== orderLength - 1 && (
            <span>
              <MoveRight />
            </span>
          )}
        </div>
      ))}
    </>
  );
};

const SoccerEmptyPlayerCard = ({
  isActive,
  playerIndex,
  setActivePlayerPosition,
  setOpenReserveModal,
}: {
  isActive: boolean;
  playerIndex: number;
  setActivePlayerPosition: (position: number) => void;
  setOpenReserveModal: (open: boolean) => void;
}) => {
  return (
    <Card className="p-4 bg-white shadow-sm border border-gray-100 rounded-lg md:w-40 w-[150px] relative h-20">
      <div className="flex justify-center items-centershadow-md rounded-full absolute top-[-15px] left-1/2 transform -translate-x-1/2">
        <Image
          src={PUBLIC_IMAGES.DEFAULT_PLAYER_IMAGE}
          alt="default player image"
        />
      </div>
      <div className=" rounded-md absolute bottom-[5px] left-1/2 transform -translate-x-1/2 p-2 py-1">
        <button
          className="flex justify-center items-center space-x-1 mt-2"
          onClick={() => {
            setOpenReserveModal(true);
          }}
        >
          <div className={cn(
            'text-white rounded-md',
            isActive ? 'bg-[#1C9A6C]' : 'bg-[#C9C9C9]',
          )}>
            <PlusIcon size={25} />
          </div>
        </button>
      </div>
    </Card>
  );
};

const SoccerReservePlayerModal = ({ children, setTeamSubmitConfirmation }: { children: ReactNode, setTeamSubmitConfirmation: (value: boolean) => void }) => {
  const { setLoginPopUp } = useAuthContext();
  const {
    soccerPlayersByRole,
    remainingBudget,
    setShowPlayerTable,
    showPlayerTable,
    // Add more soccer-specific state/actions as needed
  } = useSoccerStore();

  // Placeholder for reserve state (implement actual logic as needed)
  const reservePlayers: (SoccerPlayer | null)[] = [null, null, null];
  const reservePlayersLimit = 3;
  const openReserveModal = showPlayerTable;
  const setOpenReserveModal = setShowPlayerTable;
  const activePlayerPosition = 0;
  const setActivePlayerPosition = () => { };

  const { width } = useScreen();
  const isMobile = width < 768;

  // Placeholder handlers (implement actual logic as needed)
  const handleSaveReserves = () => {
    setOpenReserveModal(false);
    setTeamSubmitConfirmation(true);
  };
  const handleNotNow = () => {
    setOpenReserveModal(false);
  };

  if (isMobile) {
    return (
      <Drawer open={openReserveModal} onOpenChange={setOpenReserveModal}>
        <DrawerContent className='bg-white'>
          <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md">
            ADD RESERVES
          </div>
          <div className="p-2 space-y-2 mt-10  h-full ">
            <div className="grid grid-cols-2 gap-2 mt-2">
              <Card className="p-3 shadow-sm w-full bg-dark-card-gradient text-white">
                <div className="text-center">
                  <div className="text-sm text-slate-500 mb-1">
                    Remaining Salary:
                  </div>
                  <div className="font-semibold text-slate-900">
                    ${formatNumberWithCommas(remainingBudget)}
                  </div>
                </div>
              </Card>
              <Card className={cn(
                'p-3 shadow-sm  text-white w-full',
                reservePlayers.filter((player) => player !== null).length === reservePlayersLimit
                  ? 'bg-[#D1E7DF] border border-[#B4DBCD]'
                  : 'bg-dark-card-gradient',
              )}>
                <div className="text-center">
                  <div className={cn(
                    'text-sm mb-1',
                    reservePlayers.filter((player) => player !== null).length === reservePlayersLimit
                      ? 'text-black-700'
                      : 'text-slate-500',
                  )}>
                    Selected reserves:
                  </div>
                  <div className={cn(
                    'font-semibold',
                    reservePlayers.filter((player) => player !== null).length === reservePlayersLimit
                      ? 'text-green-700'
                      : 'text-slate-500',
                  )}>
                    {reservePlayers.filter((player) => player !== null).length || 0}/{reservePlayersLimit}
                  </div>
                </div>
              </Card>
            </div>
            <div className="grid md:grid-cols-4 grid-cols-2 gap-4 w-full mt-5">
              {reservePlayers.map((player, index) => (
                <div key={index} className="flex flex-col gap-2 items-center mt-4">
                  {player ? (
                    <Card className="p-4">{player.name}</Card>
                  ) : (
                    <SoccerEmptyPlayerCard
                      isActive={index === activePlayerPosition}
                      playerIndex={index}
                      setActivePlayerPosition={setActivePlayerPosition}
                      setOpenReserveModal={setOpenReserveModal}
                    />
                  )}
                  <div className="text-center text-sm text-slate-500">
                    Reserve {index + 1}
                  </div>
                </div>
              ))}
            </div>
            <div className='h-[200px] overflow-y-auto'>
              {children}
            </div>
            <div className='flex gap-2 mt-5 pb-4'>
              <Button type="submit" variant={'outline'} className="w-full" onClick={handleNotNow}>
                Not now
              </Button>
              <Button type="submit" className="w-full" onClick={handleSaveReserves}>
                Save reserves
              </Button>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <div className={cn(
      'fixed inset-0 flex items-center justify-center z-50 bg-black-100 bg-opacity-50',
      openReserveModal ? 'block' : 'hidden'
    )}>
      <Dialog open={openReserveModal} onOpenChange={setOpenReserveModal}>
        <DialogContent className="max-w-[804px] border-none">
          <DialogHeader>
            <DialogTitle className="text-[43.9px] !font-normal font-apotekCompRegular px-4 bg-gray-100">
              ADD RESERVES
            </DialogTitle>
            <DialogDescription className="px-4 text-[16px]">
              <p>
                Add reserve players to fill in for any unannounced or substitute
                players of your team.
              </p>
              <div className="flex">
                <p> Reserve priority order:</p>
                <div className="grid grid-cols-3 gap-2 px-2 mb-5">
                  <ReserveOrder orderLength={reservePlayersLimit} />
                </div>
              </div>
              <div className="grid md:grid-cols-3 grid-cols-2 gap-4 w-full">
                {reservePlayers.map((player, index) => (
                  <div key={index} className="flex flex-col gap-2 items-center">
                    {player ? (
                      <Card className="p-4">{player.name}</Card>
                    ) : (
                      <SoccerEmptyPlayerCard
                        isActive={index === activePlayerPosition}
                        playerIndex={index}
                        setActivePlayerPosition={setActivePlayerPosition}
                        setOpenReserveModal={setOpenReserveModal}
                      />
                    )}
                    <div className="text-center text-sm text-slate-500">
                      Reserve {index + 1}
                    </div>
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <Card className="p-3 shadow-sm w-full bg-dark-card-gradient text-white">
                  <div className="text-center">
                    <div className="text-xs text-[#BFCCD8] mb-1">
                      Remaining Salary:
                    </div>
                    <div className="font-semibold text-slate-900">
                      ${formatNumberWithCommas(remainingBudget)}
                    </div>
                  </div>
                </Card>
                <Card className={cn(
                  'p-3 shadow-sm  text-white w-full',
                  reservePlayers.filter((player) => player !== null).length === reservePlayersLimit
                    ? 'bg-[#D1E7DF] border border-[#B4DBCD]'
                    : 'bg-dark-card-gradient',
                )}>
                  <div className="text-center">
                    <div className={cn(
                      'text-xs text-[#BFCCD8] mb-1',
                      reservePlayers.filter((player) => player !== null).length === reservePlayersLimit
                        ? 'text-black-700'
                        : 'text-slate-500',
                    )}>
                      Selected reserves:
                    </div>
                    <div className={cn(
                      'font-semibold',
                      reservePlayers.filter((player) => player !== null).length === reservePlayersLimit
                        ? 'text-green-700'
                        : 'text-slate-500',
                    )}>
                      {reservePlayers.filter((player) => player !== null).length || 0}/{reservePlayersLimit}
                    </div>
                  </div>
                </Card>
              </div>
            </DialogDescription>
          </DialogHeader>
          <div className="px-4 h-[250px] overflow-y-auto">{children}</div>
          <DialogFooter className="p-4 grid grid-cols-2 place-items-center">
            <Button type="submit" variant={'outline'} className="w-full" onClick={handleNotNow}>
              Not now
            </Button>
            <Button type="submit" className="w-full" onClick={handleSaveReserves}>
              Save reserves
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SoccerReservePlayerModal; 