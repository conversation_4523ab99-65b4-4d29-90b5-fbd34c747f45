'use client';
import { MenubarContent } from '@/components/UI/menubar';
import { Badge } from '@/components/UI/badge';
import { cn } from '@/lib/utils';
import { FootballPlayer, Player } from '../../../../types/competitions';
import { RugbyPlayer } from '../../../../types/rugby-league';
import { SoccerPlayer } from '@/helpers/context/soccer/createSoccerTeamContext';
// Generic MobileMenuContent component
const MobileFootballMenuContent = <
  T extends Player | FootballPlayer | RugbyPlayer | SoccerPlayer,
>({
  player,
  dreamTeamIdentifier,
  isAddMore,
  handleEditTeam,
  handleSetCaptain,
  handleSetViceCaptain,
  handleRemovePlayer,
  isReserveType = false,
}: {
  player: T; // Generic player type constrained to Player
  dreamTeamIdentifier: string | null;
  isAddMore: string | null;
  handleEditTeam: () => void;
  handleSetCaptain: () => void;
  handleSetViceCaptain: () => void;
  handleRemovePlayer: () => void;
  isReserveType?: boolean;
}) => {
  const isInDreamTeam: boolean = !!dreamTeamIdentifier && isAddMore !== 'true';

  const renderBadge = (
    label: string,
    onClick: () => void,
    isDisabled: boolean,
    bgColor: string,
  ) => (
    <Badge
      className={cn(
        'text-white text-[9px] p-0 px-1 cursor-pointer',
        isDisabled ? 'bg-gray-200' : bgColor,
      )}
      onClick={onClick}
    >
      {label}
    </Badge>
  );

  return (
    <MenubarContent
      align="center"
      className="bg-transparent block lg:hidden"
      sideOffset={-30}
    >
      <div className="bg-[#1e2c3a] rounded-lg p-3 flex gap-1 relative">
        <div className="flex justify-between items-start w-full">
          <div className="flex flex-col">
            <span className="text-white text-xs font-semibold">
              {player.name}
            </span>
            <span className="text-gray-400 text-[9px]">{player.teamName}</span>
            <span className="text-gray-400 text-[9px]">{player.role}</span>
          </div>
        </div>
        <div className="w-full space-y-1">
          {!isReserveType && (
            <>
              <div className="flex justify-end space-x-1 w-full">
                {renderBadge(
                  'Captain',
                  () => {
                    if (isAddMore === 'true') {
                      handleSetCaptain();
                    }
                  },
                  isInDreamTeam,
                  'bg-secondary-100',
                )}
              </div>
              <div className="flex justify-end">
                {renderBadge(
                  'Vice Captain',
                  () => {
                    if (isAddMore === 'true') {
                      handleSetViceCaptain();
                    }
                  },
                  isInDreamTeam,
                  'bg-secondary-100',
                )}
              </div>
              <div className="flex justify-end">
                {isInDreamTeam
                  ? renderBadge(
                    'Substitute',
                    handleEditTeam,
                    false,
                    'bg-secondary-100',
                  )
                  : renderBadge(
                    'Remove',
                    handleRemovePlayer,
                    false,
                    'bg-orange-400',
                  )}
              </div>
            </>
          )}

          {isReserveType && (
            <div className="flex justify-end space-x-1 w-full">
              {renderBadge(
                'Substitute',
                handleRemovePlayer,
                false,
                'bg-orange-400',
              )}
            </div>
          )}
        </div>
      </div>
    </MenubarContent>
  );
};

export default MobileFootballMenuContent;
