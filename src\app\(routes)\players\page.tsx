'use client';
import React, { useState } from 'react';

import ActiveAFLIcon from '@/assets/images/sportTabIcon/activeAFLIcon.svg';
import ActiveCricketIcon from '@/assets/images/sportTabIcon/activeCricketIcon.svg';
import ActiveNBAIcon from '@/assets/images/sportTabIcon/activeNBAIcon.svg';
import ActiveNBLIcon from '@/assets/images/sportTabIcon/activeNBLIcon.svg';
import ActiveNFLIcon from '@/assets/images/sportTabIcon/activeNFLIcon.svg';
import ActiveNRLIcon from '@/assets/images/sportTabIcon/activeNRLIcon.svg';
import ActiveSoccerIcon from '@/assets/images/sportTabIcon/activeSoccerIcon.svg';
import AFLIcon from '@/assets/images/sportTabIcon/AFLIcon.svg';
import CricketIcon from '@/assets/images/sportTabIcon/cricketIcon.svg';
import MobileActiveAFLIcon from '@/assets/images/sportTabIcon/mobileActiveAFLIcon.svg';
import MobileActiveCricketIcon from '@/assets/images/sportTabIcon/mobileActiveCricketIcon.svg';
import MobileActiveNBAIcon from '@/assets/images/sportTabIcon/mobileActiveNBAIcon.svg';
import MobileActiveNBLIcon from '@/assets/images/sportTabIcon/mobileActiveNBLIcon.svg';
import MobileActiveNFLIcon from '@/assets/images/sportTabIcon/mobileActiveNFLIcon.svg';
import MobileActiveNRLIcon from '@/assets/images/sportTabIcon/mobileActiveNRLIcon.svg';
import MobileActiveSoccerIcon from '@/assets/images/sportTabIcon/mobileActiveSoccerIcon.svg';
import MobileAFLIcon from '@/assets/images/sportTabIcon/mobileAFLIcon.svg';
import MobileCricketIcon from '@/assets/images/sportTabIcon/mobileCricketIcon.svg';
import MobileNBAIcon from '@/assets/images/sportTabIcon/mobileNBAIcon.svg';
import MobileNBLIcon from '@/assets/images/sportTabIcon/mobileNBLIcon.svg';
import MobileNFLIcon from '@/assets/images/sportTabIcon/mobileNFLIcon.svg';
import MobileNRLIcon from '@/assets/images/sportTabIcon/mobileNRLIcon.svg';
import MobileSoccerIcon from '@/assets/images/sportTabIcon/mobileSoccerIcon.svg';
import NBAIcon from '@/assets/images/sportTabIcon/NBAIcon.svg';
import NBLIcon from '@/assets/images/sportTabIcon/NBLIcon.svg';
import NFLIcon from '@/assets/images/sportTabIcon/NFLIcon.svg';
import NRLIcon from '@/assets/images/sportTabIcon/NRLIcon.svg';
import SoccerIcon from '@/assets/images/sportTabIcon/soccerIcon.svg';
import PlayerDetails from '@/components/Players/playerDetails';
import Breadcrumbs from '@/components/UI/Breadcrumbs';

const Players = () => {
  const [activeTab, setActiveTab] = useState<number>(1);

  const breadcrumbsLinks = [
    { label: 'Home', href: '/' },
    { label: 'SmartPlay', href: '#' },
    { label: 'Players', href: '#' },
  ];

  const tabs = [
    {
      label: 'Cricket',
      labelId: 1,
      icon: <CricketIcon />,
      mobileIcon: <MobileCricketIcon />,
      activeIcon: <ActiveCricketIcon />,
      mobileActiveIcon: <MobileActiveCricketIcon />,
      comingSoon: false,
      title: 'Coming Soon',
    },
    {
      label: 'AFL',
      labelId: 2,
      icon: <AFLIcon />,
      mobileIcon: <MobileAFLIcon />,
      activeIcon: <ActiveAFLIcon />,
      mobileActiveIcon: <MobileActiveAFLIcon />,
      comingSoon: true,
      title: 'Coming Soon',
    },
    {
      label: 'NRl',
      labelId: 3,
      icon: <NRLIcon />,
      mobileIcon: <MobileNRLIcon />,
      activeIcon: <ActiveNRLIcon />,
      mobileActiveIcon: <MobileActiveNRLIcon />,
      comingSoon: true,
      title: 'Coming Soon',
    },
    {
      label: 'Soccer',
      labelId: 4,
      icon: <SoccerIcon />,
      mobileIcon: <MobileSoccerIcon />,
      activeIcon: <ActiveSoccerIcon />,
      mobileActiveIcon: <MobileActiveSoccerIcon />,
      comingSoon: true,
      title: 'Coming Soon',
    },
    {
      label: 'NFL',
      labelId: 5,
      icon: <NFLIcon />,
      mobileIcon: <MobileNFLIcon />,
      activeIcon: <ActiveNFLIcon />,
      mobileActiveIcon: <MobileActiveNFLIcon />,
      comingSoon: true,
      title: 'Coming Soon',
    },
    {
      label: 'NBL',
      labelId: 6,
      icon: <NBLIcon />,
      mobileIcon: <MobileNBLIcon />,
      activeIcon: <ActiveNBLIcon />,
      mobileActiveIcon: <MobileActiveNBLIcon />,
      comingSoon: true,
      title: 'Coming Soon',
    },
    {
      label: 'NBA',
      labelId: 7,
      icon: <NBAIcon />,
      mobileIcon: <MobileNBAIcon />,
      activeIcon: <ActiveNBAIcon />,
      mobileActiveIcon: <MobileActiveNBAIcon />,
      comingSoon: true,
      title: 'Coming Soon',
    },
  ];

  const handleTabChanges = (id: number) => {
    setActiveTab(id);
  };

  return (
    <div>
      <div className="pt-[33px] max-799:pt-[18px] pb-[12px] max-799:pb-[9px]">
        <Breadcrumbs links={breadcrumbsLinks} />
        <h1 className="text-[31.36px] max-799:text-[22.4px] max-799:leading-[28px] font-normal text-white max-1024:text-black-100 font-veneerCleanSoft">
          Players
        </h1>
      </div>
      <div className=" bg-off-white-200 max-1024:bg-white max-799:mx-[-12px]">
        <div className="w-full max-799:px-3">
          <div className="overflow-x-auto no-scrollbar no-scrollbar pb-2">
            <div className="flex gap-[18px] max-1024:gap-3 font-normal border-b-[3px] border-b-secondary-100 w-max min-w-full">
              {tabs?.map((tab) => (
                <button
                  key={tab.labelId}
                  className={`w-auto font-veneerCleanSoft ${tab?.labelId === activeTab && !tab?.comingSoon
                      ? 'border-b-[3px] bg-tab-active-gradient border-primary-200 text-secondary-100'
                      : 'border-b-[3px] border-transparent text-black-100 '
                    }`}
                  onClick={() =>
                    tab?.comingSoon === false && handleTabChanges(tab?.labelId)
                  }
                >
                  <div className="px-[34px] max-799:px-3 pt-[18px] max-799:pt-3 pb-2 relative">
                    <div className="max-799:mx-auto max-799:hidden block">
                      {tab?.labelId === activeTab ? tab?.activeIcon : tab?.icon}
                    </div>
                    <div className="tab-svg-icon max-799:mx-auto max-799:block hidden">
                      {tab?.labelId === activeTab
                        ? tab?.mobileActiveIcon
                        : tab?.mobileIcon}
                    </div>
                    {tab?.label}
                    {tab?.comingSoon && (
                      <div className="absolute left-1/2 bottom-[-20px] max-799:bottom-[-16px] transform -translate-x-1/2 -translate-y-1/2 w-max px-1.5 max-799:px-1 py-[2px] rounded-[6px] bg-negative-200 text-[11.42px] max-799:text-[7px] leading-[14px] max-799:leading-[9px] font-inter font-light text-white">
                        {tab?.title}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="mt-1.5 bg-off-white-200 shadow-[0px_1px_9px_0px_#0000002e] pt-[23px] max-799:pt-[15px] px-[27px] max-799:px-0 max-799:mx-[-12px] pb-[50px] max-799:pb-[45px]">
        <PlayerDetails />
      </div>
    </div>
  );
};

export default Players;
