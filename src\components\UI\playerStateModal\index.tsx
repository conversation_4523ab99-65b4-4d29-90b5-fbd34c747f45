import Image from 'next/image';
import React, { useState } from 'react';

import Team<PERSON>ogoAway from '@/assets/images/icons/adelaideStrikers.png';
import TeamLogo from '@/assets/images/icons/sydneySixers.png';
import CareerDetails from '@/components/Players/Career';
import SeasonDetails from '@/components/Players/Season';
import SummaryDetails from '@/components/Players/Summary';
import CustomDialog from '@/components/UI/CustomDialog';

import CustomTabs from '../CustomTab';

type PlayerInfo = {
  id: number;
  role: string;
  squadsId: number;
  tournamentId: number;
  playerId: number;
  teamName: string;
  teamId: number;
  playerName: string;
  image: string | null;
};

type Team = {
  id: number;
  name: string;
  gender: string;
  flag: string | null;
};

type CricketTournament = {
  id: number;
  name: string;
  gender: string;
};

type UpcomingEvent = {
  id: number;
  eventName: string;
  awayTeamId: number;
  homeTeamId: number;
  startTime: string;
  endTime: string;
  Eid: string;
  outrights: boolean;
  status: string;
  winnerCode: string | null;
  round: string | null;
  displayName: string | null;
  matchType: string;
  createdAt: string;
  updatedAt: string;
  SportId: number;
  CricketTournamentId: number;
  CricketSeasonId: number;
  CricketStadiumId: number;
  CricketCategoryId: number;
  CricketCategoryTypeId: number;
  awayTeam: Team;
  homeTeam: Team;
  CricketTournament: CricketTournament;
};

const PlayerStateModal = ({
  playerModal,
  handlePlayerDetailsModalClose,
}: {
  playerModal: boolean;
  handlePlayerDetailsModalClose: () => void;
}) => {
  const [playerStateActive, setPlayerStateActive] = useState<number>(1);

  const playerStateTabs = [
    {
      label: 'Summary',
      labelId: 1,
    },
    {
      label: 'Session Stats',
      labelId: 2,
    },
    {
      label: 'Career Stats',
      labelId: 3,
    },
  ];

  const renderPlayerDetails = () => {
    if (playerStateActive === 1) {
      return <SummaryDetails />;
    } else if (playerStateActive === 2) {
      return <SeasonDetails />;
    } else {
      return <CareerDetails />;
    }
  };

  return (
    <div>
      <CustomDialog
        isOpen={playerModal}
        onClose={handlePlayerDetailsModalClose}
        maxWidth={850}
        className="player-modal"
        outerClickClose={true}
        title={
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 max-799:gap-1">
              <div>
                <div className="px-[18px] max-799:px-3 py-[18px] max-799:py-3 bg-[#EE2F72] rounded-tl-[6px]">
                  <Image src={TeamLogo} alt="logo" unoptimized={true} />
                </div>
              </div>
              <div>
                <h6 className="text-[22.4px] max-799:text-[14px] leading-[31.36px] max-799:leading-[16px] font-inter font-semibold text-black-100 capitalize">
                  Josh Philippe
                </h6>
                <p className="text-[16px] max-799:text-[11.42px] leading-[19px] max-799:leading-[14px] font-inter font-normal text-black-100 capitalize mt-1.5">
                  Sydney Sixers | Wicketkeeper | Batsman
                </p>
              </div>
            </div>
            <div>
              <h6 className="text-[22.4px] max-799:text-[14px] leading-[31.36px] max-799:leading-[16px] font-inter font-semibold text-black-100 capitalize">
                $171,400
              </h6>
              <p className="text-[16px] max-799:text-[11.42px] leading-[19px] max-799:leading-[14px] font-inter font-normal text-negative-200 text-right capitalize mt-1.5">
                -$4k
              </p>
            </div>
          </div>
        }
      >
        <div>
          <div className="flex items-center justify-between px-[18px] max-799:px-3 py-[9px] bg-primary-200/15">
            <div className="flex items-center gap-2 max-799:gap-1">
              <div className="w-[36px] max-799:w-[30px] h-[36px] max-799:h-[30px] rounded-[50%]">
                <Image
                  src={TeamLogo}
                  alt="logo"
                  className="w-full h-full object-contain"
                  unoptimized={true}
                />
              </div>
              <p className="text-[14px] max-799:text-[12px]  leading-[16px] max-799:leading-[14px] text-black-100 font-inter font-normal max-799:w-min">
                Sydney Sixers
              </p>
            </div>
            <div className="text-center">
              <h6 className="text-[14px] max-799:text-[11.42px]  leading-[16px] max-799:leading-[14px] text-black-100 font-inter font-semibold mb-1">
                Upcoming Match
              </h6>
              <p className="text-[14px] max-799:text-[11.42px]  leading-[16px] max-799:leading-[14px] text-black-100 font-inter font-normal mb-1">
                [Comp_name] - Round 9
              </p>
              <p className="text-[14px] max-799:text-[11.42px]  leading-[16px] max-799:leading-[14px] text-black-100 font-inter font-normal">
                Fri 29/08/2024 | 09:50am
              </p>
            </div>
            <div className="flex items-center gap-2 max-799:gap-1">
              <p className="text-[14px] max-799:text-[12px]  leading-[16px] max-799:leading-[14px] text-black-100 font-inter font-normal max-799:text-center max-799:w-min">
                Adelaide strikers
              </p>
              <div className="w-[36px] max-799:w-[30px] h-[36px] max-799:h-[30px] rounded-[50%]">
                <Image
                  src={TeamLogoAway}
                  alt="logo"
                  className="w-full h-full object-contain"
                  unoptimized={true}
                />
              </div>
            </div>
          </div>
          <div className="pt-[9px] px-[18px] max-799:px-3 pb-[18px] bg-gray-300">
            <CustomTabs<number>
              tabs={playerStateTabs}
              setActiveTab={setPlayerStateActive}
              activeTab={playerStateActive}
            />
            <div>{renderPlayerDetails()}</div>
          </div>
        </div>
      </CustomDialog>
    </div>
  );
};

export default PlayerStateModal;
