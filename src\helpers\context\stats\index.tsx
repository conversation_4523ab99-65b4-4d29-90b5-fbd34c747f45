'use client';
import { createContext, useContext, useMemo } from 'react';
import { StatsContextType } from '../../../../types';
import { useQuery } from '@tanstack/react-query';
import {
  getAFLPlayerStats,
  getAFLSmartPlayStats,
  getAFLTeamStats,
  getSoccerPlayerStats,
  getSoccerSmartPlayStats,
  getSoccerTeamStats,
  SmartPlayStatsResponse,
  SoccerPlayerStat,
  SoccerPlayerStatsResponse,
  SoccerTeamStatsResponse,
} from '@/helpers/fetchers/stats';
import { quyerKeys } from '@/lib/queryKeys';
import { usePathname, useSearchParams } from 'next/navigation';

const StatsContext = createContext<StatsContextType | undefined>(undefined);

const StatsProvider = ({ children }: { children: React.ReactNode }) => {
  const searchParams = useSearchParams();
  const event_id = searchParams.get('event_id');
  const pathName = usePathname();

  const { data: playerStats } = useQuery({
    queryKey: [quyerKeys.getAFLPlayerStats],
    queryFn: () => getAFLPlayerStats({ eventId: Number(event_id) }),
    enabled: !!event_id && pathName.includes('football'),
  });

  const { data: smartPlayStats } = useQuery({
    queryKey: [quyerKeys.getAFLSmartPlayStats],
    queryFn: () =>
      getAFLSmartPlayStats({
        eventId: Number(event_id),
      }),
    enabled: !!event_id && pathName.includes('football'),
  });

  const { data: aflTeamStats } = useQuery({
    queryKey: [quyerKeys.getAFLTeamStats],
    queryFn: () =>
      getAFLTeamStats({
        eventId: Number(event_id),
      }),
    enabled: !!event_id && pathName.includes('football'),
  });

  const { data: soccerTeamStats } = useQuery({
    queryKey: [quyerKeys.getSoccerTeamStats],
    queryFn: () =>
      getSoccerTeamStats({
        eventId: Number(event_id),
      }),
    enabled: !!event_id && pathName.includes('soccer'),
  });

  const { data: soccerPlayerStats } = useQuery({
    queryKey: [quyerKeys.getSoccerPlayerStats],
    queryFn: () =>
      getSoccerPlayerStats({
        eventId: Number(event_id),
      }),
    enabled: !!event_id && pathName.includes('soccer'),
  });

  const { data: soccerSmartPlayStats } = useQuery({
    queryKey: [quyerKeys.getSoccerSmartPlayStats],
    queryFn: () =>
      getSoccerSmartPlayStats({
        eventId: Number(event_id),
      }),
    enabled: !!event_id && pathName.includes('soccer'),
  });

  const value = useMemo(
    (): StatsContextType => ({
      playerStats,
      smartPlayStats,
      aflTeamStats,
      soccerTeamStats: soccerTeamStats as SoccerTeamStatsResponse | undefined,
      soccerPlayerStats: soccerPlayerStats as
        | SoccerPlayerStatsResponse
        | undefined,
      soccerSmartPlayStats: soccerSmartPlayStats as
        | SmartPlayStatsResponse
        | undefined,
    }),
    [
      playerStats,
      smartPlayStats,
      aflTeamStats,
      soccerTeamStats,
      soccerPlayerStats,
      soccerSmartPlayStats,
    ],
  );
  return (
    <StatsContext.Provider value={value}>{children}</StatsContext.Provider>
  );
};

export const useStatsContext = () => {
  const context = useContext(StatsContext);
  if (!context) {
    throw new Error('useStatsContext must be used within a StatsProvider');
  }
  return context;
};

export default StatsProvider;
