export type CricketPlayerRole = 'BAT' | 'BOWL' | 'ALL';

export interface CricketPlayer {
  id: string;
  name: string;
  team: string;
  teamName: string;
  role: string;
  price: number;
  points: number;
  selected: boolean;
  number?: number; // Player's jersey number (optional)
  image?: string;
  isCaptain?: boolean;
  isViceCaptain?: boolean;
  playerValue?: number;
  squadsId: number;
  tournamentId: number;
  playerId: number;
  scoreData: {
    lastScore: number;
    totalScore: number;
    lastThreeMatch: number;
    lastFiveMatch: number;
    playerCurrentSalary: number;
    playerLastSalary: number;
    avg: number;
    livePoint?: number;
    totalPlayed: number;
    sel?: number;
  };
}

export interface CricketReserveState {
  reservePlayers: CricketPlayer[];
  remainingBudget: number;
  totalBudget: number;
  reservePlayersLimit: number;
}

export interface CricketTeamState {
  reserveState: CricketReserveState;
}
export interface CricketTeamActions {
  addReservePlayer: (player: CricketPlayer) => void;
  removeReservePlayer: (player: CricketPlayer) => void;
  setReservedRemainingBudget: (budget: number) => void;
  setReservedTotalBudget: (budget: number) => void;
}
