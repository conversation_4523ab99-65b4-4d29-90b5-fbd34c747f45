import { faker } from '@faker-js/faker';
import {
  SoccerPlayer,
  SoccerPlayerRole,
} from '@/helpers/context/soccer/createSoccerTeamContext';

const generatePlayer = (role: SoccerPlayerRole): SoccerPlayer => ({
  id: faker.string.uuid(),
  name: faker.person.fullName(),
  team: faker.company.name(),
  teamName: faker.company.name(),
  role,
  price: faker.number.int({ min: 1000000, max: 10000000 }),
  points: faker.number.int({ min: 0, max: 100 }),
  selected: false,
  number: faker.number.int({ min: 1, max: 99 }),
  image: faker.image.avatar(),
  isCaptain: false,
  isViceCaptain: false,
  playerValue: faker.number.int({ min: 1000000, max: 10000000 }),
  squadsId: faker.number.int({ min: 1, max: 100 }),
  tournamentId: faker.number.int({ min: 1, max: 100 }),
  playerId: faker.number.int({ min: 1, max: 1000 }),
  scoreData: {
    lastScore: faker.number.int({ min: 0, max: 10 }),
    totalScore: faker.number.int({ min: 0, max: 100 }),
    lastThreeMatch: faker.number.int({ min: 0, max: 30 }),
    lastFiveMatch: faker.number.int({ min: 0, max: 50 }),
    playerCurrentSalary: faker.number.int({ min: 1000000, max: 10000000 }),
    playerLastSalary: faker.number.int({ min: 1000000, max: 10000000 }),
    avg: faker.number.int({ min: 0, max: 10 }),
    livePoint: faker.number.int({ min: 0, max: 10 }),
    totalPlayed: faker.number.int({ min: 0, max: 100 }),
  },
});

export const generateSoccerPlayers = (): SoccerPlayer[] => {
  const roles: SoccerPlayerRole[] = ['GK', 'DEF', 'MID', 'FWD'];
  const players: SoccerPlayer[] = [];

  roles.forEach((role) => {
    const count = role === 'GK' ? 4 : role === 'DEF' ? 10 : 8;
    for (let i = 0; i < count; i++) {
      players.push(generatePlayer(role));
    }
  });

  return players;
};

export const soccerPlayers = generateSoccerPlayers();
