'use client';
import { useQuery } from '@tanstack/react-query';
import { createContext, useContext, useMemo } from 'react';

import { quyerKeys } from '@/lib/queryKeys';

import { Token } from '../../../db/db';
import type { FantasyUser, GetFantasyUserResponse } from '../../../types';
import axiosInstance from '../axios/axiosInstance';
import { Config } from './config';

type FantasyUserContextType = {
  user: FantasyUser;
};

const fantasyUserContext = createContext<FantasyUserContextType | undefined>(
  undefined,
);

const getFantasyUser = async (): Promise<GetFantasyUserResponse> => {
  const res = await axiosInstance.get<GetFantasyUserResponse>(
    Config.fantasyURL + '/user',
  );
  return res.data;
};

const FantasyUserProvider = ({ children }: { children: React.ReactNode }) => {
  const { data } = useQuery({
    queryFn: getFantasyUser,
    queryKey: [quyerKeys.getFantasyUser],
    enabled: !!Token,
  });

  const userState = useMemo(
    () => ({
      user: data?.result!,
    }),
    [data?.result],
  );
  return (
    <fantasyUserContext.Provider value={userState}>
      {children}
    </fantasyUserContext.Provider>
  );
};

export const useFantasyUser = () => {
  const context = useContext(fantasyUserContext);
  if (!context) {
    throw new Error('useFantasyUser must be used within a FantasyUserProvider');
  }
  return context;
};

export default FantasyUserProvider;
