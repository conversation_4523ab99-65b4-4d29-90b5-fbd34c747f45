import localFont from 'next/font/local';

// Importing all fonts with multiple file types

// Arial Font
const arial = localFont({
  src: [
    {
      path: '../../public/fonts/arial-webfont.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/arial.ttf',
      weight: '400',
      style: 'normal',
    },
  ],
  variable: '--font-arial',
});

const Apotek_Comp_Regular = localFont({
  src: '../../public/fonts/Apotek_Comp_Regular.otf',
  weight: '400',
  style: 'normal',
  variable: '--font-apotek-comp-regular',
});

// Peckham Press Trial Font
const peckhamPressTrial = localFont({
  src: '../../public/fonts/PeckhamPressTrial.otf',
  variable: '--font-peckham-press-trial',
});

// SF Pro Text Regular Font
const sfProTextRegular = localFont({
  src: [
    {
      path: '../../public/fonts/sf-pro-text-regular.woff',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/sf-pro-text-regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/sf-pro-text-regular.eot',
      weight: '400',
      style: 'normal',
    },
    // {
    //   path: '../../public/fonts/sf-pro-text-regular.svg',
    //   weight: '400',
    //   style: 'normal',
    // },
  ],
  variable: '--font-sf-pro-text-regular',
});

// SF Pro Text Semibold Font
const sfProTextSemibold = localFont({
  src: [
    {
      path: '../../public/fonts/sf-pro-text-semibold.woff',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/sf-pro-text-semibold.ttf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/sf-pro-text-semibold.eot',
      weight: '600',
      style: 'normal',
    },
    // {
    //   path: '../../public/fonts/sf-pro-text-semibold.svg',
    //   weight: '600',
    //   style: 'normal',
    // },
  ],
  variable: '--font-sf-pro-text-semibold',
});

// The Stamshons Demo Font
const theStamshonsDemo = localFont({
  src: '../../public/fonts/TheStamshonsDemo.otf',
  variable: '--font-the-stamshons-demo',
});

// Veneer Italic Font
const veneerItalic = localFont({
  src: [
    {
      path: '../../public/fonts/VeneerItalic.ttf',
      weight: '400',
      style: 'italic',
    },
    {
      path: '../../public/fonts/veneer_italic-webfont.woff2',
      weight: '400',
      style: 'italic',
    },
  ],
  variable: '--font-veneer-italic',
});

// Veneer Clean Soft Font (Multiple Formats)
const veneerCleanSoft = localFont({
  src: [
    {
      path: '../../public/fonts/VeneerClean-Soft.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/VeneerClean-Soft.woff',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/VeneerClean-Soft.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/VeneerClean-Soft.eot',
      weight: '400',
      style: 'normal',
    },
  ],
  variable: '--font-veneer-clean-soft',
});

// Export all fonts to use them in other components or global styles
export {
  arial,
  peckhamPressTrial,
  sfProTextRegular,
  sfProTextSemibold,
  theStamshonsDemo,
  veneerCleanSoft,
  veneerItalic,
  Apotek_Comp_Regular,
};
