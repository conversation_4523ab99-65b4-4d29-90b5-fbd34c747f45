import { useQuery } from '@tanstack/react-query';
import type { ColumnDef } from '@tanstack/react-table';
import moment from 'moment-timezone';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import Select, { components } from 'react-select';

import SelectIndicatorOutLine from '@/assets/images/icons/selectOutLineDropindicator.svg';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { LIVE_POLLIN_TIME } from '@/helpers/constants/index';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Config } from '@/helpers/context/config';
import useScreen from '@/hooks/useScreen';
import { quyerKeys } from '@/lib/queryKeys';

import type { FetchRoundDetailsResponse } from '../../../types';
import type {
  FetchRoundScoreResponse,
  Player,
} from '../../../types/competitions';
import DataTable from '../UI/DataTabel';
import {
  teamTrackColumns,
  upcomingTeamTrackColumns,
} from '../UI/DataTabel/teamTrackerCloumns';
import SimpleTabs from '../UI/SimpleTabs';
import BiggestValueTable from './BiggestValueTable';
import TeamSpendTable from './TeamSpendTable';
import UpcoingFixturesPage from './UpcoingFixtures';

const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const fetchRoundDetails = async (
  sportId: string | null,
  start_date: string | null,
  tournament_id: string | null,
): Promise<FetchRoundDetailsResponse> => {
  const res = await axiosInstance.get<FetchRoundDetailsResponse>(
    Config.baseURL +
      `tipping/rounds?SportId=${sportId}&tournamentId=${tournament_id}&timezone=${timezone}&startDate=${start_date}`,
  );
  return res?.data;
};

const fetchPlayerRoundScoreDetails = async (
  sportId: string | null,
  event_id: string | null,
  tournament_id: string | null,
  dream_team_id: string | null,
): Promise<FetchRoundScoreResponse> => {
  const res = await axiosInstance.get<FetchRoundScoreResponse>(
    Config.fantasyURL +
      `/events/player-list/${tournament_id}?SportId=${sportId}&dreamTeamId=${dream_team_id}&eventId=${event_id}`,
  );
  return res?.data;
};

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicatorOutLine />
    </components.DropdownIndicator>
  );
};

const RightSideDetails = ({ dreamTeamId }: { dreamTeamId?: number }) => {
  const searchParams = useSearchParams();
  const { eventDetailsResponse } = useCompetition();
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  const eventConfiguration = eventDetailsResponse?.result?.eventConfiguration;
  const dreamTeams = eventDetailsResponse?.result?.dreamTeams;
  const selectedRound = eventConfiguration?.round;
  const [activeTab, setActiveTab] = useState(1);
  const [roundSelect, setRoundSelect] = useState<string | null>(
    // @ts-expect-error
    selectedRound,
  );

  const { width } = useScreen();
  const sport_id = searchParams.get('sport_id');
  const tournament_id = searchParams.get('tournament_id');
  const event_id = searchParams.get('event_id');
  const currentDate = moment(new Date()).format('YYYY-MM-DD');

  // fetch round list
  const { data: roundLists } = useQuery({
    queryFn: () => fetchRoundDetails(sport_id, currentDate, tournament_id),
    queryKey: [quyerKeys.getRoundList],
  });

  const isLiveEvent =
    eventDetailsResponse?.result?.eventDetails?.status === 'inprogress';

  const [teamSelect, setTeamSelect] = useState<string | null>(
    // @ts-expect-error
    dreamTeams?.[0]?.id,
  );
  // fetch player round scores
  const { data: playerRoundScore, isLoading: isPlayerRoundLoding } = useQuery({
    queryFn: () =>
      fetchPlayerRoundScoreDetails(
        sport_id,
        event_id,
        tournament_id,
        teamSelect,
      ),
    queryKey: [
      quyerKeys.getPlayerRoundScore,
      roundSelect,
      teamSelect,
      activeTab,
    ],
    enabled: !!teamSelect,
    refetchInterval: () => (isLiveEvent ? LIVE_POLLIN_TIME : false),
  });

  const playerScore = playerRoundScore
    ? playerRoundScore?.result
        ?.filter((item) => item?.isAdded)
        ?.map((player) => {
          if (player?.positionType?.includes('captain')) {
            const newScoreData = {
              ...player.scoreData,
              lastPoint: (player?.scoreData?.lastPoint ?? 0) * 2,
            };
            return { ...player, scoreData: newScoreData };
          }

          if (player?.positionType?.includes('viceCaptain')) {
            const newScoreData = {
              ...player.scoreData,
              lastPoint: (player?.scoreData?.lastPoint ?? 0) * 1.5,
            };
            return { ...player, scoreData: newScoreData };
          }

          return player;
        })
    : [];

  const roundOptions: any[] = [];

  const filteredRound = roundLists?.roundList?.filter(
    // @ts-expect-error
    (round) => !roundLists?.completedRound.includes(round?.round),
  );

  filteredRound?.map((item) => {
    let label: string;

    if (item?.displayName) {
      label = item.displayName;
    } else {
      // @ts-expect-error
      label = (sport_id === 4 ? 'Day' : 'Round') + ' ' + item?.round;
    }

    roundOptions.push({
      label: label,
      value: item?.round,
      roundStartDate: item?.startTime,
      displayName: item?.displayName,
    });
  });

  useEffect(() => {
    if (roundOptions && !roundSelect) {
      setRoundSelect(roundOptions?.[0]?.value);
    }
  }, [roundOptions]);

  const teamOptions: any[] = [];

  const filteredTeam = dreamTeams;
  filteredTeam?.map((item) => {
    teamOptions.push({
      ...item,
      label: item?.name,
      value: item?.id,
    });
  });

  type TabItem = {
    name: string;
    id: number;
  };

  const tabData: TabItem[] = [
    { name: 'Match Score', id: 1 },
    {
      name: 'Player Scores',
      id: 2,
    },
  ];

  const totalScoreSum = playerScore?.reduce(
    (sum, player) =>
      sum +
      (eventStatus === 'upcoming'
        ? player?.scoreData?.lastScore
        : (player?.scoreData?.lastPoint ?? 0)),
    0,
  );

  let teamTrackColumn: ColumnDef<Player>[] = [];

  if (eventStatus === 'upcoming') {
    teamTrackColumn = upcomingTeamTrackColumns;
  } else {
    teamTrackColumn = teamTrackColumns;
  }

  return (
    <>
      <div className="shadow-[0px_1px_3px_0px_#0000002b] rounded-lg bg-white px-[9px] py-[13px]">
        <div className="flex items-center justify-between mb-3">
          <p>Team tracker</p>
          <div className="flex items-center justify-between">
            {activeTab === 2 && teamOptions?.length !== 0 && (
              <div className="min-w-[115px] max-w-full">
                <Select
                  className="React season-select-comp"
                  value={teamOptions?.find((item: any) => {
                    return item?.value === teamSelect;
                  })}
                  onChange={(e: any) => setTeamSelect(e?.value)}
                  options={teamOptions}
                  classNamePrefix="select"
                  placeholder="Team"
                  isSearchable={false}
                  components={{ DropdownIndicator }}
                />
              </div>
            )}
          </div>
        </div>
        <div className="w-full">
          <SimpleTabs
            tabs={tabData}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            size={width <= 700 ? 'small' : 'medium'}
            className="space-x-2 mb-2 md:mb-0"
          />
        </div>
        {activeTab === 1 ? (
          <UpcoingFixturesPage />
        ) : (
          <div>
            <DataTable
              columns={teamTrackColumn}
              data={playerScore}
              isLoading={isPlayerRoundLoding}
            />
            {playerScore?.length > 0 && (
              <div className="flex justify-between items-center bg-black-300 py-[5px] px-[18px] pr-6 rounded-b-[8px]">
                <p className="text-base leading-[17px] text-black-100">
                  Total - Round <span>{roundSelect}</span> scores
                </p>
                <p className="text-base leading-[17px] text-black-100 font-semibold">
                  {totalScoreSum}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
      <div className="shadow-[0px_1px_3px_0px_#0000002b] rounded-lg bg-white px-[9px] py-3 pt-3 mt-5 mb-[5rem]">
        <div className="flex items-center justify-between mb-3">
          <p>
            {eventDetailsResponse?.result?.eventDetails?.status === 'finished'
              ? 'Biggest value rise'
              : 'Team spend'}
          </p>
        </div>
        {eventDetailsResponse?.result?.eventDetails?.status === 'finished' ? (
          <BiggestValueTable
            // @ts-expect-error
            dreamTeamId={dreamTeamId!}
          />
        ) : (
          <div>
            <TeamSpendTable />
          </div>
        )}
      </div>
    </>
  );
};

export default RightSideDetails;
