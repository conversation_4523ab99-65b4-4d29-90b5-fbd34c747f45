'use client';
import { useSearchParams } from 'next/navigation';
import type { Dispatch, ReactNode, SetStateAction } from 'react';
import React, {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from 'react';

import {
  addPlayersWithinBudget,
  findCaptainViceCaptain,
  generateExpertTeamPayload,
  generateFavouriteTeamPayload,
  generateRandomTeam,
  generateTeamPayload,
  LocalStorage,
} from '@/lib/utils';

import type {
  CreateTeamPayload,
  DreamTeamPlayer,
  FantasyTeamResponse,
  FavoriteTeam,
  Player,
  PlayersByRoleLimit,
} from '../../../types/competitions';
import type { TeamComposition } from '../../../types/membership';
import { setApiMessage } from '../commonFunctions';
import { ALL_ROLES } from '../constants/index';
import { useCompetition } from './competitionContext';
import { TeamFilterState } from '../../../types';

type LuckyCreateTeamPayload = {
  playersByRole: PlayersByRole;
  sportId: string | null;
  eventId: string | null;
  tournamentId: string | null;
  eventName: string | null;
  competitionId: string | null;
  name: string;
};

export type PlayersByRole = {
  WKP: Player[];
  BAT: Player[];
  BOW: Player[];
  ALL: Player[];
};

export type LastEnterPlayerData = {
  player: Player;
  tabSection: keyof PlayersByRole;
};

export type LastEntryType = {
  players: LastEnterPlayerData[];
  mode: 'FEELING_LUCKY' | 'FAVORITE' | 'EXPERT_FAVORITE' | 'MANUAL';
};

export type ReservePlayerPayload = {
  playerId: number;
  playerValue: number;
  reserve: boolean;
  reserveRank: number;
}



interface ReserveState {
  reservePlayers: (Player | null)[];
  reservePlayersLimit: number;
  reservePlayerPayload?: ReservePlayerPayload[];
}

type TeamState = {
  playersByRole: PlayersByRole;
  totalBudget: number;
  remainingBudget: number;
  lastEntry: LastEntryType;
  alreadyCaptainId?: number | null;
  maxPlayers: number;
  createDreamTeamPayload?: CreateTeamPayload;
  playerTypeLimits: {
    WKP: number;
    BAT: number;
    BOW: number;
    ALL: number;
  };
  reserveState: ReserveState;
};

type TeamAction =
  | {
    type: 'ADD_PLAYER';
    payload: { player: Player; role: keyof PlayersByRole };
  }
  | {
    type: 'REMOVE_PLAYER';
    payload: { playerId: number; role: keyof PlayersByRole };
  }
  | { type: 'RESET_TEAM' }
  | {
    type: 'APPLY_SPORT_RULES';
    payload: { playerByRoleLimit: PlayersByRoleLimit, reservePlayersLimit: number };
  }
  | {
    type: 'SET_CAPTAIN';
    payload: { playerId: number; role: keyof PlayersByRole };
  }
  | {
    type: 'SET_VICE_CAPTAIN';
    payload: { playerId: number; role: keyof PlayersByRole };
  }
  | {
    type: 'CREATE_DREAM_TEAM';
    payload: {
      sportId: string | null;
      eventId: string | null;
      tournamentId: string | null;
      eventName: string | null;
      competitionId: string | null;
      name: string;
      coins?: number | null;
      bonusCoins?: number | null;
    };
  }
  | {
    type: 'CREATE_LUCKY_TEAM';
    payload: LuckyCreateTeamPayload;
  }
  | {
    type: 'CREATE_FAVOURITE_TEAM';
    payload: {
      favoriteTeam: FavoriteTeam;
      playersByRole: PlayersByRole;
    };
  }
  | {
    type: 'CREATE_EXPERT_TEAM';
    payload: {
      dreamPlayers: DreamTeamPlayer[];
      playerByRole: PlayersByRole;
    };
  }
  | {
    type: 'GET_DREAM_TEAM';
    payload: {
      fantasyTeamResponse: FantasyTeamResponse;
      playerId?: number;
      role?: keyof PlayersByRole;
    };
  }
  | {
    type: 'SET_TOTAL_BALANCE';
    payload: {
      amount: number;
    };
  }
  | {
    type: 'ADD_RESERVE_PLAYER';
    payload: { player: Player, position: number };
  }
  | {
    type: 'CLEAR_RESERVE_PLAYERS';
  }
  | {
    type: 'REMOVE_RESERVE_PLAYER';
    payload: { playerId: number, position: number };
  } | {
    type: "CREATE_RESERVE_PLAYER_PAYLOAD";
    payload: { reservePlayerPayload: ReservePlayerPayload[] };
  };

type RoleKey = keyof PlayersByRole; // 'WKP' | 'BAT' | 'BOW' | 'ALL'


type TeamContextType = {
  state: TeamState;
  addPlayer: (player: Player, role: keyof PlayersByRole) => void;
  removePlayer: (playerId: number, role: keyof PlayersByRole) => void;
  resetTeam: () => void;
  canAddPlayer: (
    player: Player,
    role: keyof PlayersByRole,
  ) => {
    canAdd: boolean;
    reason?: string;
  };
  getTeamStatus: () => {
    isComplete: boolean;
    remainingPlayers: {
      WKP: number;
      BAT: number;
      BOW: number;
    };
  };
  team: TeamFilterState;
  setTeam: Dispatch<SetStateAction<TeamFilterState>>;

  activeTab: keyof PlayersByRole;
  setActiveTab: Dispatch<SetStateAction<keyof PlayersByRole>>;
  showPlayerTabel: boolean;
  setShowPlayerTabel: Dispatch<SetStateAction<boolean>>;
  setPlayerRoleToCaptain: (playerId: number, role: keyof PlayersByRole) => void;
  setPlayerRoleToViceCaiptain: (
    playerId: number,
    role: keyof PlayersByRole,
  ) => void;

  createDreamTeam: (payload: {
    sportId: string | null;
    eventId: string | null;
    tournamentId: string | null;
    eventName: string | null;
    competitionId: string | null;
    name: string;
    coins?: number | null;
    bonusCoins?: number | null;
  }) => void;

  createLuckyTeam: (payload: LuckyCreateTeamPayload) => void;
  createFavouriteTeam: (payload: {
    favoriteTeam: FavoriteTeam;
    playersByRole: PlayersByRole;
  }) => void;

  createExpertTeam: (
    payload: DreamTeamPlayer[],
    playerByRole: PlayersByRole,
  ) => void;

  getDreamTeam: (
    fantasyTeamResponse: FantasyTeamResponse,
    playerI?: number,
    role?: keyof PlayersByRole,
  ) => void;
  setBudget: (amount: number) => void;
  setShowFilter: Dispatch<SetStateAction<boolean>>;
  showFilter: boolean;
  openReserveModal: boolean;
  setOpenReserveModal: Dispatch<SetStateAction<boolean>>;
  activePlayerPosition: number;
  setActivePlayerPosition: Dispatch<SetStateAction<number>>;
  addReservePlayer: (player: Player, position: number) => void;
  removeReservePlayer: (playerId: number, position: number) => void;
  createReservePlayerPayload: (reservePlayerPayload: ReservePlayerPayload[]) => void;
  clearReservePlayers: () => void;
};

// Initial state
const initialState: TeamState = {
  playersByRole: {
    WKP: [],
    BAT: [],
    BOW: [],
    ALL: [],
  },
  totalBudget: 0,
  remainingBudget: 0,
  maxPlayers: 11,
  playerTypeLimits: {
    WKP: 0,
    BAT: 0,
    BOW: 0,
    ALL: 0,
  },
  lastEntry: {
    mode: 'MANUAL',
    players: [],
  },
  reserveState: {
    reservePlayers: [null, null, null, null],
    reservePlayersLimit: 4,
  },
};

// Create context
const TeamContext = createContext<TeamContextType | undefined>(undefined);

// Helper functions
const getTotalPlayers = (playersByRole: PlayersByRole): number => {
  return Object.values(playersByRole).reduce(
    (total, players) => total + players.length,
    0,
  );
};

const calculateRemainingBudget = (
  totalBudget: number,
  playersByRole: PlayersByRole,
): number => {
  const totalCost = Object.values(playersByRole)
    .flat()
    .reduce((sum, player) => sum + player?.scoreData.playerCurrentSalary, 0);
  return totalBudget - totalCost;
};

// Reducer function
const teamReducer = (state: TeamState, action: TeamAction): TeamState => {
  const localPlayerByRole = LocalStorage.getItem<PlayersByRole>('dream_team');
  const localReservePlayers = LocalStorage.getItem<Player[]>('cricket_reserve_players');
  switch (action.type) {
    case 'ADD_PLAYER': {
      const { player, role } = action.payload;
      const newPlayersByRole = {
        ...state.playersByRole,
        [role]: [
          ...state.playersByRole[role],
          { ...player, isCaiptain: false, isViceCaiptain: false },
        ],
      };

      const LastEnterPlayer: LastEnterPlayerData = {
        player,
        tabSection: role,
      };
      return {
        ...state,
        playersByRole: newPlayersByRole,
        lastEntry: {
          ...state.lastEntry,
          players: [...(state.lastEntry?.players ?? []), LastEnterPlayer],
          mode: 'MANUAL',
        },
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          newPlayersByRole,
        ),
      };
    }

    case 'REMOVE_PLAYER': {
      const { playerId } = action.payload;
      const allRoles: RoleKey[] = ['ALL', 'BAT', 'BOW', 'WKP'];
      const newPlayersByRole: PlayersByRole = {
        ALL: [],
        BAT: [],
        BOW: [],
        WKP: [],
      };

      allRoles.forEach((role) => {
        const allPlayers = (state.playersByRole[role] = state.playersByRole[
          role
        ].filter((player) => player.playerId !== playerId));
        newPlayersByRole[role] = allPlayers;
      });

      const updatedLastEntryPlayers = state.lastEntry.players.filter(
        (entry) => entry?.player?.playerId !== playerId,
      );

      return {
        ...state,
        playersByRole: newPlayersByRole,
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          newPlayersByRole,
        ),
        lastEntry: {
          ...state.lastEntry,
          players: updatedLastEntryPlayers,
        },
      };
    }

    case 'APPLY_SPORT_RULES': {
      const { playerByRoleLimit } = action.payload;
      return { ...state, playerTypeLimits: playerByRoleLimit };
    }

    case 'SET_CAPTAIN': {
      const { playerId } = action.payload;
      const allRoles: RoleKey[] = ['ALL', 'BAT', 'BOW', 'WKP'];
      const newPlayersByRole: PlayersByRole = {
        ALL: [],
        BAT: [],
        BOW: [],
        WKP: [],
      };
      // Reset Already captain
      allRoles.forEach((role) => {
        const allPlayers = (state.playersByRole[role] = state.playersByRole[
          role
        ].map((player) => {
          return { ...player, isCaiptain: false };
        }));
        newPlayersByRole[role] = allPlayers;
      });

      // Set captain condition based
      allRoles.forEach((role) => {
        const allPlayers = (state.playersByRole[role] = state.playersByRole[
          role
        ].map((player) => {
          // If player is already captain
          if (player.playerId === playerId && player.isCaiptain) {
            return { ...player, isCaiptain: false };
          }
          // if players is fresh
          if (
            player.playerId === playerId &&
            !player.isCaiptain &&
            !player.isViceCaiptain
          ) {
            return { ...player, isCaiptain: true };
          }
          // If player is already vice captain and assing them captain
          if (player.isViceCaiptain && player.playerId === playerId) {
            return { ...player, isCaiptain: true, isViceCaiptain: false };
          }

          return player;
        }));
        newPlayersByRole[role] = allPlayers;
      });

      return { ...state, playersByRole: newPlayersByRole };
    }
    case 'SET_VICE_CAPTAIN': {
      const { playerId } = action.payload;
      const allRoles: RoleKey[] = ['ALL', 'BAT', 'BOW', 'WKP'];
      const newPlayersViceByRole: PlayersByRole = {
        ALL: [],
        BAT: [],
        BOW: [],
        WKP: [],
      };
      // Reset Already Vice captain
      allRoles.forEach((role) => {
        const allPlayers = (state.playersByRole[role] = state.playersByRole[
          role
        ].map((player) => {
          return { ...player, isViceCaiptain: false };
        }));
        newPlayersViceByRole[role] = allPlayers;
      });

      // Set Vice captain condition based
      allRoles.forEach((role) => {
        const allPlayers = (state.playersByRole[role] = state.playersByRole[
          role
        ].map((player) => {
          if (player.isCaiptain && player.playerId === playerId) {
            return { ...player, isCaiptain: false, isViceCaiptain: true };
          }
          if (!player.isViceCaiptain && player.playerId === playerId) {
            return { ...player, isViceCaiptain: !player.isViceCaiptain };
          }
          return player;
        }));
        newPlayersViceByRole[role] = allPlayers;
      });

      return { ...state, playersByRole: newPlayersViceByRole };
    }
    case 'CREATE_DREAM_TEAM': {
      const { eventId, eventName, sportId, tournamentId, competitionId, name, coins, bonusCoins } =
        action.payload;
      const { captain, viceCaptain } = findCaptainViceCaptain(
        state.playersByRole,
      );

      const playerPayloadData = generateTeamPayload(
        state.playersByRole,
        captain,
        viceCaptain,
        state.reserveState.reservePlayerPayload ?? [],
      );
      const createDreamTeamPayload: CreateTeamPayload = {
        playerData: playerPayloadData,
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
        coins,
        bonusCoins,
      };

      return { ...state, createDreamTeamPayload };
    }

    case 'CREATE_LUCKY_TEAM':
      try {
        const { playersByRole } = action.payload;
        const teamComposion: TeamComposition = {
          ALL: {
            max: state.playerTypeLimits.ALL,
            min: state.playerTypeLimits.ALL,
          },
          BAT: {
            max: state.playerTypeLimits.BAT,
            min: state.playerTypeLimits.BAT,
          },
          BOW: {
            max: state.playerTypeLimits.BOW,
            min: state.playerTypeLimits.BOW,
          },
          WKP: {
            max: state.playerTypeLimits.WKP,
            min: state.playerTypeLimits.WKP,
          },
          TOTAL_PLAYERS: 11,
        };
        const luckyPlayersByRole = generateRandomTeam(
          playersByRole,
          teamComposion,
        );
        const { captain: luckyCaptain, viceCaptain: luckyViceCaptain } =
          findCaptainViceCaptain(state.playersByRole);
        const luckyPlayerPayloadData = generateTeamPayload(
          luckyPlayersByRole,
          luckyCaptain,
          luckyViceCaptain,
          state.reserveState.reservePlayerPayload ?? [],
        );

        const validLuckyPlayersByRole = addPlayersWithinBudget(
          luckyPlayersByRole,
          state.remainingBudget,
        );

        const {
          eventId: luckyEventId,
          eventName: luckyEventName,
          sportId: luckySportId,
          tournamentId: luckyTournamentId,
          competitionId: luckyCompetitionId,
          name: luckyTeamName,
        } = action.payload;

        const createLuckyDreamTeamPayload: CreateTeamPayload = {
          playerData: luckyPlayerPayloadData,
          eventId: luckyEventId,
          eventName: luckyEventName,
          sportId: luckySportId,
          tournamentId: luckyTournamentId,
          competitionId: luckyCompetitionId,
          name: luckyTeamName,
        };

        return {
          ...state,
          playersByRole: validLuckyPlayersByRole,
          createDreamTeamPayload: createLuckyDreamTeamPayload,
          remainingBudget: calculateRemainingBudget(
            state.totalBudget,
            validLuckyPlayersByRole,
          ),
          lastEntry: {
            ...state.lastEntry,
            mode: 'FEELING_LUCKY',
          },
        };
      } catch (error) {
        if (error instanceof Error) {
          setApiMessage('error', error?.message);
        }

        return state;
      }

    case 'CREATE_FAVOURITE_TEAM': {
      const { favoriteTeam, playersByRole: favouritePlayerByRole } =
        action.payload;
      const resetPlayersByRole: PlayersByRole = {
        ALL: [],
        BAT: [],
        BOW: [],
        WKP: [],
      };

      ALL_ROLES.forEach((role) => {
        const rolesPlayer = favouritePlayerByRole[role].map((player) => {
          if (player.isCaiptain) {
            return { ...player, isCaiptain: false };
          }
          if (player.isViceCaiptain) {
            return { ...player, isViceCaiptain: false };
          }
          return player;
        });
        resetPlayersByRole[role] = rolesPlayer;
      });

      const newPlayersByRole = generateFavouriteTeamPayload(
        favoriteTeam,
        state.playerTypeLimits,
        resetPlayersByRole,
      );

      const validPlayersByRole = addPlayersWithinBudget(
        newPlayersByRole,
        state.remainingBudget,
      );

      return {
        ...state,
        playersByRole: validPlayersByRole,
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          validPlayersByRole,
        ),

        lastEntry: {
          ...state.lastEntry,
          mode: 'FAVORITE',
        },
      };
    }

    case 'CREATE_EXPERT_TEAM': {
      const { dreamPlayers, playerByRole } = action.payload;

      const resetExpertPlayersByRole: PlayersByRole = {
        ALL: [],
        BAT: [],
        BOW: [],
        WKP: [],
      };

      ALL_ROLES.forEach((role) => {
        const rolesPlayer = playerByRole[role].map((player) => {
          if (player.isCaiptain) {
            return { ...player, isCaiptain: false };
          }
          if (player.isViceCaiptain) {
            return { ...player, isViceCaiptain: false };
          }
          return player;
        });
        resetExpertPlayersByRole[role] = rolesPlayer;
      });

      const newPlayerByRole = generateExpertTeamPayload(
        dreamPlayers,
        resetExpertPlayersByRole,
      );

      const validExpertPlayersByRole = addPlayersWithinBudget(
        newPlayerByRole,
        state.remainingBudget,
      );

      return {
        ...state,
        playersByRole: validExpertPlayersByRole,
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          validExpertPlayersByRole,
        ),

        lastEntry: {
          ...state.lastEntry,
          mode: 'EXPERT_FAVORITE',
        },
      };
    }
    case 'GET_DREAM_TEAM': {
      const { fantasyTeamResponse, playerId, role } = action.payload;

      const dreamPlayersByRole = fantasyTeamResponse.result;
      const allRoles: RoleKey[] = ['ALL', 'BAT', 'BOW', 'WKP'];
      const newDreamPlayerByRole: PlayersByRole = {
        ALL: [],
        BAT: [],
        BOW: [],
        WKP: [],
      };
      const [playerCaptain] = dreamPlayersByRole?.captain || [];
      const [playerViceCaptain] = dreamPlayersByRole?.viceCaptain || [];
      allRoles.forEach((role) => {
        switch (role) {
          case 'ALL':
            newDreamPlayerByRole['ALL'] = dreamPlayersByRole?.allRounder?.map(
              (player) => {
                if (player?.playerId === playerCaptain?.playerId) {
                  return { ...player, isCaiptain: true };
                }

                if (player?.playerId === playerViceCaptain?.playerId) {
                  return { ...player, isViceCaiptain: true };
                }
                return player;
              },
            );
            break;
          case 'BAT':
            newDreamPlayerByRole['BAT'] = dreamPlayersByRole?.batsman?.map(
              (player) => {
                if (player?.playerId === playerCaptain?.playerId) {
                  return { ...player, isCaiptain: true };
                }

                if (player?.playerId === playerViceCaptain?.playerId) {
                  return { ...player, isViceCaiptain: true };
                }
                return player;
              },
            );
            break;
          case 'BOW':
            newDreamPlayerByRole['BOW'] = dreamPlayersByRole?.bowler?.map(
              (player) => {
                if (player?.playerId === playerCaptain?.playerId) {
                  return { ...player, isCaiptain: true };
                }

                if (player?.playerId === playerViceCaptain?.playerId) {
                  return { ...player, isViceCaiptain: true };
                }
                return player;
              },
            );
            break;
          case 'WKP':
            newDreamPlayerByRole['WKP'] = dreamPlayersByRole?.wicketKeeper?.map(
              (player) => {
                if (player?.playerId === playerCaptain?.playerId) {
                  return { ...player, isCaiptain: true };
                }

                if (player?.playerId === playerViceCaptain?.playerId) {
                  return { ...player, isViceCaiptain: true };
                }
                return player;
              },
            );
            break;
        }
      });
      const roleToRemove = role;
      const playerIdToRemove = playerId;
      const updatedPlayersByRole = { ...newDreamPlayerByRole };
      if (roleToRemove && playerIdToRemove) {
        updatedPlayersByRole[roleToRemove] = updatedPlayersByRole[
          roleToRemove
        ].filter((player) => player.playerId !== playerIdToRemove);
      }

      // reserve players with correct position
      // create array with reserve limit with null values
      const reservePlayers = Array(state.reserveState.reservePlayersLimit).fill(null);

      // add reserve players to correct position, ensuring we don't exceed the limit
      dreamPlayersByRole?.reserve?.forEach((player) => {
        const rank = (player.reserveRank ?? 0) - 1;
        if (rank < state.reserveState.reservePlayersLimit) {
          reservePlayers[rank] = player;
        }
      });


      return {
        ...state,
        playersByRole: updatedPlayersByRole,
        reserveState: {
          ...state.reserveState,
          reservePlayers: reservePlayers,
        },
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          updatedPlayersByRole,
        ),
      };
    }
    case 'SET_TOTAL_BALANCE': {
      const { amount } = action.payload;
      return { ...state, totalBudget: amount, remainingBudget: amount };
    }

    case "ADD_RESERVE_PLAYER": {
      const { player, position } = action.payload;
      const remainingBudget = state.remainingBudget - player.scoreData?.playerCurrentSalary;
      // adding player to correct position
      const newReservePlayers = [...state.reserveState.reservePlayers];

      // Calculate correct reserve rank by counting existing non-null players
      const existingPlayerCount = newReservePlayers.filter(p => p !== null).length;
      const reserveRank = existingPlayerCount + 1;

      newReservePlayers[position] = { ...player, reserveRank };

      return {
        ...state,
        reserveState: {
          ...state.reserveState,
          reservePlayers: newReservePlayers,
        },
        remainingBudget
      };
    }

    case "REMOVE_RESERVE_PLAYER": {
      const { playerId, position } = action.payload;
      const player = state.reserveState.reservePlayers.find((player) => player?.playerId === playerId);
      if (!player) {
        return state;
      }
      const remainingBudget = state.remainingBudget + player.scoreData?.playerCurrentSalary;

      // Remove the player and recalculate reserve ranks for remaining players
      const updatedReservePlayers = state.reserveState.reservePlayers.map((player) =>
        player?.playerId === playerId ? null : player
      );

      // Recalculate reserve ranks for remaining players
      let rankCounter = 1;
      const finalReservePlayers = updatedReservePlayers.map((player) => {
        if (player !== null) {
          return { ...player, reserveRank: rankCounter++ };
        }
        return null;
      });

      return {
        ...state,
        reserveState: {
          ...state.reserveState,
          reservePlayers: finalReservePlayers
        },
        remainingBudget
      };
    }

    case "CREATE_RESERVE_PLAYER_PAYLOAD": {
      const { reservePlayerPayload } = action.payload;
      return { ...state, reserveState: { ...state.reserveState, reservePlayerPayload } };
    }

    case "CLEAR_RESERVE_PLAYERS": {
      return { ...state, reserveState: { ...state.reserveState, reservePlayers: Array(state.reserveState.reservePlayersLimit).fill(null) } };
    }

    case 'RESET_TEAM':
      return {
        ...initialState,
        playerTypeLimits: state.playerTypeLimits,
        playersByRole: localPlayerByRole || initialState.playersByRole,
        remainingBudget: state.remainingBudget,
        totalBudget: state.totalBudget,
        reserveState: {
          reservePlayers: localReservePlayers || initialState.reserveState.reservePlayers,
          reservePlayersLimit: state.reserveState.reservePlayersLimit,
        }
      };

    default:
      return state;
  }
};

// Provider component
export const TeamProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const searchParams = useSearchParams();
  const role = searchParams.get('role') as keyof PlayersByRole;
  const [state, dispatch] = useReducer(teamReducer, initialState);
  const [activeTab, setActiveTab] = useState<keyof PlayersByRole>('WKP');
  const [showPlayerTabel, setShowPlayerTabel] = useState(false);
  const [openReserveModal, setOpenReserveModal] = useState<boolean>(false);
  const [activePlayerPosition, setActivePlayerPosition] = useState<number>(0);
  const { playerByRoleLimit, reservePlayersCount } = useCompetition();
  const [team, setTeam] = useState<TeamFilterState>({
    home: false,
    away: false,
  });


  const [showFilter, setShowFilter] = useState(false);
  const getTeamStatus = () => {
    const remaining = {
      WKP: state.playerTypeLimits.WKP - state.playersByRole.WKP.length,
      BAT: state.playerTypeLimits.BAT - state.playersByRole.BAT.length,
      BOW: state.playerTypeLimits.BOW - state.playersByRole.BOW.length,
    };

    const isComplete =
      remaining.WKP === 0 && remaining.BAT === 0 && remaining.BOW === 0;

    return {
      isComplete,
      remainingPlayers: remaining,
    };
  };

  const canAddReservePlayer = (player: Player) => {
    const isPlayerSelected = Object.values(state.playersByRole)
      .flat()
      .some((p) => p.id === player.id);

    if (isPlayerSelected) {
      return { canAdd: false, reason: 'Player already selected in team' };
    }

    // withing budget
    const price = player.scoreData?.playerCurrentSalary;
    if (price > state.remainingBudget) {
      return { canAdd: false, reason: 'Insufficient budget' };
    }

    return { canAdd: true };

  };

  const canAddPlayer = (player: Player, role: keyof PlayersByRole) => {
    // Check if player is already selected in any role
    const isPlayerSelected = Object.values(state.playersByRole)
      .flat()
      .some((p) => p.id === player.id);

    if (isPlayerSelected) {
      return { canAdd: false, reason: 'Player already selected in team' };
    }

    // Check total players limit
    if (getTotalPlayers(state.playersByRole) >= state.maxPlayers) {
      return { canAdd: false, reason: 'Team is full with 11 players' };
    }

    // // Check role-specific limit
    if (state.playersByRole[role].length >= state.playerTypeLimits[role]) {
      return {
        canAdd: false,
        reason: `Maximum ${state.playerTypeLimits[role]} ${role} player${state.playerTypeLimits[role] > 1 ? 's' : ''
          } allowed`,
      };
    }

    // // Check budget
    const price = player.scoreData?.playerCurrentSalary;
    if (price > state.remainingBudget) {
      setApiMessage('error', 'Insufficient budget');
      return { canAdd: false, reason: 'Insufficient budget' };
    }

    return { canAdd: true };
  };

  const addPlayer = (player: Player, role: keyof PlayersByRole) => {
    const { canAdd, reason } = canAddPlayer(player, role);

    if (canAdd) {
      dispatch({ type: 'ADD_PLAYER', payload: { player, role } });
    } else {
      console.warn(`Cannot add player: ${reason}`);
    }
  };

  const removePlayer = (playerId: number, role: keyof PlayersByRole) => {
    dispatch({ type: 'REMOVE_PLAYER', payload: { playerId, role } });
  };

  const createReservePlayerPayload = (reservePlayerPayload: ReservePlayerPayload[]) => {
    dispatch({ type: 'CREATE_RESERVE_PLAYER_PAYLOAD', payload: { reservePlayerPayload } });
  }

  const resetTeam = () => {
    dispatch({ type: 'RESET_TEAM' });
  };

  const setPlayerRoleToCaptain = (
    playerId: number,
    role: keyof PlayersByRole,
  ) => {
    dispatch({ type: 'SET_CAPTAIN', payload: { playerId, role } });
  };

  const setPlayerRoleToViceCaiptain = (
    playerId: number,
    role: keyof PlayersByRole,
  ) => {
    dispatch({ type: 'SET_VICE_CAPTAIN', payload: { playerId, role } });
  };

  const createDreamTeam = (payload: {
    sportId: string | null;
    eventId: string | null;
    tournamentId: string | null;
    eventName: string | null;
    competitionId: string | null;
    name: string;
    coins?: number | null;
    bonusCoins?: number | null;
  }) => {
    dispatch({ type: 'CREATE_DREAM_TEAM', payload });
  };

  const createLuckyTeam = (payload: LuckyCreateTeamPayload) => {
    dispatch({ type: 'CREATE_LUCKY_TEAM', payload });
  };

  const createFavouriteTeam = (payload: {
    favoriteTeam: FavoriteTeam;
    playersByRole: PlayersByRole;
  }) => {
    dispatch({
      type: 'CREATE_FAVOURITE_TEAM',
      payload,
    });
  };

  const createExpertTeam = (
    payload: DreamTeamPlayer[],
    playerByRole: PlayersByRole,
  ) => {
    dispatch({
      type: 'CREATE_EXPERT_TEAM',
      payload: { dreamPlayers: payload, playerByRole },
    });
  };

  // Get dream team

  const getDreamTeam = (
    fantasyTeamResponse: FantasyTeamResponse,
    playerId?: number,
    role?: keyof PlayersByRole,
  ) => {
    dispatch({
      type: 'GET_DREAM_TEAM',
      payload: { fantasyTeamResponse, playerId, role },
    });
  };

  const setBudget = (amount: number) => {
    dispatch({ type: 'SET_TOTAL_BALANCE', payload: { amount } });
  };

  const addReservePlayer = (player: Player, position: number) => {
    const { canAdd, reason } = canAddReservePlayer(player);
    if (!canAdd) {
      setApiMessage('error', reason ?? '');
      return;
    }

    dispatch({ type: 'ADD_RESERVE_PLAYER', payload: { player, position } });
  };

  const removeReservePlayer = (playerId: number, position: number) => {
    dispatch({ type: 'REMOVE_RESERVE_PLAYER', payload: { playerId, position } });
    setActivePlayerPosition(position);
  };

  const clearReservePlayers = () => {
    dispatch({ type: 'CLEAR_RESERVE_PLAYERS' });
  }

  // Apply players limit via sports rule
  useEffect(() => {
    dispatch({
      type: 'APPLY_SPORT_RULES',
      payload: { playerByRoleLimit: playerByRoleLimit, reservePlayersLimit: reservePlayersCount },
    });
  }, [playerByRoleLimit]);

  // Set Default Active Tab

  useEffect(() => {
    if (role) {
      setActiveTab(role);
    }
  }, [role]);

  // AI

  const teamContextValue = useMemo(
    (): TeamContextType => ({
      state,
      addPlayer,
      removePlayer,
      resetTeam,
      canAddPlayer,
      getTeamStatus,
      activeTab,
      setActiveTab,
      showPlayerTabel,
      setShowPlayerTabel,
      setPlayerRoleToCaptain,
      setPlayerRoleToViceCaiptain,
      createDreamTeam,
      createLuckyTeam,
      createFavouriteTeam,
      createExpertTeam,
      getDreamTeam,
      setBudget,
      setTeam,
      team,
      setShowFilter,
      showFilter,
      openReserveModal,
      setOpenReserveModal,
      activePlayerPosition,
      setActivePlayerPosition,
      addReservePlayer,
      removeReservePlayer,
      createReservePlayerPayload,
      clearReservePlayers,
    }),
    [
      state,
      addPlayer,
      removePlayer,
      resetTeam,
      canAddPlayer,
      getTeamStatus,
      activeTab,
      setActiveTab,
      showPlayerTabel,
      setShowPlayerTabel,
      setPlayerRoleToCaptain,
      setPlayerRoleToViceCaiptain,
      createDreamTeam,
      createLuckyTeam,
      createFavouriteTeam,
      createExpertTeam,
      getDreamTeam,
      setBudget,
      setTeam,
      team,
      showFilter,
      setShowFilter,
      openReserveModal,
      setOpenReserveModal,
      activePlayerPosition,
      setActivePlayerPosition,
      addReservePlayer,
      removeReservePlayer,
      createReservePlayerPayload,
      clearReservePlayers,
    ],
  );

  return (
    <TeamContext.Provider value={teamContextValue}>
      {children}
    </TeamContext.Provider>
  );
};

// Custom hook for using the context
export const useTeam = () => {
  const context = useContext(TeamContext);
  if (context === undefined) {
    throw new Error('useTeam must be used within a TeamProvider');
  }
  return context;
};
