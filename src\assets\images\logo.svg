<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="108.824" height="34" viewBox="0 0 108.824 34">
  <defs>
    <radialGradient id="radial-gradient" cx="0.393" cy="-0.106" r="0.964" gradientTransform="translate(-0.448) scale(1.896 1)" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ff4713"/>
      <stop offset="0.151" stop-color="#fc4714"/>
      <stop offset="0.257" stop-color="#f34617"/>
      <stop offset="0.35" stop-color="#e3451c"/>
      <stop offset="0.435" stop-color="#cd4423"/>
      <stop offset="0.514" stop-color="#b1422c"/>
      <stop offset="0.59" stop-color="#8e4037"/>
      <stop offset="0.663" stop-color="#643d44"/>
      <stop offset="0.731" stop-color="#353a53"/>
      <stop offset="0.797" stop-color="#003764"/>
    </radialGradient>
  </defs>
  <g id="SmartB_Logo" transform="translate(0 -0.1)">
    <g id="Group_29111" data-name="Group 29111" transform="translate(19.301 14.68)">
      <path id="Path_12109" data-name="Path 12109" d="M109.817,93.3h-3.571l-2.361-5.475c-.06-.159-.159-.436-.337-.833-.159-.417-.337-.873-.536-1.389s-.4-1.051-.615-1.607-.4-1.051-.575-1.508v3.67c0,.674,0,1.289-.02,1.884,0,.6-.02,1.051-.04,1.389l-.139,3.888H97.3L98.431,74h4.047l4.443,11.128c.139.337.317.813.536,1.448.218.615.436,1.289.635,2,.218-.694.417-1.369.635-1.984s.4-1.111.555-1.468L113.745,74h3.967l1.151,19.3h-4.424l-.179-3.888c-.02-.357-.04-.833-.04-1.428s-.02-1.21-.02-1.865,0-1.309.02-1.964c0-.655.02-1.23.02-1.706-.159.4-.317.853-.5,1.349s-.357.992-.555,1.468-.377.912-.536,1.329c-.179.417-.3.734-.4.952Z" transform="translate(-97.3 -73.921)" fill="#003764"/>
      <path id="Path_12110" data-name="Path 12110" d="M220.54,74h4.186l7.082,19.3h-4.761l-1.428-4.642h-6.249L217.881,93.3H213.2Zm4.027,11.188c-.2-.555-.377-1.131-.575-1.726s-.4-1.19-.575-1.746c-.179-.575-.337-1.111-.5-1.607s-.278-.932-.4-1.27c-.119.357-.238.793-.4,1.289s-.317,1.032-.5,1.607-.377,1.151-.575,1.746-.4,1.151-.575,1.706Z" transform="translate(-190.209 -73.921)" fill="#003764"/>
      <path id="Path_12111" data-name="Path 12111" d="M315.52,73.679c.179,0,.516,0,1.032-.02.516,0,1.091-.02,1.706-.02s1.23,0,1.825-.02c.6,0,1.051-.02,1.369-.02a9.042,9.042,0,0,1,5.475,1.389,5.561,5.561,0,0,1,.932,7.141,5.785,5.785,0,0,1-2.4,1.845,5.17,5.17,0,0,1,1.508,1.289,11.531,11.531,0,0,1,1.25,1.924c.4.754.793,1.607,1.21,2.579s.853,2.043,1.349,3.194h-5.039c-.615-1.329-1.131-2.5-1.547-3.531-.417-1.012-.813-1.865-1.17-2.559a6.271,6.271,0,0,0-1.071-1.567,1.724,1.724,0,0,0-1.27-.536H320.2v8.193h-4.7V73.679Zm4.7,8.193h1.19a2.269,2.269,0,0,0,2.4-2.539,4.159,4.159,0,0,0-.119-1.012,2.064,2.064,0,0,0-.417-.833,2.006,2.006,0,0,0-.793-.575,3.14,3.14,0,0,0-1.21-.218h-1.051Z" transform="translate(-272.217 -73.6)" fill="#003764"/>
      <path id="Path_12112" data-name="Path 12112" d="M401.147,74l-.02,3.452h-4.443V93.3h-4.7V77.452H387.4V74Z" transform="translate(-329.854 -73.921)" fill="#003764"/>
    </g>
    <path id="Path_12113" data-name="Path 12113" d="M13.231,9.562,13.152,10l-.06.258-.1.278-.218.436,1.25,1.865,1.527,2.44.139-.258.119-.238.119-.238.1-.2.1-.179.119-.218.179-.337.139-.278.159-.357.179-.4.119-.278.2-.476.079-.238.1-.278.1-.278.02-.1.02-.079.02-.06.02-.079.02-.079.04-.119.04-.119.02-.119.02-.1.02-.1.02-.1.02-.119.02-.139.02-.119.02-.119.02-.159V8.293l-.02-.2-.02-.159-.02-.119-.02-.159-.02-.119-.02-.139-.079-.2-.04-.2-.04-.2-.06-.2-.04-.179-.04-.159-.06-.2L17.3,5.853l-.06-.179L17.159,5.5l-.06-.139L17.04,5.2l-.1-.2L16.8,4.742l-.119-.2-.119-.2L16.4,4.107l-.139-.2-.1-.139-.079-.119-.1-.119-.079-.1-.079-.1-.1-.119-.1-.119-.1-.119-.1-.119-.1-.1-.1-.1L15.1,2.52l-.139-.139-.119-.1-.119-.1-.139-.119-.139-.119-.159-.119-.139-.1-.119-.06-.119-.079-.1-.06-.119-.1-.139-.079-.2-.119-.139-.079-.179-.079L12.874.993,12.7.893l-.139-.06L12.378.755l-.2-.079L11.981.6l-.159-.06-.179-.06-.2-.06-.2-.06-.179-.04L10.93.279l-.218-.04-.2-.04-.159-.02L10.2.16,10.017.14,9.859.12H9.72L9.561.1H8.45L8.272.12H8.133L7.974.14,7.816.16l-.179.02-.2.04L7.3.239l-.179.04-.159.04L6.8.358,6.665.4l-.139.04-.159.04-.159.06L6.07.6l-.139.06-.179.06-.139.06-.119.04-.139.04-.139.06-.139.06-.119.079-.179.1-.119.06-.179.1-.139.079-.139.1-.179.119-.179.139-.2.119L3.471,2l-.159.119-.139.119-.1.079-.159.159-.159.139-.1.1-.139.139L2.4,3l-.139.159L2.1,3.333l-.119.139-.119.159-.079.159-.1.139-.119.159-.119.179-.119.2L1.25,4.6l-.119.238-.119.238-.1.2-.079.2-.079.179-.06.139-.079.218-.06.159-.079.238-.06.2L.4,6.8l-.04.159-.04.2-.04.179-.04.238-.02.2L.2,7.995l-.02.2-.02.238V8.59l-.02.2v.218l.02.258.02.2.02.2.04.238.06.278.06.238.06.2.06.218.079.218.079.258.079.238.1.258.119.278.1.258.06.139.079.2.079.2.079.2.159.337.119.258.119.238.1.218.119.258.119.238.119.218.1.179.1.2.159.278.139.238.139.258.159.278.159.278.159.278.159.278.159.258.218.357.159.258.159.278.2.317.159.258.179.258.139.218.159.258.2.317.139.218.2.3.258.377.218.317.119.179.159.238.139.2.139.2.139.218.2.278.179.258.159.218.218.317.139.2.159.238.159.238.159.218.2.278.2.278.2.278.179.238.179-.238.3-.377.456-.615.317-.417.317-.436.317-.436.4-.536.218-.3.06-.1.119.1.1.079.079.06.119.1.079.079.1.1.159.179.079.1.079.1.079.119.04.06.04.079.06.079.06.079.04.079.06.119.06.119.06.139.04.119.06.179.04.139.04.119.04.139.02.139.02.139.02.119.02.139.02.139v.674l-.02.139-.02.159-.02.139-.04.159-.04.119-.04.139-.04.139-.06.139-.06.139-.079.179-.06.139-.079.139-.119.2-.1.139-.139.2-.1.119-.119.139-.139.139-.179.179-.139.119-.159.139-.2.139-.2.119L10.732,29l-.139.06-.218.1-.218.079-.2.06-.159.04-.159.04-.179.02-.179.02-.139.02H8.51l-.139-.02-.159-.02-.139-.02-.119-.02-.139-.04-.1-.02-.1-.04-.139-.04-.2-.079-.159-.06L6.963,29l-.139-.079-.139-.079-.159-.1-.2-.139-.2-.159-.2-.179-.139-.139-.139-.159-.139-.179L5.4,27.613l-.159-.238L5.1,27.137,4.979,26.9,4.9,26.681l-.079-.218-.1-.317-.06-.3-.04-.278-.02-.238v-.516l.04-.317.04-.258.06-.278.06-.218.139-.377.079-.159.1-.179-4.7-.04-.04.119-.04.119-.04.159-.04.139-.04.159-.079.2-.04.2-.02.159-.04.2-.02.179L0,24.8v.635l.02.218.02.258.02.179.02.2.02.2.04.218.04.258.06.278.079.278.119.357.079.218.1.258.1.218.079.179.079.2.079.159.06.139.06.1.079.139.079.139.119.2.1.159.119.159.1.159.119.159.1.119.159.179.159.179.139.159.139.139.139.139.258.238.258.218.179.139.179.139.218.159.218.159.179.119.238.139.2.119.2.1.238.119.278.139.258.119.2.079.2.079.218.079.258.079.238.06.278.06.3.06.3.06.337.04.3.04.2.02.218.02h.734l.258-.02.2-.02.238-.02.278-.04.3-.06.357-.079.317-.079.258-.079.278-.1.3-.1.337-.139.278-.119.218-.1.278-.139.258-.159.238-.139.278-.179.317-.238.218-.179.3-.258.278-.258.317-.317.139-.159.1-.119.1-.119.139-.159.119-.159.139-.2.1-.139.1-.139.1-.159.06-.139.119-.2.079-.139.1-.179.1-.218.1-.218.119-.278.079-.2.079-.2.079-.238.06-.218.06-.2.079-.3.04-.218.04-.278.04-.238.02-.258.02-.238.02-.3v-.536l-.02-.218-.02-.238-.02-.2-.04-.258-.06-.278-.06-.238-.1-.317-.1-.3-.1-.278-.1-.258L17,22.2l-.139-.3-.2-.456-.218-.5-.2-.4-.159-.317-.179-.337-.2-.4-.179-.337-.2-.377-.2-.377-.2-.377-.218-.357-.3-.5L14.2,16.8l-.238-.377-.179-.3-.179-.3-.258-.417L13.092,15l-.2-.3-.2-.3-.218-.337-.278-.5-.258-.377-.258-.4-.218-.337-.258-.377L10.95,11.7l-.2-.278-.278-.4-.2-.278-.179-.258-.179-.258L9.68,9.9,9.5,9.661l-.238-.317-.3-.4-.1.139-.2.238-.179.238-.238.3-.258.337-.179.238-.258.337-.218.357-.218.317-.3.417-.258.317-.139.2-.079-.04-.079-.06-.079-.06-.238-.2-.159-.179-.179-.218-.139-.179-.159-.258-.1-.159-.079-.179-.079-.159-.1-.238-.079-.238L4.8,9.939l-.04-.258-.04-.218-.02-.3V8.788l.02-.238.02-.2.02-.218.04-.179.04-.179L4.9,7.6l.06-.159.06-.179.079-.2.119-.218.119-.238.119-.159.139-.2.2-.218.2-.2.179-.159.2-.159.2-.139.218-.139.258-.159L7.3,4.96l.2-.079.179-.06.218-.06.218-.06.317-.04.317-.02h.317l.317.02.258.04L10,4.781l.357.119.278.1.4.179.337.218.3.218.258.218.179.179.179.2.159.2.139.2.139.2.079.159.119.258.1.238.1.258.079.258.06.317.04.278.02.3v.337Z" transform="translate(0 0)" fill="url(#radial-gradient)"/>
    <path id="Path_12115" data-name="Path 12115" d="M482.717,33.754a6.362,6.362,0,0,0-2.956-2.718,7.768,7.768,0,0,0,2.38-3.075,8.227,8.227,0,0,0,.952-3.987,7.977,7.977,0,0,0-2.1-5.891C479.583,16.694,477.52,16,474.822,16c-.456,0-1.051,0-1.785.02-.714.02-1.448.02-2.162.02s-1.369,0-1.964.02c-.575.02-.952.02-1.111.02v9.561l4.166,6.407V29.132h-.02V19.888a7.5,7.5,0,0,1,.992-.04h1.349a4.91,4.91,0,0,1,2.142.417,3.6,3.6,0,0,1,1.369,1.091,4.252,4.252,0,0,1,.714,1.547,7.347,7.347,0,0,1,.218,1.726c0,.932.119,3.63-1.468,5.019a2.8,2.8,0,0,0-.337,3.471,18.309,18.309,0,0,1,1.448,1.468,4.442,4.442,0,0,1,.793,1.627,7.272,7.272,0,0,1,.218,1.865,5.632,5.632,0,0,1-1.031,3.471,4.016,4.016,0,0,1-3.412,1.389h-3V35.2l-.04.04L467.8,28.933V44.228a2.574,2.574,0,0,0,2.579,2.579c.079,0,.159.02.218.02.655.02,1.329.02,2.063.02h2.023c3.075,0,5.356-.793,6.883-2.38a8.715,8.715,0,0,0,2.261-6.268A8.836,8.836,0,0,0,482.717,33.754Z" transform="translate(-375.004 -12.746)" fill="#003764"/>
  </g>
</svg>
