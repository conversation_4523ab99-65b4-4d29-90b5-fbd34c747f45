'use client';
import React, { useEffect } from 'react';
import { transferToken } from '@/lib/utils';
import '@copilotkit/react-ui/styles.css';

import { CopilotKit } from '@copilotkit/react-core';

import { Token } from '../../../db/db';
import AuthProvider from './authContext';
import CompetitionProvider from './competitionContext';
import { TeamProvider } from './createTeamContext';
import FantasyUserProvider from './fantasyUserContext';
import ReactQueryProvider from './ReactQueryProvider';
import UserProvider from './userContext';
import FootballProvider from './football/createFootballTeamContext';
import RugbyLeagueContextProvider from './rugby-league/createRugbyLeageContext';
import CricketCommentaryProvider from './commentry/cricket';
import StatsProvider from './stats';
import { SoccerTeamProvider } from './soccer/createSoccerTeamContext';
import { Config } from './config';
import { useSearchParams } from 'next/navigation';

interface RootProviderProps {
  children: React.ReactNode;
}

export default function RootProvider({
  children,
}: Readonly<RootProviderProps>) {
  const searchParams = useSearchParams();
  const referral_type = searchParams.get('referral_type');
  const referral = searchParams.get('referral');
  const userToken = Token;
  useEffect(() => {
    if (userToken) {
      transferToken(userToken);
    }
  }, [userToken]);


  console.log(referral_type, referral)


  // setting referral type and referral in local storage
  useEffect(() => {
    if (referral_type || referral) {
      localStorage.setItem('referralType', referral_type || '');
      localStorage.setItem('referralCode', referral || '');
    }
  }, [referral_type, referral]);

  return (
    // <SocketProvider>
    // <CopilotKit publicApiKey={Config.copilotAPIKEY}>
    <ReactQueryProvider>
      <AuthProvider>
        <CompetitionProvider>
          <SoccerTeamProvider>
            <CricketCommentaryProvider>
              <StatsProvider>
                <FootballProvider>
                  <RugbyLeagueContextProvider>
                    <TeamProvider>
                      <FantasyUserProvider>
                        <UserProvider>{children}</UserProvider>
                      </FantasyUserProvider>
                    </TeamProvider>
                  </RugbyLeagueContextProvider>
                </FootballProvider>
              </StatsProvider>
            </CricketCommentaryProvider>
          </SoccerTeamProvider>
        </CompetitionProvider>
      </AuthProvider>
    </ReactQueryProvider>
    // </CopilotKit>
    // </SocketProvider>
  );
}
