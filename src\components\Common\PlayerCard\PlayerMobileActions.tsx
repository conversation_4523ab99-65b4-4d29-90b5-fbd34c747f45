'use client';
import { MenubarContent } from '@/components/UI/menubar';
import { Badge } from '@/components/UI/badge';
import { cn } from '@/lib/utils';
import { BasePlayer } from '@/lib/types';

const PlayerMobileActions = <T extends BasePlayer>({
  player,
  dreamTeamIdentifier,
  isAddMore,
  handleEditTeam,
  handleSetCaptain,
  handleSetViceCaptain,
  handleRemovePlayer,
}: {
  player: T; // Generic player type constrained to Player
  dreamTeamIdentifier: string | null;
  isAddMore: boolean;
  handleEditTeam: () => void;
  handleSetCaptain: () => void;
  handleSetViceCaptain: () => void;
  handleRemovePlayer: () => void;
}) => {
  const isInDreamTeam: boolean = !!dreamTeamIdentifier && isAddMore;

  const renderBadge = (
    label: string,
    onClick: () => void,
    isDisabled: boolean,
    bgColor: string,
  ) => (
    <Badge
      className={cn(
        'text-white text-[9px] p-0 px-1 cursor-pointer',
        isDisabled ? 'bg-gray-200' : bgColor,
      )}
      onClick={onClick}
    >
      {label}
    </Badge>
  );

  return (
    <MenubarContent
      align="center"
      className="bg-transparent block lg:hidden"
      sideOffset={-30}
    >
      <div className="bg-[#003764] rounded-lg p-3 flex gap-1 relative">
        <div className="flex justify-between items-start w-full">
          <div className="flex flex-col">
            <span className="text-white text-xs font-semibold">
              {player.name}
            </span>
            <span className="text-white text-[9px]">{player.teamName}</span>
            <span className="text-white text-[9px]">{player.role}</span>
          </div>
        </div>
        <div className="w-full space-y-1">
          <div className="flex justify-end space-x-1 w-full">
            {renderBadge(
              'Captain',
              () => {
                if (isAddMore) {
                  handleSetCaptain();
                }
              },
              isInDreamTeam,
              'bg-secondary-100',
            )}
          </div>
          <div className="flex justify-end">
            {renderBadge(
              'Vice Captain',
              () => {
                if (isAddMore) {
                  handleSetViceCaptain();
                }
              },
              isInDreamTeam,
              'bg-secondary-100',
            )}
          </div>
          <div className="flex justify-end">
            {isInDreamTeam
              ? renderBadge(
                  'Substitute',
                  handleEditTeam,
                  false,
                  'bg-secondary-100',
                )
              : renderBadge(
                  'Remove',
                  handleRemovePlayer,
                  false,
                  'bg-orange-400',
                )}
          </div>
        </div>
      </div>
    </MenubarContent>
  );
};

export default PlayerMobileActions;
