'use server';

// utils/auth.ts
import { cookies, headers } from 'next/headers';

export const setAuthCookie = (token: string) => {
  // Set cookie with HttpOnly flag for security
  cookies().set('auth_token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    // maxAge: 60 * 60 * 24 * 7, // 1 week
  });
};

export const getAuthCookie = () => {
  return cookies().get('auth_token')?.value;
};

export const removeAuthCookie = () => {
  cookies().delete('auth_token');
};

export const getCurrentPath = async () => {
  return cookies().get('current_url')?.value;
};
