import React from 'react';

const CoinGraphic = ({ className = '' }: { className?: string }) => (
  <div className={`relative inline-block ${className}`}>
    <div className="w-10 h-10 bg-yellow-300 rounded-full flex items-center justify-center border-2 border-yellow-400 shadow-lg">
      <span className="text-yellow-600 text-lg">★</span>
    </div>
    <div className="absolute -top-1 -right-1 w-3 h-3 text-yellow-300">✨</div>
  </div>
);

export default CoinGraphic; 