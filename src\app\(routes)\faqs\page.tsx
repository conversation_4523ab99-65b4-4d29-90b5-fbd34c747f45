'use client';

import { Minus, Plus } from 'lucide-react';
import React, { useState } from 'react';

import Breadcrumbs from '@/components/UI/Breadcrumbs';
const tippingFAQsList = [
  {
    id: 1,
    questions: 'What is Fantasy Sports?',
    answer:
      "Fantasy sports is an online strategy game that lets you build a virtual team of real players participating in live matches around the world. Your success depends on the players' real-world performance, earning you points and the chance to win cash prizes.",
  },
  {
    id: 2,
    questions: 'How do I sign up for SmartPlay?',
    answer:
      'Signing up is easy! Just click the "Sign Up" button on the homepage, fill in your details, and verify your account to get started.',
  },
  {
    id: 3,
    questions: 'What are SmartCoins?',
    answer:
      "SmartCoins are the SmartPlay platform's currency, used to enter fantasy games and compete for prize pools. You can subscribe or purchase coins in your account and even win them in competitions.",
  },
  {
    id: 4,
    questions: 'How do I add funds to my SmartPlay account?',
    answer:
      'You can buy SmartCoins by either purchasing 100 as a one off purchase, or subscribe for weekly coin deposits to be automatically added to your account.',
  },
  {
    id: 5,
    questions: 'Is it safe to add money to SmartPlay?',
    answer:
      'Adding funds to your SmartPlay account is quick, easy, and secure. We use Stripe to process your details and we don’t store them. You can find out more at ',
    link: 'https://stripe.com',
    subAnswer:
      'And the best part? Once your personal details are verified, you can conveniently withdraw your winnings from SmartPlay directly to your bank account. Allow min 3-5 working days for the withdrawn amount to reflect in your account.',
  },
  {
    id: 6,
    questions: 'What sports are available on SmartPlay?',
    answer:
      'At launch, SmartPlay supports fantasy cricket matches, with additional sports like Rugby League, Aussie Rules, Soccer, and basketball coming soon.',
  },
  {
    id: 7,
    questions: 'How are sports points calculated?',
    answer:
      'Depending on the sport, points are calculated by the players actions in the actual live games. You can find out more about how each sport is scored by reading ',
    link: 'https://smartb.com.au/fantasy/rules-scoring',
  },
  {
    id: 8,
    questions: 'What happens if a match is canceled or postponed?',
    answer:
      'If a match is canceled or postponed, the contest will be voided, and entry fees will be refunded to your SmartPlay account.',
  },
  {
    id: 9,
    questions: 'When do I get my winnings?',
    answer:
      'Your coins are distributed to your account immediately after the game is finished and the finals scores are tallied. To convert your coins to cash, as long as you have completed the Know Your Customer (KYC) Verification Requirements, you can request a withdrawal and it will be paid in upto 5 business days. Your withdrawals will leave our accounts within 24 hours of submission of request to withdraw',
  },
  {
    id: 10,
    questions: 'What kind of contests can I join?',
    answer:
      'You can buy SmartCoins or Subscribe to receive weekly coins. You can use 100 coins to enter a team in any match, from any sport, that is open for entries. Minimum 100 coins are required to enter any game.',
  },
  {
    id: 11,
    questions: 'Can I play SmartPlay on an app?',
    answer:
      'We’ve released apps on the Apple App Store, and on the Google Play Store for you to download and play on the go. The subscription and one-off purchase prices will be higher to cover the costs of store charges.',
  },
  {
    id: 12,
    questions: 'How much are my coins worth?',
    answer:
      'The coins have no real money value and are only usable in the SmartPlay system to enter matches. If you want to convert your coins to cash and withdraw cash, you’ll be able to see your available withdrawal amount in your account. We don’t allow withdrawals of under $50 at a time.',
  },
  {
    id: 13,
    questions: 'Are there any free contests on SmartPlay?',
    answer:
      'Yes! You can play in free contests to practice, or while you’re waiting for your weekly coins. The great thing is, free contest winners win SmartCoins which can be used to also enter paid contents.',
  },
  {
    id: 14,
    questions: 'How do I contact SmartPlay support?',
    answer:
      'If you have any questions or issues, you can reach out to our support team via email at ',
    link: '<EMAIL> or live chat.',
    mail: true,
  },
];

export default function FAQs() {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const handleToggle = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };
  const breadcrumbsLinks = [
    { label: 'Home', href: '/' },
    { label: 'SmartPlay', href: '#' },
    { label: 'FAQs', href: '#' },
  ];
  return (
    <div className="mt-4 rounded-lg bg-off-white-200">
      <div className="pt-[33px] max-799:pt-[18px] pb-[12px] max-799:pb-[9px] px-[27px] max-1024:px-0">
        <Breadcrumbs links={breadcrumbsLinks} />
        <h1 className="text-[31.36px] max-799:text-[22.4px] max-799:leading-[28px] font-normal text-black-100 font-veneerCleanSoft">
          FAQs
        </h1>
      </div>
      <div className="w-full bg-off-white-200 pt-[26px] max-799:pt-3 px-[33px] max-799:px-0 pb-[100px] max-799:pb-[50px]">
        {tippingFAQsList?.map((item, index) => (
          <div
            key={item?.id}
            className={`border-l-4 bg-black-400 ${activeIndex === index
              ? 'border-primary-200'
              : 'border-primary-200/35'
              }  mb-2`}
          >
            <button
              className="flex items-center gap-1.5 py-3 px-[18px] max-799:px-3 cursor-pointer"
              onClick={() => handleToggle(index)}
            >
              <span>
                {activeIndex === index ? (
                  <Minus className="text-secondary-100" />
                ) : (
                  <Plus />
                )}
              </span>
              <span
                className={`${activeIndex === index ? 'text-secondary-100' : 'text-black-100'} text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter `}
              >
                {item?.questions}
              </span>
            </button>
            {activeIndex === index && (
              <div className="pb-3 pl-12 max-799:pl-9 pr-[18px] bg-black-400">
                <p className="text-black-100 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] ">
                  {item?.answer}{' '}
                  {item?.link && !item?.mail ? (
                    <a
                      href={item?.link}
                      target="_blank"
                      className="mt-0.5 text-secondary-100 font-normal font-inter "
                    >
                      {item?.link}
                    </a>
                  ) : (
                    <></>
                  )}
                  {item?.mail ? (
                    <a
                      href={`mailto:${item?.link}`}
                      target="_blank"
                      className="mt-0.5 font-normal font-inter "
                    >
                      {item?.link}
                    </a>
                  ) : (
                    <></>
                  )}
                </p>
                {item?.subAnswer ? (
                  <p className="mt-[22.4px] text-black-100 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] ">
                    {item?.subAnswer}
                  </p>
                ) : (
                  <></>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
