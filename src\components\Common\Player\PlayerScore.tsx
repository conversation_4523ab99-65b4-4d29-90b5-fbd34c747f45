'use client';

import { Tooltip } from '@material-tailwind/react';
import { FootballPlayer, Player } from '../../../../types/competitions';
import { footballPlayersByRole } from '../../../../types/football';
import {
  RugbyLeaguePlayersByRole,
  RugbyPlayer,
} from '../../../../types/rugby-league';

// Generic PlayerScore component
const PlayerScore = <T extends Player | FootballPlayer | RugbyPlayer>({
  score,
  isPlayerLocked,
  player,
  setPlayerRoleToCaptain,
  setPlayerRoleToViceCaiptain,
  activeTab,
}: {
  score: number;
  isPlayerLocked: boolean;
  player: T; // Generic player type
  setPlayerRoleToCaptain: (
    playerId: number,
    role: T extends FootballPlayer
      ? keyof footballPlayersByRole
      : keyof RugbyLeaguePlayersByRole,
  ) => void;
  setPlayerRoleToViceCaiptain: (
    playerId: number,
    role: T extends FootballPlayer
      ? keyof footballPlayersByRole
      : keyof RugbyLeaguePlayersByRole,
  ) => void;
  activeTab: T extends FootballPlayer
  ? keyof footballPlayersByRole
  : keyof RugbyLeaguePlayersByRole;
}) => (
  <div className="md:text-xs text-[12px] absolute top-1 right-1 flex justify-end flex-col items-end">
    {player.lineupStatus === 'unannounced' ? (
      <Tooltip
        content={`Do Not Play`}
        placement="bottom"
        className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
      >
        <p className="text-xs text-black-100">DNP</p>
      </Tooltip>
    ) : (
      score
    )}

    {player.isCaiptain && (
      <button
        className="w-5 h-5 bg-[#FC4714] flex flex-col justify-center items-center text-white rounded-full"
        onClick={() => setPlayerRoleToCaptain(player.playerId, activeTab)}
        disabled={isPlayerLocked}
      >
        C
      </button>
    )}
    {player.isViceCaiptain && (
      <button
        className="w-5 h-5 bg-[#003764] flex flex-col justify-center items-center text-white rounded-full"
        onClick={() => setPlayerRoleToViceCaiptain(player.playerId, activeTab)}
        disabled={isPlayerLocked}
      >
        VC
      </button>
    )}
  </div>
);

export default PlayerScore;
