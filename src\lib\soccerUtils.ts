// utils/soccerRules.ts

import { SoccerPlayerData } from '../../types/competitions';
import { setApiMessage } from '@/helpers/commonFunctions';
import {
  CreateSoccerDreamTeamArgs,
  CreateSoccerTeamPayload,
  SoccerExpertTeam,
  SoccerFavoriteTeam,
  SoccerPlayer,
  SoccerPlayerRole,
  SoccerPlayersByRole,
  SoccerTeamState,
  addSoccerPlayersWithinBudget,
  resetCaptainAndViceCaptain,
} from './types/soccer';

type SportRule = {
  positionType: 'GKP' | 'DEF' | 'MID' | 'FWD';
  maxPlayer: number;
  minPlayer: number;
};

export function getSoccerPlayerByRoleLimit(rules: SportRule[] | undefined): {
  GKP: { min: number; max: number };
  DEF: { min: number; max: number };
  MID: { min: number; max: number };
  FWD: { min: number; max: number };
} {
  const findRule = (type: SportRule['positionType']) =>
    rules?.find((r) => r.positionType === type);

  return {
    GKP: {
      min: findRule('GKP')?.minPlayer ?? 1,
      max: findRule('GKP')?.maxPlayer ?? 1,
    },
    DEF: {
      min: findRule('DEF')?.minPlayer ?? 3,
      max: findRule('DEF')?.maxPlayer ?? 5,
    },
    MID: {
      min: findRule('MID')?.minPlayer ?? 3,
      max: findRule('MID')?.maxPlayer ?? 5,
    },
    FWD: {
      min: findRule('FWD')?.minPlayer ?? 1,
      max: findRule('FWD')?.maxPlayer ?? 3,
    },
  };
}

export const findSoccerCaptainViceCaptain = (
  players: SoccerPlayer[],
): { captain?: SoccerPlayer; viceCaptain?: SoccerPlayer } => {
  const captain = players.find((player) => player.isCaptain);
  const viceCaptain = players.find((player) => player.isViceCaptain);
  return { captain, viceCaptain };
};

export const generateSoccerTeamPayload = (
  players: SoccerPlayersByRole,
  captain?: SoccerPlayer,
  viceCaptain?: SoccerPlayer,
): SoccerPlayerData => {
  const playerData: SoccerPlayerData = {
    GKP: [],
    DEF: [],
    MID: [],
    FWD: [],
    captain: captain
      ? [
          {
            playerId: captain.playerId,
            playerValue: captain.scoreData.playerCurrentSalary,
          },
        ]
      : [],
    viceCaptain: viceCaptain
      ? [
          {
            playerId: viceCaptain.playerId,
            playerValue: viceCaptain.scoreData.playerCurrentSalary,
          },
        ]
      : [],
  };

  for (const role of ALL_SOCCER_ROLES) {
    players[role as keyof SoccerPlayersByRole].forEach((player) => {
      const playerPayload = {
        playerId: player.playerId,
        playerValue: player.scoreData.playerCurrentSalary,
      };

      playerData[role as keyof SoccerPlayerData].push(playerPayload);
    });
  }

  return playerData;
};

export const calculateRemainingBudget = (
  totalBudget: number,
  players: SoccerPlayer[],
): number => {
  const totalCost = players.reduce(
    (sum, player) => sum + (player.scoreData?.playerCurrentSalary || 0),
    0,
  );
  return totalBudget - totalCost;
};

export const initialSoccerTeamState: SoccerTeamState = {
  showFilter: false,
  showMobileFilter: false,
  showPlayerTable: false,
  activeTabPlayer: 'GKP',
  soccerPlayersByRole: {
    GKP: [],
    DEF: [],
    MID: [],
    FWD: [],
  },
  createDreamTeamPayload: undefined,
  remainingBudget: 0,
  totalBudget: 0,
  playerByRoleLimit: {
    GKP: { min: 0, max: 0 },
    DEF: { min: 0, max: 0 },
    MID: { min: 0, max: 0 },
    FWD: { min: 0, max: 0 },
  },
  lastEntry: {
    mode: 'MANUAL',
    players: [],
  },
  reservePlayers: [null, null, null],
  reservePlayersLimit: 3,
  activeReservePosition: 0,
};

export const canAddPlayer = (
  state: SoccerTeamState,
  player: SoccerPlayer,
  role: SoccerPlayerRole,
): { canAdd: boolean; reason?: string } => {
  const MAX_PLAYERS_ALLOWED = 11;
  const { min, max } = state.playerByRoleLimit[role];

  // Get current counts for each role
  const currentCount = state.soccerPlayersByRole[role].length;
  const totalSelected =
    state.soccerPlayersByRole.GKP.length +
    state.soccerPlayersByRole.DEF.length +
    state.soccerPlayersByRole.MID.length +
    state.soccerPlayersByRole.FWD.length;

  const selectedGKP = state.soccerPlayersByRole.GKP.length;
  const selectedDEF = state.soccerPlayersByRole.DEF.length;
  const selectedMID = state.soccerPlayersByRole.MID.length;
  const selectedFWD = state.soccerPlayersByRole.FWD.length;

  // Check if player is already selected
  const isPlayerSelected = state.soccerPlayersByRole[role].some(
    (p) => p.playerId === player.playerId,
  );
  if (isPlayerSelected) {
    return { canAdd: false, reason: 'Player already selected in team' };
  }

  // Check role limits
  if (currentCount >= max) {
    return {
      canAdd: false,
      reason: `Maximum ${max} ${role} player${max > 1 ? 's' : ''} allowed`,
    };
  }

  // Check total players limit
  if (totalSelected >= MAX_PLAYERS_ALLOWED) {
    return { canAdd: false, reason: 'Team is full with 11 players' };
  }

  // Check budget
  const price = player.scoreData?.playerCurrentSalary || 0;
  if (price > state.remainingBudget) {
    setApiMessage('error', 'Insufficient budget');
    return { canAdd: false, reason: 'Insufficient budget' };
  }

  // Special case for goalkeeper when team is almost full
  if (selectedGKP < 1 && totalSelected === 10 && role === 'GKP') {
    return { canAdd: true };
  }

  // Validate team composition rules
  if (
    (selectedDEF === 5 && role === 'MID' && currentCount >= 4) ||
    (selectedFWD === 0 &&
      role === 'MID' &&
      currentCount >= 4 &&
      selectedDEF === 5) ||
    (selectedMID === 5 && selectedFWD === 0 && role !== 'FWD') ||
    ((selectedMID < 3 || selectedDEF < 3) &&
      role === 'FWD' &&
      selectedFWD >= 1) ||
    (selectedGKP < 1 && totalSelected === 10)
  ) {
    return { canAdd: false, reason: 'Invalid team composition' };
  }

  return { canAdd: true };
};

export function addPlayerToRole(
  state: SoccerTeamState,
  player: SoccerPlayer,
  role: SoccerPlayerRole,
): Partial<SoccerTeamState> {
  const { canAdd, reason } = canAddPlayer(state, player, role);
  if (!canAdd) {
    setApiMessage('error', reason || 'Failed to add player');
    return state;
  }
  const price = player.scoreData?.playerCurrentSalary || 0;
  if (state.remainingBudget < price) {
    setApiMessage('error', 'Insufficient budget');
    return state;
  }

  const updatedPlayers = [...state.soccerPlayersByRole[role], player];

  const remainingBudget = calculateRemainingBudget(
    state.totalBudget,
    updatedPlayers,
  );

  const soccerPlayersByRole = {
    ...state.soccerPlayersByRole,
    [role]: updatedPlayers,
  };

  const flatPlayerList = Object.values(soccerPlayersByRole).flat();

  return {
    activeTabPlayer: role,
    soccerPlayersByRole,
    lastEntry: {
      mode: 'MANUAL',
      players: flatPlayerList,
    },
    remainingBudget,
  };
}

export const generateRandomSoccerTeam = (
  playersByRole: Record<SoccerPlayerRole, SoccerPlayer[]>,
  teamComposition: Record<SoccerPlayerRole, { min: number; max: number }>,
): Record<SoccerPlayerRole, SoccerPlayer[]> => {
  const selectedPlayers: Record<SoccerPlayerRole, SoccerPlayer[]> = {
    GKP: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  // Helper function to get random players from a role
  const getRandomPlayers = (
    players: SoccerPlayer[],
    count: number,
  ): SoccerPlayer[] => {
    const shuffled = [...players].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  };

  // First, ensure we have exactly 1 goalkeeper
  if (playersByRole.GKP.length > 0) {
    selectedPlayers.GKP = getRandomPlayers(playersByRole.GKP, 1);
  }

  // Then select defenders (3-5)
  const defCount = Math.floor(Math.random() * 3) + 3; // Random between 3-5
  if (playersByRole.DEF.length >= defCount) {
    selectedPlayers.DEF = getRandomPlayers(playersByRole.DEF, defCount);
  }

  // Then select midfielders (3-5)
  const midCount = Math.floor(Math.random() * 3) + 3; // Random between 3-5
  if (playersByRole.MID.length >= midCount) {
    selectedPlayers.MID = getRandomPlayers(playersByRole.MID, midCount);
  }

  // Calculate remaining spots for forwards
  const remainingSpots = 11 - (1 + defCount + midCount);

  // Ensure at least one forward is selected but never more than 3
  const fwdCount = Math.min(3, Math.max(1, remainingSpots));
  if (playersByRole.FWD.length >= fwdCount) {
    selectedPlayers.FWD = getRandomPlayers(playersByRole.FWD, fwdCount);
  }

  // If we have more than 11 players, adjust the team composition
  const totalPlayers = Object.values(selectedPlayers).flat().length;
  if (totalPlayers > 11) {
    // Remove excess players while maintaining at least one forward
    const excessPlayers = totalPlayers - 11;
    let removedCount = 0;

    // Remove players in this order: MID, DEF (never remove GK or FWD)
    const rolesToRemove: SoccerPlayerRole[] = ['MID', 'DEF'];
    for (const role of rolesToRemove) {
      while (
        playersByRole[role].length > teamComposition[role].min &&
        removedCount < excessPlayers
      ) {
        selectedPlayers[role].pop();
        removedCount++;
      }
      if (removedCount >= excessPlayers) break;
    }
  }

  // Assign captain and vice-captain
  const allPlayers = Object.values(selectedPlayers).flat();
  if (allPlayers.length > 0) {
    // Assign captain (preferably from MID or FWD)
    const potentialCaptains = allPlayers.filter(
      (p) => p.role === 'MID' || p.role === 'FWD',
    );
    const captain =
      potentialCaptains.length > 0
        ? potentialCaptains[
            Math.floor(Math.random() * potentialCaptains.length)
          ]
        : allPlayers[Math.floor(Math.random() * allPlayers.length)];

    // Assign vice-captain (different from captain)
    const potentialViceCaptains = allPlayers.filter((p) => p !== captain);
    const viceCaptain =
      potentialViceCaptains[
        Math.floor(Math.random() * potentialViceCaptains.length)
      ];

    // Update the players with captain/vice-captain status
    allPlayers.forEach((player) => {
      if (player === captain) {
        player.isCaptain = true;
        player.isViceCaptain = false;
      } else if (player === viceCaptain) {
        player.isCaptain = false;
        player.isViceCaptain = true;
      } else {
        player.isCaptain = false;
        player.isViceCaptain = false;
      }
    });
  }

  return selectedPlayers;
};

export function createLuckyTeam(
  state: SoccerTeamState,
  sourcePlayersByRole: SoccerPlayersByRole,
): Partial<SoccerTeamState> {
  const teamComposition: Record<
    SoccerPlayerRole,
    { min: number; max: number }
  > = {
    GKP: state.playerByRoleLimit.GKP,
    DEF: state.playerByRoleLimit.DEF,
    MID: state.playerByRoleLimit.MID,
    FWD: state.playerByRoleLimit.FWD,
  };

  // First reset all players' captain/vice-captain status
  const resetPlayersByRole = Object.entries(sourcePlayersByRole).reduce(
    (acc, [role, players]) => ({
      ...acc,
      [role]: resetCaptainAndViceCaptain(players),
    }),
    {} as Record<SoccerPlayerRole, SoccerPlayer[]>,
  );

  const luckyPlayersByRole = generateRandomSoccerTeam(
    resetPlayersByRole,
    teamComposition,
  );

  const validLuckyPlayersByRole = addSoccerPlayersWithinBudget(
    luckyPlayersByRole,
    state.totalBudget,
  );

  const flatPlayerList = Object.values(validLuckyPlayersByRole).flat();

  return {
    ...state,
    soccerPlayersByRole: validLuckyPlayersByRole,
    remainingBudget: calculateRemainingBudget(
      state.totalBudget,
      flatPlayerList,
    ),
    lastEntry: {
      mode: 'LUCKY',
      players: flatPlayerList,
    },
  };
}

export function removePlayerFromRole(
  state: SoccerTeamState,
  player: SoccerPlayer,
  role: SoccerPlayerRole,
): Partial<SoccerTeamState> {
  const updatedPlayers = state.soccerPlayersByRole[role].filter(
    (p) => p.playerId !== player.playerId,
  );

  const soccerPlayersByRole = {
    ...state.soccerPlayersByRole,
    [role]: updatedPlayers,
  };

  const flatPlayerList = Object.values(soccerPlayersByRole).flat();

  return {
    remainingBudget: calculateRemainingBudget(
      state.totalBudget,
      flatPlayerList,
    ),
    soccerPlayersByRole,
    lastEntry: {
      mode: 'MANUAL',
      players: flatPlayerList,
    },
  };
}

export const resetCaptainStatus = (players: SoccerPlayer[]): SoccerPlayer[] => {
  return players.map((player) => {
    if (player.isCaptain) {
      return { ...player, isCaptain: false };
    }
    return player;
  });
};

export const resetViceCaptainStatus = (
  players: SoccerPlayer[],
): SoccerPlayer[] => {
  return players.map((player) => {
    if (player.isViceCaptain) {
      return { ...player, isViceCaptain: false };
    }
    return player;
  });
};

export const setPlayerToCaptain = (
  state: SoccerTeamState,
  player: SoccerPlayer,
  role: SoccerPlayerRole,
): Partial<SoccerTeamState> => {
  const upcomingPlayerId = player.playerId;

  const newPlayersByRole: SoccerPlayersByRole = {
    GKP: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  for (const role of ALL_SOCCER_ROLES) {
    newPlayersByRole[role] = resetCaptainStatus(
      state.soccerPlayersByRole[role],
    );
  }

  for (const role of ALL_SOCCER_ROLES) {
    newPlayersByRole[role] = newPlayersByRole[role].map((player) => {
      if (player.playerId === upcomingPlayerId) {
        return { ...player, isCaptain: true, isViceCaptain: false };
      }
      return player;
    });
  }

  return {
    ...state,
    soccerPlayersByRole: newPlayersByRole,
  };
};

export const ALL_SOCCER_ROLES = ['GKP', 'DEF', 'MID', 'FWD'] as const;

export const setPlayerToViceCaptain = (
  state: SoccerTeamState,
  player: SoccerPlayer,
  role: SoccerPlayerRole,
): Partial<SoccerTeamState> => {
  const upcomingPlayerId = player.playerId;

  const newPlayersByRole: SoccerPlayersByRole = {
    GKP: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  for (const role of ALL_SOCCER_ROLES) {
    newPlayersByRole[role] = resetViceCaptainStatus(
      state.soccerPlayersByRole[role],
    );
  }

  for (const role of ALL_SOCCER_ROLES) {
    newPlayersByRole[role] = newPlayersByRole[role].map((player) => {
      if (player.playerId === upcomingPlayerId) {
        return { ...player, isViceCaptain: true, isCaptain: false };
      }
      return player;
    });
  }

  return {
    ...state,
    soccerPlayersByRole: newPlayersByRole,
  };
};

export function createSoccerDreamTeamAPIPayload({
  soccerPlayersByRole,
  competitionId,
  eventId,
  sportId,
  tournamentId,
  eventDetails,
  coins,
  bonusCoins,
}: CreateSoccerDreamTeamArgs): CreateSoccerTeamPayload {
  let captain: SoccerPlayer | null = null;
  let viceCaptain: SoccerPlayer | null = null;

  for (const role of ALL_SOCCER_ROLES) {
    const players = soccerPlayersByRole[role as keyof SoccerPlayersByRole];

    if (!captain) {
      captain = players.find((player) => player.isCaptain) ?? null;
    }

    if (!viceCaptain) {
      viceCaptain = players.find((player) => player.isViceCaptain) ?? null;
    }

    if (captain && viceCaptain) break;
  }

  if (!captain || !viceCaptain) {
    throw new Error(
      'Captain or Vice-Captain is missing from selected players.',
    );
  }

  const playerData = generateSoccerTeamPayload(
    soccerPlayersByRole,
    captain,
    viceCaptain,
  );

  return {
    playerData,
    competitionId,
    eventId,
    sportId,
    tournamentId,
    eventName: eventDetails.eventName,
    coins,
    bonusCoins,
    name: `Team ${
      eventDetails.dreamTeams && eventDetails.dreamTeams.length > 0
        ? eventDetails.dreamTeams.length + 1
        : 1
    }`,
  };
}

export function createFavoriteTeam(
  state: SoccerTeamState,
  favoriteTeam: SoccerFavoriteTeam,
  sourcePlayers: SoccerPlayer[],
  eventId: string,
  eventName: string,
  sportId: string,
  tournamentId: string,
  competitionId: string,
  name: string,
): Partial<SoccerTeamState> {
  // Reset all players' captain/vice-captain status before creating favorite team
  const resetPlayers = resetCaptainAndViceCaptain(sourcePlayers);
  const newPlayersByRole = generateSoccerFavoriteTeamPayload(
    favoriteTeam,
    state.playerByRoleLimit,
    resetPlayers,
  );
  const validPlayersByRole = addSoccerPlayersWithinBudget(
    newPlayersByRole,
    state.remainingBudget,
  );
  const flatPlayerList = Object.values(validPlayersByRole).flat();
  const remainingBudget = calculateRemainingBudget(
    state.totalBudget,
    flatPlayerList,
  );

  return {
    ...state,
    soccerPlayersByRole: validPlayersByRole,
    remainingBudget,
    lastEntry: {
      mode: 'FAVORITE',
      players: flatPlayerList,
    },
  };
}

// Add these utility functions before the reducer
export const generateSoccerFavoriteTeamPayload = (
  favoriteTeam: SoccerFavoriteTeam,
  playerByRoleLimit: SoccerTeamState['playerByRoleLimit'],
  availablePlayers: SoccerPlayer[],
): Record<SoccerPlayerRole, SoccerPlayer[]> => {
  const playersByRole: Record<SoccerPlayerRole, SoccerPlayer[]> = {
    GKP: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  // Helper function to find player by ID
  const findPlayerById = (playerId: number): SoccerPlayer | undefined => {
    return availablePlayers.find((p) => p.playerId === playerId);
  };

  // Helper function to check if we've reached the total player limit
  const hasReachedTotalLimit = () => {
    return Object.values(playersByRole).flat().length >= 11;
  };

  // First, handle captain and vice-captain
  let captainPlayer: SoccerPlayer | undefined;
  let viceCaptainPlayer: SoccerPlayer | undefined;

  // Find and validate captain
  if (favoriteTeam.captain) {
    captainPlayer = findPlayerById(favoriteTeam.captain[0].playerId);
    if (captainPlayer) {
      const role = captainPlayer.role;
      if (playersByRole[role].length < playerByRoleLimit[role].max) {
        playersByRole[role].push({ ...captainPlayer, isCaptain: true });
      }
    }
  }

  // Find and validate vice-captain
  if (favoriteTeam.viceCaptain) {
    viceCaptainPlayer = findPlayerById(favoriteTeam.viceCaptain[0].playerId);
    if (viceCaptainPlayer) {
      const role = viceCaptainPlayer.role;
      if (playersByRole[role].length < playerByRoleLimit[role].max) {
        playersByRole[role].push({ ...viceCaptainPlayer, isViceCaptain: true });
      }
    }
  }

  // Now process the rest of the team
  if (favoriteTeam.GKP) {
    for (const player of favoriteTeam.GKP) {
      if (hasReachedTotalLimit()) break;
      const foundPlayer = findPlayerById(player.playerId);
      if (foundPlayer && playersByRole.GKP.length < playerByRoleLimit.GKP.max) {
        // Skip if this player is already added as captain or vice-captain
        if (
          foundPlayer.playerId !== captainPlayer?.playerId &&
          foundPlayer.playerId !== viceCaptainPlayer?.playerId
        ) {
          playersByRole.GKP.push(foundPlayer);
        }
      }
    }
  }

  if (favoriteTeam.DEF) {
    for (const player of favoriteTeam.DEF) {
      if (hasReachedTotalLimit()) break;
      const foundPlayer = findPlayerById(player.playerId);
      if (foundPlayer && playersByRole.DEF.length < playerByRoleLimit.DEF.max) {
        // Skip if this player is already added as captain or vice-captain
        if (
          foundPlayer.playerId !== captainPlayer?.playerId &&
          foundPlayer.playerId !== viceCaptainPlayer?.playerId
        ) {
          playersByRole.DEF.push(foundPlayer);
        }
      }
    }
  }

  if (favoriteTeam.MID) {
    for (const player of favoriteTeam.MID) {
      if (hasReachedTotalLimit()) break;
      const foundPlayer = findPlayerById(player.playerId);
      if (foundPlayer && playersByRole.MID.length < playerByRoleLimit.MID.max) {
        // Skip if this player is already added as captain or vice-captain
        if (
          foundPlayer.playerId !== captainPlayer?.playerId &&
          foundPlayer.playerId !== viceCaptainPlayer?.playerId
        ) {
          playersByRole.MID.push(foundPlayer);
        }
      }
    }
  }

  if (favoriteTeam.FWD) {
    for (const player of favoriteTeam.FWD) {
      if (hasReachedTotalLimit()) break;
      const foundPlayer = findPlayerById(player.playerId);
      if (foundPlayer && playersByRole.FWD.length < playerByRoleLimit.FWD.max) {
        // Skip if this player is already added as captain or vice-captain
        if (
          foundPlayer.playerId !== captainPlayer?.playerId &&
          foundPlayer.playerId !== viceCaptainPlayer?.playerId
        ) {
          playersByRole.FWD.push(foundPlayer);
        }
      }
    }
  }

  // Ensure minimum players in each position
  const ensureMinimumPlayers = () => {
    // Ensure goalkeeper
    if (playersByRole.GKP.length === 0) {
      const availableGK = availablePlayers.filter((p) => p.role === 'GKP');
      if (availableGK.length > 0) {
        playersByRole.GKP.push(availableGK[0]);
      }
    }

    // Ensure forward
    if (playersByRole.FWD.length === 0) {
      const availableFWD = availablePlayers
        .filter((p) => p.role === 'FWD')
        .filter(
          (p) =>
            !Object.values(playersByRole)
              .flat()
              .some((selected) => selected.playerId === p.playerId),
        );
      if (availableFWD.length > 0) {
        playersByRole.FWD.push(availableFWD[0]);
      }
    }

    // Ensure minimum defenders
    while (playersByRole.DEF.length < playerByRoleLimit.DEF.min) {
      const availableDEF = availablePlayers
        .filter((p) => p.role === 'DEF')
        .filter(
          (p) =>
            !Object.values(playersByRole)
              .flat()
              .some((selected) => selected.playerId === p.playerId),
        );
      if (availableDEF.length > 0) {
        playersByRole.DEF.push(availableDEF[0]);
      } else {
        break;
      }
    }

    // Ensure minimum midfielders
    while (playersByRole.MID.length < playerByRoleLimit.MID.min) {
      const availableMID = availablePlayers
        .filter((p) => p.role === 'MID')
        .filter(
          (p) =>
            !Object.values(playersByRole)
              .flat()
              .some((selected) => selected.playerId === p.playerId),
        );
      if (availableMID.length > 0) {
        playersByRole.MID.push(availableMID[0]);
      } else {
        break;
      }
    }
  };

  // Ensure minimum players in each position
  ensureMinimumPlayers();

  // Validate team composition
  const totalPlayers = Object.values(playersByRole).flat().length;
  if (totalPlayers > 11) {
    // If we have more than 11 players, remove excess players while maintaining role balance
    const excessPlayers = totalPlayers - 11;
    let removedCount = 0;

    // Remove players in this order: MID, DEF (never remove GK, FWD, captain, or vice-captain)
    const rolesToRemove: SoccerPlayerRole[] = ['MID', 'DEF'];
    for (const role of rolesToRemove) {
      while (
        playersByRole[role].length > playerByRoleLimit[role].min &&
        removedCount < excessPlayers
      ) {
        const playerToRemove = playersByRole[role].pop();
        // If we're about to remove captain or vice-captain, skip
        if (
          playerToRemove?.playerId === captainPlayer?.playerId ||
          playerToRemove?.playerId === viceCaptainPlayer?.playerId
        ) {
          continue;
        }
        removedCount++;
      }
      if (removedCount >= excessPlayers) break;
    }
  }

  return playersByRole;
};

// Add utility function to generate expert team payload
const generateSoccerExpertTeamPayload = (
  expertTeam: SoccerExpertTeam[],
  availablePlayers: SoccerPlayer[],
): Record<SoccerPlayerRole, SoccerPlayer[]> => {
  const playersByRole: Record<SoccerPlayerRole, SoccerPlayer[]> = {
    GKP: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  // Helper function to find player by ID
  const findPlayerById = (playerId: number): SoccerPlayer | undefined => {
    return availablePlayers.find((p) => p.playerId === playerId);
  };

  // Process each player from the expert team
  expertTeam.forEach((player) => {
    const foundPlayer = findPlayerById(player.playerId);
    if (!foundPlayer) return;

    switch (player.positionType) {
      case 'GKP':
        if (playersByRole.GKP.length < 1) {
          playersByRole.GKP.push(foundPlayer);
        }
        break;
      case 'DEF':
        if (playersByRole.DEF.length < 5) {
          playersByRole.DEF.push(foundPlayer);
        }
        break;
      case 'MID':
        if (playersByRole.MID.length < 5) {
          playersByRole.MID.push(foundPlayer);
        }
        break;
      case 'FWD':
        if (playersByRole.FWD.length < 3) {
          playersByRole.FWD.push(foundPlayer);
        }
        break;
      case 'captain':
        // Find the player in their respective role and set as captain
        Object.values(playersByRole).forEach((players) => {
          const playerIndex = players.findIndex(
            (p) => p.playerId === player.playerId,
          );
          if (playerIndex !== -1) {
            players[playerIndex] = { ...players[playerIndex], isCaptain: true };
          }
        });
        break;
      case 'viceCaptain':
        // Find the player in their respective role and set as vice captain
        Object.values(playersByRole).forEach((players) => {
          const playerIndex = players.findIndex(
            (p) => p.playerId === player.playerId,
          );
          if (playerIndex !== -1) {
            players[playerIndex] = {
              ...players[playerIndex],
              isViceCaptain: true,
            };
          }
        });
        break;
    }
  });

  return playersByRole;
};

export function createExpertTeam(
  state: SoccerTeamState,
  expertTeam: SoccerExpertTeam[],
  players: SoccerPlayer[],
): Partial<SoccerTeamState> {
  // Reset all players' captain/vice-captain status before creating expert team
  const resetPlayers = resetCaptainAndViceCaptain(players);
  const newPlayersByRole = generateSoccerExpertTeamPayload(
    expertTeam,
    resetPlayers,
  );

  const validPlayersByRole = addSoccerPlayersWithinBudget(
    newPlayersByRole,
    state.remainingBudget,
  );

  const remainingBudget = calculateRemainingBudget(
    state.totalBudget,
    Object.values(validPlayersByRole).flat(),
  );

  return {
    ...state,
    soccerPlayersByRole: validPlayersByRole,
    remainingBudget,
    lastEntry: {
      mode: 'EXPERT',
      players: [],
    },
  };
}

export function getDreamTeam(
  state: SoccerTeamState,
  fantasyTeamResponse: any,
  playerId?: number,
  role?: SoccerPlayerRole,
): Partial<SoccerTeamState> {
  const roleToRemove = role ? role : undefined;
  const dreamPlayersByRole = fantasyTeamResponse.result;
  let soccerPlayersByRole: SoccerPlayersByRole = {
    GKP: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  for (const role of ALL_SOCCER_ROLES) {
    if (dreamPlayersByRole[role]) {
      soccerPlayersByRole[role] = dreamPlayersByRole[role].map(
        (player: any) => ({
          ...player,
          role,
          isCaptain:
            player.playerId === dreamPlayersByRole.captain?.[0]?.playerId,
          isViceCaptain:
            player.playerId === dreamPlayersByRole.viceCaptain?.[0]?.playerId,
        }),
      );
    }
  }

  if (roleToRemove && playerId) {
    soccerPlayersByRole[roleToRemove] = soccerPlayersByRole[
      roleToRemove
    ].filter((player) => player.playerId !== playerId);
  }

  const remainingBudget = calculateRemainingBudget(
    state.totalBudget,
    Object.values(soccerPlayersByRole).flat(),
  );

  return {
    ...state,
    soccerPlayersByRole,
    remainingBudget,
    lastEntry: {
      mode: 'MANUAL',
      players: Object.values(soccerPlayersByRole).flat(),
    },
    showFilter: state.showFilter,
    showMobileFilter: state.showMobileFilter,
    showPlayerTable: state.showPlayerTable,
  };
}
