'use client';

import Breadcrumbs from '@/components/UI/Breadcrumbs';
import { useRouter, useSearchParams } from 'next/navigation';
import SportsHeaderTab from '@/components/Header/SportsHeaderTab';
import { useEffect, useState } from 'react';
import { generateUniqueId } from '@/lib/utils';
import { useHeader } from '@/hooks/useHeader';

type RulesTabType = 'Rules' | 'Scoring';

const Layout = ({ children }: { children: React.ReactNode }) => {
  const breadcrumbsLinks = [
    { label: 'Home', href: '/' },
    { label: 'SmartPlay', href: '#' },
    { label: 'Rules & Scoring', href: '#' },
  ];
  const searchParams = useSearchParams();
  const labelId = searchParams.get('labelId');
  const sports = searchParams.get('sports');
  const rules_scoring = searchParams.get('rules_scoring');
  const numberLabelId = Number(labelId);
  const [activeTab, setActiveTab] = useState<number>(numberLabelId ?? 1);
  const [activeRulesTab, setActiveRulesTab] = useState<RulesTabType>('Rules');

  const rulesTab: RulesTabType[] = ['Rules', 'Scoring'];

  const handleTabClick = (rulesTab: RulesTabType) => {
    if (sports) {
      router.push(
        `/rules-scoring?sports=${sports}&rules_scoring=${rulesTab.toLowerCase()}`,
      );
    } else {
      router.push(
        `/rules-scoring?sports=cricket&rules_scoring=${rulesTab.toLowerCase()}`,
      );
    }
    setActiveRulesTab(rulesTab);
  };

  const { tabs: allTabs } = useHeader();
  const tabs = allTabs.filter((tab) => tab.labelId !== 1);

  useEffect(() => {
    const activeTabId = tabs.find(
      (tab) =>
        tab?.label.toLowerCase() === sports ||
        (sports === 'afl' && tab.label === 'AUSSIE RULES') ||
        (sports === 'nrl' && tab.label === 'RUGBY LEAGUE'),
    );
    const numberActiveTabId = Number(activeTabId?.labelId);
    if (sports) {
      setActiveTab(numberActiveTabId);
    } else {
      setActiveTab(1);
    }
  }, [sports]);

  useEffect(() => {
    if (rules_scoring === 'scoring') {
      setActiveRulesTab('Scoring');
    } else {
      setActiveRulesTab('Rules');
    }
  }, [rules_scoring]);

  const router = useRouter();

  const handleTabChanges = (id: number, label?: string) => {
    setActiveTab(id);
    if (label) {
      let urlLabel = label.toLowerCase();
      if (label === 'AUSSIE RULES') urlLabel = 'afl';
      if (label === 'RUGBY LEAGUE') urlLabel = 'nrl';

      router.push(`/rules-scoring?sports=${urlLabel}&rules_scoring=rules`);
    }
  };

  return (
    <div className="mt-4 rounded-lg bg-off-white-200">
      <div className="pt-[33px] max-799:pt-[18px] pb-[12px] max-799:pb-[9px] max-1024:px-0 rounded-t-lg">
        <div className='px-[27px] max-1024:px-0'>
          <Breadcrumbs links={breadcrumbsLinks} />
        </div>
        <h1 className="text-[31.36px] max-799:text-[22.4px] max-799:leading-[28px] font-normal text-black-100 font-veneerCleanSoft">
          <div className='px-[27px] max-1024:px-0'>
            Rules & Scoring
          </div>
        </h1>
        <SportsHeaderTab
          tabs={tabs}
          activeTab={activeTab}
          handleTabChanges={handleTabChanges}
        />
      </div>

      <div className='className="bg-off-white-200  bg-off-white-200 max-799:pb-[22px] px-[27px] max-799:px-0 pt-5'>
        <div className="flex items-center gap-[3px] rounded-lg bg-black-300 p-[3px] w-fit">
          {rulesTab?.map((tab, index) => (
            <button
              key={generateUniqueId()}
              className={`min-w-[108px] text-center px-[9px] py-[6px] cursor-pointer  ${activeRulesTab === tab ? 'bg-primary-200 rounded-[6px]' : ''
                }`}
              onClick={() => handleTabClick(tab)}
            >
              <p
                className={`text-[22.4px] py-[6px] leading-[19px]  font-inter font-medium ${activeRulesTab === tab ? 'text-white' : 'text-primary-200'
                  }`}
              >
                {tab}
              </p>
            </button>
          ))}
        </div>
      </div>

      {children}
    </div>
  );
};

export default Layout;
