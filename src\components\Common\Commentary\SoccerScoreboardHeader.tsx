import Image from 'next/image';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Config } from '../../../helpers/context/config';
import { ScoreBoard } from '../../../../types';

export default function SoccerScoreboardHeader() {
  const { eventDetailsResponse } = useCompetition();
  const homeTeam = eventDetailsResponse?.result?.eventDetails?.homeTeam;
  const awayTeam = eventDetailsResponse?.result?.eventDetails?.awayTeam;
  const scoreBoard = eventDetailsResponse?.result?.eventDetails
    ?.ScoreBoard as unknown as ScoreBoard;
  const homeTeamScore = scoreBoard?.Tr1 || 0;
  const awayTeamScore = scoreBoard?.Tr2 || 0;
  const matchStatus = eventDetailsResponse?.result?.eventDetails?.status;
  const matchTime = scoreBoard?.matchTime || '00:00';

  return (
    <div className="w-full bg-[url('/fantasy/images/cricketHeaderBg.png')] bg-cover bg-no-repeat bg-center min-h-[150px] max-799:min-h-[100px] flex items-center font-veneerCleanSoft font-normal">
      <div className="w-full px-6">
        <div className="flex items-center justify-center md:gap-[60px] gap-0 px-10 md:px-0 text-white">
          {/* Home Team */}
          <div className="flex  items-center">
            <span className="md:text-[16px] text-[12px] font-medium tracking-wider">
              {homeTeam?.name?.split(' ')[0]?.toUpperCase()}
            </span>
            <div className="flex items-center gap-2 mt-[-10px]">
              <span className="md:text-[31.36px] text-[22px] tracking-wider">
                {homeTeam?.name?.split(' ')[1]?.toUpperCase()}
              </span>
              <Image
                src={`${Config.mediaURL}${homeTeam?.flag}`}
                alt={`${homeTeam?.name} Logo`}
                width={32}
                height={32}
                className="h-8 w-8 object-contain"
                unoptimized
              />
            </div>
          </div>

          {/* Score */}
          <div className="flex flex-col items-center">
            <div className="mx-2 md:mx-0 md:text-[31.36px] text-[22px] md:tracking-wider tracking-normal">
              {homeTeamScore} - {awayTeamScore}
            </div>
            {/* <div className="text-sm text-gray-300 mt-1">
              {matchStatus === 'inprogress' ? matchTime : matchStatus}
            </div> */}
          </div>

          {/* Away Team */}
          <div className="flex  items-center">
            <div className="flex items-center gap-2 mt-[-10px]">
              <Image
                src={`${Config.mediaURL}${awayTeam?.flag}`}
                alt={`${awayTeam?.name} Logo`}
                width={32}
                height={32}
                className="h-8 w-8 object-contain"
                unoptimized
              />
              <span className="md:text-[31.36px] text-[22px] tracking-wider">
                {awayTeam?.name?.split(' ')[1]?.toUpperCase()}
              </span>
            </div>
            <span className="md:text-[16px] text-[12px] font-medium tracking-wider">
              {awayTeam?.name?.split(' ')[0]?.toUpperCase()}
            </span>
          </div>
        </div>

        {/* Match Status */}
        {/* <div className="mt-[7px] text-center text-base text-gray-400 font-inter mb-[18px]">
          {scoreBoard?.matchStatus || matchStatus}
        </div> */}
      </div>
    </div>
  );
}
