'use client';;
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { Calendar } from '@/components/UI/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/UI/popover';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import Select from 'react-select';
import * as Yup from 'yup';

import { Button } from '../UI/button';
import { cn } from '@/lib/utils';
import useScreen from '@/hooks/useScreen';
import { Option } from '../UI/CountryDropDown';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import LeagueDropDown from '../UI/LeagueSelect';
import TeamDropDown from '../UI/TeamSelect';

type AllCompetitionsFilterProps = {
  contestType: string;
  setContestType: Dispatch<SetStateAction<string>>;
  handleResetFilter: () => void;
  entries?: number[];
  setEntries?: Dispatch<SetStateAction<number[]>>;
  prizepool?: number[];
  setPrizepool?: Dispatch<SetStateAction<number[]>>;
  screenWidth?: number;
  setIsOpen?: Dispatch<SetStateAction<boolean>>;
  date: Date | undefined;
  setDate: Dispatch<SetStateAction<Date | undefined>>;
  selectedSport: Option | null;
  setSelectedSport: Dispatch<SetStateAction<Option | null>>;
  selectedLeague: Option[] | null;
  setSelectedLeague: Dispatch<SetStateAction<Option[] | null>>;
  selectedTeam: Option[] | null;
  setSelectedTeam: Dispatch<SetStateAction<Option[] | null>>;
  teamSearch: string;
  setTeamSearch: Dispatch<SetStateAction<string>>;
  leagueSearch: string;
  setLeagueSearch: Dispatch<SetStateAction<string>>;
  handleFilterChange: (type: string, value: any) => void;
};

const AllCompetitionsFilter = ({
  contestType,
  setContestType,
  handleResetFilter,
  entries,
  setEntries,
  prizepool,
  setPrizepool,
  screenWidth,
  setIsOpen,
  date,
  setDate,
  selectedSport,
  setSelectedSport,
  selectedLeague,
  setSelectedLeague,
  selectedTeam,
  setSelectedTeam,
  teamSearch,
  setTeamSearch,
  leagueSearch,
  setLeagueSearch,
  handleFilterChange,
}: AllCompetitionsFilterProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { width } = useScreen();
  const sports = searchParams.get('sports') as string;
  const sport_id = searchParams.get('sport_id') as string;
  const filter_sport_id = searchParams.get('filter_sport_id') as string;
  const status = searchParams.get('status');

  const filterSchema = Yup.object().shape({
    sports: Yup.string().required('Sports is required'),
    league: Yup.string().required('League is required'),
    team: Yup.string().required('Team is required'),
    date: Yup.date().required('Date is required'),
    contestType: Yup.string().required('Contest type is required'),
  });

  type FilterFormData = Yup.InferType<typeof filterSchema>;

  const {
    register,
    handleSubmit,
    setValue,
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm<FilterFormData>({
    resolver: yupResolver(filterSchema) as any,
  });

  const sportOptions = [
    { id: '', value: 'all', label: 'All Sports' },
    { id: '4', value: 'cricket', label: 'Cricket' },
    { id: '9', value: 'afl', label: 'Aussie Rules' },
    { id: '12', value: 'nrl', label: 'Rugby League' },
    { id: '8', value: 'soccer', label: 'Football' },
  ];

  const selectStyles = {
    control: (base: any) => ({
      ...base,
      minHeight: '36px',
      backgroundColor: 'white',
      borderColor: '#E2E8F0',
      '&:hover': {
        borderColor: '#CBD5E0',
      },
    }),
    menu: (base: any) => ({
      ...base,
      zIndex: 9999,
    }),
    menuList: (base: any) => ({
      ...base,
      zIndex: 9999,
    }),
    placeholder: (base: any) => ({
      ...base,
      color: '#A0AEC0',
    }),
    option: (base: any, state: any) => ({
      ...base,
      backgroundColor: state.isSelected
        ? '#78C2A7'
        : state.isFocused
          ? '#EDF2F7'
          : 'white',
      color: state.isSelected ? 'white' : '#2D3748',
      '&:active': {
        backgroundColor: '#78C2A7',
      },
    }),
  };

  // Initialize values from URL parameters
  useEffect(() => {
    const sports = searchParams.get('sports');
    const league = searchParams.get('league');
    const team = searchParams.get('team');
    const urlDate = searchParams.get('date');
    const sportId = searchParams.get('sport_id');
    // Set date
    if (urlDate) {
      setDate(new Date(urlDate));
    }
  }, [searchParams]);

  // Clear league and team when sport_id changes
  useEffect(() => {
    const sportId = searchParams.get('sport_id');
    const sports = searchParams.get('sports');
    if (sportId || sports === 'all') {
      setSelectedLeague(null);
      setSelectedTeam(null);
      setLeagueSearch('');
      setTeamSearch('');
      setDate(undefined);
      setContestType('');
    }
  }, [searchParams.get('sport_id')]);

  // Reset all filters when status changes
  useEffect(() => {
    const status = searchParams.get('status');
    if (status) {
      setSelectedLeague(null);
      setSelectedTeam(null);
      setLeagueSearch('');
      setTeamSearch('');
      setDate(undefined);
      setContestType('');

      // Update URL parameters correctly
      const params = new URLSearchParams(window.location.search);
      params.delete('contestType');
      router.push(`${pathname}?${params.toString()}`);
    }
  }, [searchParams.get('status')]);

  // Watch for contestType changes in URL
  useEffect(() => {
    const urlContestType = searchParams.get('contestType');
    if (!urlContestType) {
      setContestType('');
    }
  }, [searchParams.get('contestType')]);

  const handleSportChange = (option: Option | null) => {
    handleFilterChange('sport', option);
  };

  useEffect(() => {
    const currentParams = new URLSearchParams(window.location.search);

    // Handle league updates
    if (selectedLeague && selectedLeague.length > 0) {
      currentParams.set(
        'tournamentId',
        selectedLeague.map((item) => item.value).join(','),
      );
    } else {
      currentParams.delete('tournamentId');
    }

    // Handle team updates
    if (selectedTeam && selectedTeam.length > 0) {
      currentParams.set(
        'teamId',
        selectedTeam.map((item) => item.value).join(','),
      );
    } else {
      currentParams.delete('teamId');
    }

    router.push(`${pathname}?${currentParams.toString()}`);
  }, [selectedLeague, selectedTeam]);

  const FilterContent = () => (
    <div className={cn('w-full relative')}>
      <div className={cn('bg-white rounded-lg p-4 w-full')}>
        {/* Competition Type Checkboxes */}
        <h2 className="text-lg font-bold mb-4 uppercase block md:hidden">
          Choose Your Competition Goals
        </h2>
        <div className="flex items-start md:items-center justify-end mb-6">
          <Button
            variant="ghost"
            onClick={handleResetFilter}
            className="text-primary-200"
          >
            Reset all
          </Button>
        </div>

        {/* Filters Grid */}
        <div
          className={`grid grid-cols-1 md:grid-cols-2 ${!sport_id ? 'md:grid-cols-4' : 'lg:grid-cols-3'} gap-4`}
        >
          {status !== '2' && (
            <div>
              <label className="block text-sm font-medium mb-2">
                Select Date
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="calendar"
                    className="w-full justify-start text-left font-normal border-opacity-10 border"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, 'dd/MM/yyyy') : 'dd/mm/yyyy'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={(newDate) => handleFilterChange('date', newDate)}
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}

          {/* Sport Selector */}
          {!sport_id && (
            <div>
              <label className="block text-sm font-medium mb-2">
                Select Sport
              </label>
              <Select
                value={selectedSport}
                onChange={handleSportChange}
                options={sportOptions}
                placeholder="All Sports"
                styles={selectStyles}
                isClearable={false}
              />
            </div>
          )}

          {/* League Selector */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Select League{' '}
              {selectedLeague &&
                selectedLeague?.length > 0 &&
                `(${selectedLeague?.length})`}
            </label>
            <LeagueDropDown
              control={control}
              className="filter-select"
              name="league"
              sportId={Number(sport_id) || Number(filter_sport_id)}
              placeholder="Select League"
              value={selectedLeague}
              setValue={setSelectedLeague}
              styles={selectStyles}
              status={status}
            />
          </div>

          {/* Team Selector */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Select Team
              {selectedTeam &&
                selectedTeam?.length > 0 &&
                `(${selectedTeam?.length})`}
            </label>
            <TeamDropDown
              control={control}
              name="team"
              className="filter-select"
              sportId={Number(sport_id) || Number(filter_sport_id)}
              placeholder="Select Team"
              value={selectedTeam}
              setValue={setSelectedTeam}
              styles={selectStyles}
              tournamentId={selectedLeague?.map((item) => item.value).join(',')}
              status={status}
            />
          </div>
        </div>
      </div>
    </div>
  );

  // Only render the content, let the parent handle the drawer in mobile view
  return <FilterContent />;
};

export default AllCompetitionsFilter;
