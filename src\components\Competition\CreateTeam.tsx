import { Spinner } from '@material-tailwind/react';
import type { CheckedState } from '@radix-ui/react-checkbox';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useState } from 'react';

import Coins from '@/assets/images/settings/smartBCoins.png';
import { setApiMessage } from '@/helpers/commonFunctions';
import { ALL_ROLES } from '@/helpers/constants/index';
import { useCompetition } from '@/helpers/context/competitionContext';
import {
  type PlayersByRole,
  type LastEntryType,
  useTeam,
} from '@/helpers/context/createTeamContext';
import { useFantasyUser } from '@/helpers/context/fantasyUserContext';
import { createTeam } from '@/helpers/fetchers/competitions';
import { quyerKeys } from '@/lib/queryKeys';
import { cn, LocalStorage } from '@/lib/utils';

import { Token } from '../../../db/db';
import type { PaymentMethod, Player, PlayersByRoleType } from '../../../types/competitions';
import OverlayCelebrations from '../motions/OverlayCelebrations';
import SuccessAction from '../motions/SuccessAction';
import { Button } from '../UI/button'; // Assuming you have a Button component
import OpenDrawerButton from '../UI/Buttons/OpenDrawer';
import { Checkbox } from '../UI/checkbox'; // Assuming you have a Checkbox component
import CustomDialog from '../UI/CustomDialog'; // Make sure to import your CustomDialog component
import CompetitionDetailsHeader from './CompetitionDetailsHeader';
import CricketFantasyUI from './CricketFantasyUI';
import PlayerSelectionUI from './PlayerSelectionUI';
import DreamTeamLoader from '../Loading/DreamTeamLoader';
import EventConfirmModal from '../Common/Modals/EventConfirmModal';

interface CreateTeamProps {
  stats: any; // Replace `any` with the specific type for `stats`
  plyaerRoleTab: keyof PlayersByRole;
  setPlayerRoleTab: Dispatch<SetStateAction<keyof PlayersByRole>>;
  lastEntery: LastEntryType; // Replace `any` with the specific type for `lastEntry`
  removePlayer: (id: number, tabSection: keyof PlayersByRole) => void;
  resetTeam: () => void;
  teamSubmitConfirmation: boolean;
  setTeamSubmitConfirmation: (value: boolean) => void;
  coinsAvailable: boolean;
  handleCloseDialog: () => void;
  playerByRole: PlayersByRoleType;

}

const CreateTeam: React.FC<CreateTeamProps> = ({
  stats,
  plyaerRoleTab,
  setPlayerRoleTab,
  lastEntery,
  removePlayer,
  resetTeam,
  teamSubmitConfirmation,
  setTeamSubmitConfirmation,
  handleCloseDialog,
  playerByRole,

}) => {
  const {
    setShowPlayerTabel,
    createDreamTeam,
    state,
    setBudget,
    showPlayerTabel,
  } = useTeam();
  const searchParams = useSearchParams();
  const event_id = searchParams.get('event_id');
  const compType = searchParams.get('comp_Type');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const dreamTeamId = searchParams.get('dreamTeamId');
  const playerId = searchParams.get('playerId');
  const [acceptTerms, setAcceptTerms] = useState<CheckedState>(false);
  const { eventDetailsResponse, eventTimeStatus, refetchDreamTeam, isDreamTeamResponseLoading } =
    useCompetition();
  const [smartCoinsAmount, setSmartCoinsAmount] = useState(0)
  const [bonusCoinsAmount, setBonusCoinsAmount] = useState(0)
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('smartCoins')

  const localEventId = LocalStorage.getItem('event_id');
  const isSameEvent = localEventId === event_id;
  const localEventType = LocalStorage.getItem('event_type');
  const isSameEventType = localEventType === eventDetailsResponse?.result?.eventConfiguration.eventType;




  const router = useRouter();


  const { freeCompAvailable, freeCompId, freeCompExist } =
    eventDetailsResponse?.result?.eventConfiguration ?? {};
  const showFreeCompAction = freeCompAvailable && freeCompId && !freeCompExist;

  const {
    state: { createDreamTeamPayload, playersByRole: dreamPlayersByRole, reserveState: { reservePlayers } },
    openReserveModal,
    setOpenReserveModal,
    createReservePlayerPayload,
  } = useTeam();
  const [submitedSuccess, setSubmitedSuccess] = useState(false);
  const queryClient = useQueryClient();

  const { mutate: mutateCreateTeam, isPending: isCreatingTeamPending } =
    useMutation({
      mutationFn: () => {
        if (createDreamTeamPayload) {
          return createTeam(createDreamTeamPayload, dreamTeamId);
        }
        return Promise.resolve();
      },
      onSuccess: (data: { status: boolean; message: string }) => {
        setApiMessage('success', data.message);

        LocalStorage.removeItem('dream_team');
        LocalStorage.removeItem('rugby_league_dream_team');
        LocalStorage.removeItem('redirect');
        LocalStorage.removeItem('cricket_reserve_players');
        resetTeam();
        setAcceptTerms(false);
        queryClient.invalidateQueries({
          queryKey: [
            quyerKeys.getFantasyUser,
            quyerKeys.getAllCompetitons,
            quyerKeys.getCompetiton,
          ],
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getFantasyUser],
          exact: true,
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getAllCompetitons],
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getCompetiton],
          exact: true,
        });

        if (!dreamTeamId) {
          setSubmitedSuccess(true);
        }

        if (dreamTeamId) {
          refetchDreamTeam();
        }

        if (dreamTeamId) {
          router.back();
        }

        setTeamSubmitConfirmation(false);
        setBudget(
          eventDetailsResponse?.result?.eventDetails?.CricketSeason
            ?.fantasy_sport_salary_cap ?? 0,
        );

        setSmartCoinsAmount(0);
        setBonusCoinsAmount(0);
        setPaymentMethod('smartCoins');
      },
      onError: (data: any) => {
        if (data?.message) {
          setApiMessage('error', data?.message);
        } else {
          setApiMessage('error', data?.response?.data?.message);
        }
      },
    });




  const isFreeComp = eventDetailsResponse?.result?.eventConfiguration?.eventType === 'free';
  const isDreamTeamExist: boolean = (eventDetailsResponse?.result?.dreamTeams?.length ?? 0) > 0;
  const isFreeCompAndDreamTeamExist = isFreeComp && isDreamTeamExist;

  const handleSubmitedSucessModalClose = () => {
    setSubmitedSuccess(false);
  };

  const clearLastEntery = () => {
    const lastPlayer = lastEntery.players[lastEntery.players.length - 1];

    if (lastEntery?.mode === 'MANUAL' && lastPlayer) {
      removePlayer(lastPlayer?.player?.playerId, lastPlayer?.tabSection);
    }
    if (
      lastEntery.mode === 'EXPERT_FAVORITE' ||
      lastEntery.mode === 'FAVORITE' ||
      lastEntery.mode === 'FEELING_LUCKY'
    ) {
      resetTeam();
    }
  };

  const { user } = useFantasyUser();

  const eventEntryCoin =
    eventDetailsResponse?.result?.eventConfiguration?.entryCoin;
  const userAvailabeCoins = user?.coins - (user?.holdCoins ?? 0);
  const eventConfigurationData =
    eventDetailsResponse?.result?.eventConfiguration;

  let captain: Player | undefined = undefined;
  let viceCaptain: Player | undefined = undefined;

  ALL_ROLES.forEach((role) => {
    state.playersByRole[role]?.forEach((player) => {
      if (player?.isCaiptain) {
        captain = player;
      }
      if (player?.isViceCaiptain) {
        viceCaptain = player;
      }
    });
  });

  const handelTeamCreation = ({
    coins,
    bonusCoins,
  }: {
    coins: number;
    bonusCoins: number;
  }) => {
    if (Token) {
      if (!captain) {
        setApiMessage('error', 'Please Select Captain');
      }

      if (!viceCaptain) {
        setApiMessage('error', 'Please Select Vice Captain');
      }
      if (captain && viceCaptain) {
        queryClient.refetchQueries({
          queryKey: [quyerKeys.getFantasyUser],
          exact: true,
        });

        if (
          eventDetailsResponse?.result?.eventConfiguration?.eventType ===
          'free' ||
          dreamTeamId
        ) {
          const reservePlayerPayload = reservePlayers.filter((player) => player !== null).map((player, index) => ({
            playerId: player?.playerId ?? 0,
            playerValue: player?.scoreData?.playerCurrentSalary ?? 0,
            reserve: true,
            reserveRank: player?.reserveRank ?? 0,
          }));
          createReservePlayerPayload(reservePlayerPayload);
          if (dreamTeamId) {
            handleTeamSubmitConfirmation({
              coins,
              bonusCoins,
            });
          } else {
            setOpenReserveModal(true);
          }
        } else {
          setOpenReserveModal(true);
        }
      }
    } else {
      setOpenReserveModal(true);
    }
  };



  const handleTeamSubmitConfirmation = ({
    coins,
    bonusCoins,
  }: {
    coins: number;
    bonusCoins: number;
  }) => {
    const teamName = `Team ${Array.isArray(eventDetailsResponse?.result?.dreamTeams)
      ? eventDetailsResponse.result.dreamTeams.length + 1
      : 1
      }`;

    let smartCoinsToUse = 0;
    let bonusCoinsToUse = 0;

    switch (paymentMethod) {
      case "both":
        smartCoinsToUse = coins;
        bonusCoinsToUse = bonusCoins;
        break;
      case "smartCoins":
        smartCoinsToUse = eventEntryCoin ?? 0;
        bonusCoinsToUse = 0;
        break;
      case "bonusCoins":
        bonusCoinsToUse = eventEntryCoin ?? 0;
        smartCoinsToUse = 0;
        break;
    }




    createDreamTeam({
      eventId: event_id,
      eventName: eventDetailsResponse?.result?.eventDetails?.eventName!,
      sportId: '4',
      tournamentId: tournament_id,
      competitionId: competition_id,
      name: teamName,
      coins: smartCoinsToUse,
      bonusCoins: bonusCoinsToUse,
    });
    mutateCreateTeam();
  };
  const handleBuySmartBCoins = () => {
    // Safely check localStorage availability
    if (typeof window !== 'undefined') {
      try {
        // Store current path
        const currentPath = window.location.href;
        // Safely stringify and store data
        localStorage.setItem('dream_team', JSON.stringify(state.playersByRole));
        localStorage.setItem('redirect', JSON.stringify({ url: currentPath }));
        router.push('/settings?buy_smart_b_coins=true');
      } catch (error) {
        console.error('Error storing data in localStorage:', error);
      } finally {
        // Close dialog first
        handleCloseDialog();
      }
    }
    // Use Next.js router for navigation
  };

  const handlePlayForFree = () => {
    const { freeCompAvailable, freeCompId, freeCompExist } =
      eventDetailsResponse?.result?.eventConfiguration ?? {};

    if (freeCompAvailable && freeCompId && !freeCompExist) {
      createDreamTeam({
        eventId: event_id,
        eventName: eventDetailsResponse?.result?.eventDetails?.eventName!,
        sportId: '4',
        tournamentId: tournament_id,
        competitionId: `${freeCompId}`,
        name: 'Team 1',
        coins: 0,
        bonusCoins: 0,
      });
      mutateCreateTeam();
    }
  };

  const handelEnterAgainTeam = () => {
    setBudget(
      eventDetailsResponse?.result?.eventDetails?.CricketSeason
        ?.fantasy_sport_salary_cap ?? 0,
    );
    resetTeam();
    handleSubmitedSucessModalClose();

    const compData = eventDetailsResponse?.result;
    const crikectUrl = `/competitions/${compData?.eventConfiguration?.eventId}?event_id=${compData?.eventConfiguration?.eventId}&sport_id=4&tournament_id=${compData?.eventDetails?.CricketTournamentId}&seasonId=${compData?.eventDetails?.CricketSeasonId}&competition_id=${compData?.eventConfiguration?.id}&comp_Type=my`;
    const cricketEnterUrl = `/competitions/${compData?.eventConfiguration?.eventId}?event_id=${compData?.eventConfiguration?.eventId}&sport_id=4&tournament_id=${compData?.eventDetails?.CricketTournamentId}&seasonId=${compData?.eventDetails?.CricketSeasonId}&competition_id=${compData?.eventConfiguration?.id}&add_more=true`;
    const URL =
      compData?.eventConfiguration?.eventType === 'free'
        ? crikectUrl
        : cricketEnterUrl;
    router.push(URL);

    // router.push(
    //   `/competitions/${eventDetailsResponse?.result?.eventConfiguration?.eventId}?event_id=${eventDetailsResponse?.result?.eventConfiguration?.eventId}&sport_id=4&tournament_id=${eventDetailsResponse?.result?.eventDetails?.CricketTournamentId}&competition_id=${eventDetailsResponse?.result?.eventConfiguration?.id}&add_more=true`,
    // );
  };

  const dreamTeamPlayerCount =
    dreamPlayersByRole?.ALL?.length +
    dreamPlayersByRole?.BAT?.length +
    dreamPlayersByRole?.BOW?.length +
    dreamPlayersByRole?.WKP?.length;




  // Effect to clear local storage if event id is changed
  useEffect(() => {
    if (!isSameEvent || !isSameEventType) {
      LocalStorage.removeItem('cricket_reserve_players');
    }
  }, [isSameEvent, event_id, isSameEventType, eventDetailsResponse]);




  return (
    <div className="relative md:px-8 px-0">
      <div>
        <CompetitionDetailsHeader stats={stats} status="team-creation" />
        <div className=" w-full mx-auto flex flex-col md:flex-row ">
          <div className="md:p-4 p-0 md:w-[50%] w-full overflow-hidden rounded-xl md:rounded-[3.7rem]">
            {(isDreamTeamResponseLoading && playerId) ? <DreamTeamLoader showReserve={true} /> : <CricketFantasyUI
              activeTab={plyaerRoleTab}
              setActiveTab={setPlayerRoleTab}
            />}
          </div>

          <div className="md:w-[50%] w-full overflow-auto">
            <PlayerSelectionUI
              stats={stats}
              activeTab={plyaerRoleTab}
              setActiveTab={setPlayerRoleTab}
              playerByRole={playerByRole}
            />
          </div>
        </div>
      </div>

      <div
        className={`${teamSubmitConfirmation || submitedSuccess ? 'hidden' : 'block'} bg-gray-50  fixed bottom-0 left-0 right-0 z-50`}
      >
        <div className="absolute -top-[25px] left-1/2 transform -translate-x-1/2 hidden">
          <button
            onClick={() => {
              setShowPlayerTabel(true);
            }}
          >
            <OpenDrawerButton />
          </button>
        </div>
        <div
          className={cn(
            'flex w-full space-x-2 pb-4 md:space-x-0  md:mt-0 justify-around items-center',
          )}
        >
          <div className="space-x-2 mt-5 flex flex-wrap gap-2 justify-center">
            <Button
              size="sm"
              variant="ghost"
              className=" !bg-[#335F83] text-white w-40"
              onClick={clearLastEntery}
              disabled={
                lastEntery?.players?.length === 0 &&
                lastEntery.mode == 'MANUAL'
              }
            >
              Clear last entry
            </Button>
            <Button
              size="sm"
              className="w-40 "
              onClick={() => handelTeamCreation({
                coins: smartCoinsAmount,
                bonusCoins: bonusCoinsAmount,
              })}
              disabled={
                isCreatingTeamPending ||
                dreamTeamPlayerCount !== 11 ||
                eventTimeStatus?.isLockout ||
                isFreeCompAndDreamTeamExist
              }
            >
              {dreamTeamId ? 'Update Team' : isFreeCompAndDreamTeamExist ? 'Team Entered' : 'Submit Team'}
            </Button>
            <Button
              size="sm"
              variant="link"
              className=" text-secondary-100 w-40 border-secondary-100 border"
              onClick={() => {
                resetTeam();
                LocalStorage.removeItem('dream_team');
                setBudget(
                  eventDetailsResponse?.result?.eventDetails?.CricketSeason
                    ?.fantasy_sport_salary_cap ?? 0,
                );
              }}
            >
              Clear All
            </Button>
          </div>
        </div>
      </div>






      {/* {userAvailabeCoins >= eventEntryCoin! && (
        <CustomDialog
          isOpen={teamSubmitConfirmation}
          onClose={() => {
            setAcceptTerms(false);
            handleCloseDialog();
          }}
          title="Confirmation"
          maxWidth={570}
          className="Confirmation-required-modal"
          outerClickClose={true}
        >
          <div>
            <div className="bg-gray-100 rounded-lg py-[27px] max-799:py-3 px-[18px] max-799:px-3">
              <div className="flex items-center gap-1 mb-2.5">
                <p className="text-[12px] max-799:text-[11.42px] leading-[15px] max-799:leading-[14px] font-inter font-normal text-black-100">
                  Current balance
                </p>
                <p className="w-[calc(100%-100px)] max-799:w-[calc(100%-90px)] h-[1px] bg-black-500"></p>
              </div>
              <div className="flex items-center justify-between">
                <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-primary-200">
                  SmartCoins
                </p>
                <p className="flex items-center gap-1">
                  <span className=" mr-1">
                    <Image src={Coins} alt="icon" unoptimized={true} />
                  </span>
                  <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-primary-200">
                    {(user?.coins - (user?.holdCoins ?? 0)).toFixed(2)}
                  </span>
                </p>
              </div>
            </div>
            <div className="flex items-center justify-between mx-[18px] max-799:mx-3 mt-4 mb-[23px] max-799:mb-3 pb-[23px] max-799:pb-3 border-b border-secondary-100">
              <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-primary-200">
                Entry coins:
              </p>
              <p className="flex items-center gap-1">
                <span className=" mr-1">
                  <Image src={Coins} alt="icon" unoptimized={true} />
                </span>
                <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-primary-200">
                  {eventConfigurationData?.entryCoin?.toFixed(2)}
                </span>
              </p>
            </div>
            <div className="flex items-center justify-between px-[18px] max-799:px-3  mb-[35px] max-799:mb-[15px]">
              <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-primary-200">
                Total:
              </p>
              <p className="flex items-center gap-1">
                <span className=" mr-1">
                  <Image src={Coins} alt="icon" unoptimized={true} />
                </span>
                <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-primary-200">
                  {eventConfigurationData?.entryCoin?.toFixed(2)}
                </span>
              </p>
            </div>
            <div className="bg-gray-100 rounded-lg py-[27px] max-799:py-3 px-[18px] max-799:px-3">
              <div className="flex items-center gap-1 mb-2.5">
                <p className="text-[12px] leading-[15px] font-inter font-normal text-black-100">
                  New balance
                </p>
                <p className="w-[calc(100%-100px)] max-799:w-[calc(100%-90px)] h-[1px] bg-black-500"></p>
              </div>
              <div className="flex items-center justify-between">
                <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-primary-200">
                  SmartCoins
                </p>
                <p className="flex items-center gap-1">
                  <span className=" mr-1">
                    <Image src={Coins} alt="icon" unoptimized={true} />
                  </span>
                  <span className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-primary-200">
                    {(
                      (user?.coins ?? 0) -
                      (user?.holdCoins ?? 0) -
                      (eventConfigurationData?.entryCoin ?? 0)
                    ).toFixed(2)}
                  </span>
                </p>
              </div>
            </div>
            <div className="flex items-start gap-1.5 mt-[15px]">
              <Checkbox
                id="save-card"
                onCheckedChange={(check) => {
                  setAcceptTerms(check);
                }}
              />
              <label
                htmlFor="save-card"
                className="text-[12px] font-normal leading-[17px] font-inter text-black-100 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                By completing this payment, I agree to the{' '}
                <Link
                  href="/terms-conditions"
                  className="text-secondary-100 underline"
                  target="_blank"
                >
                  Terms and Conditions
                </Link>
                . Please note that entries cannot be cancelled once confirmed.
              </label>
            </div>
            <Button
              onClick={() => {
                handleTeamSubmitConfirmation();
              }}
              className="w-full mt-8 flex space-x-2 justify-center items-center"
              disabled={isCreatingTeamPending || !acceptTerms}
            >
              {isCreatingTeamPending && (
                <Spinner {...({} as any)} color="amber" />
              )}
              <span>Confirm and submit team</span>
            </Button>
          </div>
        </CustomDialog>

      )} */}

      {userAvailabeCoins >= eventEntryCoin! && (
        <EventConfirmModal
          isOpen={teamSubmitConfirmation}
          paymentMethod={paymentMethod}
          setPaymentMethod={setPaymentMethod}
          onClose={handleCloseDialog}
          user={user}
          eventConfigurationData={{ entryCoin: eventEntryCoin ?? 0 }}
          isCreatingTeamPending={isCreatingTeamPending}
          acceptTerms={acceptTerms as boolean}
          setAcceptTerms={setAcceptTerms}
          handleTeamSubmitConfirmation={handleTeamSubmitConfirmation}
          smartCoinsAmount={smartCoinsAmount}
          setSmartCoinsAmount={setSmartCoinsAmount}
          bonusCoinsAmount={bonusCoinsAmount}
          setBonusCoinsAmount={setBonusCoinsAmount}
        />
      )}
      {userAvailabeCoins < eventEntryCoin! && (
        <CustomDialog
          isOpen={teamSubmitConfirmation}
          onClose={handleCloseDialog}
          title="Confirmation"
          maxWidth={570}
          className="Confirmation-required-modal"
          outerClickClose={true}
        >
          <div>
            <p className="text-center text-[16px] max-799:text-[14px] leading-[22.4px] max-799:leading-[16px] font-inter text-black-100">
              Please subscribe to continue with your entry. You can stop your
              subscription at any time.
            </p>
            <div
              className={cn(
                'w-full grid gap-4 grid-cols-1 place-items-center mt-8',
                showFreeCompAction ? 'md:grid-cols-2' : 'md:grid-cols-1',
              )}
            >
              <Button onClick={handleBuySmartBCoins} className="w-full">
                Buy SmartCoins
              </Button>
              {showFreeCompAction && (
                <Button
                  onClick={handlePlayForFree}
                  className="w-full"
                  disabled={isCreatingTeamPending}
                >
                  Play For Free Instead
                </Button>
              )}
            </div>
          </div>
        </CustomDialog>
      )}



      <CustomDialog
        isOpen={submitedSuccess}
        onClose={handleSubmitedSucessModalClose}
        maxWidth={570}
        className="submited-success-modal relative"
        outerClickClose={true}
      >
        <div className="text-center z-50 relative">
          <div>
            <SuccessAction />
          </div>
          <p className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-3 mt-[21px] max-799:mb-[23px]">
            Team submitted!
          </p>
          <p className="text-[16px] max-799:text-[14px] leading-[22.4px] max-799:leading-[17px] font-normal font-inter text-black-100 mb-[33px] max-799:mb-[23px]">
            Track your progress on the live leaderboard once the competition
            starts
          </p>
          <div className="mb-[18px] flex items-center justify-between gap-3">
            <div className="w-full">
              <Button
                className="w-full"
                onClick={() => {
                  router.push('/');
                }}
              >
                All Competitions
              </Button>
            </div>
            <div className="w-full">
              <Button className="w-full" onClick={handelEnterAgainTeam}>
                {eventDetailsResponse?.result?.eventConfiguration?.eventType ===
                  'free'
                  ? 'View Comp'
                  : 'Enter Again'}
              </Button>
            </div>
          </div>
        </div>

        <div className="absolute top-0 left-0 w-full h-full">
          <OverlayCelebrations />
        </div>
      </CustomDialog>
    </div>
  );
};

export default CreateTeam;
