'use client';

import Image from 'next/image';
import type { Dispatch, SetStateAction } from 'react';
import SmartBCoins from '@/assets/images/settings/smartBCoins.png';
import BuyCoinsSection from '@/assets/images/settings/buyCoinsSection.png';
import BuyCoinsSectionMobile from '@/assets/images/settings/mobileBuyCoinsSection.png';
import React from 'react';

import { Button } from '@/components/UI/button';
import { Config } from '@/helpers/context/config';
import CustomShape from './PopularTag';
import useScreen from '@/hooks/useScreen';
import PopularTagMobile from './PopularTagMobile';

type CoinCardProps = {
  coinData: any;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  setSelectedCoinsId: Dispatch<SetStateAction<object>>;
};

export default function CoinCard({
  coinData,
  setIsOpen,
  setSelectedCoinsId,
}: Readonly<CoinCardProps>) {

  const { width } = useScreen();
  const handleCoinsBuyModalOpen = (coinData: any) => {
    setIsOpen(true);
    setSelectedCoinsId(coinData);
  };

  const getCustomShapeProps = (price: number) => {
    if (price === 50) {
      return {
        fill: '#1C9A6C',
        stroke: '#4C7292',
        textColor: '#fff',
        // text: 'MOST POPULAR',
      };
    } else if (price === 80) {
      return {
        fill: '#4455C7',
        stroke: '#4C7292',
        textColor: '#fff',
        // text: 'VALUE FOR MONEY',
      };
    } else {
      return {
        fill: '#FC4714',
        stroke: '#4C7292',
        textColor: '#fff',
        // text: 'BEST DEAL',
      };
    }
  };

  const getSubtitle = (price: number): string => {
    if (price === 50) {
      return 'MOST POPULAR';
    } else if (price === 80) {
      return 'VALUE FOR MONEY';
    } else {
      return 'BEST DEAL';
    }
  }

  let coinImageUrl = '/fantasy/images/smartBCoinsSmall.svg';

  if (coinData?.coinImage) {
    if (coinData?.coinImage.includes('uploads')) {
      coinImageUrl = Config?.mediaURL + coinData?.coinImage;
    } else {
      coinImageUrl = coinData?.coinImage;
    }
  }

  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 639);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="relative overflow-hidden max-w-[242px] max-639:max-w-[174px] w-full h-[285px] max-639:h-[217px]  bg-secondary-100/[0.08] rounded-[18px] max-639:rounded-[8px] mt-[27px] bg-[linear-gradient(180deg,_rgba(0,55,100,1)_0%,_rgba(9,11,13,1)_100%)]">
      <div className="p-8 max-639:p-[9px] flex  flex-col items-center justify-center h-full">
        {coinData?.subTitle && (
          <div className="absolute top-[-2px] max-639:top-[-10px]">
            {width > 639 ?
              <CustomShape {...getCustomShapeProps(coinData?.coinPrice)} />
              : <PopularTagMobile {...getCustomShapeProps(coinData?.coinPrice)} />}
            <p className='absolute top-[10px] max-639:top-[18px] w-full flex items-center justify-center text-[14px] max-639:text-[12px] leading-[16px] max-639:leading-[14px] font-inter font-semibold text-white uppercase'>{getSubtitle(coinData?.coinPrice)}</p>
          </div>
        )}
        <div className="flex  justify-center items-center flex-col  gap-2.5 max-639:gap-1.5">
          <div className="flex space-x-1 md:mt-4 mt-0">
            <Image
              src={coinImageUrl}
              alt="coins"
              height={60}
              width={94}
              unoptimized={true}
            />
          </div>

          <div className="flex items-center justify-center gap-x-1">
            <Image
              src={SmartBCoins}
              alt="coins"
              unoptimized={true}
              className="w-[19px] h-[19px]"
            />
            <h2 className="text-[22.4px] leading-[27px] font-inter font-semibold text-white  flex flex-col justify-center items-center">
              {coinData?.coins} Coins
            </h2>
          </div>
          <p className="text-[16px] leading-[19px] font-inter font-normal text-[#93A0AD]">
            {coinData?.coinName}
          </p>
        </div>
        <div
          className="absolute bottom-[-2px] max-639:bottom-[0px] w-[242px] max-639:w-[174px] h-[58px] max-639:h-[42px] flex items-center justify-center"
          style={{
            backgroundImage: `url(${isMobile ? BuyCoinsSectionMobile.src : BuyCoinsSection.src})`
          }}
        >
          <Button
            className="text-center text-[40px] max-639:text-[31.36px] leading-[48px] max-639:leading-[37.76px] font-normal text-white font-apotekCompRegular uppercase p-0 mt-2.5 "
            onClick={() => handleCoinsBuyModalOpen(coinData)}
            variant={'link'}
          >
            Buy $ {coinData?.coinPrice}
          </Button>
        </div>
      </div>
    </div>
  );
}
