'use client';
import { Drawer } from '@material-tailwind/react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Config } from '@/helpers/context/config';
import { useUserProfileContext } from '@/helpers/context/userContext';
import { LocalStorage } from '@/lib/utils';

import whiteLogo from '../../assets/images/icons/smartB-white.png';
import Logo from '../../assets/images/SmartB_New_Logo.png';
import { MobileMenuIcon } from '../images';
import { Button } from '../UI/button';
import MobileDrawerMenu from './MobileDrawerMenu';
import ProfileIconMenu from './profileIconMenu';

const MobileHeaderMenu = () => {
  const router = useRouter();
  const [openMenu, setOpenMenu] = useState(false);

  const openDrawer = () => {
    setOpenMenu(true);
  };
  const closeDrawer = () => {
    setOpenMenu(false);
  };

  const { user } = useUserProfileContext();

  return (
    <>
      <div className="hidden max-799:block">
        {/* Mobile Menu */}
        <div className="bg-white flex justify-between items-center py-1 px-3 gap-4 max-799:flex">
          <div className="flex justify-start items-center">
            <button className="mr-[15px]" onClick={() => openDrawer()}>
              <MobileMenuIcon />
            </button>
            <div>
              <Link href="/">
                {/* <Logo /> */}
                <Image
                  src={Logo}
                  alt="sport league logo"
                  className="h-[25px] w-full"
                  priority
                  unoptimized={true}
                />
              </Link>
            </div>
          </div>
          <div className="flex items-center flex-col">
            {user ? (
              <ProfileIconMenu />
            ) : (
              <div className="flex justify-start items-center gap-[9px]">
                <Button
                  className="p-3 px-6 w-[88px] rounded-lg max-639:px-4 h-[40px] text-[11px]"
                  style={{
                    fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
                    fontWeight: 'normal',
                  }}
                  onClick={() => {
                    const currentPath = window.location.href;
                    LocalStorage.setItem('redirect', { url: currentPath });
                    router.push(Config.siteBaseURL + 'sign-up');
                  }}
                >
                  Sign up
                </Button>
                <Button
                  onClick={() => {
                    const currentPath = window.location.href;
                    LocalStorage.setItem('redirect', { url: currentPath });
                    router.push(Config.siteBaseURL + 'sign-in');
                  }}
                  style={{ fontFamily: 'roboto', fontWeight: 'normal' }}
                  className="bg-transparent w-[81px] h-[40px] border-secondary-100 text-secondary-100 border px-6 py-3 max-639:px-4  text-[11px]"
                >
                  Log in
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
      {/* @ts-expect-error */}
      <Drawer
        open={openMenu}
        onClose={closeDrawer}
        className="bg-primary-200 px-3 pt-[6px] overflow-auto"
        size={393}
      >
        <>
          <div className="flex items-center justify-between">
            <div className="flex justify-start items-center gap-[15px]">
              <button onClick={() => closeDrawer()}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={2}
                  stroke="#fff"
                  className="h-5 w-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
              <Link href="/">
                <Image
                  src={whiteLogo}
                  alt="logo"
                  className="w-[85px]"
                  unoptimized={true}
                />
              </Link>
            </div>
            <div className="flex justify-start items-center gap-[5px]">
              <Button
                onClick={() => {
                  const currentPath = window.location.href;
                  LocalStorage.setItem('redirect', { url: currentPath });
                  router.push(Config.siteBaseURL + 'sign-up');
                }}
              >
                Sign up
              </Button>
              <Button
                onClick={() => {
                  const currentPath = window.location.href;
                  LocalStorage.setItem('redirect', { url: currentPath });
                  router.push(Config.siteBaseURL + 'sign-in');
                }}
                className="bg-transparent border-white border"
              >
                Log in
              </Button>
            </div>
          </div>
          <div>
            <MobileDrawerMenu />
          </div>
        </>
      </Drawer>
    </>
  );
};

export default MobileHeaderMenu;
