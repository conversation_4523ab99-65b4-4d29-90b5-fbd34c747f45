.transction-filter-modal {
  background: linear-gradient(180deg, rgba(147, 160, 173, 1) 0%, rgba(229, 234, 239, 1) 0%, rgba(204, 212, 219, 1) 25%, rgba(182, 192, 202, 1) 50%, rgba(166, 178, 189, 1) 75%, rgba(166, 178, 189, 1) 100%);

  .header-wrap {
    background-color: #003764;
    border-color: #003764;

    .header-text {
      color: #FFFFFF;
    }

    svg {
      path {
        stroke: #FFFFFF;
      }
    }
  }

  .dialog-details {
    padding: 0px;
  }

  .sort-select {
    .select__control {




      .select__indicator {
        svg {

          path {
            fill: #000000 !important;
          }
        }
      }
    }
  }

  .filter-date-picker {

    .MuiOutlinedInput-root {
      background-color: #FFFFFF;

      input {
        color: #989898;
        font-weight: 400;
      }
    }

    #date-picker-inline::placeholder {
      color: #989898;
      opacity: 1;
    }
  }
}