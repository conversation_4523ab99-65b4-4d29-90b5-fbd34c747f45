'use client';
import SoccerFantasyUI from '@/components/Competition/Soccer/SoccerFantasyUI';
import SoccerPlayerSelectionUI from '@/components/Competition/Soccer/SoccerPlayerSelectionUI';
import CompetitionDetailsHeader from '@/components/Common/Competition/CompetitionDetailsHeader';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Token } from '../../../../../../db/db';
import { setApiMessage } from '@/helpers/commonFunctions';
import { useQueryClient } from '@tanstack/react-query';
import { quyerKeys } from '@/lib/queryKeys';
import { useSearchParams } from 'next/navigation';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { CheckedState } from '@radix-ui/react-checkbox';
import { useAuthContext } from '@/helpers/context/authContext';
import PreLoader from '@/components/UI/PreLoader';
import { CopilotPopup, CopilotSidebar } from '@copilotkit/react-ui';

import { SoccerPlayersByRole } from '@/helpers/context/soccer/createSoccerTeamContext';
import { useSoccerStore } from '@/store/soccer/useSoccerStore';
import { SoccerPlayer } from '@/lib/types/soccer';
import { ALL_SOCCER_ROLES } from '@/lib/soccerUtils';
import { Button } from '@/components/UI/button';
import { cn, LocalStorage } from '@/lib/utils';
import SmartPlayLogo from '@/components/UI/Icons/SmartPlayLogo';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import SmartPlayAIAssistant from '@/components/motions/SmartPlayAIAssistant';
import AISmartICon from '@/components/UI/AISmartICon';
import useSoccerAI from '@/hooks/AI/useSoccerAI';
import SoccerAIAction from '@/components/UI/SoccerAI';
import DreamTeamLoader from '@/components/Loading/DreamTeamLoader';

type CreateTeamModeProps = {
  handleTeamSubmitConfirmation: ({
    coins,
    bonusCoins,
  }: {
    coins: number;
    bonusCoins: number;
  }) => void;
  teamSubmitConfirmation: boolean;
  setTeamSubmitConfirmation: Dispatch<SetStateAction<boolean>>;
  showPlayerTabel: boolean;
  coins: number;
  bonusCoins: number;
};

const CreateTeamMode = ({
  handleTeamSubmitConfirmation,
  setTeamSubmitConfirmation,
  teamSubmitConfirmation,
  showPlayerTabel,
  coins,
  bonusCoins,
}: CreateTeamModeProps) => {
  const queryClient = useQueryClient();
  const [acceptTerms, setAcceptTerms] = useState<CheckedState>(false);
  const searchParams = useSearchParams();
  const event_id = searchParams.get('event_id');
  const sport_id = searchParams.get('sport_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const [submitedSuccess, setSubmitedSuccess] = useState(false);
  const { setLoginPopUp } = useAuthContext();

  const {
    remainingBudget,
    resetTeam,
    removePlayer,
    lastEntry,
    soccerPlayersByRole,
    playerByRoleLimit,
  } = useSoccerStore();

  const clearLastEntry = () => {
    if (!lastEntry) return;

    const lastPlayer = lastEntry.players?.[lastEntry.players.length - 1];

    if (lastEntry.mode === 'MANUAL' && lastPlayer) {
      removePlayer(lastPlayer, lastPlayer.role);
    }

    if (
      lastEntry.mode === 'EXPERT' ||
      lastEntry.mode === 'LUCKY' ||
      lastEntry.mode === 'FAVORITE'
    ) {
      const salaryCap =
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap || 0;

      resetTeam(salaryCap, salaryCap);
    }
  };

  const { eventDetailsResponse, refetchDreamTeam, isDreamTeamResponseLoading } =
    useCompetition();

  const validTeamSelection =
    soccerPlayersByRole.DEF.length +
    soccerPlayersByRole.MID.length +
    soccerPlayersByRole.FWD.length +
    soccerPlayersByRole.GKP.length ===
    11;

  const dreamTeamId = searchParams.get('dreamTeamId');

  const headerStats = {
    selectedPlayer: `Player ${Object.values(soccerPlayersByRole).reduce(
      (acc, role) => acc + role.length,
      0,
    )}/11`,
    remainingSalary: remainingBudget,
    totalScore: 200,
  };

  const testUserProfile = {
    nickName: 'SoccerFanatic',
    profilePicUrl: '/path/to/profile-pic.png',
  };

  const testTeamData = {
    favouriteTeam: null,
    expertTeam: null,
  };

  const testEventDetails = {
    salaryCap: 100000000,
    eventName: 'Premier League Fantasy',
  };

  const testCompetitionData = {
    currentRank: 10,
    totalRank: 100,
    totalScore: 300,
    totalLivePoints: 150,
  };

  const handelTeamCreation = () => {
    let captain: SoccerPlayer | null = null;
    let viceCaptain: SoccerPlayer | null = null;

    for (const role of ALL_SOCCER_ROLES) {
      if (soccerPlayersByRole[role].find((player) => player.isCaptain)) {
        captain =
          soccerPlayersByRole[role].find((player) => player.isCaptain) ?? null;
      }
    }

    for (const role of ALL_SOCCER_ROLES) {
      if (soccerPlayersByRole[role].find((player) => player.isViceCaptain)) {
        viceCaptain =
          soccerPlayersByRole[role].find((player) => player.isViceCaptain) ??
          null;
      }
    }

    if (Token) {
      if (!captain) {
        setApiMessage('error', 'Please Select Captain');
      }

      if (!viceCaptain) {
        setApiMessage('error', 'Please Select Vice Captain');
      }
      if (captain && viceCaptain) {
        queryClient.refetchQueries({
          queryKey: [quyerKeys.getFantasyUser],
          exact: true,
        });

        if (
          eventDetailsResponse?.result?.eventConfiguration?.eventType ===
          'free' ||
          dreamTeamId
        ) {
          handleTeamSubmitConfirmation({
            coins: 0,
            bonusCoins: 0,
          });
        } else {
          setTeamSubmitConfirmation(true);
        }
      }
    } else {
      setLoginPopUp(true);
    }
  };









  return (
    <div className="bg-off-white-200 md:px-[32px] md:py-[16px] p-0 mt-2">
      <CompetitionDetailsHeader
        stats={headerStats}
        status={'team-creation'}
        activeTab="Preview"
        userProfile={testUserProfile}
        teamData={testTeamData}
        eventDetails={testEventDetails}
        competitionData={testCompetitionData}
        playerLimits={{
          GKP: 1,
          DEF: 4,
          MID: 4,
          FWD: 2,
        }}
        playersByRole={{
          GKP: soccerPlayersByRole.GKP,
          DEF: soccerPlayersByRole.DEF,
          MID: soccerPlayersByRole.MID,
          FWD: soccerPlayersByRole.FWD,
        }}
        sportType="soccer"
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 h-full gap-x-2 mt-2">
        <div className="w-full h-full">
          {isDreamTeamResponseLoading ? <DreamTeamLoader /> : <SoccerFantasyUI />}
        </div>
        <div className="w-full h-full bg-white rounded-md shadow-lg">
          <SoccerPlayerSelectionUI />
        </div>
      </div>
      {/* <SoccerCopilot Button={SoccerAIAction} /> */}
      {/* <CopilotSidebar
        className="mb-20"
        Button={AISmartICon}
        Header={() => (
          <div className="flex flex-col items-center justify-center">
            <DotLottieReact
              src="https://lottie.host/e7e2eea8-9edc-4cd8-991c-8a27b98bed1d/x1mWEfJe0w.lottie"
              loop
              autoplay
              className="w-40 h-40"
            />
            <div className="flex items-center flex-col">
              <SmartPlayLogo />
              <SmartPlayAIAssistant />
            </div>
          </div>
        )}
      /> */}

      {/* Bottom Action Buttons */}
      <div className="fixed bottom-0 left-1/2 z-50 transform -translate-x-1/2 flex justify-center items-center bg-white w-full">
        <div
          className={cn(
            'flex w-full space-x-2 pb-4 md:space-x-0  md:mt-0 justify-around items-center',
          )}
        >
          <div className="space-x-2 mt-5 flex flex-wrap gap-2 justify-center">
            <Button
              size="sm"
              variant="ghost"
              className=" !bg-[#335F83] text-white w-40"
              onClick={clearLastEntry}
              disabled={
                lastEntry.mode === 'MANUAL' && lastEntry.players.length === 0
              }
            >
              Clear last entry
            </Button>
            <Button
              size="sm"
              className="w-40"
              disabled={!validTeamSelection}
              onClick={handelTeamCreation}
            >
              Submit Team
            </Button>
            <Button
              size="sm"
              variant="link"
              className=" text-secondary-100 w-40 border-secondary-100 border"
              onClick={() => {
                LocalStorage.removeItem('soccer_dream_team');
                resetTeam(
                  eventDetailsResponse?.result?.eventDetails?.SoccerSeason
                    ?.fantasy_sport_salary_cap || 0,
                  eventDetailsResponse?.result?.eventDetails?.SoccerSeason
                    ?.fantasy_sport_salary_cap || 0,
                );
              }}
            >
              Clear All
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateTeamMode;
