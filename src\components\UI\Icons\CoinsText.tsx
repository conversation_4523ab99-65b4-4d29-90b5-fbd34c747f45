import React from 'react';

const CoinsText = () => {
  return (
    <svg
      width="35"
      height="18"
      viewBox="0 0 35 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.49784 0.646709C5.08691 0.513105 4.65699 0.447288 4.22491 0.451841C3.75324 0.42623 3.28139 0.498556 2.83905 0.664264C2.3967 0.829972 1.99345 1.08547 1.6547 1.41468C1.31595 1.74388 1.04903 2.13966 0.870753 2.57708C0.692471 3.01451 0.606688 3.48411 0.618809 3.95632V5.78661L3.27992 9.89245V4.42882C3.27992 4.32405 3.27991 4.21929 3.29458 4.1208C3.34801 3.5603 3.57222 3.01236 4.22387 3.01236C4.87553 3.01236 5.14268 3.70697 5.14268 4.06004V5.35287C5.14268 5.67556 5.56174 5.62527 6.38522 5.62527C7.52928 5.62527 7.72624 5.55193 7.72624 5.30258V3.96156C7.76106 3.24315 7.56283 2.53273 7.16108 1.93614C6.75933 1.33956 6.17558 0.888751 5.4968 0.6509M6.45856 12.6091C5.36479 12.6091 5.14268 12.5839 5.14268 12.9318V14.2246C5.15477 14.3518 5.1399 14.4802 5.09904 14.6013C5.05819 14.7224 4.99226 14.8335 4.90556 14.9275C4.81886 15.0214 4.71335 15.0959 4.59589 15.1463C4.47843 15.1967 4.35167 15.2218 4.22387 15.2199C3.57222 15.2199 3.34801 14.7076 3.29458 14.1293C3.2841 14.0245 3.27992 13.9124 3.27992 13.8034V11.8128L0.618809 7.74262V14.365C0.598174 15.1416 0.847223 15.9014 1.3236 16.5151C1.79997 17.1288 2.47425 17.5585 3.23172 17.7312C3.55689 17.8075 3.88987 17.8454 4.22387 17.8443C4.69312 17.8898 5.16662 17.8313 5.61065 17.6729C6.05468 17.5144 6.45831 17.26 6.79277 16.9278C7.12723 16.5955 7.38429 16.1936 7.54565 15.7506C7.707 15.3076 7.76866 14.8345 7.72624 14.365V13.0763C7.72624 12.629 7.50414 12.6038 6.45856 12.6038"
        fill="#FC4714"
      />
      <path
        d="M15.3429 14.278C15.3857 14.7631 15.3208 15.2517 15.1528 15.7088C14.9848 16.1659 14.7179 16.5802 14.3712 16.9222C14.0244 17.2641 13.6064 17.5252 13.147 17.6868C12.6876 17.8484 12.1982 17.9065 11.7137 17.8569C11.2348 17.8868 10.755 17.8149 10.3059 17.6459C9.85678 17.477 9.44855 17.2148 9.10807 16.8767C8.76759 16.5386 8.50258 16.1323 8.3305 15.6843C8.15842 15.2364 8.08317 14.7571 8.1097 14.278V9.45872C8.14162 8.5214 8.53641 7.63313 9.21077 6.98135C9.88513 6.32956 10.7863 5.96523 11.7242 5.96523C12.662 5.96523 13.5632 6.32956 14.2376 6.98135C14.9119 7.63313 15.3068 8.5214 15.3387 9.45872L15.3429 14.278ZM12.7048 13.5562V10.1271C12.7048 9.35709 12.5309 8.48647 11.7106 8.48647C10.9154 8.48647 10.7163 9.35709 10.7163 10.1271V13.5572C10.7163 14.5001 10.9154 15.222 11.7106 15.222C12.556 15.222 12.7048 14.476 12.7048 13.5562Z"
        fill="#FC4714"
      />
      <path
        d="M19.0705 2.57024C19.1449 3.46496 19.2444 4.08623 17.6289 4.28529C17.4061 4.28403 17.1862 4.23458 16.9843 4.14033C16.7824 4.04608 16.6033 3.90926 16.4593 3.73926C16.3153 3.56926 16.2098 3.37012 16.15 3.15549C16.0902 2.94086 16.0775 2.71583 16.1129 2.49586C16.1873 1.70067 16.4104 0.954723 17.6289 1.00501C18.0217 1.03759 18.3879 1.21659 18.6549 1.50651C18.9219 1.79642 19.0703 2.17609 19.0705 2.57024ZM18.9961 17.4074C18.9961 17.8548 18.9458 17.8548 17.6037 17.8548C16.3612 17.8548 16.336 17.8548 16.336 17.4567V6.32511C16.336 5.927 16.4356 5.90604 17.6037 5.90604C18.8966 5.90604 18.9961 5.90604 18.9961 6.3534V17.4074Z"
        fill="#FC4714"
      />
      <path
        d="M27.3471 17.6075C27.3471 17.8317 27.1229 17.8558 26.1046 17.8558C24.9113 17.8558 24.663 17.8307 24.663 17.6568V10.0517C24.663 9.30576 24.6137 8.48647 23.917 8.48647C23.0967 8.48647 22.774 9.15803 22.774 9.97731V17.5824C22.774 17.8558 22.6745 17.8558 21.3576 17.8558C20.2397 17.8558 20.115 17.8307 20.115 17.6075V6.12501C20.115 5.90081 20.3141 5.92595 21.2329 5.92595C22.3759 5.92595 22.5498 5.85157 22.5498 6.29892C22.5498 6.94534 22.5498 7.1444 22.7237 6.87096C22.9277 6.60256 23.1858 6.37996 23.4812 6.21754C23.7766 6.05512 24.1029 5.9565 24.4388 5.92805C26.0795 5.92805 27.3471 6.89715 27.3471 9.53205V17.6075Z"
        fill="#FC4714"
      />
      <path
        d="M35.0035 14.7002C34.9607 15.6172 34.5555 16.4796 33.8768 17.0977C33.1982 17.7158 32.3018 18.039 31.3849 17.9962C30.4679 17.9534 29.6055 17.5482 28.9874 16.8695C28.3693 16.1909 28.0461 15.2945 28.0889 14.3776C28.0889 14.2036 28.2879 14.2036 29.2319 14.2036C30.1517 14.2036 30.5237 14.1785 30.5237 14.3273C30.5237 14.9988 30.7479 15.5698 31.4928 15.5698C32.2377 15.5698 32.4619 15.1225 32.4619 14.5012C32.4619 12.2644 28.1664 13.2335 28.1664 9.13289C28.1707 8.70253 28.2607 8.27734 28.4311 7.88216C28.6016 7.48697 28.8491 7.12972 29.1591 6.83125C29.4692 6.53279 29.8357 6.2991 30.2371 6.14384C30.6385 5.98859 31.0668 5.91487 31.497 5.927C31.9471 5.93396 32.3914 6.03065 32.8037 6.21141C33.216 6.39217 33.5882 6.65337 33.8983 6.97971C34.2085 7.30605 34.4504 7.69098 34.61 8.11197C34.7695 8.53296 34.8435 8.98155 34.8275 9.43148C34.8275 9.58025 34.529 9.55615 33.6342 9.55615C32.6159 9.55615 32.3173 9.50691 32.3173 9.38224C32.3173 8.56191 31.9443 8.11455 31.4477 8.11455C30.9009 8.11455 30.5771 8.48752 30.5771 9.18318C30.5771 10.8731 35.0015 10.6248 35.0015 14.7013"
        fill="#FC4714"
      />
    </svg>
  );
};

export default CoinsText;
