import React from 'react';

const DefaultPlayer = () => {
  return (
    <svg
      id="Group_132234"
      data-name="Group 132234"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="36"
      height="36"
      viewBox="0 0 36 36"
    >
      <defs>
        <clipPath id="clip-path">
          <circle
            id="Ellipse_14895"
            data-name="Ellipse 14895"
            cx="17"
            cy="17"
            r="17"
            transform="translate(0 0)"
            fill="#003764"
          />
        </clipPath>
        <clipPath id="clip-path-2">
          <rect
            id="Rectangle_63482"
            data-name="Rectangle 63482"
            width="32.194"
            height="31.311"
            fill="none"
          />
        </clipPath>
        <clipPath id="clip-path-4">
          <rect
            id="Rectangle_63478"
            data-name="Rectangle 63478"
            width="6.756"
            height="3.868"
            fill="none"
          />
        </clipPath>
        <clipPath id="clip-path-5">
          <rect
            id="Rectangle_63480"
            data-name="Rectangle 63480"
            width="10.315"
            height="14.286"
            fill="none"
          />
        </clipPath>
      </defs>
      <g
        id="Ellipse_14539"
        data-name="Ellipse 14539"
        fill="#fff"
        stroke="#e7e9ec"
        stroke-width="1"
      >
        <circle cx="18" cy="18" r="18" stroke="none" />
        <circle cx="18" cy="18" r="17.5" fill="none" />
      </g>
      <g
        id="Group_112110"
        data-name="Group 112110"
        transform="translate(1 1)"
        clip-path="url(#clip-path)"
      >
        <g
          id="Group_132211"
          data-name="Group 132211"
          transform="translate(0.903 2.689)"
        >
          <g
            id="Group_132230"
            data-name="Group 132230"
            clip-path="url(#clip-path-2)"
          >
            <g id="Group_132229" data-name="Group 132229">
              <g
                id="Group_132228"
                data-name="Group 132228"
                clip-path="url(#clip-path-2)"
              >
                <path
                  id="Path_208628"
                  data-name="Path 208628"
                  d="M125.756,245.874H108.578c1.422-.952,3.073-3.462,4.12-5.508q.093-.182.179-.359a11.165,11.165,0,0,0,.823-2.062,9.892,9.892,0,0,0,.259-2.968h6.417a9.9,9.9,0,0,0,.259,2.97,11.174,11.174,0,0,0,.822,2.061q.086.177.18.359c1.046,2.046,2.7,4.556,4.12,5.508"
                  transform="translate(-101.089 -218.771)"
                  fill="#e8aa77"
                />
                <g
                  id="Group_132224"
                  data-name="Group 132224"
                  transform="translate(12.703 17.485)"
                  opacity="0.3"
                >
                  <g id="Group_132223" data-name="Group 132223">
                    <g
                      id="Group_132222"
                      data-name="Group 132222"
                      clip-path="url(#clip-path-4)"
                    >
                      <path
                        id="Path_208629"
                        data-name="Path 208629"
                        d="M190.939,255.754q-.144.112-.293.219l-.04.028a13.207,13.207,0,0,1-1.253.793h0l-.044.024a5.181,5.181,0,0,1-1.071.481c-.493.11-.636.085-.678.061-.041.024-.184.049-.677-.061a5.194,5.194,0,0,1-1.071-.481l-.043-.024a13.307,13.307,0,0,1-1.253-.793h0l-.039-.028q-.148-.107-.293-.219a5.836,5.836,0,0,0,.252-2.242h6.252a5.84,5.84,0,0,0,.252,2.243"
                        transform="translate(-184.183 -253.511)"
                      />
                    </g>
                  </g>
                </g>
                <path
                  id="Path_208630"
                  data-name="Path 208630"
                  d="M32.194,323.568H0c.061-.191.125-2.788.19-2.966,1-2.7,2.48-3.762,3.828-4.1.112-.028.224-.051.335-.07l3.675-1.3.319-.113,2.017-.716.151-.054.955-.339a5.334,5.334,0,0,0,4.626,3.177,5.335,5.335,0,0,0,4.626-3.177l.954.339h0l.246.088,1.924.683.423.15,3.571,1.268c.11.019.222.041.335.07,1.348.336,2.828,1.4,3.828,4.1.066.177.129,2.775.19,2.966"
                  transform="translate(0 -292.257)"
                  fill="#fc4714"
                />
                <path
                  id="Path_208631"
                  data-name="Path 208631"
                  d="M320.813,321.075a1.823,1.823,0,0,1-1.207.021l-4.246-1.4a1.825,1.825,0,0,1-1.045-.881l.246.088,1.924.683.423.15,3.571,1.268c.11.019.222.041.334.07"
                  transform="translate(-292.637 -296.828)"
                  fill="#f2f2f2"
                />
                <path
                  id="Path_208632"
                  data-name="Path 208632"
                  d="M64.763,318.827a1.826,1.826,0,0,1-1.046.881l-4.245,1.4a1.823,1.823,0,0,1-1.207-.021c.112-.028.224-.051.335-.07l3.675-1.3.319-.113,2.017-.716Z"
                  transform="translate(-54.246 -296.838)"
                  fill="#f2f2f2"
                />
                <rect
                  id="Rectangle_63479"
                  data-name="Rectangle 63479"
                  width="5.518"
                  height="1.035"
                  transform="translate(19.794 27.655)"
                  fill="#f2f2f2"
                />
                <path
                  id="Path_208633"
                  data-name="Path 208633"
                  d="M128.916,7.9a7.017,7.017,0,0,1-.794-1.163,3.064,3.064,0,0,1,.426-2.586,3.918,3.918,0,0,1,2.835-2.332c.376-.036.761.012,1.133-.05.918-.151,1.533-.906,2.329-1.34a4.448,4.448,0,0,1,4.336.316,6.488,6.488,0,0,1,2.5,3.363,9.986,9.986,0,0,1-.081,6.967.782.782,0,0,1-.283.413,1.014,1.014,0,0,1-.515.1l-8.815.171c-1.537.03-1.674.231-2.145-.9-.4-.974-.182-2.049-.926-2.959"
                  transform="translate(-119.148 0)"
                  fill="#333"
                />
                <path
                  id="Path_208634"
                  data-name="Path 208634"
                  d="M134.313,159.623a2.037,2.037,0,0,0,.283,1.338c.1.132.232.241.325.379a1.346,1.346,0,0,1,.151.94.333.333,0,0,0,.418.364,1.476,1.476,0,0,0,.7-.445,2.555,2.555,0,0,0,.54-1.2c.419-2.066-2.305-4.087-2.418-1.373"
                  transform="translate(-125.044 -147.394)"
                  fill="#e8aa77"
                />
                <path
                  id="Path_208635"
                  data-name="Path 208635"
                  d="M301.92,159.623a2.037,2.037,0,0,1-.283,1.338c-.1.132-.232.241-.325.379a1.259,1.259,0,0,0-.162.833.41.41,0,0,1-.56.419,1.509,1.509,0,0,1-.548-.393,2.554,2.554,0,0,1-.54-1.2c-.419-2.066,2.305-4.087,2.418-1.373"
                  transform="translate(-278.805 -147.394)"
                  fill="#e8aa77"
                />
                <path
                  id="Path_208636"
                  data-name="Path 208636"
                  d="M148.879,31.993a6.714,6.714,0,1,0-12.45,3.489.872.872,0,0,0-.039.316c.043.347.116.9.255,1.708l.007.042a21.611,21.611,0,0,0,.636,2.941,5.746,5.746,0,0,0,1.859,2.24l.038.03a12.76,12.76,0,0,0,1.218.859l.042.026a4.806,4.806,0,0,0,1.047.524,1.394,1.394,0,0,0,.664.071.316.316,0,0,0,.124.019,2.468,2.468,0,0,0,.54-.09,4.8,4.8,0,0,0,1.045-.523l.027-.016.016-.01a12.752,12.752,0,0,0,1.219-.86l.038-.03a5.737,5.737,0,0,0,1.859-2.24,21.606,21.606,0,0,0,.637-2.941c0-.013,0-.027.006-.041.112-.685.2-1.307.254-1.707a.823.823,0,0,0-.032-.3,6.681,6.681,0,0,0,.99-3.51"
                  transform="translate(-126.11 -23.536)"
                  fill="#e8aa77"
                />
                <g
                  id="Group_132227"
                  data-name="Group 132227"
                  transform="translate(10.566 5.896)"
                  opacity="0.6"
                >
                  <g id="Group_132226" data-name="Group 132226">
                    <g
                      id="Group_132225"
                      data-name="Group 132225"
                      clip-path="url(#clip-path-5)"
                    >
                      <path
                        id="Path_208637"
                        data-name="Path 208637"
                        d="M163.266,89.957a4.738,4.738,0,0,0,.136-2.486,7.435,7.435,0,0,0-.231-.853c-.164-.092-.331-.178-.5-.262-.017-.009-.062-.027-.1-.044-.111-.042-.223-.084-.336-.122a12.01,12.01,0,0,0-1.733-.437,12.427,12.427,0,0,0-1.473-.193,18.228,18.228,0,0,0-2.082-.071,6.716,6.716,0,0,0-1.428.146,2.44,2.44,0,0,0-.738.229c-.088.051-.176.1-.26.16-.01.012-.026.029-.051.055l-.146.155c-.044.061-.1.143-.118.165-.141.228-.272.459-.4.694a10.894,10.894,0,0,1-.613,1,14.766,14.766,0,0,1,.7,2.437c.17.824.3,1.657.437,2.488a22.511,22.511,0,0,1,.008,3.16,1.292,1.292,0,0,0,.055.541c0-.***************.239a5.675,5.675,0,0,0,.509.629c.149.16.3.307.465.453-.055-.058.291.226.391.292a13.749,13.749,0,0,0,1.4.819,4.763,4.763,0,0,0,1.2.494c.158.048.318.087.479.121-.063-.024.17-.014.336-.016.061-.017.349-.067.364-.071.093-.023.185-.052.278-.077.026-.007.13-.042.2-.064s.137-.059.159-.071c.2-.108.392-.225.583-.352l.011-.011c.084-.079.171-.153.254-.234a5.9,5.9,0,0,0,.433-.475c0-.018.235-.349.275-.413a11.38,11.38,0,0,0,.647-1.2c.056-.119.109-.239.162-.359.016-.039.034-.08.041-.1.092-.237.185-.473.274-.711.135-.362.264-.725.393-1.089a4.159,4.159,0,0,1-.622-1.992,7.485,7.485,0,0,1,.564-2.576"
                        transform="translate(-153.193 -85.485)"
                        fill="#f3bb7e"
                      />
                    </g>
                  </g>
                </g>
                <path
                  id="Path_208638"
                  data-name="Path 208638"
                  d="M142.269,23.072a7.147,7.147,0,0,0-14.294,0,7.035,7.035,0,0,0,1.04,3.691,1.272,1.272,0,0,0-.038.182c.123.135.243.272.36.413.1.123.2.243.29.359a1.891,1.891,0,0,1,.523-.347c-.042-.282-.085-.564-.132-.844q-.044-.261-.092-.522a4.147,4.147,0,0,0,.165-2.566l.4-.059a1.761,1.761,0,0,1,.306-1.467.217.217,0,0,1-.09.308c1.068-.475,1.345-2.042,2.447-2.432a2.676,2.676,0,0,1-1.173,1.714,7.508,7.508,0,0,0,2.736-1.791l.255.016a1.57,1.57,0,0,1-1.289,1.3A4.274,4.274,0,0,0,136.2,19.85c.1.013.189.025.275.039a1.453,1.453,0,0,1-.821.814,5.3,5.3,0,0,0,1.664-.646q.367.082.73.186a2.872,2.872,0,0,1-1.577.886,5.408,5.408,0,0,0,1.644-.093,2.116,2.116,0,0,0,.883-.472l.2.078.158.069q-.007.316-.014.632l.275-.51.063.034.17.107.508,2.32.224-.072q.013.207.015.416a4.394,4.394,0,0,0-.6,1.826c.052-.05.117-.093.167-.135.3.412-.192,1.2-.31,1.638a2.045,2.045,0,0,1,.315-.619c-.031.116-.062.231-.092.347a2.482,2.482,0,0,1,.874.5c.089.079.172.16.251.242.018-.124.033-.238.047-.338a.866.866,0,0,0-.034-.312,7.034,7.034,0,0,0,1.054-3.713"
                  transform="translate(-119.149 -14.869)"
                  fill="#333"
                />
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
};

export default DefaultPlayer;
