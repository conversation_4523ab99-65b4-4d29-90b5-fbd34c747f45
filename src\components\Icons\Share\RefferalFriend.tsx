import React from 'react'

const Re<PERSON>alFriend = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="40.556" height="29.755" viewBox="0 0 40.556 29.755">
      <g id="Group_170183" data-name="Group 170183" transform="translate(-5.388 -13.604)">
        <g id="Group_170181" data-name="Group 170181" transform="translate(6.388 14.604)">
          <g id="Group_170176" data-name="Group 170176" transform="translate(0 0)">
            <circle id="Ellipse_24291" data-name="Ellipse 24291" cx="7.72" cy="7.72" r="7.72" transform="translate(4.411)" fill="none" stroke="#f0b500" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
            <path id="Path_314151" data-name="Path 314151" d="M1,129.6V126.08A5.882,5.882,0,0,1,6.882,120.2" transform="translate(-1 -101.846)" fill="none" stroke="#f0b500" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
            <path id="Path_314152" data-name="Path 314152" d="M126.271,129.6V126.08a5.882,5.882,0,0,0-5.882-5.882" transform="translate(-102.008 -101.846)" fill="none" stroke="#f0b500" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
          </g>
        </g>
        <line id="Line_1707" data-name="Line 1707" x2="11.107" transform="translate(34.086 22.93)" fill="none" stroke="#f0b500" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" />
        <line id="Line_1708" data-name="Line 1708" y2="11.107" transform="translate(39.639 17.375)" fill="none" stroke="#f0b500" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" />
      </g>
    </svg>

  )
}

export default RefferalFriend