import { generateUniqueId } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';
import React, { useState } from 'react';

interface Match {
  date: string;
  time: string;
  homeTeam: {
    name: string;
    logo: string;
  };
  awayTeam: {
    name: string;
    logo: string;
  };
}

const TeamTracker = () => {
  const [activeTab, setActiveTab] = useState<'fixtures' | 'scores'>('fixtures');

  const matches: Match[] = Array(5).fill({
    date: 'Friday 18/08/2022',
    time: '07:45PM',
    homeTeam: {
      name: 'Sydney Sixers',
      logo: '/api/placeholder/48/48',
    },
    awayTeam: {
      name: 'Adelaide Strikers',
      logo: '/api/placeholder/48/48',
    },
  });

  return (
    <div className="max-w-lg mx-auto bg-white rounded-lg shadow-sm border">
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-lg font-medium">Team tracker</h2>
        <button className="flex items-center gap-2 hover:bg-gray-50 px-2 py-1 rounded">
          <span className="text-sm">Round - 9</span>
          <ChevronDown className="w-4 h-4" />
        </button>
      </div>

      <div className="w-full border-b">
        <div className="grid grid-cols-2">
          <button
            onClick={() => setActiveTab('fixtures')}
            className={`text-sm font-medium py-2 text-center transition-colors
              ${
                activeTab === 'fixtures'
                  ? 'border-b-2 border-blue-600 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
          >
            Upcoming fixtures
          </button>
          <button
            onClick={() => setActiveTab('scores')}
            className={`text-sm font-medium py-2 text-center transition-colors
              ${
                activeTab === 'scores'
                  ? 'border-b-2 border-blue-600 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
          >
            Scores
          </button>
        </div>
      </div>

      <div className="divide-y">
        {matches.map((match, index) => (
          <div
            key={generateUniqueId()}
            className="p-4 hover:bg-gray-50 transition-colors"
          >
            <div className="text-xs text-gray-500 mb-2">
              {match.date} | {match.time}
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-pink-500 flex items-center justify-center">
                  <img
                    src={match.homeTeam.logo}
                    alt={match.homeTeam.name}
                    className="w-6 h-6"
                  />
                </div>
                <span className="text-sm font-medium">
                  {match.homeTeam.name}
                </span>
              </div>
              <span className="text-sm font-medium text-gray-500">VS</span>
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">
                  {match.awayTeam.name}
                </span>
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                  <img
                    src={match.awayTeam.logo}
                    alt={match.awayTeam.name}
                    className="w-6 h-6"
                  />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TeamTracker;
