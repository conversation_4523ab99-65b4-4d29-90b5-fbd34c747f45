import React from 'react';
import EmptyPlayerCard from './EmptyPlayerCard';
import { BasePlayer } from '@/lib/types';
import FilledPlayerCard from './FilledPlayerCard';
import PlayerActions from './PlayerActions';
import { <PERSON><PERSON>r, MenubarMenu, MenubarTrigger } from '@/components/UI/menubar';
import PlayerMobileActions from './PlayerMobileActions';

export type PlayerCardVariant = 'empty' | 'filled';
export type EventStatus = 'upcoming' | 'live' | 'completed';

// Generic base player card props
type EmptyCardProps<TRole extends string = string> = {
  type: PlayerCardVariant;
  activePlayerTab: TRole;
  setActivePlayerTab: (role: TRole) => void;
  setShowPlayerTabel: (show: boolean) => void;
  isActive?: boolean;
};

type FilledCardProps<
  TPlayer extends BasePlayer = BasePlayer,
  TRole extends string = string,
> = {
  type: PlayerCardVariant;
  setShowPlayerTabel: (show: boolean) => void;
  isActive?: boolean;
  player: TPlayer;
  eventStatus: EventStatus;
  activePlayerTab: TRole;
  setActivePlayerTab: (role: TRole) => void;
  isPlayerLocked?: boolean;
  onRemove: () => void;
  onSubstitute: () => void;
  onSetCaptain: () => void;
  onSetViceCaptain: () => void;
  showSubstituteButton: boolean;
  showRemoveButton: boolean;
  dreamTeamId: string | null;
  addMore: boolean;
};

type PlayerCardProps<
  TPlayer extends BasePlayer = BasePlayer,
  TRole extends string = string,
> = EmptyCardProps<TRole> | FilledCardProps<TPlayer, TRole>;

const PlayerCard = <
  TPlayer extends BasePlayer = BasePlayer,
  TRole extends string = string,
>(
  props: PlayerCardProps<TPlayer, TRole>,
) => {
  const {
    type,
    activePlayerTab,
    setActivePlayerTab,
    setShowPlayerTabel,
    isActive,
  } = props;

  if (type === 'empty') {
    return (
      <EmptyPlayerCard
        tabSection={activePlayerTab ?? ''}
        setActiveTab={setActivePlayerTab!}
        setShowPlayerTabel={setShowPlayerTabel}
        isActive={isActive}
      />
    );
  }

  const {
    player,
    eventStatus,
    onRemove,
    addMore,
    dreamTeamId,
    onSubstitute,
    onSetCaptain,
    onSetViceCaptain,
    showRemoveButton,
    showSubstituteButton,
  } = props as FilledCardProps<TPlayer, TRole>;
  const disabledMobileActions =
    eventStatus === 'live' || eventStatus === 'completed';

  // Filled card logic
  return (
    <Menubar className="border-0 bg-transparent" asChild>
      <MenubarMenu>
        <MenubarTrigger disabled={disabledMobileActions}>
          <div className="relative group">
            <FilledPlayerCard player={player} eventStatus={eventStatus} />
            {eventStatus === 'upcoming' && (
              <div className="hidden md:block absolute bottom-0 left-0 w-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div className="relative">
                  <PlayerActions
                    onRemove={onRemove}
                    addMore={addMore}
                    dreamTeamId={dreamTeamId}
                    onSubstitute={onSubstitute}
                    onSetCaptain={onSetCaptain}
                    onSetViceCaptain={onSetViceCaptain}
                    showSubstituteButton={showSubstituteButton}
                    showRemoveButton={showRemoveButton}
                  />
                </div>
              </div>
            )}
          </div>
        </MenubarTrigger>
        <PlayerMobileActions
          player={player}
          dreamTeamIdentifier={dreamTeamId}
          isAddMore={addMore}
          handleEditTeam={onSubstitute}
          handleSetCaptain={onSetCaptain}
          handleSetViceCaptain={onSetViceCaptain}
          handleRemovePlayer={onRemove}
        />
      </MenubarMenu>
    </Menubar>
  );
};

export default PlayerCard;
