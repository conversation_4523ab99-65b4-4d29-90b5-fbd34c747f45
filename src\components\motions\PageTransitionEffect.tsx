'use client';
import { AnimatePresence, motion } from 'motion/react';
import { LayoutRouterContext } from 'next/dist/shared/lib/app-router-context.shared-runtime';
import { usePathname } from 'next/navigation';
import { useContext, useRef } from 'react';

function FrozenRouter(props: Readonly<{ children: React.ReactNode }>) {
  const context = useContext(LayoutRouterContext);
  const frozen = useRef(context).current;

  return (
    <LayoutRouterContext.Provider value={frozen}>
      {props.children}
    </LayoutRouterContext.Provider>
  );
}

const variantsCollection = {
  smoothSlide: {
    hidden: { opacity: 0, x: -50, scale: 0.95 },
    enter: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 150,
        damping: 20,
        duration: 0.3,
      },
    },
    exit: {
      opacity: 0,
      x: 50,
      scale: 0.95,
      transition: { type: 'tween', duration: 0.2 },
    },
  },

  bottomToTopFade: {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    enter: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 150,
        damping: 20,
        duration: 0.3,
      },
    },
    exit: {
      opacity: 0,
      y: -50,
      scale: 0.95,
      transition: { type: 'tween', duration: 0.2 },
    },
  },

  quickZoom: {
    hidden: { opacity: 0, scale: 0.7 },
    enter: {
      opacity: 1,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 200,
        damping: 25,
        duration: 0.2,
      },
    },
    exit: {
      opacity: 0,
      scale: 0.7,
      transition: { type: 'tween', duration: 0.15 },
    },
  },

  quickRotate: {
    hidden: { opacity: 0, rotateY: 90, scale: 0.95 },
    enter: {
      opacity: 1,
      rotateY: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 180,
        damping: 22,
        duration: 0.3,
      },
    },
    exit: {
      opacity: 0,
      rotateY: -90,
      scale: 0.95,
      transition: { type: 'tween', duration: 0.2 },
    },
  },

  quickFade: {
    hidden: { opacity: 0 },
    enter: {
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 200,
        damping: 25,
        duration: 0.2,
      },
    },
    exit: {
      opacity: 0,
      transition: { type: 'tween', duration: 0.15 },
    },
  },
};

const PageTransitionEffect = ({
  children,
  variant = 'smoothSlide',
}: {
  children: React.ReactNode;
  variant?: keyof typeof variantsCollection;
}) => {
  const key = usePathname();
  const selectedVariants = variantsCollection[variant];

  return (
    <AnimatePresence mode="popLayout">
      <motion.div
        key={key}
        initial="hidden"
        animate="enter"
        exit="exit"
        variants={selectedVariants}
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
        }}
      >
        <FrozenRouter>{children}</FrozenRouter>
      </motion.div>
    </AnimatePresence>
  );
};

export default PageTransitionEffect;
