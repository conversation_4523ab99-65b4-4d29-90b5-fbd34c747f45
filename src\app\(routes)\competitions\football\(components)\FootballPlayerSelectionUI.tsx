'use client';
import { <PERSON><PERSON><PERSON><PERSON>on } from '@material-tailwind/react';
import type { SortingState } from '@tanstack/react-table';
import { Search, SlidersHorizontal } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import { useState, useEffect } from 'react';
import Select, { components } from 'react-select';

import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { Card } from '@/components/UI/card';
import { Input } from '@/components/UI/input';
import { useDataTable } from '@/hooks/useDataTable';
import useScreen from '@/hooks/useScreen';
import { cn, formatNumberWithCommas } from '@/lib/utils';

import DataTable from '@/components/UI/DataTabel';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import SimpleTabs from '@/components/UI/SimpleTabs';
import type { FiltersState } from '@/components/Competition/CricketPlayerFilter';
import {
  footballPlayersByRole,
  TabItem,
} from '../../../../../../types/football';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import FootballPlayerFilter from '@/components/Common/Player/PlayerFilter';
import MobileFootballPlayerFilter from '@/components/Common/Player/MobilePlayerFilter';
import { ProfileStats } from '@/components/Competition/CompetitionDetailsHeader';
import { footballUpcomingColumn } from '@/components/UI/DataTabel/columns/createTeamColumn';
import { useCompetition } from '@/helpers/context/competitionContext';

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

type FootballPlayerSelectionUIProps = {
  activeTab: keyof footballPlayersByRole;
  setActiveTab: Dispatch<SetStateAction<keyof footballPlayersByRole>>;
  stats: ProfileStats;
  playerByRole: footballPlayersByRole;
};

const getSelectionMessage = (activeTab: keyof footballPlayersByRole) => {
  switch (activeTab) {
    case 'BL':
      return 'Select 1 Goalkeeper';
    case 'FOL':
      return 'Select 3 - 5 Defenders';
    case 'FL':
      return 'Select 1 - 3 Forwards';
    case 'MID':
      return 'Select 3 - 5 Midfielders';
    default:
      return '';
  }
};

const FootballPlayerSelectionUI = ({
  activeTab,
  setActiveTab,
  stats,
  playerByRole,
}: FootballPlayerSelectionUIProps) => {
  const [showFilter, setShowFilter] = useState(false);
  const [showMobileFilter, setShowMobileFilter] = useState(false);
  const { width } = useScreen();
  const playerData: footballPlayersByRole = playerByRole;

  // Using context within a memo/effect to prevent context conflicts
  const footballContext = useFootballContext();
  const {
    state: {
      playersByRole: { BL, FL, FOL, HBL, HFL, MID, IC },
      playerByRoleLimit: {
        BL: BLLIMIT,
        FOL: FOLLIMIT,
        FL: FLLIMIT,
        HBL: HBLLIMIT,
        HFL: HFLLIMIT,
        IC: ICLIMIT,
        MID: MIDLIMIT,
      },
    },
    showPlayerTabel,
    setShowPlayerTabel,
  } = footballContext;

  const { isPlayerListResponseLoading } = useCompetition();

  const [filters, setFilters] = useState<FiltersState>({
    matchesPlayed: false,
    priceRange: [0, 100],
    dualPosition: false,
    teams: {
      awayTeam: false,
      homeTeam: false,
    },
    breakeven: [0, 100],
    projectedScore: [0, 100],
    projectedValueChange: [6300, 9300],
    selectionPercentage: [0, 100],
  });

  const sortOptions = [
    { value: 'player_asc', label: 'Sort Players A to Z' },
    { value: 'player_desc', label: 'Sort Players Z to A' },
    { value: 'team_asc', label: 'Sort Teams A to Z' },
    { value: 'team_desc', label: 'Sort Teams Z to A' },
  ];

  // Initialize with current active tab data
  const { table } = useDataTable({
    columns: footballUpcomingColumn,
    data: playerData[activeTab] || [],
  });

  const sortedData = table.getSortedRowModel().rows.map((row) => row.original);

  const handleSortChange = (option: string): void => {
    const sorting: SortingState = [];
    switch (option) {
      case 'player_asc':
        sorting.push({ id: 'name', desc: false });
        break;
      case 'player_desc':
        sorting.push({ id: 'name', desc: true });
        break;
      case 'team_asc':
        sorting.push({ id: 'teamName', desc: false });
        break;
      case 'team_desc':
        sorting.push({ id: 'teamName', desc: true });
        break;
      default:
        break;
    }
    table.setSorting(sorting);
  };

  // Check for valid team selection - implement your validation logic here
  const validTeamSelection =
    BL?.length +
    HBL?.length +
    MID?.length +
    HFL?.length +
    FL?.length +
    FOL?.length +
    IC?.length ===
    22;

  // Create tab data from context
  const AFLTABDATA: TabItem[] = [
    { count: BL?.length, id: 'BL', name: 'BL', seq: 0, maxCount: BLLIMIT },
    { count: HBL?.length, id: 'HBL', name: 'HBL', maxCount: HBLLIMIT, seq: 2 },
    { count: MID?.length, id: 'MID', name: 'MID', maxCount: MIDLIMIT, seq: 3 },
    { count: HFL?.length, id: 'HFL', name: 'HFL', maxCount: HFLLIMIT, seq: 1 },
    { count: FL?.length, id: 'FL', name: 'FL', maxCount: FLLIMIT, seq: 4 },
    { count: FOL?.length, id: 'FOL', name: 'FOL', maxCount: FOLLIMIT, seq: 5 },
    { count: IC?.length, id: 'IC', name: 'IC', maxCount: ICLIMIT, seq: 6 },
  ];

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const column = table.getColumn('name');
    if (column) {
      column.setFilterValue(e.target.value);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto bg-white relative hidden lg:block">
      <div className="p-2 space-y-2">
        <div className="flex justify-between items-center space-x-2">
          <div className="relative w-[60%] hidden md:block">
            <Search
              className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={16}
            />
            <Input
              placeholder="Search (by player name)"
              className="border-gray-100 pl-10"
              onChange={handleSearchInputChange}
            />
          </div>
          <div className="flex items-center gap-2 w-full md:w-fit justify-between md:justify-start sort-container">
            <Select
              className="React desktop-odds-select"
              onChange={(e) => e?.value && handleSortChange(e?.value)}
              options={sortOptions}
              classNamePrefix="select"
              placeholder="Sort Players"
              defaultValue={sortOptions[0]}
              isSearchable={false}
              components={{ DropdownIndicator }}
            />
            <div>
              <IconButton
                {...({} as any)}
                className="bg-secondary-100 hidden md:block"
                onClick={() => setShowFilter(!showFilter)}
              >
                <SlidersHorizontal />
              </IconButton>
              <IconButton
                {...({} as any)}
                className="bg-secondary-100 md:hidden block"
                onClick={() => setShowMobileFilter(!showMobileFilter)}
              >
                <SlidersHorizontal />
              </IconButton>
            </div>
          </div>
        </div>

        <div className="w-full">
          <SimpleTabs
            tabs={AFLTABDATA}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
          />
        </div>

        <div>
          <DataTable
            columns={footballUpcomingColumn}
            data={sortedData}
            stickyColumns={width > 390 ? [] : [0]}
            isLoading={isPlayerListResponseLoading}
            initialColumnVisibility={{
              name: false,
              team: false,
              teamName: false,
            }}
            maxHeight="1320px"
            noDataMessage={{
              title: '',
              hideTitle: true,
              description: 'Lineup will be updated when players are confirmed.',
            }}
          />
        </div>
      </div>

      {showFilter && (
        <div className="absolute top-14 right-0 w-full z-40">
          <FootballPlayerFilter
            playerTable={table}
            filters={filters}
            setFilters={setFilters}
            setShowFilter={setShowFilter}
          />
        </div>
      )}

      {showMobileFilter && (
        <MobileFootballPlayerFilter
          open={showMobileFilter}
          setOpen={setShowMobileFilter}
        />
      )}

      <Drawer open={showPlayerTabel} onOpenChange={setShowPlayerTabel}>
        <DrawerContent className="bg-white mb-[70px]">
          <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md">
            SELECT PLAYERS
          </div>
          <div className="p-2 space-y-2 mt-10 h-full">
            <div className="flex justify-between items-center space-x-2">
              <Card className="p-3 shadow-sm w-1/2 bg-dark-card-gradient text-white">
                <div className="text-center">
                  <div className="text-xs text-[#BFCCD8] mb-1">
                    Remaining Salary:
                  </div>
                  <div className="font-semibold text-white text-sm">
                    ${formatNumberWithCommas(stats?.remainingSalary)}
                  </div>
                </div>
              </Card>

              <Card
                className={cn(
                  'p-3 shadow-sm text-white w-1/2',
                  validTeamSelection
                    ? 'bg-[#D1E7DF] border border-[#B4DBCD]'
                    : 'bg-dark-card-gradient',
                )}
              >
                <div className="text-center">
                  <div
                    className={cn(
                      'text-xs text-[#BFCCD8] mb-1',
                      validTeamSelection ? 'text-black-700' : 'text-slate-500',
                    )}
                  >
                    Selected Player:
                  </div>
                  <div
                    className={cn(
                      'font-semibold',
                      validTeamSelection ? 'text-[#1C9A6C]' : 'text-slate-900',
                    )}
                  >
                    {stats.selectedPlayer}
                  </div>
                </div>
              </Card>
            </div>
            <div className="grid grid-cols-[60%_40%] gap-x-1 place-items-center">
              <div className="relative w-full">
                <Search
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400"
                  size={16}
                />
                <Input
                  placeholder="Search (by player name)"
                  className="pl-8 border-gray-100 !py-4"
                  onChange={handleSearchInputChange}
                />
              </div>
              <div className="flex items-center gap-2 w-full md:w-fit px-1 md:px-0 justify-end md:justify-start sort-container">
                <Select
                  className="React desktop-odds-select"
                  onChange={(e) => e?.value && handleSortChange(e?.value)}
                  options={sortOptions}
                  classNamePrefix="select"
                  placeholder="Sort Players"
                  defaultValue={sortOptions[0]}
                  isSearchable={false}
                  components={{ DropdownIndicator }}
                />
                <div>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 hidden md:block"
                    onClick={() => setShowFilter(!showFilter)}
                  >
                    <SlidersHorizontal />
                  </IconButton>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 md:hidden block"
                    onClick={() => setShowMobileFilter(!showMobileFilter)}
                  >
                    <SlidersHorizontal />
                  </IconButton>
                </div>
              </div>
            </div>

            <div className="w-full">
              <SimpleTabs
                tabs={AFLTABDATA}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                size="small"
              />
            </div>

            <div>
              <DataTable
                columns={footballUpcomingColumn}
                data={sortedData}
                stickyColumns={[0]}
                initialColumnVisibility={{ name: false, teamName: false }}
                isLoading={isPlayerListResponseLoading}
                noDataMessage={{
                  title: '',
                  hideTitle: true,
                  description: 'Lineup will be updated when players are confirmed.',
                }}
              />
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </Card>
  );
};

export default FootballPlayerSelectionUI;
