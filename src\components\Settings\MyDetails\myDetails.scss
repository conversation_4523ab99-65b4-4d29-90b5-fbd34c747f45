.phone-code {
  width: 110px;

  .select__indicator {
    padding: 6px 3px;
  }

  .select__menu {
    width: 307px;
  }
}

.phone-input {
  .select__control {
    padding: 0px 8px !important;

    .select__input-container {
      padding: 0px;
    }
  }

  .select__menu {
    width: 307px !important;
  }

  .parent-container .phone-input,
  .parent-container .select__control {
    background: white !important;
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
    height: 40px !important;
  }

  .select__control .select__dropdown-indicator {
    padding: 0px !important;
    color: black;
  }
}

.details-dob {
  .common-date-picker .MuiOutlinedInput-root {
    background: white !important;
    border: #d4d6d8 1px solid !important;
    height: 44px;

    input {
      padding: 8px 11px;
      font-weight: normal;
      color: #5c5c5c;
    }
  }
}

.drop-down-selector {
  .details-select {
    .select__control {
      height: 44px !important;
      background-color: white !important;
      max-width: 100% !important;
    }
  }
}
