'use client';
import Breadcrumbs from '@/components/UI/Breadcrumbs';
import { useEffect, useState } from 'react';
import CustomTabs from '@/components/UI/CustomTab';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import { cn, getTournamentName, LocalStorage } from '@/lib/utils';
import { Button } from '@/components/UI/button';
import FootballLeaderBoard from '../(components)/FootballLeaderBoard';
import Prizes from '@/components/Competition/Prizes';
import Standings from '@/components/Competition/Standings';
import TournamentHeader from '@/components/Common/Layout/TournamentHeader';
import { useCompetition } from '../../../../../helpers/context/competitionContext';
import useScreen from '@/hooks/useScreen';
import { useRouter, useSearchParams } from 'next/navigation';
import AustralianRulesMobileIcon from '@/assets/images/sportIcon/whiteOutlineSportIcon/AustralianRules.svg';
import AustralianRulesIcon from '@/assets/images/sportIcon/whiteSportIcon/AustralianRules.svg';
import { useFantasyUser } from '@/helpers/context/fantasyUserContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import CustomDialog from '@/components/UI/CustomDialog';
import CreateTeamMode from '../(components)/CreateTeamMode';
import OverlayCelebrations from '@/components/motions/OverlayCelebrations';
import SuccessAction from '@/components/motions/SuccessAction';
import { footballPlayersByRole } from '../../../../../../types/football';
import { setApiMessage } from '@/helpers/commonFunctions';
import { createAFLTeam } from '@/helpers/fetchers/competitions';
import { quyerKeys } from '@/lib/queryKeys';
import { Spinner } from '@material-tailwind/react';
import Coins from '@/assets/images/settings/smartBCoins.png';

import { CheckedState } from '@radix-ui/react-checkbox';
import Image from 'next/image';
import { Checkbox } from '@/components/UI/checkbox';
import Link from 'next/link';
import PreviewTeam from '../(components)/PreviewTeam';
import AFLStats from '@/components/Common/Stats';
import moment from 'moment';
import LiveUpdates from '../(components)/LiveUpdates';
import SharePopup from '@/components/PopUp/Share/SharePopup';
import ShareIcon from '@/components/Icons/Share/ShareIcon';
import FootballReservePlayerModal from '@/components/Players/Reserve/FootballReservePlayerModal';
import DataTable from '@/components/UI/DataTabel';
import { footballUpcomingColumn } from '@/components/UI/DataTabel/columns/createTeamColumn';
import ContentWrapper from '@/components/Layout/ContentWrapper';
import { title } from 'process';
import EventConfirmModal from '@/components/Common/Modals/EventConfirmModal';
import { PaymentMethod } from '../../../../../../types/competitions';

const Page = () => {
  const { width } = useScreen();
  const { eventDetailsResponse, refetchDreamTeam, dreamTeamResponse } =
    useCompetition();
  const eventDetailsData = eventDetailsResponse?.result;
  const searchParams = useSearchParams();
  const compType = searchParams.get('comp_Type');
  const add_more = searchParams.get('add_more');
  const event_id = searchParams.get('event_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const dreamTeamId = searchParams.get('dreamTeamId');
  const leaderboard = searchParams.get('leaderboard');
  const scoreCard = searchParams.get('score_card');
  const playerId = searchParams.get('playerId');
  const role = searchParams.get('role') as keyof footballPlayersByRole;
  const localAFLLeaguePlayersByRole =
    LocalStorage.getItem<footballPlayersByRole>('afl_dream_team');
  const router = useRouter();
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  const { freeCompAvailable, freeCompId, freeCompExist } =
    eventDetailsResponse?.result?.eventConfiguration ?? {};
  const { user } = useFantasyUser();
  const showFreeCompAction = freeCompAvailable && freeCompId && !freeCompExist;

  const [smartCoinsAmount, setSmartCoinsAmount] = useState(0)
  const [bonusCoinsAmount, setBonusCoinsAmount] = useState(0)
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('smartCoins')

  const isCommentaryAvailable = (): boolean => {
    const sportId = eventDetailsResponse?.result?.eventDetails?.SportId;
    const matchStatus = (eventStatus || '').toLowerCase();
    const isLiveOrCompleted =
      matchStatus === 'inprogress' ||
      matchStatus === 'finished' ||
      matchStatus === 'innings break' ||
      matchStatus === 'drink';

    const hasMatchStarted = moment().isAfter(
      moment.utc(eventDetailsData?.eventDetails?.startTime).local(),
    );

    return isLiveOrCompleted && hasMatchStarted;
  };

  const disabledMyTeamTab =
    ((leaderboard === 'true' || scoreCard === 'true') ||
      (eventDetailsData?.dreamTeams?.length ?? 0) === 0) &&
    eventStatus !== 'upcoming';

  const breadcrumbsLinks = [
    { label: 'Home', href: '/' },
    { label: 'SmartPlay', href: '#' },
    {
      label: compType === 'my' ? 'My Competitions' : 'All Competitions',
      href: compType === 'my' ? '/my-competitions?status=1&compType=my' : '/',
    },
    {
      label: eventDetailsResponse?.result?.eventDetails?.eventName!,
      href: '#',
    },
  ];

  const tabs = [
    {
      label: 'my team',
      labelId: 1,
      count: eventDetailsData?.dreamTeams
        ? eventDetailsData?.dreamTeams?.length
        : '',
      disabled: disabledMyTeamTab,
    },
    {
      label: 'leaderboard',
      labelId: 2,
    },
    {
      label: 'stats',
      labelId: 6,
      disabled:
        eventDetailsResponse?.result?.eventDetails?.status === 'upcoming',
    },
    {
      label: 'prizes',
      labelId: 3,
    },
    {
      label: 'standings',
      labelId: 4,
    },
    {
      label: 'live updates',
      labelId: 5,
      disabled: !isCommentaryAvailable(),
    },
  ];
  const [activeTab, setActiveTab] = useState<number | string>(1);

  const {
    activeTabPlayer,
    setActiveTabPlayer,
    removePlayer,
    showPlayerTabel,
    setShowPlayerTabel,
    setBudget,
    state: {
      lastEntry,
      remainingBudget,
      playersByRole: { BL, HBL, MID, HFL, FL, FOL, IC },
      createDreamTeamPayload,
      playerByRoleLimit: {
        BL: BLLIMIT,
        HBL: HBLLIMIT,
        MID: MIDLIMIT,
        HFL: HFLLIMIT,
        FL: FLLIMIT,
        FOL: FOLLIMIT,
        IC: ICLIMIT,
      },
    },
    clearTeam,
    createDreamTeam,
    getDreamTeam: dispatchDreamTeam,
    setActivePlayerPosition
  } = useFootballContext();

  let previewMode: boolean = false;
  let createTeamMode: boolean = false;
  switch (eventStatus) {
    case 'finished':
    case 'inprogress':
    case 'innings break':
      previewMode = true;
      break;
    case 'team-creation':
    case 'upcoming':
      if (
        add_more &&
        add_more === 'true' &&
        eventDetailsResponse?.result?.dreamTeams?.length! > 0
      ) {
        createTeamMode = true;
      } else if (!eventDetailsResponse?.result?.dreamTeams?.length) {
        createTeamMode = true;
      } else {
        previewMode = true;
      }
      break;
  }

  const [submitedSuccess, setSubmitedSuccess] = useState(false);
  const [teamSubmitConfirmation, setTeamSubmitConfirmation] =
    useState<boolean>(false);
  const [acceptTerms, setAcceptTerms] = useState<CheckedState>(false);
  const queryClient = useQueryClient();

  const handleCloseDialog = () => {
    setTeamSubmitConfirmation(false);
    setSmartCoinsAmount(0);
    setBonusCoinsAmount(0);
    setPaymentMethod('smartCoins');
  };

  const { mutate: mutateCreateTeam, isPending: isCreatingTeamPending } =
    useMutation({
      mutationFn: () => {
        if (createDreamTeamPayload) {
          return createAFLTeam(createDreamTeamPayload, dreamTeamId);
        }
        return Promise.resolve();
      },
      onSuccess: (data: { status: boolean; message: string }) => {
        setApiMessage('success', data.message);
        setActivePlayerPosition(0);
        LocalStorage.removeItem('dream_team');
        LocalStorage.removeItem('rugby_league_dream_team');
        LocalStorage.removeItem('afl_dream_team');
        LocalStorage.removeItem('redirect');
        clearTeam();
        setAcceptTerms(false);
        queryClient.invalidateQueries({
          queryKey: [
            quyerKeys.getFantasyUser,
            quyerKeys.getAllCompetitons,
            quyerKeys.getCompetiton,
          ],
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getFantasyUser],
          exact: true,
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getAllCompetitons],
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getCompetiton],
          exact: true,
        });

        if (!dreamTeamId) {
          setSubmitedSuccess(true);
        }

        if (dreamTeamId) {
          refetchDreamTeam();
        }

        if (dreamTeamId) {
          router.back();
        }

        setTeamSubmitConfirmation(false);
        setSmartCoinsAmount(0);
        setBonusCoinsAmount(0);
        setPaymentMethod('smartCoins');
      },
      onError: (data: any) => {
        if (data?.message) {
          setApiMessage('error', data?.message);
        } else {
          setApiMessage('error', data?.response?.data?.message);
        }
      },
    });

  const eventEntryCoin =
    eventDetailsResponse?.result?.eventConfiguration?.entryCoin;
  const userAvailabeCoins = user?.coins - (user?.holdCoins ?? 0);
  const eventConfigurationData =
    eventDetailsResponse?.result?.eventConfiguration;

  const handleBuySmartBCoins = () => {
    // Safely check localStorage availability
    if (typeof window !== 'undefined') {
      try {
        // Store current path
        const currentPath = window.location.href;
        LocalStorage.setItem('redirect', { url: currentPath });

        if (currentPath.includes('football')) {
          LocalStorage.setItem('afl_dream_team', {
            BL,
            HBL,
            MID,
            HFL,
            FL,
            FOL,
            IC,
          });
        }
        router.push('/settings?buy_smart_b_coins=true');
      } catch (error) {
        console.error('Error storing data in localStorage:', error);
      } finally {
        // Close dialog first
        // handleCloseDialog();
      }
    }
    // Use Next.js router for navigation
  };
  const handelEnterAgainTeam = () => {
    handleSubmitedSucessModalClose();

    const compData = eventDetailsResponse?.result;

    const aflUrl = `/competitions/football/${compData?.eventConfiguration?.eventId}?event_id=${compData?.eventConfiguration?.eventId}&sport_id=9&tournament_id=${compData?.eventDetails?.ARTournamentId}&seasonId=${compData?.eventDetails?.ARSeasonId}&competition_id=${compData?.eventConfiguration?.id}&comp_Type=my`;
    const aflEnterUrl = `/competitions/football/${compData?.eventConfiguration?.eventId}?event_id=${compData?.eventConfiguration?.eventId}&sport_id=9&tournament_id=${compData?.eventDetails?.ARTournamentId}&seasonId=${compData?.eventDetails?.ARSeasonId}&competition_id=${eventDetailsResponse?.result?.eventConfiguration?.id}&add_more=true`;
    const URL =
      compData?.eventConfiguration?.eventType === 'free' ? aflUrl : aflEnterUrl;
    if (compData?.eventDetails?.SportId === 9) {
      return router.push(URL);
    }
  };

  const handleSubmitedSucessModalClose = () => {
    setSubmitedSuccess(false);
  };

  const handleTeamSubmitConfirmation = ({
    coins,
    bonusCoins,
  }: {
    coins: number;
    bonusCoins: number;
  }) => {
    const teamName = `Team ${Array.isArray(eventDetailsResponse?.result?.dreamTeams)
      ? eventDetailsResponse.result.dreamTeams?.length + 1
      : 1
      }`;

    let smartCoinsToUse = 0;
    let bonusCoinsToUse = 0;

    switch (paymentMethod) {
      case "both":
        smartCoinsToUse = coins;
        bonusCoinsToUse = bonusCoins;
        break;
      case "smartCoins":
        smartCoinsToUse = eventEntryCoin ?? 0;
        bonusCoinsToUse = 0;
        break;
      case "bonusCoins":
        bonusCoinsToUse = eventEntryCoin ?? 0;
        smartCoinsToUse = 0;
        break;
    }

    createDreamTeam({
      eventId: event_id,
      eventName: eventDetailsResponse?.result?.eventDetails?.eventName!,
      sportId: '9',
      tournamentId: tournament_id,
      competitionId: competition_id,
      name: teamName,
      coins: smartCoinsToUse,
      bonusCoins: bonusCoinsToUse,
    });
    mutateCreateTeam();
  };

  const handlePlayForFree = () => {
    const { freeCompAvailable, freeCompId, freeCompExist } =
      eventDetailsResponse?.result?.eventConfiguration ?? {};

    if (freeCompAvailable && freeCompId && !freeCompExist) {
      createDreamTeam({
        eventId: event_id,
        eventName: eventDetailsResponse?.result?.eventDetails?.eventName!,
        sportId: '9',
        tournamentId: tournament_id,
        competitionId: `${freeCompId}`,
        name: 'Team 1',
        coins: 0,
        bonusCoins: 0,
      });
      mutateCreateTeam();
    }
  };

  // Rest Previous Team State
  useEffect(() => {
    if (dreamTeamResponse && dreamTeamId) {
      dispatchDreamTeam(dreamTeamResponse, +playerId!, role);
    }
  }, [dreamTeamResponse, activeTab, dreamTeamId, playerId, role]);

  useEffect(() => {
    if (role) {
      setActiveTabPlayer(role);
    }
  }, [role]);

  // // Set Budget

  useEffect(() => {
    if (eventDetailsData?.eventDetails?.ARSeason?.fantasy_sport_salary_cap) {
      setBudget(
        eventDetailsData?.eventDetails?.ARSeason?.fantasy_sport_salary_cap,
      );
    }
  }, [eventDetailsData?.eventDetails?.ARSeason?.fantasy_sport_salary_cap]);

  useEffect(() => {
    if (localAFLLeaguePlayersByRole) {
      clearTeam();
    }
    if (add_more && add_more === 'true' && !dreamTeamId) {
      clearTeam();
    }

    if (!eventDetailsData?.dreamTeams) {
      clearTeam();
    }

    return () => {
      clearTeam();
    };
  }, [dreamTeamId, add_more]);

  useEffect(() => {
    if (leaderboard === 'true') {
      setActiveTab(2);
    }
    if (scoreCard === 'true') {
      setActiveTab(6);
    }
    if (eventStatus === 'finished' && disabledMyTeamTab) {
      setActiveTab(2);
    }
  }, [leaderboard, scoreCard, eventStatus]);

  const [showSharePopup, setShowSharePopup] = useState(false);

  const { AFLPlayersByRoles: playerByRole } = useCompetition();
  const { state } = useFootballContext();

  const sourcePlayers = Object.values(playerByRole)?.flat();
  const selectedPlayers = Object.values(state?.playersByRole)?.flat();
  const selectedPlayerIds = selectedPlayers?.map((player) => player?.playerId);
  const reservePlayers = sourcePlayers?.filter((player) => !selectedPlayerIds?.includes(player?.playerId));
  const uniqueReservePlayers = new Set(reservePlayers);
  const uniqueReservePlayersArray = Array.from(uniqueReservePlayers);



  return (
    // <div className="min-h-screen">
    <div className="mt-2">
      <ContentWrapper>
        {/* HEADER  */}
        <div className="pt-[33px] max-799:pt-[18px] pb-[12px] max-799:pb-[9px] mb-1 relative p-0 md:p-8 md:pb-0">
          <Breadcrumbs links={breadcrumbsLinks} />
          <h1 className="text-[31.36px] max-799:text-[22.4px] mt-2 md:mt-0 leading-[40px] max-799:leading-[28px] font-normal text-white max-1024:text-black-100 font-veneerCleanSoft flex items-center justify-between max-799:items-start">
            <span className="flex items-center max-799:flex-col max-799:items-start text-black-100">
              {eventDetailsResponse?.result?.eventDetails?.eventName}
              <span className="text-[14px] leading-[16px] font-inter font-normal text-black-100 ml-[15px] max-799:ml-0 max-1024:text-black-100 flex items-center ">
                <span>
                  {width > 1024 ? (
                    <AustralianRulesMobileIcon />
                  ) : (
                    <AustralianRulesMobileIcon />
                  )}
                </span>
                <span>
                  {getTournamentName(
                    eventDetailsResponse?.result?.eventDetails?.SportId ?? 0,
                    eventDetailsResponse?.result?.eventDetails ?? {},
                  )}
                </span>
              </span>
            </span>

            <SharePopup
              isOpen={showSharePopup}
              onClose={() => setShowSharePopup(false)}
            >
              <button
                type="button"
                className="cursor-pointer border-none bg-transparent p-0"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowSharePopup(true);
                }}
              >
                <ShareIcon />
              </button>
            </SharePopup>
          </h1>
        </div>
        <div className="bg-off-white-200 md:px-[32px] md:py-[16px] md:pt-0 p-0">
          <TournamentHeader
            competitionStatus={
              eventDetailsResponse?.result?.eventDetails?.status
            }
            tournamentDetails={eventDetailsResponse}
          />

          <div className="mt-2">
            <CustomTabs<number | string>
              tabs={tabs}
              setActiveTab={setActiveTab}
              activeTab={activeTab}
            />
          </div>
        </div>
        {/* MAIN */}

        {activeTab === 1 && createTeamMode && (
          <CreateTeamMode
            handleTeamSubmitConfirmation={handleTeamSubmitConfirmation}
            coins={smartCoinsAmount}
            bonusCoins={bonusCoinsAmount}
            teamSubmitConfirmation={teamSubmitConfirmation}
            setTeamSubmitConfirmation={setTeamSubmitConfirmation}
            activeTabPlayer={activeTabPlayer}
            clearTeam={clearTeam}
            lastEntry={lastEntry}
            removePlayer={removePlayer}
            showPlayerTabel={showPlayerTabel}
            footballRolePlayers={{
              BL,
              HBL,
              MID,
              HFL,
              FL,
              FOL,
              IC,
            }}
            playerByRoleLimit={{
              BL: BLLIMIT,
              HBL: HBLLIMIT,
              MID: MIDLIMIT,
              HFL: HFLLIMIT,
              FL: FLLIMIT,
              FOL: FOLLIMIT,
              IC: ICLIMIT,
            }}
            remainingBudget={remainingBudget}
            setActiveTabPlayer={setActiveTabPlayer}
          />
        )}

        {activeTab === 1 && previewMode && <PreviewTeam />}

        {activeTab === 2 && (
          <FootballLeaderBoard />
        )}

        {activeTab === 3 && (
          <Prizes />
        )}
        {activeTab === 4 && (
          <Standings />
        )}

        {activeTab === 6 && <AFLStats />}

        {activeTab === 5 && (
          <LiveUpdates
            matchId={event_id?.toString()}
            sportId={eventDetailsResponse?.result?.eventDetails?.SportId}
          />
        )}
        {
          userAvailabeCoins >= eventEntryCoin! && (
            <EventConfirmModal
              isOpen={teamSubmitConfirmation}
              paymentMethod={paymentMethod}
              setPaymentMethod={setPaymentMethod}
              onClose={handleCloseDialog}
              user={user}
              eventConfigurationData={{ entryCoin: eventEntryCoin ?? 0 }}
              isCreatingTeamPending={isCreatingTeamPending}
              acceptTerms={acceptTerms as boolean}
              setAcceptTerms={setAcceptTerms}
              handleTeamSubmitConfirmation={handleTeamSubmitConfirmation}
              smartCoinsAmount={smartCoinsAmount}
              setSmartCoinsAmount={setSmartCoinsAmount}
              bonusCoinsAmount={bonusCoinsAmount}
              setBonusCoinsAmount={setBonusCoinsAmount}
            />
          )
        }

        {
          userAvailabeCoins < eventEntryCoin! && (
            <CustomDialog
              isOpen={teamSubmitConfirmation}
              onClose={handleCloseDialog}
              title="Confirmation"
              maxWidth={570}
              className="Confirmation-required-modal"
              outerClickClose={true}
            >
              <div>
                <p className="text-center text-[16px] max-799:text-[14px] leading-[22.4px] max-799:leading-[16px] font-inter text-black-100">
                  Please subscribe to continue with your entry. You can stop your
                  subscription at any time.
                </p>
                <div
                  className={cn(
                    'w-full grid gap-4 grid-cols-1 place-items-center mt-8',
                    showFreeCompAction ? 'md:grid-cols-2' : 'md:grid-cols-1',
                  )}
                >
                  <Button onClick={handleBuySmartBCoins} className="w-full">
                    Buy SmartCoins
                  </Button>
                  {showFreeCompAction && (
                    <Button
                      onClick={handlePlayForFree}
                      className="w-full"
                      disabled={isCreatingTeamPending}
                    >
                      Play For Free Instead
                    </Button>
                  )}
                </div>
              </div>
            </CustomDialog>
          )
        }
        <CustomDialog
          isOpen={submitedSuccess}
          onClose={handleSubmitedSucessModalClose}
          maxWidth={570}
          className="submited-success-modal relative"
          outerClickClose={true}
        >
          <div className="text-center z-[1200] relative">
            <div>
              <SuccessAction />
            </div>
            <p className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-3 mt-[21px] max-799:mb-[23px]">
              Team submitted!
            </p>
            <p className="text-[16px] max-799:text-[14px] leading-[22.4px] max-799:leading-[17px] font-normal font-inter text-black-100 mb-[33px] max-799:mb-[23px]">
              Track your progress on the live leaderboard once the competition
              starts
            </p>
            <div className="mb-[18px] flex items-center justify-between gap-3">
              <div className="w-full">
                <Button
                  className="w-full"
                  onClick={() => {
                    router.push('/');
                  }}
                >
                  All Competitions
                </Button>
              </div>
              <div className="w-full">
                <Button className="w-full" onClick={handelEnterAgainTeam}>
                  {eventDetailsResponse?.result?.eventConfiguration
                    ?.eventType === 'free'
                    ? 'View Comp'
                    : 'Enter Again'}
                </Button>
              </div>
            </div>
          </div>

          <div className="absolute top-0 left-0 w-full h-full">
            <OverlayCelebrations />
          </div>
        </CustomDialog>

        <FootballReservePlayerModal setTeamSubmitConfirmation={setTeamSubmitConfirmation}>
          <DataTable
            columns={footballUpcomingColumn}
            data={uniqueReservePlayersArray}
            stickyColumns={[]}
            initialColumnVisibility={{
              name: false,
              team: false,
              teamName: false,
            }}
          />
        </FootballReservePlayerModal>
      </ContentWrapper>
    </div>
  );
};

export default Page;
