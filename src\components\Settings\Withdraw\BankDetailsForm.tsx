'use client';

import { Button } from '@/components/UI/button';
import { Input } from '@material-tailwind/react';
import {
  FieldErrors,
  SubmitError<PERSON><PERSON><PERSON>,
  SubmitHandler,
  UseFormRegister,
} from 'react-hook-form';
type BankDetailsFormData = {
  accountName: string;
  bsb: string;
  accountNumber: string;
  bankName: string;
};

type withdrawData = {
  accountName: string;
  bsb: string;
  accountNumber: string;
  bankName: string;
};
type BankDetailsFormProps = {
  onSubmit: SubmitHandler<BankDetailsFormData>;
  errors: FieldErrors<BankDetailsFormData>;
  register: UseFormRegister<BankDetailsFormData>;
  bankDetailsLoading: boolean;
  verificationStatus: string;
  handleSubmit: (
    onValid: SubmitHandler<withdrawData>,
    onInvalid?: SubmitErrorHandler<withdrawData> | undefined,
  ) => (e?: React.BaseSyntheticEvent) => Promise<void>;
};

const BankDetailsForm: React.FC<BankDetailsFormProps> = ({
  onSubmit,
  errors,
  register,
  bankDetailsLoading,
  verificationStatus,
  handleSubmit,
}) => {
  return (
    <div className="mt-7">
      <div className="mb-[18px] common-input-wrap">
        {/* @ts-expect-error */}
        <Input
          variant="outlined"
          label="Account name"
          placeholder="Account name"
          color="indigo"
          className="bg-white !text-[16px] !leading-[19px] !font-inter font-normal !text-black-900 placeholder:font-inter placeholder:text-black-900"
          {...register('accountName')}
          error={!!errors.accountName?.message}
        />
        {errors.accountName?.message && (
          <p className="text-red-400 text-sm mt-[2px]">
            {errors.accountName.message}
          </p>
        )}
      </div>

      <div className="mb-[18px] common-input-wrap">
        {/* @ts-expect-error */}
        <Input
          type="number"
          maxLength={6}
          variant="outlined"
          label="BSB"
          placeholder="BSB"
          color="indigo"
          className="bg-white !text-[16px] !leading-[19px] !font-inter font-normal !text-black-900 placeholder:font-inter placeholder:text-black-900"
          {...register('bsb', {
            onChange: (event) => {
              let bsbNumber = event.target.value;
              if (bsbNumber.length > 6) {
                bsbNumber = bsbNumber.slice(0, 6);
                event.target.value = bsbNumber;
              }
              return bsbNumber;
            },
          })}
          error={!!errors.bsb?.message}
        />
        {errors.bsb?.message && (
          <p className="text-red-400 text-sm mt-[2px]">{errors.bsb.message}</p>
        )}
      </div>

      <div className="mb-[18px] common-input-wrap">
        {/* @ts-expect-error */}
        <Input
          type="number"
          required
          variant="outlined"
          label="Account number"
          placeholder="Account number"
          color="indigo"
          className="bg-white !text-[16px] !leading-[19px] !font-inter font-normal !text-black-900 placeholder:font-inter placeholder:text-black-900"
          {...register('accountNumber', {
            onChange: (event) => {
              let accountNumber = event.target.value;
              if (accountNumber.length > 9) {
                accountNumber = accountNumber.slice(0, 9);
                event.target.value = accountNumber;
              }
              return accountNumber;
            },
          })}
          error={!!errors.accountNumber?.message}
        />
        {errors.accountNumber?.message && (
          <p className="text-red-400 text-sm mt-[2px]">
            {errors.accountNumber.message}
          </p>
        )}
      </div>

      <div className="mb-[18px] common-input-wrap">
        {/* @ts-expect-error */}
        <Input
          type="text"
          variant="outlined"
          label="Bank name"
          placeholder="Bank name"
          color="indigo"
          className="bg-white !text-[16px] !leading-[19px] !font-inter font-normal !text-black-900 placeholder:font-inter placeholder:text-black-900"
          {...register('bankName')}
          error={!!errors.bankName?.message}
        />
        {errors.bankName?.message && (
          <p className="text-red-400 text-sm mt-[2px]">
            {errors.bankName.message}
          </p>
        )}
      </div>

      <div className="mt-[100px] max-799:mt-[50px] text-center">
        <Button
          className="w-full"
          disabled={bankDetailsLoading || verificationStatus !== 'verified'}
          onClick={handleSubmit(onSubmit)}
        >
          Continue
        </Button>
        <p className="text-[14px] max-799:text-[11.42px] leading-[16px] max-799:leading-[14px] font-inter font-normal mt-[9px] text-gray-200">
          Minimum withdrawal balance should be no less than $50 worth of coins.
        </p>
      </div>
    </div>
  );
};

export default BankDetailsForm;
