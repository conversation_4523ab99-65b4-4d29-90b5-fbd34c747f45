'use client';

import { cn } from '@/lib/utils';
import { FantasyUser } from '../../../../types';
import Image from 'next/image';

interface WithdrawHeaderProps {
  loading: boolean;
  verificationStatus: string;
  verificationStatusMessage: () => JSX.Element;
  setIdentityVerification: (status: boolean) => void;
  fantasyUser: FantasyUser;
  CoinsAvailable: any;
  Withdrawal: any;
}

const WithdrawHeader: React.FC<WithdrawHeaderProps> = ({
  loading,
  verificationStatus,
  verificationStatusMessage,
  setIdentityVerification,
  fantasyUser,
  CoinsAvailable,
  Withdrawal,
}) => {
  const coinsAvailable = fantasyUser?.coins
    ? fantasyUser.coins - (fantasyUser.holdCoins ?? 0)
    : 0;
  return (
    <div>
      <div className="flex items-center max-639:flex-col max-639:items-start">
        <h4 className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[16px] font-inter font-semibold text-black-100 capitalize">
          Withdraw
        </h4>

        {!loading && (
          <button
            onClick={() => setIdentityVerification(true)}
            className={cn(
              verificationStatus === 'verified' && 'bg-success-100',
              verificationStatus === 'pending' && 'bg-primary-400',
              verificationStatus !== 'verified' &&
              verificationStatus !== 'pending' &&
              'bg-negative-300',
              'text-white text-sm leading-[17px] flex justify-center items-center font-semibold rounded-md cursor-pointer px-3 py-[10px] ml-[18px] max-639:ml-0 max-639:mt-[6px]',
            )}
          >
            {verificationStatusMessage()}
          </button>
        )}
      </div>
      <div className="mt-[33px] flex gap-9 max-799:gap-3 items-center">
        <div className="flex items-center bg-black-300 rounded-lg p-3 max-799:p-2 max-639:px-2 max-639:py-2 w-1/2 justify-center gap-4 max-799:gap-2">
          <div className="max-799:w-[30px] max-799:h-[30px]">
            <Image
              src={CoinsAvailable}
              alt="coins"
              className="m-auto max-799:w-full max-799:h-full"
              unoptimized={true}
            />
          </div>
          <div>
            <p className="text-[31.36px] max-799:text-[22.4px] leading-[37px] max-799:leading-[27px] font-semibold font-inter text-primary-200 mb-2">
              {fantasyUser?.coins
                ? fantasyUser.coins - (fantasyUser.holdCoins ?? 0)
                : 0}
            </p>
            <p className="text-[16px] max-799:text-[11.22px] leading-[19px] max-799:leading-[14px] font-normal font-inter text-primary-200/80">
              SmartCoins Available
            </p>
          </div>
        </div>
        {coinsAvailable >= 1000 && (
          <div className="flex items-center bg-black-300 rounded-lg p-3 max-799:p-2 w-1/2 justify-center gap-4 max-799:gap-2">
            <div className="max-799:w-[30px] max-799:h-[30px]">
              <Image
                src={Withdrawal}
                alt="withdrawal"
                className="m-auto max-799:w-full max-799:h-full"
                unoptimized={true}
              />
            </div>
            <div>
              <p className="text-[31.36px] max-799:text-[22.4px] leading-[37px] max-799:leading-[27px] font-semibold font-inter text-primary-200 mb-2">
                {fantasyUser?.coins - (fantasyUser?.holdCoins || 0)
                  ? '$' +
                  (
                    (fantasyUser.coins - (fantasyUser.holdCoins ?? 0)) *
                    0.05
                  ).toFixed(2)
                  : '$0'}
              </p>
              <p className="text-[16px] max-799:text-[11.22px] leading-[14px] max-799:leading-[15px] font-normal font-inter text-primary-200/80">
                Withdrawal Balance
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WithdrawHeader;
