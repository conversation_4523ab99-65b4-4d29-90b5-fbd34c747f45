'use client';

import {
  ModalHeaderDownIcon,
  PlayerCaptainViewIcon,
} from '@/components/images';
import DataTable from '@/components/UI/DataTabel';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import useScreen from '@/hooks/useScreen';
import {
  completedLeaderBoardTeamColumns,
  leaderboardTeamColumns,
  liveLeaderBoardTeamColumns,
} from '@/components/UI/DataTabel/columns/createTeamColumn';
import { FetchLeadingResponse, Team } from '../../../../../../types';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { Config } from '@/helpers/context/config';
import { useSearchParams } from 'next/navigation';
import { useCompetition } from '@/helpers/context/competitionContext';
import { ColumnDef } from '@tanstack/react-table';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { LIVE_POLLIN_TIME } from '@/helpers/constants/index';
import { quyerKeys } from '@/lib/queryKeys';

export const fetchLeaderDetails = async (
  competitionId: string | null,
  eventId: string | null,
  tournamentId: string | null,
  status?: string | undefined,
): Promise<FetchLeadingResponse> => {
  const res = await axiosInstance.get<FetchLeadingResponse>(
    Config.fantasyURL +
    `/ladder-board/${competitionId}?eventId=${eventId}&SportId=12&tournamentId=${tournamentId}&status=${status}`,
  );
  return res?.data;
};

const SoccerLeaderBoard = () => {
  const searchParams = useSearchParams();

  const { width } = useScreen();
  const event_id = searchParams.get('event_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const { eventDetailsResponse, dreamTeamResponse } = useCompetition();
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  let playerTeamColums: ColumnDef<Team>[] = [];
  const [openDrawer, setOpenDrawer] = useState<boolean>(false);
  switch (eventStatus) {
    case 'finished':
      playerTeamColums = completedLeaderBoardTeamColumns;
      break;
    case 'inprogress':
    case 'innings break':
      playerTeamColums = liveLeaderBoardTeamColumns;
      break;
    case 'upcoming':
      playerTeamColums = leaderboardTeamColumns;
      break;
  }

  // fetch leader borad list
  const { data: leaderList, isLoading } = useQuery({
    queryFn: () =>
      fetchLeaderDetails(
        competition_id,
        event_id,
        tournament_id,
        eventDetailsResponse?.result?.eventDetails?.status,
      ),
    refetchInterval: () =>
      eventDetailsResponse?.result?.eventDetails?.status === 'inprogress'
        ? LIVE_POLLIN_TIME
        : false,
    queryKey: [quyerKeys.getLeaderBoardList],
    enabled:
      !!tournament_id && !!eventDetailsResponse?.result?.eventDetails?.status,
  });

  const leaderBordList =
    eventDetailsResponse?.result?.eventDetails?.status === 'upcoming'
      ? leaderList?.result || [] // Default to an empty array if result is null/undefined
      : [
        ...(leaderList?.currentUser?.map((user, index, array) => ({
          ...user,
          myTeam: true,
          myLastTeam: index === array.length - 1, // Mark as myLastTeam if it's the last item
        })) || []), // Default to an empty array if currentUser is null/undefined
        ...(leaderList?.result || []), // Default to an empty array if result is null/undefined
      ];

  return (
    <div className="md:px[32px] px-0">
      <div className="md:px-4 px-0">
        <div className="flex flex-col md:flex-row justify-between  md:space-x-2 md:space-y-0">
          <div className="w-full h-full pb-2">
            <DataTable
              columns={playerTeamColums}
              // @ts-expect-error
              data={leaderBordList}
              isLoding={isLoading}
              stickyColumns={width <= 700 ? [0] : []}
              layout="position"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SoccerLeaderBoard;
