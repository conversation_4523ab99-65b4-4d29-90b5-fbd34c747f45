import { Tooltip } from '@material-tailwind/react';
import moment from 'moment';
import Image from 'next/image';
import Countdown from 'react-countdown';
import type { TimeDifference } from '@/lib/utils';
import CountDownTimer from '@/components/UI/CountDownTimer';
import StatsCard from '@/components/Competition/StatsCard';
import { CompetitionStatusProps } from '../../../../types';
import { CompetitionResponse } from '../../../../types/competitions';
import { TriangleAlert } from 'lucide-react';

type TournamentHeaderData = {
  competitionStatus?: CompetitionStatusProps;
  tournamentDetails: CompetitionResponse | undefined;
};

export default function TournamentHeader({
  competitionStatus,
  tournamentDetails,
}: Readonly<TournamentHeaderData>) {
  const totalPrizePool =
    tournamentDetails?.result?.eventConfiguration?.prizePool ?? 0;

  const renderer = ({
    days,
    hours,
    minutes,
    seconds,
    completed,
  }: TimeDifference) => {
    return (
      <CountDownTimer
        completed={completed}
        days={days}
        minutes={minutes}
        hours={hours}
        seconds={seconds}
      />
    );
  };

  const isTournamentCompleted = competitionStatus === 'finished';
  const { userEntry } = tournamentDetails?.result || {};

  const { eventType, startTime, drawPool } =
    tournamentDetails?.result?.eventConfiguration || {};
  const drawPoolAvailable = drawPool;
  const winningPrize = tournamentDetails?.result?.winningPrice;

  let firstPrize = 0;
  if (eventType === 'free') {
    firstPrize = totalPrizePool / 2;
  } else {
    firstPrize = tournamentDetails?.result?.eventConfiguration?.prizePoolFirst ?? 0;
  }

  let topTeamPayout: number = 0;
  switch (eventType) {
    case 'free':
    case 'paid':
      topTeamPayout = (userEntry ?? 0) > 20 ? 20 : (userEntry ?? 0);
      break;
  }

  return (
    <div>
      {/* Stats */}
      <div className="flex justify-between flex-col-reverse md:flex-row md:gap-16 gap-3">
        <div className="flex flex-col md:w-[calc(100%-270px)] w-full">
          <div className="flex flex-wrap gap-2 mb-4 items-center justify-center md:flex-nowrap md:justify-between">
            {/* Prize Pool */}
            <StatsCard
              title="Prize Pool"
              value={totalPrizePool ?? ''}
              icon="/fantasy/images/smartbCoin.svg"
              alt="coins"
            />
            {/* Entry Coins */}
            <StatsCard
              title="Entry Coins"
              value={
                tournamentDetails?.result?.eventConfiguration?.eventType ===
                  'paid'
                  ? tournamentDetails?.result?.eventConfiguration?.entryCoin
                  : 'Free'
              }
              icon="/fantasy/images/smartbCoin.svg"
              alt="coins"
            />
            {/* Max Entries */}
            <StatsCard
              title="Max Entries"
              value={
                tournamentDetails?.result?.eventConfiguration?.minUserEntry
                  ? tournamentDetails?.result?.eventConfiguration?.minUserEntry
                  : 'Unlimited'
              }
            />
            {/* Entries */}
            <StatsCard
              title="Entries"
              value={userEntry ? `${userEntry} Teams` : ''}
            />

            {isTournamentCompleted &&
              tournamentDetails?.result?.currentRank! ? (
              <StatsCard
                title={`Rank`}
                value={`${tournamentDetails?.result?.currentRank!} (${tournamentDetails?.result?.name})`}
              />
            ) : null}

            {isTournamentCompleted && winningPrize ? (
              <StatsCard
                title="Winnings"
                icon="/fantasy/images/smartbCoin.svg"
                alt="coins"
                value={winningPrize}
              />
            ) : null}
          </div>

          {/* Icons */}
          <div className="flex justify-between md:justify-start space-x-4">
            <Tooltip
              content={`1st prize: ${firstPrize} coins`}
              placement="bottom"
              className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
            >
              <div className="bg-[#D4D6D8] rounded px-2 py-1 flex items-center space-x-1 w-full lg:w-[88px] justify-center">
                <Image
                  src="/fantasy/images/icons/winIcon.svg"
                  width={15}
                  height={15}
                  alt="win"
                  unoptimized={true}
                />
                <span className="text-[#003764] text-sm">
                  {firstPrize}
                </span>
              </div>
            </Tooltip>

            <Tooltip
              content={`Payout: Top ${topTeamPayout} teams`}
              placement="bottom"
              className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
            >
              <div className="bg-[#D4D6D8] rounded px-2 py-1 flex items-center w-full lg:w-[88px] justify-center">
                <Image
                  src="/fantasy/images/icons/topIcon.svg"
                  width={20}
                  height={20}
                  alt="win"
                  unoptimized={true}
                />
                <span className="text-[#003764] text-sm">
                  Top {topTeamPayout}
                </span>
              </div>
            </Tooltip>
            {drawPoolAvailable && (
              <Tooltip
                content={`Draw: For members`}
                placement="bottom"
                className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
              >
                <div className="bg-[#D4D6D8] rounded px-2 py-1 flex items-center w-full lg:w-[88px] justify-center">
                  <Image
                    src="/fantasy/images/icons/drawIcon.svg"
                    width={20}
                    height={20}
                    alt="win"
                    unoptimized={true}
                  />
                  <span className="text-[#003764] text-sm">Draw</span>
                </div>
              </Tooltip>
            )}
            {eventType === 'free' && (
              <div className="bg-[#D4D6D8] text-[#003764] rounded p-1 mb-2 items-center max-w-[50rem]  space-x-2 md:flex mt-2  hidden">
                <TriangleAlert className="w-4 h-4 text-orange-400" />
                <span className="text-[16px] w-full text-center">
                  Free coins will be awarded to the top 20 players only if the
                  game has minimum 10 entries.
                </span>
              </div>
            )}
          </div>
          {eventType === 'free' && (
            <div className="bg-[#D4D6D8] text-[#003764] rounded p-1 items-center max-w-[50rem]  space-x-2 flex mt-2 md:hidden">
              <TriangleAlert className="w-4 h-4 text-orange-400" />
              <span className="md:text-[16px] text-sm w-full md:text-center text-left">
                Free coins will be awarded to the top 20 players only if the
                game has minimum 10 entries.
              </span>
            </div>
          )}
        </div>

        <div className="flex md:justify-end justify-end items-center md:w-[270px] w-full  rounded md:rounded-none">
          <div className="md:w-auto w-full">
            <Countdown
              date={moment.utc(startTime).local().toDate()}
              renderer={renderer}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
