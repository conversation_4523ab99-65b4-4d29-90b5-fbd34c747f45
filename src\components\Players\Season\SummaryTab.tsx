import React from 'react';

import SortingDown from '@/assets/images/icons/sortingDown.svg';
import SortingUp from '@/assets/images/icons/sortingUp.svg';
import { generateUniqueId } from '@/lib/utils';

interface MatchData {
  rd?: number;
  opponent?: string;
  venue?: string;
  score?: number;
  totalScore?: number;
  avg?: number;
  price?: number;
  priceChange?: number;
  points?: number;
  teams?: number;
}

interface SeasonSummaryStateData {
  seasonSummaryStateData: MatchData[];
}

const HeaderCell = ({ label }: any) => (
  <th className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left">
    <div className="flex items-center gap-1">
      {label}
      <div className="flex items-center flex-col">
        <span>
          <SortingUp />
        </span>
        <span className="mt-[1.3px]">
          <SortingDown />
        </span>
      </div>
    </div>
  </th>
);

const StickyHeaderCell = ({ label, left, width }: any) => (
  <th
    className={`px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left sticky bg-primary-200 z-[9]`}
    style={{ left, width }}
  >
    <div className="flex items-center gap-1">
      {label}
      <div className="flex items-center flex-col">
        <span>
          <SortingUp />
        </span>
        <span className="mt-[1.3px]">
          <SortingDown />
        </span>
      </div>
    </div>
  </th>
);

const TableCell = ({ content, isSticky, left, width, textCenter }: any) => (
  <td
    className={`px-[15px] py-[3px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-left ${isSticky ? 'sticky bg-white z-[9]' : ''}`}
    style={{ width, textAlign: textCenter, ...(isSticky ? { left } : {}) }}
  >
    {content}
  </td>
);

const SummaryTabPage = ({ seasonSummaryStateData }: SeasonSummaryStateData) => {
  return (
    <div>
      <div className="upcoming-matches-table overflow-x-auto no-scrollbar rounded-b-lg">
        <table className="w-full min-w-max table-auto">
          <thead>
            <tr className="bg-primary-200">
              <StickyHeaderCell label="Rd" left="0px" width="60px" />
              <StickyHeaderCell label="Opponent" left="60px" />
              <HeaderCell label="Venue" />
              <HeaderCell label="Score" />
              <HeaderCell label="Total Score" />
              <HeaderCell label="Avg" />
              <HeaderCell label="Price" />
              <HeaderCell label="Price Change" />
              <HeaderCell label="$ per point" />
              <HeaderCell label="Picked % teams" />
            </tr>
          </thead>
          <tbody>
            {seasonSummaryStateData?.map((match, index) => (
              <tr
                key={generateUniqueId()}
                className={`${index === seasonSummaryStateData?.length - 1 ? '' : 'border-b-[1px] border-black-300'}`}
              >
                <TableCell
                  content={match?.rd}
                  isSticky={true}
                  left="0px"
                  width="60px"
                />
                <TableCell
                  content={
                    <div className="flex items-center gap-2">
                      <div className="bg-primary-200 w-[26px] h-[26px] rounded-full"></div>
                      <div>
                        <p>{match?.opponent}</p>
                      </div>
                    </div>
                  }
                  isSticky={true}
                  left="60px"
                />
                <TableCell content={match?.venue} />
                <TableCell content={match?.score} />
                <TableCell content={match?.totalScore} />
                <TableCell content={match?.avg} />
                <TableCell content={`$${match?.price}k`} />
                <TableCell content={`$${match?.priceChange}k`} />
                <TableCell content={`$${match?.points}`} />
                <TableCell content={`${match?.teams}%`} />
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SummaryTabPage;
