'use client';
import { useEffect, useState } from 'react';

// Define an interface for the return type of the hook
interface ScreenSize {
  width: number;
  height: number;
}

// Create the custom hook
const useScreen = (): ScreenSize => {
  // Set initial dimensions to default values for SSR
  const [screenSize, setScreenSize] = useState<ScreenSize>({
    width: 0, // Default value for SSR
    height: 0, // Default value for SSR
  });

  useEffect(() => {
    // Check if `window` is available
    if (typeof window !== 'undefined') {
      // Function to set dimensions based on window size
      const handleResize = () => {
        setScreenSize({
          width: window.innerWidth,
          height: window.innerHeight,
        });
      };

      // Set initial dimensions on mount
      handleResize();

      // Add event listener for window resize
      window.addEventListener('resize', handleResize);

      // Cleanup function to remove event listener on unmount
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, []);

  return screenSize;
};

export default useScreen;
