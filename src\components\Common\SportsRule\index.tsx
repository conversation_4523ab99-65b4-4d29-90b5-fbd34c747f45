// GuidePage.tsx
'use client';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/UI/card';
import BonusIcon from '@/components/UI/Icons/BonusIcon';
import PrizePooIcon from '@/components/UI/Icons/PrizePooIcon';
import SkillBasedIcon from '@/components/UI/Icons/SkillBasedIcon';
import sportsData from '../../../../data/sportsData.json';
import { NestedDetail, SportData, SportsData } from '../../../../types';
import { cn, generateUniqueId } from '@/lib/utils';
import TableScoring from '@/components/RulesScoring/Scoring/tableScoring';
import Loader from '@/components/Loader';
import {
  NAVIGATE_COINS_TEXT,
  USER_COINS_ENTER_TEXT,
  USER_STEP_TEXT,
} from '@/helpers/constants/index';
import { step } from '@material-tailwind/react';
// types.ts

const SportsRule = ({ sport }: { sport: string | null }) => {
  const [sportData, setSportData] = useState<SportData | null>(null);

  useEffect(() => {
    const data: SportsData = sportsData as unknown as SportsData;
    if (sport && data[sport]) {
      setSportData(data[sport]);
    } else {
      setSportData(null);
    }
  }, [sport]);

  if (!sportData) {
    return (
      <div>
        <Loader />
      </div>
    );
  }

  const renderNestedDetails = (nestedDetails: NestedDetail[] | undefined) => {
    if (!nestedDetails) return null;

    return (
      <ul className="pl-6 space-y-2 list-disc">
        {nestedDetails.map((detail) => (
          <li key={generateUniqueId()}>
            <li>{detail.content}</li>
            {detail.nestedContent && renderNestedDetails(detail.nestedContent)}
          </li>
        ))}
      </ul>
    );
  };

  const renderDetails = (details: NestedDetail[] | undefined) => {
    if (!details) return null;

    let start = details.map((detail) => {
      const matchText =
        detail.content === NAVIGATE_COINS_TEXT ||
        detail.content === USER_COINS_ENTER_TEXT ||
        USER_STEP_TEXT;

      const result = matchText ? 2 : 1;
      return result;
    });

    return (
      <ol className="list-decimal pl-6 space-y-2 my-[18px]" start={start[0]}>
        {details.map((detail) => (
          <>
            {detail.images && (
              <div className="rounded-lg mt-[18px]">
                {detail.images.map((image) => (
                  <Image
                    key={generateUniqueId()}
                    src={image.src}
                    alt={image.alt}
                    width={925}
                    height={489}
                    className={`rounded-lg mt-[2rem] ${image.className}`}
                    unoptimized={true}
                    style={{ mixBlendMode: 'darken' }}
                  />
                ))}
              </div>
            )}
            <li key={generateUniqueId()}>
              {detail.prefixBold ? formatText(detail.content) : detail.content}
              {detail.nestedContent &&
                renderNestedDetails(detail.nestedContent)}
            </li>
          </>
        ))}
      </ol>
    );
  };

  const formatText = (text: string) => {
    const colonIndex = text.indexOf(':');

    // If there's no colon, return the text as is
    if (colonIndex === -1) {
      return <span>{text}</span>;
    }

    // Extract bold and non-bold parts of the text
    const boldText = text.substring(0, colonIndex + 1); // Text before and including the colon
    const nonBoldText = text.substring(colonIndex + 1).trim(); // Text after the colon

    return (
      <span>
        <strong>{boldText}</strong>
        {''} {nonBoldText}
      </span>
    );
  };

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header>
        <div className="container mx-auto px-4 py-4">
          <h1 className="text-[22.4px] font-bold">{sportData.title}</h1>
          <p className="text-[16px]">{sportData.description}</p>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mt-[33px]">
        <div className="max-w-full mx-4 space-y-12">
          {/* Steps */}
          <div className="md:space-y-12 space-y-6">
            {sportData.steps.map((step) => (
              <section key={generateUniqueId()} className="space-y-4">
                <div className="flex flex-col items-start space-y-2">
                  <h2 className="text-xl font-semibold text-[22.4px]">
                    {step.title}
                  </h2>
                  {step.subTitle && (
                    <p
                      className={cn(
                        'text-[16px]',
                        step.subTitle.bold && 'font-bold',
                      )}
                    >
                      {step.subTitle.content}
                    </p>
                  )}
                  <p className={cn('text-[16px] pl-2')}>
                    {formatText(step.content)}
                  </p>

                  {step.table && (
                    <div className="md:w-[950px] md:p-4 p-0 w-full">
                      <TableScoring
                        headerData={step.table?.header || []}
                        bodyData={step.table?.body || []}
                      />
                    </div>
                  )}
                </div>
                {step.images && (
                  <div className="rounded-lg mt-[18px]">
                    {step.images.map((image) => (
                      <Image
                        key={generateUniqueId()}
                        src={image.src}
                        alt={image.alt}
                        width={925}
                        height={489}
                        className={`rounded-lg ${image.className}`}
                        unoptimized={true}
                        style={{ mixBlendMode: 'darken' }}
                      />
                    ))}
                  </div>
                )}

                {step.details && renderDetails(step.details)}
              </section>
            ))}

            {/* Why Choose Section */}
            <section className="space-y-4">
              <h2 className="text-2xl font-bold">
                {sportData.whyChoose.title}
              </h2>
              <div className="grid md:grid-cols-3 gap-6">
                {sportData.whyChoose.benefits.map((benefit, index) => {
                  const IconComponent = {
                    SkillBasedIcon,
                    PrizePooIcon,
                    BonusIcon,
                  }[benefit.icon];

                  return (
                    <Card
                      key={generateUniqueId()}
                      className={cn(
                        'bg-gradient-to-b',
                        benefit.gradient
                          ? `from-[${benefit.gradient.from}] to-[${benefit.gradient.to}]`
                          : '',
                        'text-white',
                      )}
                    >
                      <CardContent className="pt-6">
                        <div className="space-y-2">
                          <div className="flex justify-start">
                            {IconComponent && <IconComponent />}
                          </div>
                          <h3 className="text-xl font-semibold">
                            {benefit.heading}
                          </h3>
                          <p className="text-muted-foreground">
                            {benefit.description}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
              {sportData.whyChoose.footer && (
                <>
                  <p>{sportData.whyChoose.footer.content}</p>
                  <p>
                    <span>Support email: </span>
                    <a
                      href={`mailto:${sportData.whyChoose.footer.supportEmail}`}
                      className="text-[#1A73E8] underline"
                    >
                      {sportData.whyChoose.footer.supportEmail}
                    </a>
                  </p>
                </>
              )}
            </section>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SportsRule;
