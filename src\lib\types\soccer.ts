import { setApiMessage } from '@/helpers/commonFunctions';

export type SoccerPlayerRole = 'GKP' | 'DEF' | 'MID' | 'FWD';

export interface SoccerPlayer {
  id: string;
  name: string;
  team: string;
  teamName: string;
  role: SoccerPlayerRole;
  price: number;
  points: number;
  selected: boolean;
  number?: number; // Player's jersey number (optional)
  image?: string;
  isCaptain?: boolean;
  isViceCaptain?: boolean;
  playerValue?: number;
  squadsId: number;
  tournamentId: number;
  playerId: number;
  scoreData: {
    lastScore: number;
    totalScore: number;
    lastThreeMatch: number;
    lastFiveMatch: number;
    playerCurrentSalary: number;
    playerLastSalary: number;
    avg: number;
    livePoint?: number;
    totalPlayed: number;
    sel?: number;
  };
  playedLastMatch?: boolean;
}

export interface SoccerPlayersByRole {
  GKP: SoccerPlayer[];
  DEF: SoccerPlayer[];
  MID: SoccerPlayer[];
  FWD: SoccerPlayer[];
}

type dreamPlayerPayload = {
  playerId: number;
  playerValue: number;
  role?: string;
};

export type SoccerPlayerData = {
  GKP: dreamPlayerPayload[];
  DEF: dreamPlayerPayload[];
  MID: dreamPlayerPayload[];
  FWD: dreamPlayerPayload[];
  captain: dreamPlayerPayload[];
  viceCaptain: dreamPlayerPayload[];
};

export type CreateSoccerTeamPayload = {
  sportId?: string | null;
  eventId?: string | null;
  eventName?: string | null;
  tournamentId?: string | null;
  competitionId?: string | null;
  playerData: SoccerPlayerData;
  name?: string;
  coins?: number | null;
  bonusCoins?: number | null;
};

export interface SoccerTeamState {
  activeTabPlayer: SoccerPlayerRole;
  soccerPlayersByRole: SoccerPlayersByRole;
  createDreamTeamPayload?: CreateSoccerTeamPayload;
  showFilter: boolean;
  showMobileFilter: boolean;
  showPlayerTable: boolean;
  remainingBudget: number;
  totalBudget: number;
  playerByRoleLimit: {
    GKP: { min: number; max: number };
    DEF: { min: number; max: number };
    MID: { min: number; max: number };
    FWD: { min: number; max: number };
  };
  lastEntry: {
    mode: 'MANUAL' | 'LUCKY' | 'DREAM' | 'FAVORITE' | 'EXPERT';
    players: SoccerPlayer[];
  };
  reservePlayers?: (SoccerPlayer | null)[];
  reservePlayersLimit?: number;
  activeReservePosition?: number;
}

export type SoccerTeamActions = {
  addPlayer: (player: SoccerPlayer, role: SoccerPlayerRole) => void;
  removePlayer: (player: SoccerPlayer, role: SoccerPlayerRole) => void;
  setCreateDreamTeamPayload: (payload: CreateSoccerTeamPayload) => void;
  setActiveTabPlayer: (role: SoccerPlayerRole) => void;
  setRemainingBudget: (budget: number) => void;
  setTotalBudget: (budget: number) => void;
  setPlayerByRoleLimit: (limits: SoccerTeamState['playerByRoleLimit']) => void;
  setLastEntry: (entry: SoccerTeamState['lastEntry']) => void;
  resetTeam: (totalBudget: number, remainingBudget: number) => void;
  setPlayerToCaptain: (player: SoccerPlayer, role: SoccerPlayerRole) => void;
  setPlayerToViceCaptain: (
    player: SoccerPlayer,
    role: SoccerPlayerRole,
  ) => void;

  createLuckyTeam: (sourcePlayersByRole: SoccerPlayersByRole) => void;
  createFavoriteTeam: (
    favoriteTeam: SoccerFavoriteTeam,
    sourcePlayers: SoccerPlayer[],
    eventId: string,
    eventName: string,
    sportId: string,
    tournamentId: string,
    competitionId: string,
    name: string,
  ) => void;

  createExpertTeam: (
    expertTeam: SoccerExpertTeam[],
    players: SoccerPlayer[],
  ) => void;

  getDreamTeam: (
    fantasyTeamResponse: any,
    playerId?: number,
    role?: SoccerPlayerRole,
  ) => void;
  setShowPlayerTable: (show: boolean) => void;
  setShowFilter: (show: boolean) => void;
  setShowMobileFilter: (show: boolean) => void;
  addReservePlayer: (player: SoccerPlayer, position: number) => void;
  removeReservePlayer: (position: number) => void;
  setActiveReservePosition: (position: number) => void;
  setReservePlayers: (players: (SoccerPlayer | null)[]) => void;
};

export interface CreateSoccerDreamTeamArgs {
  soccerPlayersByRole: SoccerPlayersByRole;
  competitionId: string;
  eventId: string;
  sportId: string;
  tournamentId: string;
  eventDetails: {
    eventName: string;
    dreamTeams?: any[];
  };
  coins?: number | null;
  bonusCoins?: number | null;
}

export interface SoccerPlayerFavouriteType {
  maxSelected: number;
  playerId: number;
  positionType: string;
}

export interface SoccerFavoriteTeam {
  DEF: SoccerPlayerFavouriteType[];
  FWD: SoccerPlayerFavouriteType[];
  GKP: SoccerPlayerFavouriteType[];
  MID: SoccerPlayerFavouriteType[];
  captain: SoccerPlayerFavouriteType[];
  viceCaptain: SoccerPlayerFavouriteType[];
}

// Add these utility functions before the reducer
export const resetCaptainAndViceCaptain = (
  players: SoccerPlayer[],
): SoccerPlayer[] => {
  return players.map((player) => ({
    ...player,
    isCaptain: false,
    isViceCaptain: false,
  }));
};

export const addSoccerPlayersWithinBudget = (
  playersByRole: Record<SoccerPlayerRole, SoccerPlayer[]>,
  remainingBudget: number,
): Record<SoccerPlayerRole, SoccerPlayer[]> => {
  let totalCost = 0;
  const validPlayers: Record<SoccerPlayerRole, SoccerPlayer[]> = {
    GKP: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  // Helper function to calculate total cost of players
  const calculateTotalCost = (players: SoccerPlayer[]): number => {
    return players.reduce(
      (sum, player) => sum + (player.scoreData?.playerCurrentSalary || 0),
      0,
    );
  };

  // Calculate total cost of all players first
  const allPlayersCost = Object.values(playersByRole).reduce(
    (sum, players) => sum + calculateTotalCost(players),
    0,
  );

  // Check if total cost exceeds budget before processing
  if (allPlayersCost > remainingBudget) {
    setApiMessage('error', 'Insufficient budget');
    // Still process players to include as many as possible within budget
  }

  // Process each role
  for (const role of Object.keys(playersByRole) as SoccerPlayerRole[]) {
    const players = playersByRole[role];
    const roleCost = calculateTotalCost(players);

    if (totalCost + roleCost <= remainingBudget) {
      totalCost += roleCost;
      validPlayers[role] = players;
    } else {
      // Try to add players individually within budget
      const affordablePlayers = [];
      for (const player of players) {
        const playerCost = player.scoreData?.playerCurrentSalary || 0;
        if (totalCost + playerCost <= remainingBudget) {
          affordablePlayers.push(player);
          totalCost += playerCost;
        }
      }
      validPlayers[role] = affordablePlayers;
    }
  }

  return validPlayers;
};

export interface SoccerExpertTeam {
  id: number;
  dreamTeamId: number;
  playerId: number;
  positionType: 'GKP' | 'DEF' | 'MID' | 'FWD' | 'captain' | 'viceCaptain';
  playerValue: number;
}
