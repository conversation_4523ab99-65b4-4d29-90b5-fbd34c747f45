'use client';
import DataTable from '@/components/UI/DataTabel';
import { newCricketTableHeader } from '@/components/UI/DataTabel/columns/createTeamColumn';

import CricketPlayerFilter from '@/components/Competition/CricketPlayerFilter';
import CricketPlayerFilterMobile from '@/components/Competition/CricketPlayerFilterMobile';
import { Table, VisibilityState } from '@tanstack/react-table';
import { FiltersState } from '../Player/PlayerFilter';
import { StatsLegend } from '../Commentary/CricketLegend';
import CustomisePanel from '../Commentary/CustomisePanel';
import CustomisePanelMobile from '../Commentary/CustomisePanelMobile';
import { useEffect, useState, useMemo } from 'react';
import { SmartPlayCricketPlayer } from '../../../../types/commentry';

const categories = [
  {
    id: 'batting',
    name: '<PERSON><PERSON>',
    children: [
      { id: 'scoreData.runPoint', name: 'Runs' },
      { id: 'scoreData.fourPoint', name: '4s' },
      { id: 'scoreData.sixPoint', name: '6s' },
      { id: 'scoreData.sr150', name: 'SR>150' },
      { id: 'scoreData.sr120to150', name: 'SR 120-150' },
      { id: 'scoreData.sr50', name: 'SR<50' },
    ],
  },
  {
    id: 'bowling',
    name: 'Bowling',
    children: [
      { id: 'scoreData.moPoint', name: 'Maidens' },
      { id: 'scoreData.dbPoint', name: 'Dot Balls' },
      { id: 'scoreData.er5', name: 'ER<5.0' },
      { id: 'scoreData.er5to6', name: 'ER 5.0-6.5' },
      { id: 'scoreData.er9', name: 'ER>9.0' },
    ],
  },
  {
    id: 'fielding',
    name: 'Fielding',
    children: [
      { id: 'scoreData.caPoint', name: 'Catches' },
      { id: 'scoreData.roPoint', name: 'Run Outs' },
      { id: 'scoreData.stPoint', name: 'Stumpings' },
    ],
  },
  {
    id: 'bonus',
    name: 'Bonus',
    children: [{ id: 'scoreData.allrounderPoint', name: 'All-Rounder' }],
  },
];

const statsData = [
  { column: 'Batting', label: 'RUN', text: 'Runs Scored' },
  { column: 'Batting', label: '4sB', text: 'Four Bonus' },
  { column: 'Batting', label: '6sB', text: 'Six Bonus' },
  {
    column: 'Strike Rate Bonus',
    label: 'SR>150',
    text: 'Strike Rate above 150',
  },
  { column: 'Bowling', label: 'MO', text: 'Maiden Overs Bowled' },
  { column: 'Economy Rate', label: 'ER<5.0', text: 'Economy Rate below 5.0' },
];

type SmartPlayScoresProps = {
  showFilter: boolean;
  setShowMobileFilter: React.Dispatch<React.SetStateAction<boolean>>;
  table: Table<SmartPlayCricketPlayer>;
  filters: FiltersState;
  setFilters: React.Dispatch<React.SetStateAction<FiltersState>>;
  setShowFilter: React.Dispatch<React.SetStateAction<boolean>>;
  showMobileFilter: boolean;
  showTaleCustomization: boolean;
  setShowTableCustomization: React.Dispatch<React.SetStateAction<boolean>>;
  sortedData: SmartPlayCricketPlayer[];
  showTableCustomizationMobile: boolean;
  setShowTableCustomizationMobile: React.Dispatch<
    React.SetStateAction<boolean>
  >;
  homeTeam?: string;
  awayTeam?: string;
};

const SmartPlayScores = ({
  setShowMobileFilter,
  showFilter,
  table,
  filters,
  setFilters,
  setShowFilter,
  showMobileFilter,
  setShowTableCustomization,
  showTaleCustomization,
  sortedData,
  setShowTableCustomizationMobile,
  showTableCustomizationMobile,
  homeTeam,
  awayTeam,
}: SmartPlayScoresProps) => {
  const [selectedTeam, setSelectedTeam] = useState<string>('all');

  const filteredData = useMemo(() => {
    if (!selectedTeam || selectedTeam === 'all') {
      return sortedData;
    }
    return sortedData.filter((player) => player.teamName === selectedTeam);
  }, [sortedData, selectedTeam]).sort((a, b) => a.name.localeCompare(b.name));

  const initializeSelectedState = () => {
    const state: Record<string, boolean> = {};

    categories.forEach((category) => {
      // Set parent category as checked
      state[category.id] = true;

      // Set all children as checked
      category.children.forEach((child) => {
        state[`${child.id}`] = true;
      });
    });

    return state;
  };

  const [selected, setSelected] = useState<Record<string, boolean>>(
    initializeSelectedState(),
  );
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    ...selected,
  });

  useEffect(() => {
    setColumnVisibility(selected);
  }, [selected]);

  return (
    <div className="relative">
      {showTaleCustomization && (
        <div className="absolute top-0 right-0 z-50">
          <CustomisePanel
            isOpen={showTaleCustomization}
            setIsOpen={setShowTableCustomization}
            selected={selected}
            setSelected={setSelected}
            categories={categories}
          />
        </div>
      )}

      {showFilter && (
        <div className="absolute top-0 right-0 z-[1200] w-1/2 px-2 md:block hidden">
          <CricketPlayerFilter<SmartPlayCricketPlayer>
            playerTable={table}
            filters={filters}
            setFilters={setFilters}
            setShowFilter={setShowFilter}
            selectedTeam={selectedTeam}
            setSelectedTeam={setSelectedTeam}
            homeTeam={homeTeam}
            awayTeam={awayTeam}
          />
        </div>
      )}
      {/* old Table */}
      <DataTable
        columns={newCricketTableHeader}
        data={filteredData || []}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={setColumnVisibility}
      />

      {showTableCustomizationMobile && (
        <CustomisePanelMobile
          isOpen={showTableCustomizationMobile}
          setIsOpen={setShowTableCustomizationMobile}
          categories={categories}
          selected={selected}
          setSelected={setSelected}
        />
      )}

      {showMobileFilter && (
        <div>
          <CricketPlayerFilterMobile<SmartPlayCricketPlayer>
            open={showMobileFilter}
            setOpen={setShowMobileFilter}
            selectedTeam={selectedTeam}
            setSelectedTeam={setSelectedTeam}
            homeTeam={homeTeam}
            awayTeam={awayTeam}
          />
        </div>
      )}

      <div className="mt-4">
        <StatsLegend stats={statsData} />
      </div>
    </div>
  );
};

export default SmartPlayScores;
