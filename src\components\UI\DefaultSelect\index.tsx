import { Option, Select } from '@material-tailwind/react';
import React from 'react';
import type { Control, FieldValues } from 'react-hook-form';
import { Controller } from 'react-hook-form';

interface OptionType {
  value: string | number;
  label: string;
}

interface SelectComponentProps {
  label?: string;
  options: OptionType[];
  name?: string;
  control?: Control<FieldValues>;
  className?: string;
  onChange?: (value: any) => void;
  value?: string | number;
  error?: string | boolean;
  placeholder?: string;
  [key: string]: any; // for other props that might be passed
  color?: string;
}

const SelectComponent: React.FC<SelectComponentProps> = ({
  label = 'Select Option',
  options = [],
  name,
  control,
  className = '',
  onChange,
  value,
  error,
  placeholder = 'Select an option...',
  color,
  ...props
}) => {
  // If control is provided, render form-controlled select
  if (control) {
    return (
      <Controller
        control={control}
        name={name ?? ''}
        render={({ field, fieldState: { error: fieldError } }) => (
          <div className={`w-full md:w-[532px] ${className}`}>
            <Select
              {...(props as any)}
              color={color}
              label={label}
              value={field.value}
              onChange={(value) => {
                field.onChange(value);
                if (onChange) onChange(value);
              }}
              error={!!fieldError}
            >
              {options.map((option) => (
                <Option key={option.value} value={option.value as any}>
                  {option.label}
                </Option>
              ))}
            </Select>
            {fieldError && (
              <div className="mt-1 text-xs text-red-500">
                {fieldError.message}
              </div>
            )}
          </div>
        )}
      />
    );
  }

  // If no control is provided, render uncontrolled select
  return (
    <div className={`w-full md:w-[532px] ${className}`}>
      <Select
        {...(props as any)}
        label={label}
        value={value}
        color={color}
        onChange={(value) => {
          if (onChange) onChange(value);
        }}
        error={!!error}
      >
        {options.map((option) => (
          <Option key={option.value} value={option.value as any}>
            {option.label}
          </Option>
        ))}
      </Select>
      {error && (
        <div className="mt-1 text-xs text-red-500">
          {typeof error === 'string' ? error : ''}
        </div>
      )}
    </div>
  );
};

export default SelectComponent;
