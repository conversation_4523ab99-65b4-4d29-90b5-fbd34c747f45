import { LineUpPlayerCricketRole, ReactSelectOptionType } from '..';
import { MatchDetailUI } from '../../db/commentary/indext';
import { Dispatch, SetStateAction } from 'react';

interface Commentary {
  inningsId: number;
  commentaryList: CommentaryItem[];
}

export type TeamOption = { value: string | number; label: string };

export type eventType =
  | 'WICKET'
  | 'FOUR'
  | 'over-break,WICKET'
  | 'NONE'
  | 'over-break';

export interface CommentaryItem {
  commText: string;
  timestamp: number;
  ballNbr: number;
  event: string;
  batTeamName: string;
  commentaryFormats: CommentaryFormat;
  batsmanStriker: Batsman;
  bowlerStriker: Bowler;
  batTeamScore: number;
  overNumber?: number;
}

export interface CommentaryFormat {
  bold: {
    formatId: string[];
    formatValue: string[];
  };
  italic: {
    formatId: string[];
    formatValue: string[];
  };
}

interface Batsman {
  batBalls: number;
  batFours: number;
  batId: number;
  batName: string;
  batRuns: number;
  batSixes: number;
  batStrikeRate?: number; // Optional
  batMins?: number; // Optional
}

interface Bowler {
  bowlId: number;
  bowlName: string;
  bowlMaidens: number;
  bowlNoballs: number;
  bowlOvs: number;
  bowlRuns: number;
  bowlWides: number;
  bowlWkts: number;
  bowlEcon?: number; // Optional
}

interface MatchHeader {
  matchId: number;
  matchDescription: string;
  matchFormat: string;
  matchType: string;
  complete: boolean;
  domestic: boolean;
  matchStartTimestamp: number;
  matchCompleteTimestamp: number;
  dayNight: boolean;
  year: number;
  state: string;
  status: string;
  tossResults: TossResults;
  result: MatchResult;
  revisedTarget: RevisedTarget;
  playersOfTheMatch: Player[];
  playersOfTheSeries: Player[];
  matchTeamInfo: MatchTeamInfo[];
  isMatchNotCovered: boolean;
  team1: Team;
  team2: Team;
  seriesDesc: string;
  venue: Venue;
  seriesId: number;
  seriesName: string;
  alertType: string;
  livestreamEnabled: boolean;
}

interface TossResults {
  tossWinnerId: number;
  tossWinnerName: string;
  decision: string;
}

interface MatchResult {
  resultType: string;
  winningTeam: string;
  winningteamId: number;
  winningMargin: number;
  winByRuns: boolean;
  winByInnings: boolean;
}

interface RevisedTarget {
  reason: string;
}

interface Player {
  id: number;
  name: string;
  fullName: string;
  nickName: string;
  captain: boolean;
  keeper: boolean;
  substitute: boolean;
  teamName: string;
  faceImageId: number;
}

interface MatchTeamInfo {
  battingTeamId: number;
  battingTeamShortName: string;
  bowlingTeamId: number;
  bowlingTeamShortName: string;
}

interface Team {
  id: number;
  name: string;
  playerDetails: any[]; // Can be further defined if needed
  shortName: string;
}

interface Venue {
  id: number;
  name: string;
  city: string;
  country: string;
  timezone: string;
  latitude: string;
  longitude: string;
}

interface MiniScore {
  inningsId: number;
  batsmanStriker: BatsmanPerformance;
  batsmanNonStriker: BatsmanPerformance;
  batTeam: BatTeam;
  bowlerStriker: BowlerPerformance;
  bowlerNonStriker: BowlerPerformance;
  overs: number;
  recentOvsStats: string;
  target: number;
  partnerShip: Partnership;
  currentRunRate: number;
  requiredRunRate: number;
  lastWicket: string;
  matchScoreDetails: MatchScoreDetails;
  latestPerformance: Performance[];
  ppData: PPData;
  matchUdrs: MatchUdrs;
  overSummaryList: any[]; // Can be further defined if needed
  status: string;
  lastWicketScore: number;
  remRunsToWin: number;
  responseLastUpdated: number;
  event: string;
}

interface BatsmanPerformance extends Batsman {
  batDots?: number; // Optional
  batMins?: number; // Optional
}

interface BatTeam {
  teamId: number;
  teamScore: number;
  teamWkts: number;
}

interface BowlerPerformance extends Bowler {
  bowlEcon?: number; // Optional
}

interface Partnership {
  balls: number;
  runs: number;
}

interface MatchScoreDetails {
  matchId: number;
  inningsScoreList: InningsScore[];
  tossResults: TossResults;
  matchTeamInfo: MatchTeamInfo[];
  isMatchNotCovered: boolean;
  matchFormat: string;
  state: string;
  customStatus: string;
  highlightedTeamId: number;
}

interface InningsScore {
  inningsId: number;
  batTeamId: number;
  batTeamName: string;
  score: number;
  wickets: number;
  overs: number;
  isDeclared: boolean;
  isFollowOn: boolean;
  ballNbr: number;
}

interface Performance {
  runs: number;
  wkts: number;
  label: string;
}

interface PPData {
  pp_1: PP;
  pp_2: PP;
}

interface PP {
  ppId: number;
  ppOversFrom: number;
  ppOversTo: number;
  ppType: string;
  runsScored: number;
}

interface MatchUdrs {
  matchId: number;
  inningsId: number;
  timestamp: string;
  team1Id: number;
  team1Remaining: number;
  team1Successful: number;
  team1Unsuccessful: number;
  team2Id: number;
  team2Remaining: number;
  team2Successful: number;
  team2Unsuccessful: number;
}

export interface MatchResponse {
  matchId: number;
  commentary: Commentary[];
  commentarySnippetList: any[]; // Can be further defined if needed
  matchDetails: {
    matchHeader: MatchHeader;
    miniscore: MiniScore;
  };
}

export interface CommentaryPlayer {
  name: string;
  role: 'batsman' | 'bowler';
  label: string;
  value: string;
}

export interface CricketCommentaryContextType {
  state: {
    commentary: CommentaryItem[];
    matchDetails: MatchDetailUI[];
    option: ReactSelectOptionType[];
    HIGHLIGHTOPTIONS: ReactSelectOptionType[];
    teamPlayerOptions: CommentaryPlayer[];
    matchDetailsData: CricketEvent | undefined;
    commentaryList: CommentaryApiResponse | undefined;
    selectedBatterPlayers: ReactSelectOptionType[] | undefined;
    awayTeamBowlerPlayers: ReactSelectOptionType[] | undefined;
    battingTeam: CricketBatInnings[] | undefined;
    bowlingTeam: CricketBowlInnings[] | undefined;
    gameStats: CricketMatchData | undefined;
    matchId: number | undefined;
    liveCommentary: CricketCommentaryResponse | undefined;
    isLiveCommentaryLoading: boolean;
    smartPlayCricketStats: SmartPlayCricketApiResponse | undefined;
  };
  selectedOption: ReactSelectOptionType;
  setSelectedOption: Dispatch<SetStateAction<ReactSelectOptionType>>;
  setTeamId: Dispatch<SetStateAction<number | undefined>>;
  showPlayerDetailsTable: boolean;
  setShowPlayerDetailsTable: Dispatch<SetStateAction<boolean>>;
  setMatchId: Dispatch<SetStateAction<number | undefined>>;
  setEventFilter: Dispatch<SetStateAction<string | undefined>>;
}

export interface Category {
  id: string;
  name: string;
  children: SubCategory[];
}

export interface SubCategory {
  id: string;
  name: string;
}

type CricketSeason = {
  id: number;
  name: string;
  rapidSeasonId: number;
  year: string;
  startDate: string | null;
  endDate: string | null;
  type: 'international' | 'domestic' | 'league'; // Adjust based on possible values
  isSync: boolean;
  fantasy_sport_salary_cap: number;
  player_base_salary: number | string; // Keeping it flexible if it comes as a string
  isActive: boolean;
  SportId: number;
  CricketTournamentId: number;
};

type CricketLineUpPlayer = {
  id: number;
  name: string;
  rapidPlayerId: number;
  image: string;
  role: string;
  dob: string;
  CountryId: number;
  SportId: number;
};

type LineupPlayer = {
  id: number;
  Pos: number;
  teamId: number;
  eventId: number;
  playerId: number;
  CricketPlayer: CricketLineUpPlayer;
};

export type CricketEvent = {
  status: boolean;
  result: {
    id: number;
    eventName: string;
    CricketLineups: LineupPlayer[];
    awayTeamId: number;
    homeTeamId: number;
    startTime: string;
    outrights: boolean;
    status: string;
    winnerCode: number | null;
    SportId: number;
    CricketTournamentId: number;
    CricketSeason: CricketSeason;
    CricketStadiumId: number;
    CricketTournament: {
      id: number;
      name: string;
      CricketCategoryId: number | null;
      SportId: number;
      gender: string;
      CricketCategory: any | null;
    };
    awayTeam: {
      id: number;
      name: string;
      gender: string;
    };
    homeTeam: {
      id: number;
      name: string;
      gender: string;
    };
    CricketStadium: {
      id: number;
      name: string;
      SportId: number;
      CountryId: number;
      CityId: number;
      timezone: string | null;
      Country: {
        id: number;
        country: string;
        variation: string;
        countryCode: string;
        country_flag: string;
      };
      City: {
        id: number;
        stateId: number;
        cityName: string;
        variation: string | null;
        stateCode: string;
        countryId: number;
        countryCode: string;
      };
    };
    ScoreBoard: {
      id: number;
      eventId: number;
      TPa: number;
      TCho: number;
      ErnInf: string;
    };
    CricketEventReferees: {
      id: number;
      Kn: number | null;
      eventId: number;
      refereeId: number;
      CricketReferee: {
        id: number;
        Nm: string;
        Kn: number;
      };
    }[];
  };
};

type CricketPlayer = {
  id: number;
  name: string;
  SportId: number;
};

type PlayerResult = {
  id: number;
  role: LineUpPlayerCricketRole;
  Pos: number;
  teamId: number;
  eventId: number;
  playerId: number;
  CricketPlayer: CricketPlayer;
};

type LineUpTeam = {
  id: number;
  name: string;
  gender: string;
};

type Event = {
  id: number;
  awayTeamId: number;
  homeTeamId: number;
  awayTeam: LineUpTeam;
  homeTeam: LineUpTeam;
};

type ResultData = {
  result: PlayerResult[];
  event: Event;
};

export type MatchLineUpApiResponse = {
  status: boolean;
  result: ResultData;
};

// Main API response structure
export interface CommentaryApiResponse {
  status: boolean;
  result: {
    count: number;
    result: CommentaryItemAPI[];
  };
}

export type CommentaryEvent = {
  id: number;
  Ov: string; // Over number (e.g., "2.3")
  Aid: number | null;
  Oid: number | null;
  T: string; // Description of the ball (e.g., "Siraj to Arya")
  S: string; // Possibly a score-related field
  Sv: string; // Possibly a score-related field
  event: string | null; // Event type (e.g., "Four", "Six", "Wicket")
  ballNbr: string; // Ball number in the over
  Run: string; // Runs scored on this ball
  over_ended: boolean; // Indicates if the over has ended
  legbyes: string | null; // Leg byes runs, if applicable
  createdAt: string; // Timestamp of creation
  updatedAt: string; // Timestamp of last update
  inningId: number | null;
  eventId: number;
  bowler: string | null; // Bowler's name, if provided
  betting: string | null; // Betting-related data, if applicable
};

export type CricketCommentaryResponse = {
  status: boolean;
  result: {
    count: number;
    result: CommentaryEvent[];
  };
};

export type SportEventCricketCommentaryEvent =
  | 'FOUR'
  | 'NONE'
  | 'WICKET'
  | 'SIX'
  | 'OUT';

type OverSeparator = {
  runs: number;
  event: string;
  score: number;
  bowlIds: number[];
  overNum: number;
  wickets: number;
  bowlRuns: number;
  bowlNames: string[];
  bowlOvers: number;
  inningsId: number;
  o_summary: string;
  timestamp: number;
  batTeamName: string;
  bowlMaidens: number;
  bowlWickets: number;
  batStrikerIds: number[];
  batStrikerRuns: number;
  batStrikerBalls: number;
  batStrikerNames: string[];
  batNonStrikerIds: number[];
  batNonStrikerRuns: number;
  batNonStrikerBalls: number;
  batNonStrikerNames: string[];
};

// Individual commentary item
export interface CommentaryItemAPI {
  id: number;
  Ov: string | null; // Over information
  Aid: number | null; // Appears to be some kind of ID (possibly Action ID)
  Oid: number | null; // Appears to be some kind of ID (possibly Origin ID)
  T: string; // Text content of the commentary
  S: string | null; // Unknown field
  Sv: string | null; // Unknown field
  inningId: number | null;
  eventId: number | null;
  timestamp: string; // Stored as string even though it appears to be a number
  event: SportEventCricketCommentaryEvent; // Event type (e.g., "NONE")

  // Format information for displaying bold/italic text
  commentaryFormats: CommentaryFormat;

  overSeparator: OverSeparator | null;
  batTeamName: string | null;
  inningsId: number | null;
  ballNbr: number | null;

  // Player information
  bowler: {
    id: number;
    name: string;
  } | null;

  betting: {
    // This might be a typo for "batting"
    id: number;
    name: string;
  } | null;
}

export interface CricketMatchData {
  status: boolean;
  result: {
    id: number;
    CricketMatchInnings: CricketMatchInnings[];
  };
}

interface CricketMatchInnings {
  id: number;
  Pt: number;
  Wk: number;
  Ov: number;
  Ti: string;
  Tn: number;
  Inn: number;
  Rr: number;
  Ex: number;
  B: number;
  LB: number;
  NB: number;
  WB: number;
  Pen: number;
  SportId: number;
  eventId: number;
  teamId: number;
  CricketBatInnings: CricketBatInnings[];
  CricketBowlInnings: CricketBowlInnings[];
  CricketCommentaries: CricketCommentary[];
  CricketFallOfWickets: CricketFallOfWicket[];
}

export interface CricketBatInnings {
  id: number;
  Lp: null | string;
  R: number;
  four: number;
  six: number;
  B: number;
  Bid: number | null;
  Fid: number | null;
  LpTx: string;
  A: number;
  Pl: null | string;
  Pid: null | number;
  teamId: number;
  playerId: number;
  Sr: number;
  inningId: number;
  inningIndex: number;
  CricketPlayer: CricketPlayer;
  Bplayer: CricketPlayer | null;
  Fplayer: CricketPlayer | null;
  comment?: CricketCommentary;
  expand?: boolean;
}

export interface CricketBowlInnings {
  id: number;
  Ov: string;
  Md: number;
  Wk: number;
  R: number;
  WB: number;
  NB: number;
  Er: number;
  A: null;
  inningId: number;
  playerId: number;
  inningIndex: number;
  CricketPlayer: CricketPlayer;
  expand?: boolean;
}

export interface CricketCommentary {
  id: number;
  Ov: string;
  Aid: number;
  Oid: number | null;
  T: string;
  S: string;
  Sv: string;
  timestamp: string;
  event: string;
  commentaryFormats: {
    bold: {
      formatId: string[];
      formatValue: string[];
    };
  };
  overSeparator: null;
  batTeamName: string;
  inningsId: number;
  ballNbr: string;
  createdAt: string;
  updatedAt: string;
  inningId: number;
  eventId: number;
}

interface CricketFallOfWicket {
  id: number;
  Pid: null;
  Bid: number;
  R: number;
  B: string;
  Wk: null;
  WkN: number;
  Co: null;
  createdAt: string;
  updatedAt: string;
  inningId: number;
  teamId: number;
  bowler: CricketPlayer;
}

export type SmartPlayCricketScoreData = {
  fourPoint: number;
  sixPoint: number;
  catchPoint: number;
  stumpingPoint: number;
  runOutPoint: number;
  dotBallPoint: number;
  economyRatePoint: number;
  strikeRatePoint: number;
  maidenOverPoint: number;
  runPoint: number;
  wicketPoint: number;
  allrounderPoint: number;
  strikeRate150: number;
  strikeRate120to150: number;
  strikeRateBelow50: number;
  economyRateAbove9: number;
  economyRate5to6: number;
  economyRateBelow5: number;
  totalPoints: number;
  playerCurrentSalary: number;
  playerLastSalary: number;
};

export type SmartPlayCricketPlayer = {
  role: string;
  tournamentId: number;
  playerId: number;
  name: string;
  teamId: number;
  image: string | null;
  teamName: string;
  scoreData: SmartPlayCricketScoreData;
};

export type SmartPlayCricketApiResponse = {
  status: boolean;
  message: string;
  result: SmartPlayCricketPlayer[];
};
