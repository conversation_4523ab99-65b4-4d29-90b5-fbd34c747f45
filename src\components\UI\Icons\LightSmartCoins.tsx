import React from 'react';

function LightSmartCoins() {
  return (
    <div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        width="82.933"
        height="18.451"
        viewBox="0 0 82.933 18.451"
      >
        <defs>
          <radialGradient
            id="radial-gradient"
            cx="0.393"
            cy="-0.106"
            r="0.963"
            gradientTransform="translate(-0.355) scale(1.904 1)"
            gradientUnits="objectBoundingBox"
          >
            <stop offset="0" stop-color="#f04b23" />
            <stop offset="0.151" stop-color="#f04b23" />
            <stop offset="0.257" stop-color="#ef4823" />
            <stop offset="0.35" stop-color="#e24726" />
            <stop offset="0.435" stop-color="#cd4627" />
            <stop offset="0.514" stop-color="#b2432b" />
            <stop offset="0.59" stop-color="#8e4038" />
            <stop offset="0.663" stop-color="#643d44" />
            <stop offset="0.731" stop-color="#363a53" />
            <stop offset="0.797" stop-color="#173963" />
          </radialGradient>
        </defs>
        <g
          id="Smart_Coins_Blue"
          data-name="Smart Coins_Blue"
          transform="translate(0 0)"
        >
          <g
            id="Group_132173"
            data-name="Group 132173"
            transform="translate(0 0)"
          >
            <g
              id="Group_132172"
              data-name="Group 132172"
              transform="translate(10.467 7.944)"
            >
              <path
                id="Path_208519"
                data-name="Path 208519"
                d="M50.224,43.568H48.289L47,40.6q-.046-.124-.178-.457t-.294-.758q-.163-.425-.333-.874t-.309-.82v2q0,.542-.008,1.021t-.023.758l-.077,2.1H43.43l.619-10.476h2.2l2.414,6.035q.108.279.286.781T49.3,41q.17-.572.348-1.076t.3-.8l2.414-6.035H54.51l.619,10.476h-2.4l-.093-2.1q-.016-.294-.023-.774t-.008-1.014q0-.534.008-1.068t.008-.921q-.124.325-.271.735t-.3.8q-.155.387-.294.719l-.217.518-1.315,3.11Z"
                transform="translate(-43.43 -33.06)"
                fill="#003764"
              />
              <path
                id="Path_208520"
                data-name="Path 208520"
                d="M99.224,33.091H101.5l3.838,10.476h-2.584l-.774-2.522H98.589l-.8,2.522H95.247Zm2.182,6.066q-.155-.449-.317-.936t-.31-.952q-.147-.464-.271-.874t-.217-.689q-.093.294-.217.7t-.271.874q-.147.464-.309.944t-.317.928h2.228Z"
                transform="translate(-82.759 -33.06)"
                fill="#003764"
              />
              <path
                id="Path_208521"
                data-name="Path 208521"
                d="M140.964,32.994q.139,0,.557-.008t.928-.008q.511,0,.99-.008t.743-.008a4.851,4.851,0,0,1,2.971.758,3.03,3.03,0,0,1,.511,3.876,3.143,3.143,0,0,1-1.3,1,2.929,2.929,0,0,1,.82.7,6.325,6.325,0,0,1,.681,1.045q.325.612.658,1.4t.735,1.733h-2.739q-.5-1.083-.836-1.911t-.635-1.393a3.5,3.5,0,0,0-.58-.851.939.939,0,0,0-.689-.286h-.263V43.47h-2.553V32.994Zm2.553,4.441h.65a1.347,1.347,0,0,0,.921-.333,1.32,1.32,0,0,0,.379-1.045,2.084,2.084,0,0,0-.07-.549,1.227,1.227,0,0,0-.224-.449,1.074,1.074,0,0,0-.426-.309,1.668,1.668,0,0,0-.658-.116h-.572v2.8h0Z"
                transform="translate(-117.459 -32.963)"
                fill="#003764"
              />
              <path
                id="Path_208522"
                data-name="Path 208522"
                d="M180.47,33.091l-.006,1.873H178.05v8.6H175.5v-8.6h-2.491V33.091Z"
                transform="translate(-141.778 -33.06)"
                fill="#003764"
              />
            </g>
            <path
              id="Path_208523"
              data-name="Path 208523"
              d="M7.182,5.148a2.478,2.478,0,0,1-.259.772c.386.552,1.148,1.746,1.5,2.326a7.747,7.747,0,0,0,1.244-3.62A4.8,4.8,0,0,0,.11,4.167c-.222,1.5.63,2.9,1.287,4.188a49.682,49.682,0,0,0,3.4,5.228c.47-.6.947-1.239,1.389-1.858a2.334,2.334,0,1,1-3.442.765L.2,12.471a4.7,4.7,0,0,0,3.114,5.7A4.812,4.812,0,0,0,9.6,13.428C9.215,10.855,6.417,6.99,4.864,4.823c-.456.616-.943,1.233-1.382,1.863a2.335,2.335,0,1,1,3.7-1.537Z"
              transform="translate(0 0)"
              fill="url(#radial-gradient)"
            />
          </g>
          <g
            id="Group_132174"
            data-name="Group 132174"
            transform="translate(50.115 1.839)"
          >
            <path
              id="Path_208524"
              data-name="Path 208524"
              d="M212.605,7.817a3.8,3.8,0,0,0-1.215-.186,3.265,3.265,0,0,0-3.441,3.345v1.747l2.54,3.919V11.427c0-.1,0-.2.014-.294.051-.535.265-1.058.887-1.058s.877.663.877,1V12.3c0,.308.4.26,1.186.26,1.092,0,1.28-.07,1.28-.308v-1.28a3.185,3.185,0,0,0-2.128-3.159Zm.918,11.414c-1.044,0-1.256-.024-1.256.308v1.234a.868.868,0,0,1-.877.95c-.622,0-.836-.489-.887-1.041-.01-.1-.014-.207-.014-.311v-1.9l-2.54-3.885v6.331a3.208,3.208,0,0,0,2.494,3.213,4.084,4.084,0,0,0,.947.108,3.06,3.06,0,0,0,3.343-3.321V19.682c0-.427-.212-.451-1.21-.451Z"
              transform="translate(-207.949 -7.632)"
              fill="#fc4714"
            />
            <path
              id="Path_208525"
              data-name="Path 208525"
              d="M244.519,37.21a3.155,3.155,0,0,1-3.463,3.416,3.242,3.242,0,0,1-3.44-3.416v-4.6a3.452,3.452,0,0,1,6.9,0v4.6ZM242,36.522V33.249c0-.735-.166-1.566-.949-1.566-.759,0-.949.831-.949,1.566v3.273c0,.9.19,1.589.949,1.589C241.862,38.112,242,37.4,242,36.522Z"
              transform="translate(-230.466 -24.014)"
              fill="#fc4714"
            />
            <path
              id="Path_208526"
              data-name="Path 208526"
              d="M272.105,11.313c.071.854.166,1.447-1.376,1.637a1.474,1.474,0,0,1-1.447-1.708c.071-.759.284-1.47,1.447-1.423A1.5,1.5,0,0,1,272.105,11.313Zm-.071,14.162c0,.427-.047.427-1.329.427-1.186,0-1.21,0-1.21-.38V14.895c0-.38.095-.4,1.21-.4,1.234,0,1.329,0,1.329.427V25.475Z"
              transform="translate(-254.494 -9.29)"
              fill="#fc4714"
            />
            <path
              id="Path_208527"
              data-name="Path 208527"
              d="M292.062,40.45c0,.214-.214.237-1.186.237-1.139,0-1.376-.024-1.376-.19V33.238c0-.712-.047-1.494-.712-1.494-.783,0-1.091.641-1.091,1.423v7.259c0,.261-.1.261-1.352.261-1.068,0-1.186-.024-1.186-.237V29.49c0-.213.19-.19,1.068-.19,1.091,0,1.257-.071,1.257.356,0,.617,0,.806.166.545a2.3,2.3,0,0,1,1.637-.9c1.566,0,2.775.925,2.775,3.44V40.45Z"
              transform="translate(-266.551 -24.075)"
              fill="#fc4714"
            />
            <path
              id="Path_208528"
              data-name="Path 208528"
              d="M323.352,37.688a3.3,3.3,0,0,1-6.595-.308c0-.166.19-.166,1.091-.166.878,0,1.233-.024,1.233.119,0,.641.214,1.186.925,1.186s.925-.427.925-1.02c0-2.135-4.1-1.21-4.1-5.124a3.092,3.092,0,0,1,3.179-3.06,3.231,3.231,0,0,1,3.179,3.345c0,.142-.285.119-1.139.119-.972,0-1.257-.047-1.257-.166,0-.783-.356-1.21-.83-1.21-.522,0-.831.356-.831,1.02,0,1.613,4.223,1.376,4.223,5.266Z"
              transform="translate(-290.535 -24.089)"
              fill="#fc4714"
            />
          </g>
        </g>
      </svg>
    </div>
  );
}

export default LightSmartCoins;
