'use client';
import React from 'react';

const PlayerLockIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
    >
      <g
        id="Group_110580"
        data-name="Group 110580"
        transform="translate(-485 -792)"
      >
        <rect
          id="Rectangle_57467"
          data-name="Rectangle 57467"
          width="16"
          height="16"
          rx="3"
          transform="translate(485 792)"
          fill="#ffc69a"
        />
        <g id="Lock-unlock" transform="translate(486.72 792.794)">
          <g
            id="Group_97430"
            data-name="Group 97430"
            transform="translate(2.28 2.206)"
          >
            <g
              id="Group_97429"
              data-name="Group 97429"
              transform="translate(0 0)"
            >
              <path
                id="Path_167113"
                data-name="Path 167113"
                d="M8.132,106.427V103.4a1.515,1.515,0,0,0-1.513-1.513h-.29l-.009-1.361a2.3,2.3,0,0,0-.7-1.651,2.25,2.25,0,0,0-3.8,1.628v1.383h-.3A1.515,1.515,0,0,0,0,103.4v3.028a1.515,1.515,0,0,0,1.513,1.513H6.619a1.515,1.515,0,0,0,1.513-1.513M2.688,100.5a1.374,1.374,0,0,1,1.317-1.376,1.355,1.355,0,0,1,1,.381,1.4,1.4,0,0,1,.434,1.017l.009,1.361H2.688ZM.877,106.427V103.4a.637.637,0,0,1,.636-.636H6.619a.637.637,0,0,1,.636.636v3.028a.637.637,0,0,1-.636.636H1.513a.637.637,0,0,1-.636-.636"
                transform="translate(0 -98.249)"
                fill="#fc4714"
              />
              <path
                id="Path_167114"
                data-name="Path 167114"
                d="M254.124,509.738a.823.823,0,0,0-.46,1.5l.022.015v.628a.438.438,0,1,0,.877,0v-.628l.022-.015a.823.823,0,0,0-.46-1.5"
                transform="translate(-250.059 -504.47)"
                fill="#fc4714"
              />
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
};

export default PlayerLockIcon;
