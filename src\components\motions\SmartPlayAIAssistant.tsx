'use client';

import { useEffect, useState } from 'react';
import { motion } from 'motion/react';

export default function SmartPlayAIAssistant() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="flex items-center justify-center w-full">
      <motion.div
        className="text-xl font-bold text-primary-200 flex items-center justify-center flex-wrap space-x-2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
        >
          AI
        </motion.span>{' '}
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.0, duration: 0.5 }}
          className="relative text-orange-500"
        >
          <span>Assistant</span>
        </motion.span>
      </motion.div>
    </div>
  );
}
