'use client';
import { Dialog } from '@material-tailwind/react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';

import whiteLogo from '@/assets/images/login-popup-white-logo.png';
import { useAuthContext } from '@/helpers/context/authContext';
import { Config } from '@/helpers/context/config';
import { useTeam } from '@/helpers/context/createTeamContext';
import useScreen from '@/hooks/useScreen';
import { LocalStorage } from '@/lib/utils';

import { Button } from './UI/button';
import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import { useSoccerStore } from '@/store/soccer/useSoccerStore';
import { useCompetition } from '@/helpers/context/competitionContext';

const LoginPopUp = () => {
  const router = useRouter();
  const { width } = useScreen();
  const { LoginPopUp, setLoginPopUp } = useAuthContext();
  const { state } = useTeam();
  const { state: rugbyLeagueState } = useRugbyLeagueContext();
  const { soccerPlayersByRole } = useSoccerStore();
  const platform = useSearchParams().get('platform');
  const { eventDetailsResponse } = useCompetition();
  const eventType = eventDetailsResponse?.result?.eventConfiguration.eventType;
  const {
    state: { playersByRole: AFLPlayersByRole, reserveState: { reservePlayers: AFLReservePlayers } },
  } = useFootballContext();
  const searchParams = useSearchParams();
  const event_id = searchParams.get('event_id');

  const handleModal = () => {
    if (platform !== 'mobile') {
      setLoginPopUp(!LoginPopUp);
    }
  };

  const handleClose = () => {
    setLoginPopUp(false);
  };

  const authHandelar = ({ isSingup }: { isSingup: boolean }) => {
    const currentPath = window.location.href;
    LocalStorage.setItem('redirect', { url: currentPath });
    if (event_id) {
      LocalStorage.setItem('event_id', event_id);
    }
    if (eventType) {
      LocalStorage.setItem('event_type', eventType);
    }
    const cricketReservePlayers = state.reserveState.reservePlayers
    LocalStorage.setItem('cricket_reserve_players', cricketReservePlayers);
    if (currentPath.includes('rugby-league')) {
      LocalStorage.setItem(
        'rugby_league_dream_team',
        rugbyLeagueState.playersByRole,
      );
      LocalStorage.setItem('rugby_league_reserve_players', rugbyLeagueState.reserveState.reservePlayers);
    } else if (currentPath.includes('football')) {
      LocalStorage.setItem('afl_dream_team', AFLPlayersByRole);
      LocalStorage.setItem('afl_reserve_players', AFLReservePlayers);
    } else if (currentPath.includes('soccer')) {
      LocalStorage.setItem('soccer_dream_team', soccerPlayersByRole);
    } else {
      LocalStorage.setItem('dream_team', state.playersByRole);
    }
    if (isSingup) {
      router.push(Config.siteBaseURL + 'sign-up');
    } else {
      router.push(Config.siteBaseURL + 'sign-in');
    }
  };

  return (
    <>
      {/* @ts-expect-error */}
      <Dialog
        className="login-required-dialog no-scrollbar"
        open={LoginPopUp}
        handler={handleModal}
      >
        <div className="login-dialog">
          <div className="dialog-content-box">
            {platform !== 'mobile' && (
              <button
                className="dialog-close absolute right-3 top-3 p-3 max-639:p-0 cursor-pointer"
                onClick={() => handleClose()}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={2}
                  stroke="#989898"
                  className="h-7 w-7"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            )}
          </div>
          <div className="text-center">
            <p className="font-apotekCompRegular text-[60px] max-639:text-[25px] leading-[50px] max-639:leading-[35px] text-white pt-[30px] max-639:pt-[23px] pb-[10px] max-639:pb-[7px]">
              Log In Required
            </p>
            <div className="flex justify-center flex-wrap font-inter text-[22.4px] max-639:text-xs leading-[27px] max-639:leading-4 text-white pb-[18px] max-639:pb-[15px] max-639:w-[236px] max-639:mx-auto max-639:my-0">
              <span>Please log in with your </span>
              <Image
                src={whiteLogo}
                className="text-logo mx-2 max-639:mx-1 h-full max-639:w-9"
                alt="logo"
                unoptimized={true}
              />
              <span> account for unrestricted access.</span>
            </div>

            <Button
              className="font-apotekCompRegular text-[30px] z-[1500] leading-[19px] max-w-[374px] max-639:max-w-[252px] w-full px-0 py-[9px] text-white bg-orange-500 !ring-0 !ring-offset-transparent"
              onClick={() => authHandelar({ isSingup: false })}
            >
              Log In
            </Button>
            <div className="font-inter mt-[10px] max-639:mt-[11px]">
              <span className="text-black-200">Don't have an account?</span>{' '}
              <button
                className="text-orange-500 cursor-pointer z-[1500] underline font-semibold"
                onClick={() => authHandelar({ isSingup: true })}
              >
                Sign Up Now
              </button>
            </div>
            {/* <div className="pt-[33px] max-1024:pt-[25px] max-639:pt-[21px] pb-[123px] max-1024:pb-10 max-639:pb-[115px]">
              <p className="font-theStamshonsDemo text-[32px] max-639:text-[17px] leading-[39px] max-639:leading-[21px] text-orange-500 pb-1 max-639:pb-[3px]">
                Your ultimate sports ecosystem!
              </p>
              <div className="font-peckhamPressTrial text-white">
                {width > 639 ? (
                  <>
                    <p className="text-[22px] max-639:text-[13px] leading-[26px] max-639:leading-4">
                      Smart odds comparison{' '}
                      <span className="text-sm max-639:text-[8px] text-orange-500">
                        |
                      </span>{' '}
                      Daily Tips{' '}
                    </p>
                    <p className="text-[22px] max-639:text-[13px] leading-[26px] max-639:leading-4">
                      Results{' '}
                      <span className="text-sm max-639:text-[8px] text-orange-500">
                        |
                      </span>{' '}
                      News{' '}
                      <span className="text-sm max-639:text-[8px] text-orange-500">
                        |
                      </span>{' '}
                      Stats{' '}
                      <span className="text-sm max-639:text-[8px]">&</span> info
                    </p>
                    <p className="text-[22px] max-639:text-[13px] leading-[26px] max-639:leading-4">
                      Live scores
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-[22px] max-639:text-[13px] leading-[26px] max-639:leading-4">
                      Smart odds comparison
                    </p>
                    <p className="text-[22px] max-639:text-[13px] leading-[26px] max-639:leading-4">
                      Daily Tips{' '}
                      <span className="text-sm max-639:text-[8px] text-orange-500">
                        |
                      </span>{' '}
                      Results{' '}
                      <span className="text-sm max-639:text-[8px] text-orange-500">
                        |
                      </span>{' '}
                      News{' '}
                    </p>
                    <p className="text-[22px] max-639:text-[13px] leading-[26px] max-639:leading-4">
                      Stats{' '}
                      <span className="text-sm max-639:text-[8px]">&</span> info{' '}
                      <span className="text-sm max-639:text-[8px] text-orange-500">
                        |
                      </span>{' '}
                      Live scores
                    </p>
                  </>
                )}
              </div>
            </div> */}
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default LoginPopUp;
