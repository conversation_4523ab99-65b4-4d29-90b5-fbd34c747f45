'use client';;
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Button } from '@/components/UI/button';
import { PlayerCaptainViewIcon } from '@/components/images';
import SoccerFantasyUI from '@/components/Competition/Soccer/SoccerFantasyUI';
import SoccerPlayerSelectionUI from '@/components/Competition/Soccer/SoccerPlayerSelectionUI';
import DataTable from '@/components/UI/DataTabel';
import useScreen from '@/hooks/useScreen';
import {
  completedCricketPlayerListColumn,
  liveCricketPlayerListColumn,
} from '@/components/UI/DataTabel/columns/createTeamColumn';
import { upcomingPlayerListColumn } from '@/components/UI/DataTabel/upcomingPlayerlistColumns';
import { ColumnDef } from '@tanstack/react-table';
import { Player } from '../../../../../../types/competitions';
import { useSoccerStore } from '@/store/soccer/useSoccerStore';
import CompetitionDetailsHeader from '@/components/Competition/CompetitionDetailsHeader';
import SharePopup from '@/components/PopUp/Share/SharePopup';
import ShareIcon from '@/components/Icons/Share/ShareIcon';
import DreamTeamLoader from '@/components/Loading/DreamTeamLoader';

const PreviewTeam = () => {
  const {
    eventDetailsResponse,
    dreamTeamResponse,
    activeTeam,
    setActiveTeam,
    isDreamTeamResponseLoading,
    isPlayerListResponseLoading,
    eventTimeStatus: { isLockout, isLive, isCompleted },
  } = useCompetition();


  const {
    getDreamTeam,
    setRemainingBudget,
    setTotalBudget,
    remainingBudget,
    resetTeam,
    soccerPlayersByRole,
    showPlayerTable,
    setShowPlayerTable,
  } = useSoccerStore();

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const event_id = searchParams.get('event_id');
  const sport_id = searchParams.get('sport_id');
  const tournament_id = searchParams.get('tournament_id');
  const seasonId = searchParams.get('seasonId');
  const competition_id = searchParams.get('competition_id');
  const comp_Type = searchParams.get('comp_Type');
  const dreamTeamId = searchParams.get('dreamTeamId');
  const [activeTab, setActiveTab] = useState<string | number>(1);
  const allLiveScore = dreamTeamResponse?.result?.totalLivePoints;
  const totalScore = dreamTeamResponse?.result?.totalLivePoints;
  const [showSharePopup, setShowSharePopup] = useState(false);
  const flattenedPlayers = Object.values(soccerPlayersByRole).flat();

  console.log(flattenedPlayers, 'flattenedPlayers');

  const router = useRouter();
  const { width } = useScreen();
  const query = {
    event_id: event_id || '',
    sport_id: sport_id || '',
    tournament_id: tournament_id || '',
    seasonId: seasonId || '',
    competition_id: competition_id || '',
    dreamTeamId: `${activeTeam?.id}`,
    comp_Type: comp_Type || '',
  };

  let playerColumn: ColumnDef<Player>[] = [];
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  switch (eventStatus) {
    case 'finished':
      playerColumn = completedCricketPlayerListColumn;
      break;
    case 'innings break':
    case 'inprogress':
    case 'drink':
      playerColumn = liveCricketPlayerListColumn;
      break;
    case 'upcoming':
      playerColumn = upcomingPlayerListColumn;
      break;
  }

  useEffect(() => {
    if (dreamTeamResponse && dreamTeamId) {
      setRemainingBudget(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      setTotalBudget(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
      getDreamTeam(dreamTeamResponse);
    }
    return () => {
      resetTeam(
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
        eventDetailsResponse?.result?.eventDetails?.SoccerSeason
          ?.fantasy_sport_salary_cap ?? 0,
      );
    };
  }, [dreamTeamResponse, dreamTeamId, eventDetailsResponse]);

  useEffect(() => {
    if (activeTeam?.id) {
      router.push(`${pathname}?${new URLSearchParams(query).toString()}`);
    }
  }, [activeTeam]);

  const dreamTeams = eventDetailsResponse?.result?.dreamTeams;

  useEffect(() => {
    if (dreamTeams && !activeTeam?.id && dreamTeamId) {
      setActiveTeam(() => dreamTeams.find((team) => team.id === +dreamTeamId));
    }

    if (dreamTeams && !activeTeam?.id && !dreamTeamId) {
      setActiveTeam(() => dreamTeams[0]);
    }
  }, [dreamTeamId, dreamTeams]);



  return (
    <div className="bg-white px-0">
      <div>
        <div className="px-[33px] max-799:px-0 bg-off-white-200 pb-[40px]">
          {/* Competition Header */}
          <div className="mt-5">
            <CompetitionDetailsHeader
              stats={{
                selectedPlayer: '17',
                remainingSalary:
                  eventStatus === 'upcoming'
                    ? remainingBudget
                    : dreamTeamResponse?.result?.totalSpendValue!,

                allLiveScore: allLiveScore,
              }}
              status={eventDetailsResponse?.result?.eventDetails?.status}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              dreamTeamResponse={dreamTeamResponse}
            />
          </div>

          <div className="flex space-x-2 mt-2 flex-col lg:flex-row w-full">
            <div className="w-full">
              {activeTab === 1 ? (
                isDreamTeamResponseLoading ? <DreamTeamLoader /> : <SoccerFantasyUI />
              ) : (
                <DataTable
                  columns={playerColumn}
                  data={flattenedPlayers as any[]}
                  stickyColumns={width <= 700 ? [0] : []}
                  initialColumnVisibility={{
                    lastFiveMatch: false,
                  }}
                  isLoading={isPlayerListResponseLoading || isDreamTeamResponseLoading}
                />
              )}
            </div>
            <div className="w-[95%]">
              <SoccerPlayerSelectionUI />
            </div>
          </div>
        </div>
      </div>

      {eventDetailsResponse?.result?.eventDetails?.status === 'upcoming' &&
        !showPlayerTable &&
        !isLockout &&
        !isLive &&
        !isCompleted && (
          <div className="bg-gray-50 z-[1200] fixed bottom-0 left-0 right-0">
            <div className="absolute -top-[25px] left-1/2 transform -translate-x-1/2 hidden"></div>
            <div className="flex w-full space-x-2 pb-4 md:space-x-0 md:mt-0 justify-around items-center">
              <div className="space-x-2 mt-5 flex flex-wrap gap-2 justify-center">
                <Button
                  size="sm"
                  disabled={
                    eventDetailsResponse?.result?.eventConfiguration
                      ?.eventType === 'free' &&
                    eventDetailsResponse?.result?.dreamTeams?.length > 0
                  }
                  variant="ghost"
                  className="!bg-[#335F83] text-white w-40 !disabled:cursor-not-allowed"
                  onClick={() => {
                    resetTeam(
                      eventDetailsResponse?.result?.eventDetails?.SoccerSeason
                        ?.fantasy_sport_salary_cap ?? 0,
                      eventDetailsResponse?.result?.eventDetails?.SoccerSeason
                        ?.fantasy_sport_salary_cap ?? 0,
                    );
                    const soccerUrl = `/competitions/soccer/${eventDetailsResponse?.result?.eventConfiguration?.eventId}?event_id=${eventDetailsResponse?.result?.eventConfiguration?.eventId}&sport_id=8&tournament_id=${eventDetailsResponse?.result?.eventDetails?.SoccerTournamentId}&competition_id=${eventDetailsResponse?.result?.eventConfiguration?.id}&seasonId=${eventDetailsResponse?.result?.eventDetails?.SoccerSeasonId}&add_more=true`;
                    router.push(soccerUrl);
                  }}
                >
                  {eventDetailsResponse?.result?.eventConfiguration
                    ?.eventType === 'free' &&
                    eventDetailsResponse?.result?.dreamTeams?.length > 0
                    ? 'Team Entered'
                    : 'Add one more team'}
                </Button>
                <SharePopup
                  isOpen={showSharePopup}
                  onClose={() => setShowSharePopup(false)}
                >
                  <button
                    type="button"
                    className="cursor-pointer border-none bg-transparent p-0 flex items-center justify-center"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setShowSharePopup(true);
                    }}
                  >
                    <ShareIcon width={35} height={35} />
                  </button>
                </SharePopup>
              </div>

              <div className="fixed bottom-[71px] left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center ">
                <PlayerCaptainViewIcon
                  className="md:hidden block"
                  onClick={() => setShowPlayerTable(true)}
                />
              </div>
            </div>
          </div>
        )}

      {eventDetailsResponse?.result?.eventDetails?.status !== 'upcoming' && (
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center ">
          <PlayerCaptainViewIcon
            className="md:hidden block"
            onClick={() => setShowPlayerTable(true)}
          />
        </div>
      )}
    </div>
  );
};

export default PreviewTeam;
