// cancel modal css

.cancel-modal {
  .dialog-details {
    padding: 12px 42px 45px;

    @media (max-width: 799px) {
      padding: 12px 18px 33px;
    }
  }
}

// hold membership date picker
.membership-date-picker {
  .MuiOutlinedInput-root {
    background-color: #ffffff !important;
    border: 1px solid #d4d6d8;

    input {
      color: #989898;
      font-weight: 400;
    }
  }

  #date-picker-inline::placeholder {
    color: #989898;
    opacity: 1;
  }
}

// membership select box

.membership-select {
  .select__control {

    .select__indicator {
      svg {
        path {
          fill: #000000 !important;
        }
      }
    }


  }
}

.add-payment-modal {
  .dialog-details {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.coupon-number {
  width: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coupon-card {
  padding: 16.9px 12.8px;
  background-color: white;
  font-size: 16px;
  line-height: 19px;
  font-weight: 400;
  color: white;
  max-width: 100px;
  word-wrap: break-word;
}

.border-element {
  position: relative;
}

.border-element:before {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: #e7e9ec;
  border-radius: 50%;
  top: -6px;
  left: 75%;
  transform: translateX(-50%);
}

.border-element .circle-bottom {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: #e7e9ec;
  border-radius: 50%;
  bottom: -6px;
  left: 75%;
  transform: translateX(-50%);
}

.border-element:after {
  content: '';
  position: absolute;
  height: 100%;
  border-right: 7px dotted #e7e9ec;
}
