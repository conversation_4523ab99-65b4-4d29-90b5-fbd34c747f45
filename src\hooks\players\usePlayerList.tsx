import { useQuery } from '@tanstack/react-query';
import { getPlayerList } from '@/helpers/fetchers/competitions';
import { quyerKeys } from '@/lib/queryKeys';

type PlayerListProps = {
  tournamentId: string | null;
  sportId: string | null;
  eventId: string | null;
  dreamTeamId?: number | null;
  seasonId?: string | null;
};

export const usePlayerList = <T extends unknown>({
  tournamentId,
  sportId,
  eventId,
  dreamTeamId,
  seasonId,
}: PlayerListProps) => {
  return useQuery({
    queryKey: [
      quyerKeys.GET_PLAYER_LIST,
      tournamentId,
      sportId,
      eventId,
      dreamTeamId,
      seasonId,
    ],
    queryFn: () =>
      getPlayerList<T>(tournamentId, sportId, eventId, dreamTeamId, seasonId),
  });
};
