'use client';

import { Tooltip } from '@material-tailwind/react';
import { formatNumberWithCommas, getShortName } from '@/lib/utils';
import PlayerValueChange from '@/components/Competition/PlayerValueChange';
import {
  FootballPlayer,
  Player,
  RugbyPlayerPosition,
} from '../../../../types/competitions';
import { RugbyPlayer } from '../../../../types/rugby-league';
import { PlayerRoleType } from '../../../../types/competitions/index';
import { FootBallRole } from '../../../../types/football/index';
import { RugbyLeaguePlayerRoleType } from '../../../../types/rugby-league/index';

type PlayerCardDisplayRole =
  | PlayerRoleType
  | FootBallRole
  | RugbyLeaguePlayerRoleType
  | RugbyPlayerPosition
  | 'G'
  | 'F'
  | 'D'
  | 'M'
  | 'GK'
  | 'DEF'
  | 'MID'
  | 'FWD'
  | 'BAC'
  | 'HAL'
  | 'BR'
  | 'FRF'
  | 'IC'
  | 'w'
  | 'c'
  | 'Batter'
  | 'Batting Allrounder'
  | 'WK-Batter'
  | 'Bowler'
  | 'Bowling Allrounder';

export const getPlayerRole = (role: PlayerCardDisplayRole): string => {
  let roleType: string = role;
  switch (role) {
    case 'Goalkeeper': {
      roleType = 'GKP';
      break;
    }
    case 'Defender': {
      roleType = 'DEF';
      break;
    }
    case 'Ruck': {
      roleType = 'RUC';
    }
    case 'Forward': {
      roleType = 'FWD';
      break;
    }
    case 'Midfielder': {
      roleType = 'MID';
      break;
    }
    case 'Centres':
      roleType = 'C';
      break;
    case 'Five Eighth':
      roleType = 'FE';
      break;
    case 'Back Row':
      roleType = 'BR';
      break;
    case 'Lock':
      roleType = 'LOC';
      break;
    case 'Prop Forward':
      roleType = 'PF';
      break;
    case 'Prop':
      roleType = 'PF';
      break;
    case 'Interchange':
      roleType = 'IC';
      break;
    case 'Halfback':
      roleType = 'HB';
      break;
    case 'Centre':
      roleType = 'CEN';
      break;
    case 'Wing':
      roleType = 'WING';
      break;
    case 'Full Back':
      roleType = 'FB';
      break;
    case 'Hooker':
      roleType = 'HOK';
      break;
    case 'Fullback':
      roleType = 'FB';
      break;
    case 'FB':
      roleType = 'FB';
      break;
    case 'half back':
      roleType = 'HB';
      break;
    case 'Back':
      roleType = 'BR';
      break;
    case 'Outside Back':
      roleType = 'OB';
      break;
    case 'Second Row':
      roleType = 'SR';
      break;
    case 'Utility':
      roleType = 'UTILITY';
      break;

    default:
      roleType = role;
      break;
  }

  return roleType;
};

// Generic PlayerCardContent component
const PlayerCardContent = <T extends Player | FootballPlayer | RugbyPlayer>({
  player,
  isPlayerLocked,
  playerCurrentSalary,
  playerLastSalary,
}: {
  player: T; // Generic player type constrained to Player
  isPlayerLocked: boolean;
  playerCurrentSalary: number;
  playerLastSalary: number;
}) => (
  <div className="bg-white rounded-lg shadow p-3 md:w-40 w-[150px]">
    <div className="flex items-center gap-2 mt-2">
      <div className="w-full">
        <h3 className="font-semibold text-left md:text-sm text-xs truncate w-[95%]">
          <Tooltip
            content={player?.name}
            placement="bottom"
            className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
          >
            {getShortName(player.name)}
          </Tooltip>
        </h3>
        <p className="text-xs text-left text-gray-600 truncate">
          {player.teamName}
        </p>
        <div className="flex justify-between items-center flex-wrap mt-1">
          <span className="md:text-xs text-[9px] text-gray-500">
            {getPlayerRole(player.role)}
          </span>
          {!isPlayerLocked && (
            <PlayerValueChange
              formatToCustomStyle={formatNumberWithCommas}
              playerCurrentSalary={playerCurrentSalary}
              playerLastSalary={playerLastSalary}
            />
          )}
        </div>
      </div>
    </div>
  </div>
);

export default PlayerCardContent;
