'use client';
import { Radio } from '@material-tailwind/react';
import { useEffect, useRef, useState } from 'react';

import { CheckedRadioIcon, FillDropDown } from '../images';
import { Checkbox } from '../UI/checkbox';

const FilterDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({});
  const [selectAll, setSelectAll] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      // @ts-expect-error
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const filterOptions = [
    {
      name: 'County Championship Division One 2024',
      value: 'county_championship_division_one_2024',
      id: 1,
      isChecked: false,
    },
    {
      name: 'County Championship Division Two 2024',
      value: 'county_championship_division_two_2024',
      id: 2,
      isChecked: false,
    },
    {
      name: 'Rachael Heyhoe Flint Trophy 2024',
      value: 'rachael_heyoe_flint_trophy_2024',
      id: 3,
      isChecked: false,
    },
    {
      name: 'T20 Blast 2024',
      value: 't20_blast_2024',
      id: 4,
      isChecked: false,
    },
    {
      name: 'England v Sri Lanka Tests - Men',
      value: 'england_sri_lanka_tests_men',
      id: 5,
      isChecked: false,
    },
    {
      name: 'Pakistan v Bangladesh Tests - Men',
      value: 'pakistan_bangladesh_tests_men',
      id: 6,
      isChecked: false,
    },
    {
      name: 'Scotland v Australia T20Is - Men',
      value: 'scotland_australia_t20is_men',
      id: 7,
      isChecked: false,
    },
    {
      name: 'Ireland v England ODIs - Women 1st ODI',
      value: 'ireland_england_women_1st_odi',
      id: 8,
      isChecked: false,
    },
    {
      name: 'Afghanistan v New Zealand - Men',
      value: 'afghanistan_new_zealand_men',
      id: 9,
      isChecked: false,
    },
    {
      name: 'England v Australia T20Is - Men 1st T20I',
      value: 'england_australia_men_1st_t20i',
      id: 10,
      isChecked: false,
    },
    {
      name: 'Ireland v England T20Is - Women 1st T20I',
      value: 'ireland_england_women_1st_t20i',
      id: 11,
      isChecked: false,
    },
    {
      name: 'Afghanistan v South Africa - Men 1st ODI',
      value: 'afghanistan_south_africa_men_1st_odi',
      id: 12,
      isChecked: false,
    },
    {
      name: 'England v Australia ODIs - Men 1st ODI',
      value: 'england_australia_men_1st_odi',
      id: 13,
      isChecked: false,
    },
  ];

  useEffect(() => {
    // Update selectedFilters based on selectAll state
    if (selectAll) {
      const allFilterOptions = filterOptions.reduce((obj, option) => {
        // @ts-expect-error
        obj[option?.value] = true;
        return obj;
      }, {});

      setSelectedFilters(allFilterOptions);
    } else {
      setSelectedFilters({});
    }
  }, [selectAll]);

  const handleSelectFilter = (optionValue: any) => {
    setSelectedFilters((prev: any) => ({
      ...prev,
      [optionValue]: !prev[optionValue], // Toggle checkbox state
    }));
  };

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
  };

  const handleClearAll = () => {
    setSelectedFilters({});
    setSelectAll(false);
  };

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="inline-flex justify-between items-center w-24 max-767:w-auto p-[9px] text-xs leading-[15px] font-semibold text-black-800 bg-black-300 rounded-lg"
      >
        <span className="max-767:hidden">Filter</span>
        <span
          className={`transform transition-transform duration-500 ${
            isOpen ? 'rotate-180' : 'rotate-0'
          }`}
        >
          <FillDropDown />
        </span>
      </button>

      {isOpen && (
        <div className="absolute left-0 mt-2 bg-primary-200 border border-gray-300 divide-y divide-gray-100 rounded-md shadow-lg w-80 z-10 overflow-auto h-[calc(100vh-200px)] no-scrollbar">
          <div className="px-3 py-[17px]">
            <div className="flex justify-between items-center mb-[9.5px]">
              <div className="flex justify-start items-center">
                <Checkbox
                  id="select-all"
                  name="selectall"
                  className="filter-checkbox"
                  checked={selectAll}
                  onClick={handleSelectAll}
                />
                <p className="text-[11.42px] text-secondary-200 ml-[9px]">
                  Select All
                </p>
              </div>
              <div>
                <button
                  className="text-[11.42px] text-secondary-200 cursor-pointer"
                  onClick={handleClearAll}
                >
                  Clear all
                </button>
              </div>
            </div>
            {filterOptions?.map((option: any) => (
              <button
                key={option?.id}
                onClick={() => handleSelectFilter(option.value)}
                className="w-full py-[7.5px] text-xs flex justify-start items-start"
              >
                {/* @ts-expect-error */}
                <Radio
                  name={option?.value}
                  id={option?.value}
                  // @ts-expect-error
                  checked={selectedFilters[option.value] || false}
                  ripple={false}
                  icon={<CheckedRadioIcon />}
                  className="hover:before:opacity-0 w-4 h-4 !bg-transparent "
                  containerProps={{
                    className: 'p-0',
                  }}
                />
                <span className="text-white ml-[9px]">{option?.name}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterDropdown;
