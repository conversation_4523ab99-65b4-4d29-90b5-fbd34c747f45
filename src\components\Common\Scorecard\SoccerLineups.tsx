'use client';
import React, { useState } from 'react';
import SoccerLineupCard from './SoccerLineupCard';
import SoccerScoreboardHeader from '../Commentary/SoccerScoreboardHeader';
import { ChevronUp, ChevronDown, ChevronRight } from 'lucide-react';
import { useCompetition } from '@/helpers/context/competitionContext';
import Goals from '@/components/UI/Icons/goals';
import { generateUniqueId } from '@/lib/utils';
import useScreen from '@/hooks/useScreen';

interface SoccerPlayer {
  name: string;
  position: string;
  number: number;
  playerId: number;
}

interface TeamPanelProps {
  teamName: string;
  players: any[];
  isExpanded: boolean;
  expandedPlayers: number[];
  onToggleTeam: () => void;
  onTogglePlayer: (playerId: number) => void;
}

const SoccerplayersRollName = (data: any) => {
  let title;
  switch (data?.role) {
    case 'G':
      title = 'GKP';
      break;
    case 'D':
      title = 'DEF';
      break;
    case 'M':
      title = 'MID';
      break;
    case 'F':
      title = 'FWD';
      break;
  }
  return title;
};

const TeamPanel: React.FC<TeamPanelProps> = ({
  teamName,
  players,
  isExpanded,
  expandedPlayers,
  onToggleTeam,
  onTogglePlayer,
}) => {
  return (
    <div className="w-full rounded-[8px] overflow-hidden shadow-[0px_1px_9px_0px_#0000002e]">
      <div
        className=" text-white pt-1.5 pb-[9px] px-[18px] flex justify-between items-center cursor-pointer"
        style={{ background: 'linear-gradient(to right, #4455C7, #003764)' }}
        onClick={onToggleTeam}
      >
        <h2 className="text-[22.4px] leading-[31.36px] font-veneerCleanSoft font-normal ">
          {teamName}
        </h2>
        {isExpanded ? (
          <ChevronUp className="h-5 w-5" />
        ) : (
          <ChevronDown className="h-5 w-5" />
        )}
      </div>
      {isExpanded && (
        <div>
          <div className="bg-[#E3E4E5] px-4 py-2 flex">
            <div className="w-8 text-[11.42px] leading-[15px] font-inter font-normal text-black-100">
              #
            </div>
            <div className="flex-1 text-[11.42px] leading-[15px] font-inter font-normal text-black-100">
              Player
            </div>
          </div>
          <div>
            {players?.map((player: any, index: any) => (
              <div key={player.id}>
                <div
                  className={`px-4 py-2 flex items-center cursor-pointer ${
                    index % 2 === 0 ? 'bg-[#FDFDFD]' : 'bg-[#F7F7F8]'
                  }`}
                  onClick={() => onTogglePlayer(player.playerId)}
                >
                  <div className="mr-2 text-[14px] leading-[19px] font-inter font-normal text-black-100">
                    {index + 1}
                  </div>
                  <div className="flex-1 flex items-center">
                    <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                      {player.player?.name}, {SoccerplayersRollName(player)}
                    </span>
                    {player.goals > 0 && (
                      <span className="ml-auto mr-[15%] text-black-100 flex items-center">
                        <Goals />
                        <span className="mr-1 text-[14px] leading-[19px] font-inter font-normal text-black-100">
                          {player.goals}
                        </span>
                      </span>
                    )}
                  </div>
                  <div className="ml-2">
                    {expandedPlayers.includes(player?.playerId) ? (
                      <ChevronDown className="h-5 w-5 text-gray-400" />
                    ) : (
                      <ChevronRight className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>
                {expandedPlayers.includes(player?.playerId) && (
                  <div className=" px-8 space-y-1">
                    <div className="flex justify-between py-1.5">
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        Goals
                      </span>
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        {player.goals}
                      </span>
                    </div>
                    <div className="flex justify-between py-1.5">
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        Shots
                      </span>
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        {player.shots}
                      </span>
                    </div>
                    <div className="flex justify-between py-1.5">
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        Shots on Target
                      </span>
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        {player.shotsOnGoal}
                      </span>
                    </div>
                    <div className="flex justify-between py-1.5">
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        Assists
                      </span>
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        {player.assists}
                      </span>
                    </div>
                    <div className="flex justify-between py-1.5">
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        Offsides
                      </span>
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        {player.offsides}
                      </span>
                    </div>
                    <div className="flex justify-between py-1.5">
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        Fouls Committed
                      </span>
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        {player.foulsCommited}
                      </span>
                    </div>
                    <div className="flex justify-between py-1.5">
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        Fouls Against
                      </span>
                      <span className="text-[14px] leading-[19px] font-inter font-normal text-black-100">
                        {player.foulsDrawn}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

interface SoccerLineupsProps {
  homeTeam: any;
  awayTeam: any;
  onPlayerClick?: (playerId: number) => void;
}

const SoccerLineups: React.FC<SoccerLineupsProps> = ({
  homeTeam,
  awayTeam,
  onPlayerClick,
}) => {
  const { eventDetailsResponse } = useCompetition();
  const homeTeamName =
    eventDetailsResponse?.result?.eventDetails?.homeTeam?.name;
  const awayTeamName =
    eventDetailsResponse?.result?.eventDetails?.awayTeam?.name;
  const [homeTeamData, setHomeTeamData] = useState<number[]>([]);
  const [awayTeamData, setAwayTeamData] = useState<number[]>([]);
  const [isHomeTeamExpanded, setIsHomeTeamExpanded] = useState(true);
  const [isAwayTeamExpanded, setIsAwayTeamExpanded] = useState(true);
  const [activeTab, setActiveTab] = useState<any>(homeTeamName);

  const toggleExpand = (team: any, playerId: number) => {
    if (team === homeTeamName) {
      if (homeTeamData.includes(playerId)) {
        setHomeTeamData([]);
      } else {
        setHomeTeamData([playerId]);
      }
    } else {
      if (awayTeamData.includes(playerId)) {
        setAwayTeamData([]);
      } else {
        setAwayTeamData([playerId]);
      }
    }
  };

  const toggleTeamExpand = (team: string | undefined) => {
    if (!team) return;
    if (team === homeTeamName) {
      setIsHomeTeamExpanded(!isHomeTeamExpanded);
    } else {
      setIsAwayTeamExpanded(!isAwayTeamExpanded);
    }
  };

  const tabs = [homeTeamName, awayTeamName];

  const handleTabClick = (tab: any) => {
    setActiveTab(tab);
  };

  const { width } = useScreen();

  return (
    <div className="">
      <div className="hidden max-799:block">
        <div className="flex items-center gap-[3px] rounded-lg bg-black-300 p-[3px] mb-[12px] ">
          {tabs?.map((tab, index) => (
            <button
              key={generateUniqueId()}
              className={`px-[9px] py-[6px] cursor-pointer w-1/2 ${
                activeTab === tab ? 'bg-primary-200 rounded-[6px]' : ''
              }`}
              onClick={() => handleTabClick(tab)}
            >
              <p
                className={`text-[16px] leading-[19px] font-inter font-medium ${
                  activeTab === tab ? 'text-white' : 'text-primary-200'
                }`}
              >
                {tab}
              </p>
            </button>
          ))}
        </div>
      </div>
      {width > 799 ? (
        <div className="px-4 flex items-baseline justify-between gap-x-8">
          {homeTeamName && (
            <TeamPanel
              teamName={homeTeamName}
              players={homeTeam}
              isExpanded={isHomeTeamExpanded}
              expandedPlayers={homeTeamData}
              onToggleTeam={() => toggleTeamExpand(homeTeamName)}
              onTogglePlayer={(playerId) =>
                toggleExpand(homeTeamName, playerId)
              }
            />
          )}

          {awayTeamName && (
            <TeamPanel
              teamName={awayTeamName}
              players={awayTeam}
              isExpanded={isAwayTeamExpanded}
              expandedPlayers={awayTeamData}
              onToggleTeam={() => toggleTeamExpand(awayTeamName)}
              onTogglePlayer={(playerId) =>
                toggleExpand(awayTeamName, playerId)
              }
            />
          )}
        </div>
      ) : (
        <>
          {activeTab === homeTeamName && (
            <TeamPanel
              teamName={homeTeamName!}
              players={homeTeam}
              isExpanded={isHomeTeamExpanded}
              expandedPlayers={homeTeamData}
              onToggleTeam={() => toggleTeamExpand(homeTeamName)}
              onTogglePlayer={(playerId) =>
                toggleExpand(homeTeamName, playerId)
              }
            />
          )}

          {activeTab === awayTeamName && (
            <TeamPanel
              teamName={awayTeamName!}
              players={awayTeam}
              isExpanded={isAwayTeamExpanded}
              expandedPlayers={awayTeamData}
              onToggleTeam={() => toggleTeamExpand(awayTeamName)}
              onTogglePlayer={(playerId) =>
                toggleExpand(awayTeamName, playerId)
              }
            />
          )}
        </>
      )}
    </div>
  );
};

export default SoccerLineups;
