'use client';

import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Collapse } from '@material-tailwind/react';
import CategoryCheckbox from './CategoryCheckbox';

interface CustomisePanelProps {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  selected: Record<string, boolean>;
  setSelected: Dispatch<SetStateAction<Record<string, boolean>>>;
  categories: {
    id: string;
    name: string;
    children: { id: string; name: string }[];
  }[];
}

export default function CustomisePanel({
  isOpen,
  setIsOpen,
  selected,
  setSelected,
  categories,
}: CustomisePanelProps) {
  const [indeterminate, setIndeterminate] = useState<Record<string, boolean>>(
    {},
  );

  // Initialize indeterminate state
  useEffect(() => {
    updateIndeterminateState(selected);
  }, [selected]);

  // Function to update indeterminate state based on selected checkboxes
  const updateIndeterminateState = (selectedState: Record<string, boolean>) => {
    const newIndeterminate: Record<string, boolean> = {};

    categories.forEach((category) => {
      const selectedChildrenCount = category.children.filter(
        (child) => selectedState[`${child.id}`],
      ).length;

      if (
        selectedChildrenCount > 0 &&
        selectedChildrenCount < category.children.length
      ) {
        newIndeterminate[category.id] = true;
      } else {
        newIndeterminate[category.id] = false;
      }
    });

    setIndeterminate(newIndeterminate);
  };

  // Handle parent category checkbox change
  const handleParentChange = (categoryId: string, checked: boolean) => {
    const newSelected = { ...selected };

    // Update the parent
    newSelected[categoryId] = checked;

    // Find the category
    const category = categories.find((c) => c.id === categoryId);
    if (category) {
      // Update all children to match parent
      category.children.forEach((child) => {
        newSelected[`${child.id}`] = checked;
      });
    }

    setSelected(newSelected);
  };

  // Handle child category checkbox change
  const handleChildChange = (
    categoryId: string,
    childId: string,
    checked: boolean,
  ) => {
    const newSelected = { ...selected };

    // Update the child
    newSelected[`${childId}`] = checked;

    // Find the category
    const category = categories.find((c) => c.id === categoryId);
    if (category) {
      // Count selected children
      const selectedChildrenCount = category.children.filter(
        (child) => newSelected[`${child.id}`],
      ).length;

      // Update parent based on children state
      if (selectedChildrenCount === 0) {
        newSelected[categoryId] = false;
      } else if (selectedChildrenCount === category.children.length) {
        newSelected[categoryId] = true;
      } else {
        newSelected[categoryId] = false;
      }
    }

    setSelected(newSelected);
  };

  return (
    <Collapse
      open={isOpen}
      className="max-w-[672px] shadow-lg border rounded border-gray-100 bg-white"
    >
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-2 w-[672px]">
        {categories.map((category) => (
          <div key={category.id} className="space-y-2">
            <CategoryCheckbox
              id={category.id}
              label={category.name}
              checked={selected[category.id] || false}
              indeterminate={indeterminate[category.id] || false}
              onChange={(checked) => handleParentChange(category.id, checked)}
              isParent={true}
            />
            <div className="pl-2 space-y-1">
              {category.children.map((child) => (
                <CategoryCheckbox
                  key={`${child.id}`}
                  id={`${child.id}`}
                  label={child.name}
                  checked={selected[`${child.id}`] || false}
                  onChange={(checked) =>
                    handleChildChange(category.id, child.id, checked)
                  }
                  isParent={false}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </Collapse>
  );
}
