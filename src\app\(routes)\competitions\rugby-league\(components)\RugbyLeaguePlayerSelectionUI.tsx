'use client';
import { I<PERSON><PERSON><PERSON>on } from '@material-tailwind/react';
import type { SortingState } from '@tanstack/react-table';
import { Search, SlidersHorizontal } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import { useRef, useState } from 'react';
import { useClickAway } from 'react-use';
import Select, { components } from 'react-select';

import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { Card } from '@/components/UI/card';
import { Input } from '@/components/UI/input';
import { useDataTable } from '@/hooks/useDataTable';
import useScreen from '@/hooks/useScreen';
import { cn, formatNumberWithCommas } from '@/lib/utils';

import DataTable from '@/components/UI/DataTabel';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import SimpleTabs from '@/components/UI/SimpleTabs';

import type { FiltersState } from '@/components/Competition/CricketPlayerFilter';
import { RugbyLeaguePlayersByRole } from '../../../../../../types/rugby-league';
import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';
import FootballPlayerFilter from '@/components/Common/Player/PlayerFilter';
import MobileFootballPlayerFilter from '@/components/Common/Player/MobilePlayerFilter';
import { ProfileStats } from '@/components/Competition/CompetitionDetailsHeader';
import { rugbyLeagueUpcomingColumn } from '@/components/UI/DataTabel/columns/createTeamColumn';
import { useCompetition } from '@/helpers/context/competitionContext';

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

type RugbyLeaguePlayerSelectionUIProps = {
  activeTab: keyof RugbyLeaguePlayersByRole;
  setActiveTab: Dispatch<SetStateAction<keyof RugbyLeaguePlayersByRole>>;
  stats: ProfileStats;
  playerByRole: RugbyLeaguePlayersByRole;
};

const RugbyLeaguePlayerSelectionUI = ({
  activeTab,
  setActiveTab,
  stats,
  playerByRole,
}: RugbyLeaguePlayerSelectionUIProps) => {
  const [showFilter, setShowFilter] = useState(false);
  const [showMobileFilter, setShowMobileFilter] = useState(false);
  const playerData: RugbyLeaguePlayersByRole = playerByRole;
  const filterRef = useRef(null);
  const { isPlayerListResponseLoading } = useCompetition();
  const {
    state: {
      playersByRole: { BAC, BR, FRF, HAL, IC },
      playerByRoleLimit: {
        BAC: BACLimit,
        BR: BRLimit,
        FRF: FRFLimit,
        HAL: HALLimit,
        IC: ICLimit,
      },
    },
    showPlayerTabel,
    setShowPlayerTabel,
  } = useRugbyLeagueContext();

  const [filters, setFilters] = useState<FiltersState>({
    matchesPlayed: false,
    priceRange: [0, 100],
    dualPosition: false,
    teams: {
      awayTeam: false,
      homeTeam: false,
    },
    breakeven: [0, 100],
    projectedScore: [0, 100],
    projectedValueChange: [6300, 9300],
    selectionPercentage: [0, 100],
  });

  interface TabItem {
    id: keyof RugbyLeaguePlayersByRole;
    name: keyof RugbyLeaguePlayersByRole;
    count: number;
    maxCount: number;
    seq: number;
  }

  const sortOptions = [
    { value: 'player_asc', label: 'Sort Players A to Z' },
    { value: 'player_desc', label: 'Sort Players Z to A' },
    { value: 'team_asc', label: 'Sort Teams A to Z' },
    { value: 'team_desc', label: 'Sort Teams Z to A' },
  ];
  const { width } = useScreen();

  const { table } = useDataTable({
    columns: rugbyLeagueUpcomingColumn,
    data: playerData[activeTab] || [],
  });

  const handleSortChange = (option: string): void => {
    const sorting: SortingState = [];
    switch (option) {
      case 'player_asc':
        sorting.push({ id: 'name', desc: false });
        break;
      case 'player_desc':
        sorting.push({ id: 'name', desc: true });
        break;
      case 'team_asc':
        sorting.push({ id: 'teamName', desc: false });
        break;
      case 'team_desc':
        sorting.push({ id: 'teamName', desc: true });
        break;
      default:
        break;
    }
    // Update sorting on the table and log it
    table.setSorting(sorting);
  };

  const sortedData = table.getSortedRowModel().rows.map((row) => row.original);

  const validTeamSelection =
    BAC?.length + BR?.length + FRF?.length + HAL?.length + IC?.length === 17;
  BAC?.length + BR?.length + FRF?.length + HAL?.length + IC?.length === 17;
  const tabData: TabItem[] = [
    { count: BAC?.length || 0, id: 'BAC', name: 'BAC', seq: 0, maxCount: BACLimit },
    { count: HAL?.length || 0, id: 'HAL', name: 'HAL', seq: 0, maxCount: HALLimit },
    { count: BR?.length || 0, id: 'BR', name: 'BR', seq: 0, maxCount: BRLimit },
    { count: FRF?.length || 0, id: 'FRF', name: 'FRF', seq: 0, maxCount: FRFLimit },
    { count: IC?.length || 0, id: 'IC', name: 'IC', seq: 0, maxCount: ICLimit },
  ];

  // useClickAway(filterRef, () => {
  //   setFilters({
  //     matchesPlayed: false,
  //     priceRange: [0, 100],
  //     dualPosition: false,
  //     teams: {
  //       homeTeam: false,
  //       awayTeam: false,
  //     },
  //     breakeven: [0, 100],
  //     projectedScore: [0, 100],
  //     projectedValueChange: [6300, 9300],
  //     selectionPercentage: [0, 100],
  //   });

  //   setShowFilter(false);
  //   table.resetColumnFilters();
  // });

  return (
    <Card className="w-full max-w-4xl mx-auto bg-white relative hidden lg:block">
      <div className="p-2 space-y-2">
        <div className="flex justify-between items-center space-x-2">
          <div className="relative w-[60%] hidden lg:block">
            <Search
              className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 "
              size={16}
            />
            <Input
              placeholder="Search (by player name)"
              className=" border-gray-100 pl-10"
              onChange={(e) => {
                table.getColumn('name')?.setFilterValue(e.target.value);
              }}
            />
          </div>
          <div className="flex items-center gap-2  w-full lg:w-fit justify-between lg:justify-start sort-container">
            <Select
              className="React desktop-odds-select"
              onChange={(e) => e?.value && handleSortChange(e?.value)}
              options={sortOptions}
              classNamePrefix="select"
              placeholder="Sort Players"
              defaultValue={sortOptions[0]}
              isSearchable={false}
              components={{ DropdownIndicator }}
            />
            <div>
              <IconButton
                {...({} as any)}
                className="bg-secondary-100 hidden lg:block"
                onClick={() => setShowFilter(!showFilter)}
              >
                <SlidersHorizontal />
              </IconButton>
              <IconButton
                {...({} as any)}
                className="bg-secondary-100 lg:hidden block"
                onClick={() => setShowMobileFilter(!showMobileFilter)}
              >
                <SlidersHorizontal />
              </IconButton>
            </div>
          </div>
        </div>

        <div className="w-full">
          <SimpleTabs
            tabs={tabData}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
          />
        </div>

        <div>
          <DataTable
            columns={rugbyLeagueUpcomingColumn}
            data={sortedData}
            stickyColumns={width > 390 ? [] : [0]}
            initialColumnVisibility={{
              name: false,
              team: false,
              teamName: false,
            }}
            isLoading={isPlayerListResponseLoading}
            maxHeight="700px"
            noDataMessage={{
              hideTitle: true,
              title: '',
              description: 'Lineup will be updated when players are confirmed.',
            }}
          />
        </div>
      </div>
      {/* <div ref={filterRef}> */}
      {showFilter && (
        <div className="absolute top-14 right-0 w-full z-40 ">
          <FootballPlayerFilter
            playerTable={table}
            filters={filters}
            setFilters={setFilters}
            setShowFilter={setShowFilter}
          />
        </div>
      )}
      {/* </div> */}

      {showMobileFilter && (
        <MobileFootballPlayerFilter
          open={showMobileFilter}
          setOpen={setShowMobileFilter}
        />
      )}

      <Drawer open={showPlayerTabel} onOpenChange={setShowPlayerTabel}>
        <DrawerContent className="bg-white mb-[70px]">
          <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md">
            SELECT PlAYERS
          </div>
          <div className="p-2 space-y-2 mt-10  h-full ">
            <div className="flex justify-between items-center space-x-2">
              <Card className="p-3 shadow-sm w-1/2 bg-dark-card-gradient text-white">
                <div className="text-center">
                  <div className="text-sm text-slate-500 mb-1">
                    Remaining salary:
                  </div>
                  <div className="font-semibold text-slate-900">
                    ${formatNumberWithCommas(stats?.remainingSalary)}
                  </div>
                </div>
              </Card>

              <Card
                className={cn(
                  'p-3 shadow-sm  text-white w-1/2',
                  validTeamSelection
                    ? 'bg-[#D1E7DF] border border-[#B4DBCD]'
                    : 'bg-dark-card-gradient',
                )}
              >
                <div className="text-center">
                  <div
                    className={cn(
                      'text-sm  mb-1',
                      validTeamSelection ? 'text-black-700' : 'text-slate-500',
                    )}
                  >
                    Selected Player:
                  </div>
                  <div
                    className={cn(
                      'font-semibold',
                      validTeamSelection ? 'text-[#1C9A6C]' : 'text-slate-900',
                    )}
                  >
                    {stats.selectedPlayer}
                  </div>
                </div>
              </Card>
            </div>
            <div className="grid grid-cols-[60%_40%] gap-x-1 place-items-center">
              <div className="relative w-full">
                <Search
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 "
                  size={16}
                />
                <Input
                  placeholder="Search (by player name)"
                  className="pl-8 border-gray-100 !py-4"
                  onChange={(e) => {
                    table.getColumn('name')?.setFilterValue(e.target.value);
                  }}
                />
              </div>
              <div className="flex items-center gap-2  w-full lg:w-fit px-1 lg:px-0 justify-end lg:justify-start sort-container">
                <Select
                  className="React desktop-odds-select"
                  onChange={(e) => e?.value && handleSortChange(e?.value)}
                  options={sortOptions}
                  classNamePrefix="select"
                  placeholder="Sort Players"
                  defaultValue={sortOptions[0]}
                  isSearchable={false}
                  components={{ DropdownIndicator }}
                />
                <div>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 hidden lg:block"
                    onClick={() => setShowFilter(!showFilter)}
                  >
                    <SlidersHorizontal />
                  </IconButton>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 lg:hidden block"
                    onClick={() => setShowMobileFilter(!showMobileFilter)}
                  >
                    <SlidersHorizontal />
                  </IconButton>
                </div>
              </div>
            </div>

            <div className="w-full">
              <SimpleTabs
                tabs={tabData}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                size="small"
              />
            </div>

            <div>
              <DataTable
                columns={rugbyLeagueUpcomingColumn}
                data={sortedData}
                stickyColumns={[0]}
                initialColumnVisibility={{ name: false, teamName: false }}
                isLoading={isPlayerListResponseLoading}
                noDataMessage={{
                  title: '',
                  hideTitle: true,
                  description: 'Lineup will be updated when players are confirmed.',
                }}
              />
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </Card>
  );
};

export default RugbyLeaguePlayerSelectionUI;
