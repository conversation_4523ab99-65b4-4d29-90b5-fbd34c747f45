'use client';

import { useCompetition } from '@/helpers/context/competitionContext';
import { formatDate } from '@/lib/utils';

interface TimeBoxProps {
  value: number;
  label: string;
}

function TimeBox({ value, label }: TimeBoxProps) {
  const displayValue = value.toString().padStart(2, '0');

  return (
    <div className="flex-1">
      <div className="flex flex-col text-center">
        <div className="bg-[#e6eef5] rounded-t-md p-2">
          <span className="text-[#003366] text-2xl font-bold">
            {displayValue}
          </span>
        </div>
        <div className="bg-[#003366] text-white text-[10px] font-semibold py-0.5 rounded-b-md">
          {label}
        </div>
      </div>
    </div>
  );
}

interface CountDownTimerProps {
  completed: boolean;
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

export default function CountDownTimer({
  completed,
  days,
  hours,
  minutes,
  seconds,
}: CountDownTimerProps) {
  const { eventDetailsResponse } = useCompetition();
  const startTime = eventDetailsResponse?.result?.eventDetails?.startTime;

  if (!startTime) return null;

  return (
    <div className="max-w-full mx-auto md:p-4 p-0">
      <div className="text-sm font-medium mb-2 whitespace-nowrap">
        Starts in: {formatDate(startTime)}
      </div>
      <div className="flex gap-2">
        <TimeBox value={days} label="DAYS" />
        <TimeBox value={hours} label="HOURS" />
        <TimeBox value={minutes} label="MINS" />
        <TimeBox value={seconds} label="SECS" />
      </div>
    </div>
  );
}
