import React from 'react';

type MySvgLabelProps = {
  text: string;
  variant: 'Bronze' | 'Silver' | 'Gold' | 'Diamond' | 'Platinum';
};

const LabelButton = ({ text, variant }: MySvgLabelProps) => {
  const gradients = {
    Bronze: (
      <linearGradient
        id="bronze-gradient"
        x1="0.5"
        x2="0.5"
        y2="1"
        gradientUnits="objectBoundingBox"
      >
        <stop offset="0" stopColor="#cfb698" />
        <stop offset="0.146" stopColor="#be8565" />
        <stop offset="0.272" stopColor="#b9793d" />
        <stop offset="0.682" stopColor="#ddc1ae" />
        <stop offset="1" stopColor="#f1bb8c" />
      </linearGradient>
    ),
    Gold: (
      <linearGradient
        id="gold-gradient"
        x1="0.5"
        x2="0.5"
        y2="1"
        gradientUnits="objectBoundingBox"
      >
        <stop offset="0" stopColor="#cfb698" />
        <stop offset="0.146" stopColor="#bc8129" />
        <stop offset="0.849" stopColor="#d79f6a" />
        <stop offset="0.946" stopColor="#e8b97b" />
        <stop offset="1" stopColor="#ebc17d" />
      </linearGradient>
    ),
    Silver: (
      <linearGradient
        id="silver-gradient"
        x1="0.5"
        x2="0.5"
        y2="1"
        gradientUnits="objectBoundingBox"
      >
        <stop offset="0" stopColor="#d0cbca" />
        <stop offset="0.46" stopColor="#737377" />
        <stop offset="1" stopColor="#efeff0" />
        <stop offset="1" stopColor="#e9e9ea" />
      </linearGradient>
    ),
    Diamond: (
      <linearGradient
        id="diamond-gradient"
        x1="0.5"
        x2="0.5"
        y2="1"
        gradientUnits="objectBoundingBox"
      >
        <stop offset="0" stopColor="#9bc1ff" />
        <stop offset="0.146" stopColor="#9cc3fc" />
        <stop offset="0.272" stopColor="#bbd0fe" />
        <stop offset="0.782" stopColor="#e8edff" />
        <stop offset="1" stopColor="#9bc1ff" />
      </linearGradient>
    ),
    Platinum: (
      <linearGradient
        id="platinum-gradient"
        x1="0.5"
        x2="0.5"
        y2="1"
        gradientUnits="objectBoundingBox"
      >
        <stop offset="0" stopColor="#d8dfe9" />
        <stop offset="0.146" stopColor="#abbbcc" />
        <stop offset="0.272" stopColor="#d3dbe7" />
        <stop offset="0.832" stopColor="#bac7d7" />
        <stop offset="1" stopColor="#a9bace" />
      </linearGradient>
    ),
  };

  const sizes = {
    Bronze: { width: 127, height: 30 },
    Gold: { width: 127, height: 30 },
    Silver: { width: 127, height: 30 },
    Diamond: { width: 127, height: 30 },
    Platinum: { width: 127, height: 30 },
  };

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width={sizes[variant].width}
      height={sizes[variant].height}
      viewBox={`0 0 ${sizes[variant].width} ${sizes[variant].height}`}
    >
      <defs>{gradients[variant]}</defs>
      <rect
        width={sizes[variant].width}
        height={sizes[variant].height}
        rx="15"
        fill={`url(#${variant.toLowerCase()}-gradient)`}
      />
      <text
        x="10" // Position the text closer to the left
        y="50%"
        dominantBaseline="middle"
        textAnchor="start" // Align the text to the start (left)
        fill="white"
        fontSize="14"
        className="text-left"
      >
        {text}
      </text>
    </svg>
  );
};

export default LabelButton;
