'use client';

import { useState, useEffect, useMemo } from 'react';
import { FiltersState } from '../Player/PlayerFilter';
import DataTable from '@/components/UI/DataTabel';
import { SoccerPlayerStatsColumns } from '@/components/UI/DataTabel/SoccerPlayerStatsColumn';
import { useStatsContext } from '@/helpers/context/stats';
import { StatsLegend } from '../Commentary/CricketLegend';
import { Button } from '@/components/UI/button';
import { Search, SlidersHorizontal } from 'lucide-react';
import { Input } from '@/components/UI/input';
import CustomisePanel from '../Commentary/CustomisePanel';
import CustomisePanelMobile from '../Commentary/CustomisePanelMobile';
import { VisibilityState } from '@tanstack/react-table';
import { IconButton } from '@material-tailwind/react';
import SettingsIcon from '@/components/UI/Icons/SettingsIcon';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Card } from '@/components/UI/card';
import { cn } from '@/lib/utils';
import Select, { components } from 'react-select';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { Checkbox } from '@/components/UI/checkbox';
import { useCompetition } from '@/helpers/context/competitionContext';
import { SOCCER_PLAYER_STATS_LEGEND } from '@/helpers/constants';
import { Category } from '@/types/stats';

interface SoccerPlayerStatsProps {
  columnVisibility: VisibilityState;
  onColumnVisibilityChange: (visibility: VisibilityState) => void;
  searchQuery: string;
  selectedPositions: string;
  selectedTeams: string;
}

interface CategoryChild {
  id: string;
  name: string;
}

const soccerCategories: Category[] = [
  {
    id: 'player',
    name: 'Player Info',
    children: [
      { id: 'name', name: 'Name' },
      { id: 'position', name: 'Position' },
      { id: 'team', name: 'Team' },
    ],
  },
  {
    id: 'basic',
    name: 'Basic Stats',
    children: [
      { id: 'goals', name: 'Goals' },
      { id: 'assists', name: 'Assists' },
      { id: 'minutes', name: 'Minutes' },
      { id: 'yellow_cards', name: 'Yellow Cards' },
      { id: 'red_cards', name: 'Red Cards' },
    ],
  },
  {
    id: 'attacking',
    name: 'Attacking',
    children: [
      { id: 'shots', name: 'Shots' },
      { id: 'shots_on_target', name: 'Shots on Target' },
      { id: 'passes', name: 'Passes' },
      { id: 'key_passes', name: 'Key Passes' },
      { id: 'crosses', name: 'Crosses' },
      { id: 'dribbles', name: 'Dribbles' },
      { id: 'fouls', name: 'Fouls' },
      { id: 'fouls_won', name: 'Fouls Won' },
    ],
  },
  {
    id: 'defending',
    name: 'Defending',
    children: [
      { id: 'tackles', name: 'Tackles' },
      { id: 'interceptions', name: 'Interceptions' },
      { id: 'blocks', name: 'Blocks' },
      { id: 'clearances', name: 'Clearances' },
      { id: 'headed_clearances', name: 'Headed Clearances' },
    ],
  },
  {
    id: 'goalkeeping',
    name: 'Goalkeeping',
    children: [
      { id: 'saves', name: 'Saves' },
      { id: 'clean_sheets', name: 'Clean Sheets' },
      { id: 'goals_conceded', name: 'Goals Conceded' },
      { id: 'penalties_saved', name: 'Penalties Saved' },
      { id: 'penalties_conceded', name: 'Penalties Conceded' },
    ],
  },
];

const positionOptions = [
  {
    value: 'all',
    label: 'All Positions',
  },
  {
    value: 'G',
    label: 'Goalkeeper',
  },
  {
    value: 'D',
    label: 'Defender',
  },
  {
    value: 'M',
    label: 'Midfielder',
  },
  {
    value: 'F',
    label: 'Forward',
  },
];

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const SoccerPlayerStats = ({
  columnVisibility,
  onColumnVisibilityChange,
  searchQuery,
  selectedPositions,
  selectedTeams,
}: SoccerPlayerStatsProps) => {
  const [showFilter, setShowFilter] = useState(false);
  const [showTableCustomization, setShowTableCustomization] = useState(false);
  const [showTableCustomizationMobile, setShowTableCustomizationMobile] =
    useState(false);
  const [showMobileFilter, setShowMobileFilter] = useState(false);
  const [showLegends, setShowLegends] = useState(false);

  const initializeSelectedState = () => {
    const state: Record<string, boolean> = {};

    soccerCategories.forEach((category) => {
      state[category.id] = true;
      category.children.forEach((child) => {
        state[child.id] = true;
      });
    });

    return state;
  };

  const [selected, setSelected] = useState<Record<string, boolean>>(
    initializeSelectedState(),
  );

  useEffect(() => {
    onColumnVisibilityChange(selected);
  }, [selected, onColumnVisibilityChange]);

  const { soccerPlayerStats } = useStatsContext();
  const { eventDetailsResponse } = useCompetition();
  const homeTeam = eventDetailsResponse?.result?.eventDetails?.homeTeam;
  const awayTeam = eventDetailsResponse?.result?.eventDetails?.awayTeam;

  const filteredData = useMemo(() => {
    if (!soccerPlayerStats?.result?.rows) {
      return [];
    }

    let data = soccerPlayerStats.result.rows;

    // Apply search filter
    if (searchQuery.trim()) {
      data = data.filter((player) =>
        player.player.name.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    // Apply position filter
    if (selectedPositions.length > 0) {
      data = data.filter((player) =>
        selectedPositions.includes(player.position),
      );
    }

    // Apply team filter
    if (selectedTeams !== 'all') {
      data = data.filter((player) => selectedTeams.includes(player.team?.name));
    }

    return data;
  }, [
    searchQuery,
    selectedPositions,
    selectedTeams,
    soccerPlayerStats?.result?.rows,
  ]);

  if (filteredData.length === 0) {
    return (
      <div className="flex justify-center items-start h-screen">
        <p className="text-lg text-gray-600 font-bold mt-10">No data found</p>
      </div>
    );
  }

  return (
    <div className="relative">
      <DataTable
        columns={SoccerPlayerStatsColumns}
        data={filteredData}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={onColumnVisibilityChange}
      />

      <div className="mt-4">
        <StatsLegend stats={SOCCER_PLAYER_STATS_LEGEND} />
      </div>
    </div>
  );
};

export default SoccerPlayerStats;
