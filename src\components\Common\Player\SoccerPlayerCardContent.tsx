import { SoccerPlayer } from '@/helpers/context/soccer/createSoccerTeamContext';

interface SoccerPlayerCardContentProps {
  player: SoccerPlayer;
  isPlayerLocked: boolean;
  playerCurrentSalary: number;
  playerLastSalary: number;
}

const SoccerPlayerCardContent = ({
  player,
  isPlayerLocked,
  playerCurrentSalary,
  playerLastSalary,
}: SoccerPlayerCardContentProps) => {
  const salaryDiff = playerCurrentSalary - playerLastSalary;
  const diffPercentage = (salaryDiff / playerLastSalary) * 100;

  return (
    <div className="w-[165px] h-full bg-white rounded-lg shadow-md flex flex-col relative">
      <div className="flex flex-col items-left mt-5 pl-2">
        <p className="font-medium text-left text-sm truncate max-w-[130px]">
          {player.name}
        </p>
        <p className="text-xs text-left text-gray-600 truncate max-w-[130px]">
          {player.teamName}
        </p>

        <div className="flex items-end justify-between pr-1">
          <div className="text-xs text-gray-600">{player.role}</div>
          <div>
            <p className="text-xs text-gray-600">
              ${playerCurrentSalary?.toLocaleString() || 0}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SoccerPlayerCardContent;
