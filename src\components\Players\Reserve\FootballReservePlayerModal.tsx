import { Button } from '@/components/UI/button';
import { Card } from '@/components/UI/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/UI/dialog';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import { MoveRight, PlusIcon } from 'lucide-react';
import { ReactNode } from 'react';
import { cn, formatNumberWithCommas, LocalStorage } from '@/lib/utils';
import { useSearchParams } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createAFLTeam } from '@/helpers/fetchers/competitions';
import { setApiMessage } from '@/helpers/commonFunctions';
import { quyerKeys } from '@/lib/queryKeys';
import { useCompetition } from '@/helpers/context/competitionContext';
import useScreen from '@/hooks/useScreen';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Token } from '../../../../db/db';
import { useAuthContext } from '@/helpers/context/authContext';
import { FootballPlayer } from '../../../../types/competitions';
import { getDefaultProfileImage } from '../../../../db/db';
import PlayerAvatar from '@/components/UI/PlayerAvatar/indext';
import { AnimatePresence, motion } from 'motion/react';
import FootballPlayerCard from '@/components/Common/Player/FootballPlayerCard';
import { footballPlayersByRole } from '../../../../types/football';
import Image from 'next/image';
import { PUBLIC_IMAGES } from '@/lib/constants/constants';
import src from 'react-select';

const generateReserveOrder = (orderLength: number) => {
  return Array.from({ length: orderLength }, (_, index) => ({
    name: `Reserve ${index + 1}`,
  }));
};

const ReserveOrder = ({ orderLength }: { orderLength: number }) => {
  const orders = generateReserveOrder(orderLength);
  return (
    <>
      {orders.map((order, index) => (
        <div key={index} className="flex space-x-2">
          <span>{order.name}</span>
          {index !== orderLength - 1 && (
            <span>
              <MoveRight />
            </span>
          )}
        </div>
      ))}
    </>
  );
};

// Football-specific EmptyPlayerCard component for reserve players
const FootballEmptyPlayerCard = ({
  isActive,
  playerIndex,
  setActivePlayerPosition,
  setOpenReserveModal
}: {
  isActive: boolean;
  playerIndex: number;
  setActivePlayerPosition: (position: number) => void;
  setOpenReserveModal: (open: boolean) => void;
}) => {
  return (
    <AnimatePresence>
      <motion.div
        key="modal"
        initial={{ opacity: 1, scale: 1 }}
        animate={{ opacity: 1, scale: 1 }}
      >
        <Card className="p-4 bg-white shadow-sm border border-gray-100 rounded-lg md:w-40 w-[150px] relative h-20">
          <div className="flex justify-center items-centershadow-md rounded-full absolute top-[-15px] left-1/2 transform -translate-x-1/2">
            <Image
              src={PUBLIC_IMAGES.DEFAULT_PLAYER_IMAGE}
              alt="default player image"

            />
          </div>
          <div className=" rounded-md absolute bottom-[5px] left-1/2 transform -translate-x-1/2 p-2 py-1">
            <button
              className="flex justify-center items-center space-x-1 mt-2"
              onClick={() => {
                setOpenReserveModal(true);
              }}
            >
              <div className={cn(
                'text-white rounded-md',
                isActive ? 'bg-[#1C9A6C]' : 'bg-[#C9C9C9]',
              )}>
                <PlusIcon size={25} />
              </div>
            </button>
          </div>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};

const FootballReservePlayerModal = ({ children, setTeamSubmitConfirmation }: { children: ReactNode, setTeamSubmitConfirmation: (value: boolean) => void }) => {
  const { setLoginPopUp } = useAuthContext();

  const {
    state: { reserveState: { reservePlayers = [], reservePlayersLimit = 3 }, remainingBudget, createDreamTeamPayload },
    openReserveModal,
    setOpenReserveModal,
    activePlayerPosition,
    setActivePlayerPosition,
    createReservePlayerPayload,
    createDreamTeam,
    removeReservePlayer,
  } = useFootballContext();

  const { eventDetailsResponse, refetchDreamTeam } = useCompetition();

  const searchParams = useSearchParams();
  const dreamTeamId = searchParams.get('dreamTeamId');
  const event_id = searchParams.get('event_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const add_more = searchParams.get('add_more');
  const { width } = useScreen()

  const queryClient = useQueryClient()

  const { mutate: mutateCreateTeam, isPending: isCreatingTeamPending } =
    useMutation({
      mutationFn: () => {
        if (createDreamTeamPayload) {
          return createAFLTeam(createDreamTeamPayload, dreamTeamId);
        }
        return Promise.resolve();
      },
      onSuccess: (data: { status: boolean; message: string }) => {
        setApiMessage('success', data.message);
        LocalStorage.removeItem('dream_team');
        LocalStorage.removeItem('rugby_league_dream_team');
        LocalStorage.removeItem('afl_dream_team');
        LocalStorage.removeItem('afl_reserve_players');
        LocalStorage.removeItem('redirect');
        queryClient.invalidateQueries({
          queryKey: [
            quyerKeys.getFantasyUser,
            quyerKeys.getAllCompetitons,
            quyerKeys.getCompetiton,
          ],
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getFantasyUser],
          exact: true,
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getAllCompetitons],
        });

        queryClient.refetchQueries({
          queryKey: [quyerKeys.getCompetiton],
          exact: true,
        });
        setTeamSubmitConfirmation(false);
        refetchDreamTeam();
      },
      onError: (data: any) => {
        if (data?.message) {
          setApiMessage('error', data?.message);
        } else {
          setApiMessage('error', data?.response?.data?.message);
        }
      },
    });

  const handleSaveReserves = () => {
    if (!Token) {
      setOpenReserveModal(false)
      setLoginPopUp(true);
      return;
    }

    const reservePlayerPayload = reservePlayers.filter((player) => player !== null).map((player, index) => ({
      playerId: player?.playerId ?? 0,
      playerValue: player?.scoreData?.playerCurrentSalary ?? 0,
      reserve: true,
      reserveRank: index + 1,
    }));
    createReservePlayerPayload(reservePlayerPayload);
    const teamName = `Team ${Array.isArray(eventDetailsResponse?.result?.dreamTeams)
      ? eventDetailsResponse.result.dreamTeams.length + 1
      : 1
      }`;

    createDreamTeam({
      eventId: event_id,
      eventName: eventDetailsResponse?.result?.eventDetails?.eventName!,
      sportId: '9',
      tournamentId: tournament_id,
      competitionId: competition_id,
      name: teamName,
    });
    setOpenReserveModal(false);
    if (!dreamTeamId) {
      setTeamSubmitConfirmation(true);
    }
    if (dreamTeamId) {
      mutateCreateTeam();
    }
  }

  const handleNotNow = () => {

    if (!Token) {
      setOpenReserveModal(false)
      setLoginPopUp(true);
      return;
    }

    if (add_more === 'true') {
      setOpenReserveModal(false)
      setTeamSubmitConfirmation(true)
      localStorage.removeItem('football_reserve_players');
    } else {
      setOpenReserveModal(false)
      localStorage.removeItem('football_reserve_players');
    }
  }

  const isMobile = width < 768;

  if (isMobile) {
    return (
      <>
        <Drawer open={openReserveModal} onOpenChange={setOpenReserveModal}>
          <DrawerContent className='bg-white'>
            <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md">
              ADD RESERVES
            </div>

            <div className="p-2 space-y-2 mt-10  h-full ">
              <div className="grid grid-cols-2 gap-2 mt-2">
                <Card className="p-3 shadow-sm w-full bg-dark-card-gradient text-white">
                  <div className="text-center">
                    <div className="text-sm text-slate-500 mb-1">
                      Remaining Salary:
                    </div>
                    <div className="font-semibold text-slate-900">
                      ${formatNumberWithCommas(remainingBudget)}
                    </div>
                  </div>
                </Card>
                <Card className={cn(
                  'p-3 shadow-sm  text-white w-full',
                  reservePlayers?.filter((player) => player !== null).length === reservePlayersLimit
                    ? 'bg-[#D1E7DF] border border-[#B4DBCD]'
                    : 'bg-dark-card-gradient',
                )}>
                  <div className="text-center">
                    <div className={cn(
                      'text-sm mb-1',
                      reservePlayers?.filter((player) => player !== null).length === reservePlayersLimit
                        ? 'text-black-700'
                        : 'text-slate-500',
                    )}>
                      Selected reserves:
                    </div>
                    <div className={cn(
                      'font-semibold',
                      reservePlayers?.filter((player) => player !== null).length === reservePlayersLimit
                        ? 'text-green-700'
                        : 'text-slate-500',
                    )}>
                      {reservePlayers?.filter((player) => player !== null).length || 0}/{reservePlayersLimit}
                    </div>
                  </div>
                </Card>
              </div>
              <div className="grid md:grid-cols-4 grid-cols-2 gap-4 w-full mt-5">
                {reservePlayers?.map((player, index) => (
                  <div key={index} className="flex flex-col gap-2 items-center mt-4">
                    {player ? (
                      <FootballPlayerCard
                        key={player.id}
                        player={player}
                        activePlayerTab="BL"
                        setActivePlayerTab={() => { }}
                        setShowPlayerTabel={() => { }}
                        isActive={false}
                        isPlayerLocked={false}
                        isReserveType={true}
                        playerIndex={index}
                      />
                    ) : (
                      <FootballEmptyPlayerCard
                        isActive={index === activePlayerPosition}
                        playerIndex={index}
                        setActivePlayerPosition={setActivePlayerPosition}
                        setOpenReserveModal={setOpenReserveModal}
                      />
                    )}
                    <div className="text-center text-sm text-slate-500">
                      Reserve {index + 1}
                    </div>
                  </div>
                ))}

              </div>
              <div className='h-[200px] overflow-y-auto'>
                {children}
              </div>
              <div className='flex gap-2 mt-5 pb-4'>
                <Button type="submit" variant={'outline'} className="w-full" onClick={handleNotNow}>
                  Not now
                </Button>
                <Button type="submit" className="w-full" onClick={handleSaveReserves}>
                  Save reserves
                </Button>
              </div>
            </div>
          </DrawerContent>
        </Drawer>
      </>
    )
  }

  return (
    <div className={cn(
      'fixed inset-0 flex items-center justify-center z-50 bg-black-100 bg-opacity-50',
      openReserveModal ? 'block' : 'hidden'
    )}>
      <Dialog open={openReserveModal} onOpenChange={setOpenReserveModal}>
        <DialogContent className="max-w-[804px] border-none">
          <DialogHeader>
            <DialogTitle className="text-[43.9px] !font-normal font-apotekCompRegular px-4 bg-gray-100">
              ADD RESERVES
            </DialogTitle>
            <DialogDescription className="px-4 text-[16px]">
              <p>
                Add reserve players to fill in for any unannounced or substitute
                players of your team.
              </p>
              <div className="flex">
                <p> Reserve priority order:</p>
                <div className="grid grid-cols-3 gap-2 px-2 mb-5">
                  <ReserveOrder orderLength={reservePlayersLimit} />
                </div>
              </div>
              <div className="grid md:grid-cols-3 grid-cols-2 gap-4 w-full">
                {reservePlayers?.map((player, index) => {
                  return (
                    <div key={index} className="flex flex-col gap-2 items-center">
                      {player ? (
                        <FootballPlayerCard
                          player={player}
                          activePlayerTab="BL"
                          setActivePlayerTab={() => { }}
                          setShowPlayerTabel={() => { }}
                          isActive={false}
                          isPlayerLocked={false}
                          isReserveType={true}
                          playerIndex={index}
                        />
                      ) : (
                        <FootballEmptyPlayerCard
                          isActive={index === activePlayerPosition}
                          playerIndex={index}
                          setActivePlayerPosition={setActivePlayerPosition}
                          setOpenReserveModal={setOpenReserveModal}
                        />
                      )}
                      <div className="text-center text-sm text-slate-500">
                        Reserve {index + 1}
                      </div>
                    </div>
                  )
                })}
              </div>

              <div className="grid grid-cols-2 gap-2 mt-2">
                <Card className="p-3 shadow-sm w-full bg-dark-card-gradient text-white">
                  <div className="text-center">
                    <div className="text-xs text-[#BFCCD8] mb-1">
                      Remaining Salary:
                    </div>
                    <div className="font-semibold text-slate-900">
                      ${formatNumberWithCommas(remainingBudget)}
                    </div>
                  </div>
                </Card>
                <Card className={cn(
                  'p-3 shadow-sm  text-white w-full',
                  reservePlayers?.filter((player) => player !== null).length === reservePlayersLimit
                    ? 'bg-[#D1E7DF] border border-[#B4DBCD]'
                    : 'bg-dark-card-gradient',
                )}>
                  <div className="text-center">
                    <div className={cn(
                      'text-xs text-[#BFCCD8] mb-1',
                      reservePlayers?.filter((player) => player !== null).length === reservePlayersLimit
                        ? 'text-black-700'
                        : 'text-slate-500',
                    )}>
                      Selected reserves:
                    </div>
                    <div className={cn(
                      'font-semibold',
                      reservePlayers?.filter((player) => player !== null).length === reservePlayersLimit
                        ? 'text-green-700'
                        : 'text-slate-500',
                    )}>
                      {reservePlayers?.filter((player) => player !== null).length || 0}/{reservePlayersLimit}
                    </div>
                  </div>
                </Card>
              </div>
            </DialogDescription>
          </DialogHeader>
          <div className="px-4 h-[250px] overflow-y-auto">{children}</div>
          <DialogFooter className="p-4 grid grid-cols-2 place-items-center">
            <Button type="submit" variant={'outline'} className="w-full" onClick={handleNotNow}>
              Not now
            </Button>
            <Button type="submit" className="w-full" onClick={handleSaveReserves}>
              Save reserves
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FootballReservePlayerModal; 