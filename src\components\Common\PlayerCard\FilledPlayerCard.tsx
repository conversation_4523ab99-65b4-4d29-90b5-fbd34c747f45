'use client';
import { Card } from '@/components/UI/card';
import { AnimatePresence, motion } from 'framer-motion';
import { PUBLIC_IMAGES } from '@/lib/constants/constants';
import Image from 'next/image';

import { BasePlayer } from '@/lib/types';
import { EventStatus } from '.';
import PlayerLockIcon from '@/components/UI/Icons/PlayerLockIcon';
import { Config } from '@/helpers/context/config';
import { useState } from 'react';
import PlayerValueChange from '@/components/Competition/PlayerValueChange';
import { formatNumberWithCommas } from '@/lib/utils';

type FilledPlayerCardProps = {
  player: BasePlayer;
  eventStatus: EventStatus;
};

const FilledPlayerCard = ({ player, eventStatus }: FilledPlayerCardProps) => {
  const isLive = eventStatus === 'live';

  const isCaptain = player.isCaptain;
  const isViceCaptain = player.isViceCaptain;
  const playerImageSrc = Config.mediaURL! + player.image;

  return (
    <AnimatePresence>
      <motion.div
        key="empty-player-card"
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
      >
        <div className="flex md:w-[165px] w-[120px] justify-center mb-[-15px] z-10 relative">
          <Image
            src={
              player.image ? playerImageSrc : PUBLIC_IMAGES.DEFAULT_PLAYER_IMAGE
            }
            alt="default player image"
            width={36}
            height={36}
          />
        </div>
        <Card className="p-4 bg-white shadow-sm border border-gray-100 rounded-md md:w-[165px] w-[120px] relative min-h-[90px]">
          <div className="md:text-xs text-[12px] absolute top-1 right-1 left-1 flex flex-col items-end">
            <div className="flex justify-between items-center w-full  border-gray-100 rounded-md">
              <span>
                {isLive ? (
                  <PlayerLockIcon />
                ) : (
                  (player?.scoreData?.avg?.toFixed(2) ?? 0)
                )}
              </span>
              <span>{player.scoreData.lastScore}</span>
            </div>
            <div className="flex justify-between items-start w-full  border-gray-100 rounded-md">
              <div className="flex flex-col items-start">
                <span className="font-medium text-left text-sm truncate md:max-w-[130px] max-w-[60px]">
                  {player.name}
                </span>
                <span className="text-xs text-left text-gray-600 truncate md:max-w-[130px] max-w-[60px]">
                  {player.teamName}
                </span>
              </div>

              {isCaptain && (
                <button className="w-5 h-5 bg-[#FC4714] flex flex-col justify-center items-center text-white rounded-full">
                  C
                </button>
              )}
              {isViceCaptain && (
                <button className="w-5 h-5 bg-[#003764] flex flex-col justify-center items-center text-white rounded-full">
                  VC
                </button>
              )}
            </div>
            <div className="flex justify-between items-center w-full  border-gray-100 rounded-md">
              <span>{player.role}</span>
              {!isLive && (
                <PlayerValueChange
                  playerCurrentSalary={player.scoreData.playerCurrentSalary}
                  playerLastSalary={player.scoreData.playerLastSalary}
                  formatToCustomStyle={formatNumberWithCommas}
                />
              )}
            </div>
          </div>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};

export default FilledPlayerCard;
