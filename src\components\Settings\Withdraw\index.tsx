'use client';
import './identityVerification.scss';

import { yupResolver } from '@hookform/resolvers/yup';
import { Input } from '@material-tailwind/react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { ChevronLeft, TriangleAlert } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as Yup from 'yup';

import CoinsAvailable from '@/assets/images/settings/coinsAvailable.png';
import Withdrawal from '@/assets/images/settings/withdrawal.png';
import { VerifiedIcon, VerifyInProgressIcon } from '@/components/images';
import { Button } from '@/components/UI/button';
import CustomDialog from '@/components/UI/CustomDialog';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { setApiMessage } from '@/helpers/commonFunctions';
import { URLS } from '@/helpers/constants/urls';
import { Config } from '@/helpers/context/config';
import { useFantasyUser } from '@/helpers/context/fantasyUserContext';
import { usePlanContext } from '@/helpers/context/userPlanContext';
import { quyerKeys } from '@/lib/queryKeys';

import type { FetchBankDetailsResponse } from '../../../../types';
import IdentityVerification from './IdentityVerification';
import { cn } from '@/lib/utils';
import WithdrawHeader from './WithdrawHeader';
import BankDetailsForm from './BankDetailsForm';
import WithdrawForm from './WithdrawForm';
import { Token } from '../../../../db/db';

const fetchBankDetails = async (): Promise<FetchBankDetailsResponse> => {
  const res = await axiosInstance.get<FetchBankDetailsResponse>(
    Config.fantasyURL + `/withdraw/get-bank/`,
  );
  return res.data;
};

type withdrawData = {
  accountName: string;
  bsb: string;
  accountNumber: string;
  bankName: string;
};

type withdrawAmountData = {
  enterPoint: number;
};

interface SetActiveTabData {
  setActiveTab: Dispatch<SetStateAction<number>>;
}

const WithdrawPage = ({ setActiveTab }: SetActiveTabData) => {
  const [isVerification, setIsVerification] = useState<boolean>(false);
  const [isProgress, setIsProgress] = useState<boolean>(false);
  const [isWithdraw, setIsWithdraw] = useState<boolean>(false);
  const [identityVerification, setIdentityVerification] =
    useState<boolean>(false);
  const [bankDetailsLoading, setBankDetailsLoading] = useState<boolean>(false);
  const [withDrawLoading, setWithDrawLoading] = useState<boolean>(false);
  const [verificationStatus, setVerificationStatus] = useState('');
  const [loading, setLoading] = useState(true);
  const [verificationDetails, setVerificationDetails] = useState('');
  const { user: fantasyUser } = useFantasyUser();

  const { deleteBank, isDeleteBankSuccess } = usePlanContext();

  useEffect(() => {
    if (isDeleteBankSuccess) {
      setIsWithdraw(false);
      reset({
        accountName: undefined,
        accountNumber: undefined,
        bankName: undefined,
        bsb: undefined,
      });
    }
  }, [isDeleteBankSuccess]);

  type withdrawAmountData = {
    enterPoint: number;
  };
  type verificationRequiredData = {
    enterCode: string;
  };

  const { data: bankDetailsData } = useQuery({
    queryFn: fetchBankDetails,
    queryKey: [quyerKeys.getAllBankDetails],
  });

  const bankDetails = bankDetailsData?.result;

  const withdrawSchema = Yup.object().shape({
    accountName: Yup.string().required('Account name is required'),
    bsb: Yup.string()
      .required('BSB is required')
      .min(6, 'BSB must 6 be digits')
      .max(6, 'BSB must 6 be digits'),
    accountNumber: Yup.string()
      .required('Account Number is required')
      .min(9, 'Account number must be 9 digits')
      .max(9, 'Account number must be 9 digits'),
    bankName: Yup.string().required('Bank Number is required'),
  });

  const withdrawAmountSchema = Yup.object().shape({
    enterPoint: Yup.number()
      .transform((value, originalValue) => {
        if (originalValue === '') return undefined;
        return isNaN(value) ? undefined : value;
      })
      .required('Points is required')
      .min(1, 'Points must be greater than 0'),
  });

  const verificationRequiredSchema = Yup.object().shape({
    enterCode: Yup.string().required('enter Code is required'),
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<withdrawData>({
    resolver: yupResolver(withdrawSchema),
  });

  const {
    register: withdrawAmountRegister,
    watch: withdrawAmountWatch,
    handleSubmit: withdrawAmounthandleSubmit,
    formState: { errors: withdrawAmountErrors },
  } = useForm<withdrawAmountData>({
    resolver: yupResolver(withdrawAmountSchema),
  });

  const {
    register: verificationRegister,
    handleSubmit: verificationhandleSubmit,
    formState: { errors: verificationErrors },
  } = useForm<verificationRequiredData>({
    resolver: yupResolver(verificationRequiredSchema),
  });

  const queryClient = useQueryClient();

  const onSubmit = async (withdrawData: withdrawData) => {
    setBankDetailsLoading(true);

    if (!withdrawData) return;

    // Check if withdrawData matches bankDetails
    const isSameBankDetails = () =>
      withdrawData.accountName === bankDetails?.accountHolderName &&
      withdrawData.accountNumber === bankDetails?.accountNumber &&
      withdrawData.bsb === bankDetails?.bsb &&
      withdrawData.bankName === bankDetails?.bankName;

    // Refetch bank details query
    const refetchBankDetails = () =>
      queryClient.refetchQueries({
        queryKey: [quyerKeys.getAllBankDetails],
        exact: true,
      });

    // Handle API call for updating or creating bank details
    const handleBankDetailsAPI = async () => {
      const bankDetailsPayload = {
        accountHolderName: withdrawData.accountName,
        accountNumber: withdrawData.accountNumber,
        bsb: withdrawData.bsb,
        bankName: withdrawData.bankName,
      };

      const passAPI = bankDetails
        ? `/withdraw/update-bank/${bankDetails.id}`
        : `/withdraw/create-bank`;
      const method: 'put' | 'post' = bankDetails ? 'put' : 'post';

      try {
        const { status, data } = await axiosInstance[method](
          Config.fantasyURL + passAPI,
          bankDetailsPayload,
        );

        setBankDetailsLoading(false);
        setApiMessage(status === 200 ? 'success' : 'error', data?.message);

        if (status === 200) {
          refetchBankDetails();
          setIsWithdraw(true);
        }
      } catch (error) {
        setBankDetailsLoading(false);
        // @ts-expect-error
        setApiMessage('error', error?.response?.data?.message);
      }
    };

    // Main logic
    if (isSameBankDetails()) {
      setBankDetailsLoading(false);
      refetchBankDetails();
      setIsWithdraw(true);
    } else {
      await handleBankDetailsAPI();
    }
  };

  useEffect(() => {
    if (bankDetails) {
      reset({
        accountName: bankDetails?.accountHolderName,
        bsb: bankDetails?.bsb,
        accountNumber: bankDetails?.accountNumber,
        bankName: bankDetails?.bankName,
      });
    }
  }, [bankDetails]);

  const withdrawAmountSubmit = async (
    withdrawAmountData: withdrawAmountData,
  ) => {
    setWithDrawLoading(true);
    if (withdrawAmountData) {
      const withdrawAmountPayload = {
        coins: withdrawAmountData?.enterPoint,
        bankId: bankDetails?.id,
      };
      const passAPI = `/withdraw/apply-for-withdraw`;

      try {
        const response = await fetch(Config.fantasyURL + passAPI, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${Token}`,
          },
          body: JSON.stringify(withdrawAmountPayload),
        });

        const data = await response.json();

        if (response.ok) {
          setWithDrawLoading(false);
          setApiMessage('success', data?.message);
          setIsProgress(true);
          setIsWithdraw(false);
          queryClient.refetchQueries({
            queryKey: [quyerKeys.getFantasyUser],
            exact: true,
          });
        } else {
          setWithDrawLoading(false);
          setApiMessage(
            'error',
            data?.message || 'Failed to process withdrawal',
          );
        }
      } catch (error) {
        setWithDrawLoading(false);
        setApiMessage('error', 'Network error. Please try again.');
      }
    }
  };

  const verificationSubmit = (data: verificationRequiredData) => {
    if (data) {
      handleCloseDialog();
      setIsWithdraw(true);
    }
  };

  const handleCloseDialog = () => setIsVerification(false);

  const handleCloseProgressDialog = () => setIsProgress(false);

  useEffect(() => {
    getVerificationData();
  }, []);
  // fetch verification document data
  const getVerificationData = async () => {
    setLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        Config.baseURL + URLS.VERIFICATION_URL,
      );
      if (status === 200 || status === 201) {
        const verifyData = data?.result;
        setVerificationStatus(verifyData?.verificationStatus);
        setVerificationDetails(verifyData);
        setLoading(false);
      }
      setLoading(false);
    } catch (err) {
      console.error(err);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (verificationStatus === 'verified' && bankDetails?.id) {
      setIsWithdraw(true);
    }
  }, [verificationStatus]);

  const verificationStatusMessage = () => {
    if (verificationStatus === 'verified') {
      return (
        <>
          <VerifiedIcon />
          <span className="ml-1">Verified</span>
        </>
      );
    } else if (verificationStatus === 'pending') {
      return (
        <>
          <VerifyInProgressIcon />
          <span className="ml-1">Verification in progress</span>
        </>
      );
    } else {
      return (
        <>
          <TriangleAlert size={17} />
          <span className="ml-1">Verify your identity</span>
        </>
      );
    }
  };

  if (identityVerification) {
    return (
      <div className="identityVerification">
        <div className="flex items-center mb-[21px] max-639:flex-col max-639:items-start">
          <div className="flex items-center">
            <button
              onClick={() => setIdentityVerification(false)}
              className="h-7 w-7 max-639:w-5 max-639:h-5 flex justify-center items-center bg-secondary-100 rounded-full text-white"
            >
              <ChevronLeft />
            </button>
            <h2 className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[16px] font-inter font-semibold text-black-100 ml-[9px]">
              Verfify you identity
            </h2>
          </div>
          {verificationStatus ? (
            <div
              className={cn(
                verificationStatus === 'verified' && 'bg-success-100',
                verificationStatus === 'pending' && 'bg-primary-400',
                verificationStatus !== 'verified' &&
                  verificationStatus !== 'pending' &&
                  'bg-negative-300',
                'text-white text-sm leading-[17px] flex justify-center items-center font-semibold rounded-md cursor-pointer px-3 py-[10px] ml-[18px] max-639:ml-0 max-639:mt-[6px]',
              )}
            >
              {verificationStatusMessage()}
            </div>
          ) : null}
        </div>

        <IdentityVerification
          verificationDetails={verificationDetails}
          setVerificationStatus={setVerificationStatus}
        />
      </div>
    );
  }

  // for calculation
  const enterPoint = +withdrawAmountWatch('enterPoint');

  return (
    <>
      <div>
        <WithdrawHeader
          CoinsAvailable={CoinsAvailable}
          Withdrawal={Withdrawal}
          fantasyUser={fantasyUser}
          loading={loading}
          setIdentityVerification={setIdentityVerification}
          verificationStatus={verificationStatus}
          verificationStatusMessage={verificationStatusMessage}
        />
        {!isWithdraw ? (
          <BankDetailsForm
            bankDetailsLoading={bankDetailsLoading}
            errors={errors}
            handleSubmit={handleSubmit}
            onSubmit={onSubmit}
            register={register}
            verificationStatus={verificationStatus}
          />
        ) : (
          <WithdrawForm
            deleteBank={deleteBank}
            enterPoint={enterPoint}
            setIsWithdraw={setIsWithdraw}
            withDrawLoading={withDrawLoading}
            withdrawAmountErrors={withdrawAmountErrors}
            withdrawAmountRegister={withdrawAmountRegister}
            withdrawAmountSubmit={withdrawAmountSubmit}
            withdrawAmounthandleSubmit={withdrawAmounthandleSubmit}
            bankDetails={bankDetails}
          />
        )}
      </div>
      <CustomDialog
        isOpen={isVerification}
        onClose={handleCloseDialog}
        title="Verification required"
        maxWidth={570}
        className="verification-required-modal"
        outerClickClose={true}
      >
        <div>
          <p>
            Please enter the code we&apos;ve sent to your number ending in
            +61******666
          </p>
          <div className="mt-[18px] mb-[21px] common-input-wrap">
            <Input
              type="number"
              variant="outlined"
              label="Enter Code"
              placeholder="Enter Code"
              color="indigo"
              className=" bg-white !text-[16px] !leading-[19px] !font-inter font-normal !text-black-900 placeholder:font-inter placeholder:text-black-900"
              {...(verificationRegister('enterCode') as any)}
              error={verificationErrors.enterCode?.message}
            />
            {verificationErrors.enterCode?.message && (
              <p className="text-red-400 text-sm mt-[2px]">
                {verificationErrors.enterCode.message}
              </p>
            )}
          </div>
          <div className=" text-center">
            <Button
              className="w-full"
              onClick={verificationhandleSubmit(verificationSubmit)}
            >
              Submit
            </Button>
            <p className="text-[14px] leading-[16px] font-inter font-normal mt-[9px] text-gray-200">
              Didn&apos;t get the code?{' '}
              <Button
                variant="link"
                className="text-secondary-100 underline p-0"
              >
                Resend code{' '}
              </Button>
            </p>
          </div>
        </div>
      </CustomDialog>

      <CustomDialog
        isOpen={isProgress}
        onClose={handleCloseProgressDialog}
        title="Processing"
        maxWidth={570}
        className="verification-required-modal"
        outerClickClose={true}
      >
        <div className="">
          <p className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[23px] max-799:mb-[13px]">
            We are processing your request!
          </p>
          <p className="text-[16px] leading-[22.4px] font-inter font-normal text-black-100 mb-[37px]">
            Please allow 2 to 5 business days for completion. In the meantime,
            you can track its status in your transaction history.
          </p>
          <div className="mb-[18px]">
            <Button
              className="w-full"
              onClick={() => {
                setActiveTab(2);
                handleCloseProgressDialog();
              }}
            >
              Transactions
            </Button>
          </div>
          <div>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => {
                handleCloseProgressDialog();
              }}
            >
              Close
            </Button>
          </div>
        </div>
      </CustomDialog>
    </>
  );
};

export default WithdrawPage;
