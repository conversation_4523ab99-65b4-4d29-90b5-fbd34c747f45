import React from 'react';
import { MessageCircle } from 'lucide-react';

interface CommentaryButtonProps {
  onClick: () => void;
  className?: string;
  disabled?: boolean;
}

const CommentaryButton: React.FC<CommentaryButtonProps> = ({
  onClick,
  className = '',
  disabled = false,
}) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`flex items-center gap-1.5 px-3 py-2 rounded-md border ${
        disabled
          ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed'
          : 'border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100'
      } transition-colors ${className}`}
    >
      <MessageCircle size={16} />
      <span className="font-medium text-sm">Live Updates</span>
    </button>
  );
};

export default CommentaryButton;