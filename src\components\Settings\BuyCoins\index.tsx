import './buyCoins.scss';

import { yupResolver } from '@hookform/resolvers/yup';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { AnimatePresence, motion } from 'motion/react';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as Yup from 'yup';

import Crown from '@/assets/images/settings/Crown.svg';
import Loader from '@/components/Loader';
import CoinCard from '@/components/Settings/BuyCoins/CoinCard';
import MemberShip from '@/components/Settings/BuyCoins/Membership';
import SmartB_Coins_Blue from '@/assets/images/settings/Smart_Coins_Blue.png';
import SmartB_Coins_White from '@/assets/images/settings/Smart_Coins_White.png';
import { Button } from '@/components/UI/button';
import type { CardFormData } from '@/components/UI/CardInput';
import CustomDialog from '@/components/UI/CustomDialog';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { setApiMessage } from '@/helpers/commonFunctions';
import { Config } from '@/helpers/context/config';
import { quyerKeys } from '@/lib/queryKeys';
import {
  calculateTotalWithCardFee,
  generateUniqueId,
  LocalStorage,
} from '@/lib/utils';

import { Token } from '../../../../db/db';
import type { CardType, GetCardsResponse } from '../../../../types';
import PaymentSelection from './PaymentSelection';
import { usePlanContext } from '@/helpers/context/userPlanContext';

// Yup validation schema
const cardSchema = Yup.object().shape({
  cardNumber: Yup.string()
    .min(19, 'Card number must be at least 16 characters') // Corrected to "at least"
    .max(19, 'Card number must be at most 19 characters')
    .required('Card number is required'),
  expirationDate: Yup.string()
    .required('Expiration date is required')
    .typeError('Not a valid expiration date. Example: MM/YYYY')
    .max(7, 'Not a valid expiration date. Example: MM/YYYY')
    .matches(/\d{2}\/\d{4}/, 'Not a valid expiration date. Example: MM/YYYY')
    .test(
      'is-future',
      'Card expiration date should be in the future',
      function (value) {
        if (!value) return false;
        const currentDate = new Date();
        const [month, year] = value.split('/');
        const cardExpirationDate = new Date(
          parseInt(year),
          parseInt(month) - 1,
        );
        return cardExpirationDate > currentDate;
      },
    )
    .test(
      'not-over-100',
      'Card expiration date should not exceed 100 years in the future',
      function (value) {
        if (!value) return false;
        const currentDate = new Date();
        const [month, year] = value.split('/');
        const cardExpirationDate = new Date(
          parseInt(year),
          parseInt(month) - 1,
        );
        return (
          cardExpirationDate.getFullYear() <= currentDate.getFullYear() + 100
        );
      },
    ),
  cvv: Yup.string()
    .min(3, 'CVV must be exactly 3 characters')
    .max(3, 'CVV must be exactly 3 characters')
    .required('CVV number is required'),
  cardHolderName: Yup.string().required('Card holder name is required'),
  isFeatured: Yup.boolean().optional(),
});

const fetchCoins = async () => {
  const res = await axiosInstance.get(Config.fantasyURL + `/coins/get`);
  return res.data;
};

const getCards = async (): Promise<CardType[]> => {
  const res = await fetch(`${Config.baseURL}subscription/get-card`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Token}`,
    },
  });

  const data: GetCardsResponse = await res.json();
  return data.card; // Return only the card array
};

const BuyCoinspage = () => {
  const [activeTab, setActiveTab] = useState<number>(1);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selectedCoinsId, setSelectedCoinsId] = useState<any>({});
  const searchParams = useSearchParams();
  const membership = searchParams.get('membership');

  const [isBuyingCoingLoading, setIsBuyingCoingLoading] =
    useState<boolean>(false);
  const queryClient = useQueryClient();
  const router = useRouter();

  const { data: cards } = useQuery({
    queryFn: getCards,
    queryKey: [quyerKeys.getCards],
  });

  // Use state to manage selected option with a default value
  const [selectedOption, setSelectedOption] = useState<any>(
    cards && cards.length > 0 ? cards?.[0]?.id : 'new',
  );
  const cardData = cards?.find((c) => c.id === +selectedOption!);

  const tabs = [
    { id: 1, name: 'Buy', icon: <img src={SmartB_Coins_Blue.src} alt="SmartCoins" className='w-full h-full' />, drakIcon: <img src={SmartB_Coins_White.src} alt="SmartCoins" className='w-full h-full' /> },
    { id: 2, name: 'Membership', icon: "", drakIcon: "" },
  ];

  const handleTabClick = (tab: any) => {
    setActiveTab(tab?.id);
  };

  const { data: buyCoinsData, isLoading } = useQuery({
    queryFn: fetchCoins,
    queryKey: [quyerKeys.getAllCoins],
    enabled: activeTab === 1,
  });

  const {
    register,
    formState: { errors },
    handleSubmit,
    setValue,
    reset,
  } = useForm<CardFormData>({
    resolver: selectedOption === 'new' ? yupResolver(cardSchema) : undefined,
  });
  const onSubmit = async (coinsBuyData: CardFormData) => {
    let formattedDate = '';
    if (coinsBuyData?.expirationDate) {
      const expirationDate = coinsBuyData?.expirationDate;
      const parts = expirationDate.split('/');
      if (parts.length === 2) {
        const month = parts[0];
        const year = parts[1];
        formattedDate = `${year}-${month}`;
      }
    }

    const cardIdPayload = {
      plateform: 'web',
      cardId: cardData?.id,
      currency: 'AUD',
    };

    const cardPayload = {
      plateform: 'web',
      isFeatured: coinsBuyData?.isFeatured,
      card: {
        card_holder_name: coinsBuyData?.cardHolderName,
        card_number: coinsBuyData?.cardNumber?.replace(/\s/g, ''),
        card_exp: formattedDate,
        cvv: coinsBuyData?.cvv,
      },
    };

    const redirect = LocalStorage.getItem<{ url: string }>('redirect');
    try {
      setIsBuyingCoingLoading(true);
      const { status, data } = await axiosInstance.post(
        Config.fantasyURL + `/coins/buy/${selectedCoinsId?.id}`,
        selectedOption === 'new' ? cardPayload : cardIdPayload,
      );
      if (status === 200) {
        setApiMessage('success', data?.message);
        if (redirect?.url) {
          window.location.href = redirect?.url;
        }

        queryClient.invalidateQueries({
          queryKey: [quyerKeys.getFantasyUser],
        });

        queryClient?.refetchQueries({
          queryKey: [quyerKeys.getFantasyUser],
          exact: true,
        });

        queryClient?.refetchQueries({
          queryKey: [quyerKeys.getCards],
          exact: true,
        });
        setIsOpen(false);
        setSelectedOption('new');
      }
    } catch (error) {
      // @ts-expect-error
      const messgae = error?.response?.data?.message;

      if (messgae) {
        setApiMessage('error', messgae);
      } else {
        // @ts-expect-error
        setApiMessage('error', error?.message);
      }
    } finally {
      setIsBuyingCoingLoading(false);

      reset({
        acceptTermsandConditions: undefined,
        cardHolderName: undefined,
        cardNumber: undefined,
        cvv: undefined,
        expirationDate: undefined,
        isFeatured: undefined,
      });
      router.push('/settings');
    }
  };

  const result = calculateTotalWithCardFee(selectedCoinsId?.coinPrice || 0);

  // Select Default Card Form

  useEffect(() => {
    if (cards?.length === 0 || !cards) {
      setSelectedOption('new');
    }
  }, [cards]);

  useEffect(() => {
    if (membership === 'true') {
      setActiveTab(2);
    }
  }, [membership]);

  const { setValidateUserCardData } = usePlanContext();


  let tabContent;

  if (activeTab === 1) {
    if (isLoading) {
      tabContent = (
        <div className="mt-1.5">
          <Loader />
        </div>
      );
    } else if (buyCoinsData?.result?.length > 0) {
      tabContent = (
        // <div className="grid md:grid-cols-4 grid-cols-1 gap-4">
        <div className='flex flex-wrap align-baseline gap-x-[18px] max-639:gap-x-[9px]'>
          {buyCoinsData?.result?.map((coin: any, index: number) => (
            <CoinCard
              key={generateUniqueId()}
              coinData={coin}
              setIsOpen={setIsOpen}
              setSelectedCoinsId={setSelectedCoinsId}
            />
          ))}
        </div>
      );
    } else {
      tabContent = (
        <div className="mt-2 p-2 text-center">
          <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter text-black-100">
            No Data Available
          </p>
        </div>
      );
    }
  } else if (activeTab === 2) {
    tabContent = <MemberShip />;
  }

  return (
    <>
      <div>
        {/* <h4 className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[16px] font-inter font-semibold text-black-100 capitalize">
          Buy Coins
        </h4> */}
        <div className=" flex items-center justify-between flex-wrap gap-y-1.5">
          <div>
            <div className="flex items-center gap-[3px] rounded-lg bg-black-300 p-[3px]">
              {tabs?.map((tab: any, index) => (
                <button
                  key={generateUniqueId()}
                  className={`px-[33px] py-[6px] cursor-pointer ${activeTab === tab?.id ? 'bg-primary-200 rounded-[6px]' : ''
                    }`}
                  onClick={() => handleTabClick(tab)}
                >
                  <p
                    className={`text-[22.4px] leading-[19px] font-apotekCompRegular font-normal uppercase flex items-baseline gap-x-[5px] ${activeTab === tab?.id ? 'text-white' : 'text-primary-200'
                      }`}
                  >

                    {tab?.name}
                    {tab?.icon && (
                      <div
                        className={`${activeTab === tab?.id ? 'active-icon' : ''} w-[83px] h-[19px]`}
                      >
                        {activeTab === tab?.id ? tab?.drakIcon : tab?.icon}
                      </div>
                    )}
                  </p>
                </button>
              ))}
            </div>
          </div>
        </div>
        <div>{tabContent}</div>
      </div>
      <AnimatePresence>
        <motion.div
          className="box relative"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
        >
          <CustomDialog
            isOpen={isOpen}
            onClose={() => {
              setIsOpen(false);
              setSelectedOption('new');
              setValidateUserCardData(undefined);
            }}
            title="Payment"
            maxWidth={570}
            className="coins-payment-modal"
            outerClickClose={true}
          >
            <div className="card-input-content">
              <PaymentSelection
                errors={errors}
                register={register}
                setValue={setValue}
                cards={cards || []}
                result={result}
                selectedCoinsId={selectedCoinsId}
                setSelectedOption={setSelectedOption}
                selectedOption={selectedOption}
                reset={reset}
              />
              <div className="w-full flex flex-col justify-center items-center p-[18px] border-t border-black-400">
                <Button
                  onClick={handleSubmit(onSubmit)}
                  disabled={isBuyingCoingLoading || !selectedOption}
                >
                  Buy Coins
                </Button>
              </div>
            </div>
          </CustomDialog>
        </motion.div>
      </AnimatePresence>
    </>
  );
};

export default BuyCoinspage;
