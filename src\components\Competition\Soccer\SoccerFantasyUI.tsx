import React from 'react';
import { cn, generateUniqueId } from '@/lib/utils';
import { useCompetition } from '@/helpers/context/competitionContext';
import useScreen from '@/hooks/useScreen';
import { useSoccerStore } from '@/store/soccer/useSoccerStore';
import PlayerCard from '@/components/Common/PlayerCard';
import { SoccerPlayer, SoccerPlayerRole } from '@/lib/types/soccer';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

const SoccerFantasyUI = () => {
  const {
    activeTabPlayer,
    setActiveTabPlayer,
    soccerPlayersByRole,
    setShowPlayerTable,
    playerByRoleLimit,
    removePlayer,
    setPlayerToCaptain,
    setPlayerToViceCaptain,
  } = useSoccerStore();
  const { eventDetailsResponse } = useCompetition();
  const { refetchDreamTeam } = useCompetition();

  const searchParams = useSearchParams();
  const dreamTeamId = searchParams.get('dreamTeamId');
  const add_more = searchParams.get('add_more');
  const router = useRouter();
  const pathname = usePathname();

  const handleEditTeam = (player: SoccerPlayer, role: SoccerPlayerRole) => {
    const query = {
      event_id: searchParams.get('event_id') || '',
      sport_id: searchParams.get('sport_id') || '',
      tournament_id: searchParams.get('tournament_id') || '',
      dreamTeamId: dreamTeamId || '',
      competition_id: searchParams.get('competition_id') || '',
      add_more: 'true',
      seasonId: searchParams.get('seasonId') || '',
      playerId: player.playerId.toString(),
      role: role,
    };
    refetchDreamTeam?.();
    router.push(`${pathname}?${new URLSearchParams(query).toString()}`);
  };

  const renderEmptyCards = (
    players: SoccerPlayer[],
    limit: number,
    role: SoccerPlayerRole,
  ) => {
    const emptyCount = Math.max(0, limit - players?.length);
    return Array.from({ length: emptyCount }).map((_, index) => (
      <PlayerCard<SoccerPlayer, SoccerPlayerRole>
        key={generateUniqueId()}
        activePlayerTab={role}
        setActivePlayerTab={setActiveTabPlayer}
        isActive={role === activeTabPlayer}
        setShowPlayerTabel={setShowPlayerTable}
        type="empty"
      />
    ));
  };

  const renderPlayerSection = (role: SoccerPlayerRole) => {
    const players = soccerPlayersByRole[role];
    const limit = playerByRoleLimit[role].min;
    let fullRole: string = '';

    switch (role) {
      case 'GKP':
        fullRole = 'Goalkeeper';
        break;
      case 'DEF':
        fullRole = 'Defender';
        break;
      case 'MID':
        fullRole = 'Midfielder';
        break;
      case 'FWD':
        fullRole = 'Forward';
        break;
      default:
        break;
    }

    return (
      <>
        <div className="text-center">
          <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
            {fullRole}: {players?.length}
          </span>
        </div>

        <div className="flex justify-center md:gap-8 gap-4 flex-wrap">
          {players.map((player) => (
            <PlayerCard<SoccerPlayer, SoccerPlayerRole>
              key={player.playerId}
              player={player}
              type="filled"
              eventStatus={
                eventDetailsResponse?.result?.eventDetails?.status ===
                'inprogress'
                  ? 'live'
                  : 'upcoming'
              }
              activePlayerTab={role as SoccerPlayerRole}
              setActivePlayerTab={setActiveTabPlayer}
              setShowPlayerTabel={setShowPlayerTable}
              isPlayerLocked={
                eventDetailsResponse?.result?.eventDetails?.status ===
                'inprogress'
              }
              dreamTeamId={dreamTeamId}
              onRemove={() => removePlayer(player, role)}
              onSubstitute={() => handleEditTeam(player, role)}
              onSetCaptain={() => setPlayerToCaptain(player, role)}
              onSetViceCaptain={() => setPlayerToViceCaptain(player, role)}
              showSubstituteButton={!!dreamTeamId && add_more !== 'true'}
              showRemoveButton={
                !eventDetailsResponse?.result?.dreamTeams || add_more === 'true'
              }
              addMore={add_more === 'true'}
            />
          ))}
          {renderEmptyCards(players, limit, role)}
        </div>
      </>
    );
  };

  const { width } = useScreen();
  const isMobile = width < 768;
  const desktopBg =
    'bg-[url("/fantasy/images/soccerBG.png")] bg-no-repeat bg-center bg-cover relative rounded';
  const mobileBg =
    'bg-[url("/fantasy/images/mobileSoccerBg.png")] bg-no-repeat bg-center bg-cover relative rounded';

  return (
    <div className={cn(isMobile ? mobileBg : desktopBg)}>
      <div className="max-w-6xl mx-auto md:p-8 p-3 mb-4">
        <div className="flex flex-col gap-8">
          {renderPlayerSection('GKP')}
          {renderPlayerSection('DEF')}
          {renderPlayerSection('MID')}
          {renderPlayerSection('FWD')}
        </div>
      </div>
    </div>
  );
};

export default SoccerFantasyUI;
