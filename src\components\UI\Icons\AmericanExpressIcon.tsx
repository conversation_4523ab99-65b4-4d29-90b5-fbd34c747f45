'use client';

const AmericanExpressIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="30"
      height="20"
      viewBox="0 0 30 20"
    >
      <defs>
        <clipPath id="clip-path">
          <rect
            id="Rectangle_9135"
            data-name="Rectangle 9135"
            width="14.458"
            height="14.422"
            fill="none"
          />
        </clipPath>
      </defs>
      <g
        id="Group_32658"
        data-name="Group 32658"
        transform="translate(-0.007 0)"
      >
        <g
          id="Rectangle_9133"
          data-name="Rectangle 9133"
          transform="translate(0.007 0)"
          fill="#fff"
          stroke="#c3c3c3"
          strokeWidth="0.2"
        >
          <rect width="30" height="20" rx="1" stroke="none" />
          <rect
            x="0.1"
            y="0.1"
            width="29.8"
            height="19.8"
            rx="0.9"
            fill="none"
          />
        </g>
        <g
          id="Group_32657"
          data-name="Group 32657"
          transform="translate(7.423 2.499)"
        >
          <g
            id="Group_32656"
            data-name="Group 32656"
            transform="translate(0 0)"
            clipPath="url(#clip-path)"
          >
            <path
              id="Path_29458"
              data-name="Path 29458"
              d="M629.262,381.226h-.453c-.136,0-.179.072-.179.14a.148.148,0,0,0,.156.148h.4a.479.479,0,0,1,.5.281v-.916l-.162.346Z"
              transform="translate(-615.262 -372.78)"
              fill="#fff"
            />
            <path
              id="Path_29459"
              data-name="Path 29459"
              d="M403.846,397.067h-.484v.357h.491a.192.192,0,0,0,.195-.178.2.2,0,0,0-.2-.178"
              transform="translate(-394.783 -388.623)"
              fill="#fff"
            />
            <path
              id="Path_29460"
              data-name="Path 29460"
              d="M323.054,397.277h-.47v.39h.468a.2.2,0,0,0,.211-.2.191.191,0,0,0-.209-.2"
              transform="translate(-315.724 -388.829)"
              fill="#fff"
            />
            <path
              id="Path_29461"
              data-name="Path 29461"
              d="M134.577,366.12l-.266.335-.257-.335h-2.4v2.264h2.382l.261-.342.257.342H136v-.524h.323a.8.8,0,0,0,.41-.085v.609h.95v-.357l.29.357h4.819a1,1,0,0,0,.491-.156v-.459a.469.469,0,0,1-.168.225.587.587,0,0,1-.349.1h-.9v-.368h.848a.176.176,0,0,0,.16-.071.134.134,0,0,0,.02-.071c0-.07-.053-.141-.174-.143h-.389a.474.474,0,0,1-.519-.508c0-.272.17-.534.665-.534h.809v-.279Zm-1.247.658h-.955v.29h.933v.357h-.933v.3h.955v.368h-1.383v-1.7h1.383Zm1.9,1.316H134.7l-.4-.522-.389.522h-.526l.673-.837-.689-.858h.534l.411.531.412-.531h.513l-.68.848Zm.932-.535h-.446v.535h-.435v-1.7h.936a.558.558,0,0,1,.609.57.586.586,0,0,1-.664.59m1.958.535-.508-.6h-.178v.6h-.424v-1.7h.943a.548.548,0,0,1,.593.55.516.516,0,0,1-.465.521l.558.625Zm1.986-1.316h-.955v.29h.928v.357h-.928v.3h.955v.368h-1.383v-1.7h1.383Zm1.1,1.316h-.9v-.368h.848c.126,0,.18-.068.18-.142s-.054-.143-.18-.143h-.383a.474.474,0,0,1-.519-.508c0-.272.17-.534.665-.534h.825l-.178.382h-.714c-.136,0-.178.072-.178.14a.148.148,0,0,0,.156.148h.4a.465.465,0,0,1,.533.487.508.508,0,0,1-.553.54"
              transform="translate(-128.857 -358.334)"
              fill="#fff"
            />
            <path
              id="Path_29462"
              data-name="Path 29462"
              d="M35.865,258.871h.478l-.238-.593Z"
              transform="translate(-35.102 -252.785)"
              fill="#fff"
            />
            <path
              id="Path_29463"
              data-name="Path 29463"
              d="M480.935,258.277l-.239.593h.478Z"
              transform="translate(-470.474 -252.785)"
              fill="#fff"
            />
            <path
              id="Path_29464"
              data-name="Path 29464"
              d="M6.771,228.5l.29.357H8.522v-.251a.958.958,0,0,0,.636.251h.969l.141-.357h.384l.144.357h1.429v-.4l.257.4h.982V226.6h-.982v.4l-.268-.4h-.973v.7l-.3-.7L9.247,226.6a1.126,1.126,0,0,0-.725.257V226.6H3.3l-.177.51-.183-.51H1.782v.706l-.307-.706H.541L0,227.855v1H.668l.143-.357h.383l.144.357H6.771Zm4.752-1.629h.539l.688,1.065v-1.065h.424v1.7h-.521l-.706-1.093v1.093h-.424Zm-1.331,0h.553l.729,1.7h-.485l-.134-.34h-.782l-.135.34H9.462Zm-.9,0H9.76v.4H9.274a.408.408,0,0,0-.4.457.415.415,0,0,0,.457.466h.112l-.178.37H9.176a.747.747,0,0,1-.726-.841.777.777,0,0,1,.842-.855m-1.481,0h.433v1.7H7.81Zm-6.282,1.7-.134-.34H.612l-.135.34H0l.208-.483.522-1.213h.553l.729,1.7Zm2.655,0H3.759V227.32l-.449,1.249H2.938l-.451-1.249v1.249H2.064v-1.7l.675,0,.392,1.093.383-1.1h.669Zm1.673-1.316H4.9v.29h.932v.357H4.9v.3h.955v.368H4.473v-1.7H5.856Zm.669.714v.6H6.1v-1.7h.943a.548.548,0,0,1,.593.55.516.516,0,0,1-.465.521l.558.625H7.212l-.508-.6Z"
              transform="translate(0 -221.776)"
              fill="#fff"
            />
            <path
              id="Path_29465"
              data-name="Path 29465"
              d="M307.334,257.556h-.484v.357h.491a.192.192,0,0,0,.194-.178.2.2,0,0,0-.2-.178"
              transform="translate(-300.324 -252.079)"
              fill="#fff"
            />
            <path
              id="Path_29466"
              data-name="Path 29466"
              d="M9.113,10.05l-.29-.357v.357h-.95V9.441a.8.8,0,0,1-.41.085H7.139v.524H5.7l-.257-.343-.261.343H2.8V7.786H5.2l.257.335.266-.335h8.7V0H0V6.079l.541-1.26h.935l.307.706V4.819H2.945l.183.51.177-.51H8.522v.257a1.126,1.126,0,0,1,.725-.257l1.693.006.3.7v-.7h.973l.268.4v-.4h.982V7.083h-.982l-.257-.4v.4H10.8l-.144-.357h-.384l-.141.357H9.158a.958.958,0,0,1-.636-.251v.251H7.061l-.29-.357v.357H1.337l-.144-.357H.811l-.143.357H0v7.339H14.422V9.894a1,1,0,0,1-.491.156Z"
              transform="translate(0 0)"
              fill="#016fd0"
            />
            <path
              id="Path_29467"
              data-name="Path 29467"
              d="M.74,241.078h.782l.134.339h.485l-.729-1.7H.86l-.522,1.213-.208.483H.606Zm.39-.961.238.593H.891Z"
              transform="translate(-0.128 -234.624)"
              fill="#016fd0"
            />
            <path
              id="Path_29468"
              data-name="Path 29468"
              d="M99.157,241.4v-1.7h-.669l-.383,1.1-.392-1.093-.675,0v1.7h.424v-1.249l.451,1.249h.372l.449-1.249V241.4Z"
              transform="translate(-94.974 -234.611)"
              fill="#016fd0"
            />
            <path
              id="Path_29469"
              data-name="Path 29469"
              d="M211.719,240.088v-.379h-1.383v1.7h1.383v-.368h-.955v-.3h.932v-.357h-.932v-.29Z"
              transform="translate(-205.863 -234.611)"
              fill="#016fd0"
            />
            <path
              id="Path_29470"
              data-name="Path 29470"
              d="M288.453,240.272a.548.548,0,0,0-.593-.55h-.943v1.7h.424v-.6h.178l.508.6h.518l-.558-.625a.516.516,0,0,0,.465-.521m-.621.186h-.491V240.1h.485a.2.2,0,0,1,.2.178.192.192,0,0,1-.194.178"
              transform="translate(-280.816 -234.624)"
              fill="#016fd0"
            />
            <rect
              id="Rectangle_9134"
              data-name="Rectangle 9134"
              width="0.433"
              height="1.695"
              transform="translate(7.81 5.097)"
              fill="#016fd0"
            />
            <path
              id="Path_29471"
              data-name="Path 29471"
              d="M397.329,240.563a.747.747,0,0,0,.726.841h.093l.178-.37h-.112a.415.415,0,0,1-.457-.466.408.408,0,0,1,.4-.457h.486v-.4h-.468a.777.777,0,0,0-.842.855"
              transform="translate(-388.88 -234.611)"
              fill="#016fd0"
            />
            <path
              id="Path_29472"
              data-name="Path 29472"
              d="M445.438,241.417l.135-.339h.782l.134.339h.485l-.728-1.7h-.553l-.729,1.7Zm.524-1.3.238.593h-.477Z"
              transform="translate(-435.501 -234.624)"
              fill="#016fd0"
            />
            <path
              id="Path_29473"
              data-name="Path 29473"
              d="M542.966,241.4h.521v-1.7h-.424v1.065l-.688-1.065h-.539v1.7h.424v-1.093Z"
              transform="translate(-530.315 -234.611)"
              fill="#016fd0"
            />
            <path
              id="Path_29474"
              data-name="Path 29474"
              d="M146.678,379.612v-.379h-1.383v1.7h1.383v-.368h-.955v-.3h.932V379.9h-.932v-.29Z"
              transform="translate(-142.205 -371.168)"
              fill="#016fd0"
            />
            <path
              id="Path_29475"
              data-name="Path 29475"
              d="M465.38,379.612v-.379H464v1.7h1.383v-.368h-.955v-.3h.928V379.9h-.928v-.29Z"
              transform="translate(-454.13 -371.168)"
              fill="#016fd0"
            />
            <path
              id="Path_29476"
              data-name="Path 29476"
              d="M213.462,379.233l-.412.531-.411-.531h-.534l.689.858-.673.837h.526l.389-.522.4.522h.534l-.675-.848.68-.848Z"
              transform="translate(-207.595 -371.168)"
              fill="#016fd0"
            />
            <path
              id="Path_29477"
              data-name="Path 29477"
              d="M302.128,379.246v1.7h.435v-.535h.446a.586.586,0,0,0,.664-.59.558.558,0,0,0-.609-.57Zm1.114.579a.2.2,0,0,1-.211.2h-.469v-.39h.47a.191.191,0,0,1,.209.2"
              transform="translate(-295.703 -371.181)"
              fill="#016fd0"
            />
            <path
              id="Path_29478"
              data-name="Path 29478"
              d="M384.966,379.782a.548.548,0,0,0-.593-.55h-.943v1.7h.424v-.6h.178l.508.6h.518l-.558-.625a.516.516,0,0,0,.465-.521m-.621.186h-.491v-.357h.484a.2.2,0,0,1,.2.178.192.192,0,0,1-.194.179"
              transform="translate(-375.276 -371.167)"
              fill="#016fd0"
            />
            <path
              id="Path_29479"
              data-name="Path 29479"
              d="M536.641,380.559h-.848v.368h.9a.508.508,0,0,0,.553-.54.465.465,0,0,0-.533-.487h-.4a.148.148,0,0,1-.156-.148c0-.068.042-.14.179-.14h.714l.179-.381H536.4c-.5,0-.665.262-.665.534a.474.474,0,0,0,.519.508h.383c.126,0,.18.072.18.143s-.054.142-.18.142"
              transform="translate(-524.346 -371.167)"
              fill="#016fd0"
            />
            <path
              id="Path_29480"
              data-name="Path 29480"
              d="M610.369,380.183a.479.479,0,0,0-.5-.281h-.4a.148.148,0,0,1-.156-.148c0-.068.042-.14.178-.14h.714l.162-.346.016-.035h-.825c-.5,0-.665.262-.665.534a.474.474,0,0,0,.519.508h.389c.121,0,.174.073.174.143a.134.134,0,0,1-.02.071.176.176,0,0,1-.16.071h-.848v.368h.9a.587.587,0,0,0,.349-.1.468.468,0,0,0,.168-.225.629.629,0,0,0,.036-.215.562.562,0,0,0-.036-.205"
              transform="translate(-595.947 -371.167)"
              fill="#016fd0"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};

export default AmericanExpressIcon;
