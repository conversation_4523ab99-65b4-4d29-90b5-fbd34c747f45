'use client';
import type { Column, ColumnDef } from '@tanstack/react-table';

import { getDefaultProfileImage } from '../../../../db/db';
import type { TeamData } from '../../../../types';
import SortingDownIcon from '../Icons/SortingDownIcon';
import SortingUpIcon from '../Icons/SortingUpIcon';
import PlayerAvatar from '../PlayerAvatar/indext';

const renderSortHeader = (column: Column<TeamData, unknown>, label: string) => (
  <button
    className="text-xs flex items-center space-x-1 font-bold"
    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  >
    <span className="text-white">{label}</span>
    <div className="flex items-center flex-col">
      <span>
        <SortingUpIcon isActive={column.getIsSorted() === 'asc'} />
      </span>
      <span className="mt-[1.3px]">
        <SortingDownIcon isActive={column.getIsSorted() === 'desc'} />
      </span>
    </div>
  </button>
);

export const teamColumns: ColumnDef<TeamData>[] = [
  {
    accessorKey: 'teamName',
    header: ({ column }) => (
      <div className="flex justify-start ml-5 items-center">
        {renderSortHeader(column, 'Team')}
      </div>
    ),

    cell: ({ row }) => (
      <div className="flex space-x-2 px-2">
        <PlayerAvatar
          avatarUrl={row?.original?.profileImage || getDefaultProfileImage()}
        />
        <p>{row.original.teamName}</p>
      </div>
    ),
  },
  {
    accessorKey: 'matches',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Matches')}
      </div>
    ),
  },
  {
    accessorKey: 'wins',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Wins')}
      </div>
    ),
  },
  {
    accessorKey: 'losses',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Losses')}
      </div>
    ),
  },
  {
    accessorKey: 'ties',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Ties')}
      </div>
    ),
  },
  {
    accessorKey: 'noResults',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'No Results')}
      </div>
    ),
  },
  {
    accessorKey: 'netRunRate',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Net Run Rate')}
      </div>
    ),
  },
  {
    accessorKey: 'deductions',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Deductions')}
      </div>
    ),
  },
  {
    accessorKey: 'points',
    header: ({ column }) => (
      <div className="flex justify-center items-center">
        {renderSortHeader(column, 'Points')}
      </div>
    ),
  },
];
