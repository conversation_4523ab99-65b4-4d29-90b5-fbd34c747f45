import React from 'react';

interface PopularTagMobileProps extends React.SVGProps<SVGSVGElement> {
  fill?: string;
  stroke?: string;
  text?: string;
  textColor?: string;
}

const PopularTagMobile: React.FC<PopularTagMobileProps> = ({
  fill = '#1c9a6c',
  stroke = '#4c7292',
  text = '',
  textColor = '#fff',
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="208"
      height="57"
      viewBox="0 0 208 57"
      {...props}
    >
      <defs>
        <filter id="Path_313874" x="0" y="0" width="208" height="57" filterUnits="userSpaceOnUse">
          <feOffset dy="2" in="SourceAlpha" />
          <feGaussianBlur stdDeviation="4.5" result="blur" />
          <feFlood floodOpacity="0.341" />
          <feComposite operator="in" in2="blur" />
          <feComposite in="SourceGraphic" />
        </filter>
      </defs>
      <g transform="translate(14 8)">
        <g transform="translate(0 4)">
          <g>
            <g transform="matrix(1, 0, 0, 1, -14, -12)" filter="url(#Path_313874)">
              <path
                d="M180,0V3.67h-1.365a28.448,28.448,0,0,0-17.495,6.3l-17.259,13.4A25.394,25.394,0,0,1,128.261,29H51.731a25.394,25.394,0,0,1-15.62-5.63L18.846,9.968A28.427,28.427,0,0,0,1.365,3.67H0V0Z"
                transform="translate(14 12)"
                fill={fill}
                stroke={stroke}
                strokeWidth="1"
              />
            </g>
          </g>
        </g>
        <rect width="180" height="7" fill={fill} />
      </g>

      <text
        x="50%" y="50%"
        textAnchor="middle"
        dominantBaseline="middle"
        fill={textColor}
        fontSize="13"
        fontFamily="Inter-SemiBold, Inter"
        fontWeight="600"
      >
        {text}
      </text>
    </svg>
  );
};

export default PopularTagMobile;
