export interface NrlMatchData {
  status: boolean;
  result: NrlMatch;
}

export interface NrlMatch {
  id: string;
  date: string;
  time: string;
  localteam: EnhancedTeam;
  visitorteam: EnhancedTeam;
  status: Status;
  events: {
    event: MatchEvent[];
  };
  venue?: Venue;
  fulltime?: Score;
  halftime?: Score;
  players_stats?: {
    localteam: TeamStats;
    visitorteam: TeamStats;
  };
  squads?: {
    localteam: TeamSquad;
    visitorteam: TeamSquad;
  };
}

export interface EnhancedTeam {
  id: string;
  name: string;
  score: string;
  flag?: string;
  nameCode?: string;
  period1?: string;
  period2?: string;
}

export interface Venue {
  id: string;
  name: string;
}

export interface Status {
  time: string;
  value: string;
}

export interface Score {
  localteam: string;
  visitorteam: string;
}

export interface MatchEvent {
  min: string;
  player_id: string;
  player_name: string;
  team: 'localteam' | 'visitorteam';
  type: string;
}

export interface TeamStats {
  id: string;
  player: PlayerStat[];
}

export interface PlayerStat {
  id: string;
  name: string;
  attacking_kicks: string;
  run_metres: string;
  tries: string;
  tackle_busts: string;
  line_breaks: string;
  tackles: string;
}

export interface TeamSquad {
  id: string;
  player: SquadPlayer[];
}

export interface SquadPlayer {
  id: string;
  name: string;
  number: string;
  position: string;
  captain: string;
  subst: string;
}

export interface CommentaryFilters {
  type: 'Full Commentary' | 'Try' | 'Conversion' | 'Conv Miss' | 'Penalty' | 'Penalty Try' | 'Penalty Goal' | 'Field Goal' | '2Pts FG Miss';
}

export interface BettingOdds {
  localteam: {
    value: string;
    fluctuation?: 'up' | 'down' | 'stable';
  };
  visitorteam: {
    value: string;
    fluctuation?: 'up' | 'down' | 'stable';
  };
}
