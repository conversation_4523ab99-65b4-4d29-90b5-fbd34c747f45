<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="70" height="70" viewBox="0 0 70 70">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_58121" data-name="Rectangle 58121" width="50.874" height="60.113" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_113868" data-name="Group 113868" transform="translate(0.491)">
    <rect id="Rectangle_56920" data-name="Rectangle 56920" width="70" height="70" transform="translate(-0.491 0)" fill="none"/>
    <g id="Group_113694" data-name="Group 113694" transform="translate(9.072 4.944)">
      <g id="Group_109949" data-name="Group 109949" clip-path="url(#clip-path)">
        <path id="Path_180367" data-name="Path 180367" d="M316.519,745.367q-.033,2.337-.067,4.673a2.852,2.852,0,0,1-1.4,2.157c-3.194,2.345-10.851,3.194-17.1,1.9-4.421-.916-6.953-2.661-6.929-4.432q.033-2.338.067-4.673c-.025,1.771,2.507,3.515,6.929,4.432,6.251,1.295,13.907.446,17.1-1.9a2.852,2.852,0,0,0,1.394-2.159" transform="translate(-278.893 -714.01)" fill="#f0b500"/>
        <path id="Path_180368" data-name="Path 180368" d="M311.142,633.486c-6.258-1.295-13.909-.446-17.1,1.9s-.717,5.295,5.53,6.591,13.907.446,17.1-1.9.716-5.295-5.53-6.591" transform="translate(-280.445 -606.561)" fill="#f0b500"/>
        <path id="Path_180369" data-name="Path 180369" d="M323.982,638.688c-5.967-1.237-13.286-.426-16.339,1.813s-.685,5.058,5.286,6.3,13.286.425,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-293.541 -611.573)" fill="#fff511"/>
        <path id="Path_180370" data-name="Path 180370" d="M391.313,671.733c4.618.929,6.447,3.049,4.087,4.724s-8.016,2.289-12.634,1.362-6.447-3.049-4.088-4.724,8.016-2.289,12.634-1.36" transform="translate(-361.905 -643.385)" fill="#ffff89"/>
        <path id="Path_180371" data-name="Path 180371" d="M385.734,662.546c4.535.942,6.334,3.09,4.014,4.791s-7.873,2.323-12.41,1.381-6.333-3.089-4.015-4.791,7.875-2.323,12.409-1.381" transform="translate(-356.79 -634.572)" fill="#ca7700"/>
        <path id="Path_180372" data-name="Path 180372" d="M393.656,670.3c4.481.915,6.258,3,3.967,4.646s-7.78,2.252-12.26,1.339-6.258-3-3.967-4.646,7.78-2.253,12.261-1.34" transform="translate(-364.539 -642.015)" fill="#fff511"/>
        <path id="Path_180373" data-name="Path 180373" d="M728.892,847.425v-4.646a21.905,21.905,0,0,1-2.62.589v4.661a21.487,21.487,0,0,0,2.62-.6" transform="translate(-695.997 -807.732)" fill="#dc8e00"/>
        <path id="Path_180374" data-name="Path 180374" d="M864.715,760.83a2.853,2.853,0,0,0,1.4-2.157q.032-2.336.067-4.673a2.85,2.85,0,0,1-1.394,2.159c-.063.045-.126.09-.191.133v4.626l.122-.087" transform="translate(-828.552 -722.644)" fill="#dc8e00"/>
        <path id="Path_180375" data-name="Path 180375" d="M424.961,843.274q-.779-.161-1.477-.356v4.691q.668.183,1.409.337c.409.084.824.16,1.242.227v-4.684c-.394-.064-.787-.135-1.173-.215" transform="translate(-405.831 -807.865)" fill="#ffde00"/>
        <path id="Path_180376" data-name="Path 180376" d="M792.261,813.785V809.16a11.43,11.43,0,0,1-3.146,1.392V815.2a11.159,11.159,0,0,0,3.146-1.419" transform="translate(-756.219 -775.511)" fill="#ca7700"/>
        <path id="Path_180377" data-name="Path 180377" d="M323.88,797.429v4.724a8.918,8.918,0,0,0,1.793,1.035v-4.7a8.681,8.681,0,0,1-1.793-1.057" transform="translate(-310.379 -764.268)" fill="#ffff89"/>
        <path id="Path_180378" data-name="Path 180378" d="M366.892,822.879v4.7a16.409,16.409,0,0,0,2.359.826v-4.691a16.18,16.18,0,0,1-2.359-.838" transform="translate(-351.598 -788.659)" fill="#fffa13"/>
        <path id="Path_180379" data-name="Path 180379" d="M291.13,744.992q-.033,2.336-.067,4.673a2.9,2.9,0,0,0,1.368,2.233v-4.72a2.854,2.854,0,0,1-1.3-2.178" transform="translate(-278.93 -714.011)" fill="#fffa13"/>
        <path id="Path_180380" data-name="Path 180380" d="M669.191,638.772c3.293.682,5.489,1.847,6.285,3.129l.479-.157c-.891-1.3-3.155-2.468-6.505-3.161-.783-.163-1.588-.29-2.4-.386l-.508.167c.9.1,1.79.231,2.652.41" transform="translate(-638.754 -611.657)" fill="#f0a400"/>
        <path id="Path_180381" data-name="Path 180381" d="M303.7,764.022c-3.694-.765-6.008-2.137-6.513-3.6l-.508.167c.629,1.477,3.018,2.847,6.763,3.63q.608.125,1.233.225l.479-.158q-.736-.11-1.455-.257" transform="translate(-284.316 -728.799)" fill="#f0a400"/>
        <path id="Path_180382" data-name="Path 180382" d="M812.723,737.706a1.593,1.593,0,0,0-.183-.466,1.432,1.432,0,0,1,.1.494Z" transform="translate(-778.668 -706.582)" fill="#fff511"/>
        <path id="Path_180383" data-name="Path 180383" d="M425.477,799.522a13.033,13.033,0,0,1-2.937-.941,12.258,12.258,0,0,0,3.2,1.064,21.107,21.107,0,0,0,2.595.357l.243-.08a21.427,21.427,0,0,1-3.112-.4" transform="translate(-404.926 -765.372)" fill="#fff511"/>
        <path id="Path_180384" data-name="Path 180384" d="M294.469,635.5c2.611-1.917,8.345-2.788,13.68-2.223l.508-.167c-5.7-.672-11.906.234-14.7,2.285-1.281.941-1.649,1.977-1.228,2.968l.508-.167c-.313-.9.063-1.841,1.227-2.694" transform="translate(-280.369 -606.573)" fill="#f0b500"/>
        <path id="Path_180385" data-name="Path 180385" d="M310.235,640.914c1.708-1.257,5.179-1.924,8.7-1.809l2.551-.841c-5.343-.565-11.077.307-13.68,2.223-1.164.854-1.54,1.793-1.227,2.694l2.663-.871a2.219,2.219,0,0,1,1-1.389" transform="translate(-293.701 -611.559)" fill="#ffff89"/>
        <path id="Path_180386" data-name="Path 180386" d="M382.426,721.635a2.3,2.3,0,0,0-.487.611l.068-.023a2.085,2.085,0,0,1,.419-.588" transform="translate(-366.018 -691.626)" fill="#ca7700"/>
        <path id="Path_180387" data-name="Path 180387" d="M384.278,671.5a3.454,3.454,0,0,0-.453.385,2.083,2.083,0,0,0-.421.588l8.1-2.673.194-.064a14.276,14.276,0,0,0-7.422,1.762" transform="translate(-367.423 -641.877)" fill="#ffff89"/>
        <path id="Path_180388" data-name="Path 180388" d="M504.359,726.881c1.482-1.087,1.747-2.307.972-3.429l-.479.157c.646,1.042.368,2.163-1,3.168-2.809,2.06-9.225,2.912-14.885,2.07l-.479.158c5.993.952,12.883.068,15.863-2.124" transform="translate(-468.126 -693.367)" fill="#ca7700"/>
        <path id="Path_180389" data-name="Path 180389" d="M512.629,730.053c-2,1.423-6.358,2.078-10.449,1.681l-2.2.726c5.658.841,12.079-.01,14.885-2.07,1.368-1,1.648-2.125,1-3.168l-2.3.758c.274.693-.009,1.415-.931,2.072" transform="translate(-479.133 -696.98)" fill="#ffe400"/>
        <path id="Path_180390" data-name="Path 180390" d="M572.439,748.447l-.083.028-10.309,3.4-.243.08c3.829.291,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.812" transform="translate(-538.384 -717.323)" fill="#ffe400"/>
        <path id="Path_180391" data-name="Path 180391" d="M493.525,702.294a9.823,9.823,0,0,1,4.452.3c.451.155,1.007.164,1.221.29v.32l-2.037.29a3.08,3.08,0,0,0-.7-.328,4.765,4.765,0,0,0-2.222-.135c-1.528.213-1.841.963-1.262,1.561a4.657,4.657,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.674a1.437,1.437,0,0,0,0-.726l.93.081,1.019-.125a.927.927,0,0,1,.173.846c-.213.63-1.233,1.1-2.868,1.333a10.8,10.8,0,0,1-3.988-.184,5.123,5.123,0,0,1-2.363-1.15,1.041,1.041,0,0,1-.161-1.461,4.28,4.28,0,0,1,2.719-1.127" transform="translate(-470.126 -672.967)" fill="#ca7700"/>
        <path id="Path_180392" data-name="Path 180392" d="M490.728,693.286a9.82,9.82,0,0,1,4.453.3,4.407,4.407,0,0,1,1.219.615l-2.037.29a3.114,3.114,0,0,0-.7-.327,4.766,4.766,0,0,0-2.222-.136c-1.527.215-1.841.963-1.26,1.562a4.665,4.665,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.673a.579.579,0,0,0-.086-.5l1.01-.145,1.019-.125a.928.928,0,0,1,.175.845c-.212.63-1.231,1.1-2.868,1.334a10.777,10.777,0,0,1-3.988-.184,5.112,5.112,0,0,1-2.363-1.15A1.042,1.042,0,0,1,488,694.41a4.284,4.284,0,0,1,2.718-1.127" transform="translate(-467.441 -664.333)" fill="#ffff89"/>
        <path id="Path_180393" data-name="Path 180393" d="M493.417,694.651a9.823,9.823,0,0,1,4.452.3,4.363,4.363,0,0,1,1.221.616l-2.037.29a3.1,3.1,0,0,0-.7-.327,4.762,4.762,0,0,0-2.222-.135c-1.528.215-1.841.963-1.262,1.562a4.653,4.653,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.672a.592.592,0,0,0-.084-.5l1.01-.145,1.019-.124a.924.924,0,0,1,.173.845c-.213.63-1.233,1.1-2.868,1.334a10.8,10.8,0,0,1-3.988-.184,5.124,5.124,0,0,1-2.363-1.151,1.042,1.042,0,0,1-.163-1.461,4.284,4.284,0,0,1,2.718-1.127" transform="translate(-470.018 -665.641)" fill="#f0b500"/>
        <path id="Path_180394" data-name="Path 180394" d="M316.519,618.778q-.033,2.337-.067,4.673a2.853,2.853,0,0,1-1.4,2.157c-3.194,2.345-10.851,3.194-17.1,1.9-4.421-.916-6.953-2.661-6.929-4.432q.033-2.336.067-4.674c-.025,1.773,2.507,3.516,6.929,4.433,6.251,1.295,13.907.445,17.1-1.9a2.852,2.852,0,0,0,1.394-2.159" transform="translate(-278.893 -592.685)" fill="#f0b500"/>
        <path id="Path_180395" data-name="Path 180395" d="M311.142,506.9c-6.258-1.3-13.909-.446-17.1,1.9s-.717,5.295,5.53,6.59,13.907.446,17.1-1.9.716-5.295-5.53-6.591" transform="translate(-280.445 -485.235)" fill="#f0b500"/>
        <path id="Path_180396" data-name="Path 180396" d="M323.982,512.083c-5.967-1.238-13.286-.425-16.339,1.813s-.685,5.058,5.286,6.3,13.286.425,16.337-1.815.685-5.058-5.286-6.295" transform="translate(-293.541 -490.232)" fill="#fff511"/>
        <path id="Path_180397" data-name="Path 180397" d="M391.313,545.145c4.618.929,6.447,3.049,4.087,4.724s-8.016,2.29-12.634,1.362-6.447-3.049-4.088-4.724,8.016-2.29,12.634-1.361" transform="translate(-361.905 -522.06)" fill="#ffff89"/>
        <path id="Path_180398" data-name="Path 180398" d="M385.734,535.956c4.535.942,6.334,3.089,4.014,4.791s-7.873,2.323-12.41,1.381-6.333-3.089-4.015-4.791,7.875-2.323,12.409-1.381" transform="translate(-356.79 -513.247)" fill="#ca7700"/>
        <path id="Path_180399" data-name="Path 180399" d="M393.656,543.755c4.481.913,6.258,3,3.967,4.646s-7.78,2.253-12.26,1.339-6.258-3-3.967-4.646,7.78-2.253,12.261-1.339" transform="translate(-364.539 -520.733)" fill="#fff511"/>
        <path id="Path_180400" data-name="Path 180400" d="M728.892,720.837v-4.646a21.93,21.93,0,0,1-2.62.59v4.66a21.725,21.725,0,0,0,2.62-.6" transform="translate(-695.997 -686.408)" fill="#dc8e00"/>
        <path id="Path_180401" data-name="Path 180401" d="M864.715,634.275a2.852,2.852,0,0,0,1.4-2.157q.032-2.338.067-4.673a2.852,2.852,0,0,1-1.394,2.159l-.191.133v4.625l.122-.087" transform="translate(-828.552 -601.352)" fill="#dc8e00"/>
        <path id="Path_180402" data-name="Path 180402" d="M424.961,716.825q-.779-.161-1.477-.356v4.691q.668.183,1.409.337c.409.084.824.16,1.242.227V717.04c-.394-.064-.787-.136-1.173-.216" transform="translate(-405.831 -686.675)" fill="#ffde00"/>
        <path id="Path_180403" data-name="Path 180403" d="M792.261,687.3v-4.621a11.412,11.412,0,0,1-3.146,1.392v4.646a11.134,11.134,0,0,0,3.146-1.418" transform="translate(-756.219 -654.285)" fill="#ca7700"/>
        <path id="Path_180404" data-name="Path 180404" d="M323.88,670.839v4.726a8.909,8.909,0,0,0,1.793,1.035v-4.7a8.652,8.652,0,0,1-1.793-1.058" transform="translate(-310.379 -642.942)" fill="#ffff89"/>
        <path id="Path_180405" data-name="Path 180405" d="M366.892,696.288v4.7a16.387,16.387,0,0,0,2.359.826v-4.691a16.138,16.138,0,0,1-2.359-.838" transform="translate(-351.598 -667.333)" fill="#fffa13"/>
        <path id="Path_180406" data-name="Path 180406" d="M291.13,618.472q-.033,2.336-.067,4.673a2.9,2.9,0,0,0,1.368,2.233V620.65a2.853,2.853,0,0,1-1.3-2.178" transform="translate(-278.93 -592.753)" fill="#fffa13"/>
        <path id="Path_180407" data-name="Path 180407" d="M669.191,512.148c3.293.683,5.489,1.847,6.285,3.129l.479-.157c-.891-1.3-3.155-2.468-6.505-3.16-.783-.163-1.588-.29-2.4-.388l-.508.168a26.58,26.58,0,0,1,2.652.409" transform="translate(-638.754 -490.298)" fill="#f0a400"/>
        <path id="Path_180408" data-name="Path 180408" d="M303.7,637.433c-3.694-.767-6.008-2.137-6.513-3.6l-.508.167c.629,1.478,3.018,2.847,6.763,3.63q.608.126,1.233.225l.479-.158q-.736-.11-1.455-.257" transform="translate(-284.316 -607.475)" fill="#f0a400"/>
        <path id="Path_180409" data-name="Path 180409" d="M812.723,611.117a1.594,1.594,0,0,0-.183-.466,1.433,1.433,0,0,1,.1.494Z" transform="translate(-778.668 -585.257)" fill="#fff511"/>
        <path id="Path_180410" data-name="Path 180410" d="M425.477,673a13,13,0,0,1-2.937-.939,12.3,12.3,0,0,0,3.2,1.064,21.225,21.225,0,0,0,2.595.357l.243-.081a21.385,21.385,0,0,1-3.112-.4" transform="translate(-404.926 -644.112)" fill="#fff511"/>
        <path id="Path_180411" data-name="Path 180411" d="M294.469,508.882c2.611-1.917,8.345-2.788,13.68-2.223l.508-.169c-5.7-.672-11.906.235-14.7,2.285-1.281.941-1.649,1.978-1.228,2.968l.508-.167c-.313-.9.063-1.841,1.227-2.695" transform="translate(-280.369 -485.219)" fill="#f0b500"/>
        <path id="Path_180412" data-name="Path 180412" d="M310.235,514.325c1.708-1.257,5.179-1.924,8.7-1.809l2.551-.841c-5.343-.565-11.077.306-13.68,2.223-1.164.854-1.54,1.793-1.227,2.694l2.663-.871a2.219,2.219,0,0,1,1-1.389" transform="translate(-293.701 -490.234)" fill="#ffff89"/>
        <path id="Path_180413" data-name="Path 180413" d="M382.426,595.046a2.291,2.291,0,0,0-.487.611l.068-.023a2.079,2.079,0,0,1,.419-.588" transform="translate(-366.018 -570.301)" fill="#ca7700"/>
        <path id="Path_180414" data-name="Path 180414" d="M384.278,544.914a3.419,3.419,0,0,0-.453.383,2.075,2.075,0,0,0-.421.588l8.1-2.673.194-.064a14.278,14.278,0,0,0-7.422,1.764" transform="translate(-367.423 -520.552)" fill="#ffff89"/>
        <path id="Path_180415" data-name="Path 180415" d="M504.359,600.324c1.482-1.089,1.747-2.307.972-3.429l-.479.157c.646,1.042.368,2.163-1,3.166-2.809,2.06-9.225,2.912-14.885,2.072l-.479.158c5.993.954,12.883.068,15.863-2.124" transform="translate(-468.126 -572.073)" fill="#ca7700"/>
        <path id="Path_180416" data-name="Path 180416" d="M512.629,603.462c-2,1.423-6.358,2.077-10.449,1.681l-2.2.726c5.658.841,12.079-.01,14.885-2.072,1.368-1,1.648-2.124,1-3.167l-2.3.758c.274.692-.009,1.416-.931,2.072" transform="translate(-479.133 -575.653)" fill="#ffe400"/>
        <path id="Path_180417" data-name="Path 180417" d="M572.439,621.858l-.083.028-10.309,3.4-.243.081c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.812" transform="translate(-538.384 -595.998)" fill="#ffe400"/>
        <path id="Path_180418" data-name="Path 180418" d="M493.525,575.845a9.818,9.818,0,0,1,4.452.3c.451.156,1.007.164,1.221.291v.318l-2.037.29a3.1,3.1,0,0,0-.7-.328,4.776,4.776,0,0,0-2.222-.135c-1.528.215-1.841.962-1.262,1.562a4.657,4.657,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.674a1.438,1.438,0,0,0,0-.726l.93.081,1.019-.125a.928.928,0,0,1,.173.846c-.213.629-1.233,1.1-2.868,1.333a10.8,10.8,0,0,1-3.988-.185,5.124,5.124,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.161-1.461,4.276,4.276,0,0,1,2.719-1.127" transform="translate(-470.126 -551.776)" fill="#ca7700"/>
        <path id="Path_180419" data-name="Path 180419" d="M490.728,566.734a9.83,9.83,0,0,1,4.453.3,4.393,4.393,0,0,1,1.219.616l-2.037.29a3.13,3.13,0,0,0-.7-.326,4.757,4.757,0,0,0-2.222-.135c-1.527.215-1.841.962-1.26,1.562a4.658,4.658,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.674a.579.579,0,0,0-.086-.5l1.01-.145,1.019-.125a.928.928,0,0,1,.175.845c-.212.63-1.231,1.1-2.868,1.334a10.8,10.8,0,0,1-3.988-.184,5.118,5.118,0,0,1-2.363-1.151,1.042,1.042,0,0,1-.163-1.461,4.286,4.286,0,0,1,2.718-1.127" transform="translate(-467.441 -543.044)" fill="#ffff89"/>
        <path id="Path_180420" data-name="Path 180420" d="M493.417,568.068a9.816,9.816,0,0,1,4.452.3,4.375,4.375,0,0,1,1.221.616l-2.037.29a3.062,3.062,0,0,0-.7-.327,4.756,4.756,0,0,0-2.222-.135c-1.528.215-1.841.962-1.262,1.562a4.655,4.655,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.674a.59.59,0,0,0-.084-.5l1.01-.145,1.019-.125a.928.928,0,0,1,.173.847c-.213.63-1.233,1.1-2.868,1.334a10.8,10.8,0,0,1-3.988-.184,5.117,5.117,0,0,1-2.363-1.151,1.042,1.042,0,0,1-.163-1.461,4.289,4.289,0,0,1,2.718-1.127" transform="translate(-470.018 -544.323)" fill="#f0b500"/>
        <path id="Path_180421" data-name="Path 180421" d="M316.519,492.222q-.033,2.337-.067,4.672a2.854,2.854,0,0,1-1.4,2.159c-3.194,2.343-10.851,3.194-17.1,1.9-4.421-.916-6.953-2.66-6.929-4.431q.033-2.339.067-4.673c-.025,1.773,2.507,3.516,6.929,4.432,6.251,1.295,13.907.446,17.1-1.9a2.852,2.852,0,0,0,1.394-2.159" transform="translate(-278.893 -471.393)" fill="#f0b500"/>
        <path id="Path_180422" data-name="Path 180422" d="M311.142,380.342c-6.258-1.295-13.909-.446-17.1,1.9s-.717,5.295,5.53,6.59,13.907.446,17.1-1.9.716-5.295-5.53-6.59" transform="translate(-280.445 -363.945)" fill="#f0b500"/>
        <path id="Path_180423" data-name="Path 180423" d="M323.982,385.521c-5.967-1.238-13.286-.427-16.339,1.813s-.685,5.058,5.286,6.3,13.286.427,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-293.541 -368.933)" fill="#fff511"/>
        <path id="Path_180424" data-name="Path 180424" d="M391.313,418.519c4.618.929,6.447,3.049,4.087,4.724s-8.016,2.289-12.634,1.36-6.447-3.049-4.088-4.724,8.016-2.289,12.634-1.36" transform="translate(-361.905 -400.699)" fill="#ffff89"/>
        <path id="Path_180425" data-name="Path 180425" d="M385.734,409.436c4.535.942,6.334,3.089,4.014,4.791s-7.873,2.323-12.41,1.382-6.333-3.09-4.015-4.791,7.875-2.323,12.409-1.381" transform="translate(-356.79 -391.989)" fill="#ca7700"/>
        <path id="Path_180426" data-name="Path 180426" d="M393.656,417.164c4.481.915,6.258,3,3.967,4.646s-7.78,2.253-12.26,1.339-6.258-3-3.967-4.646,7.78-2.253,12.261-1.339" transform="translate(-364.539 -399.407)" fill="#fff511"/>
        <path id="Path_180427" data-name="Path 180427" d="M728.892,594.247V589.6a21.789,21.789,0,0,1-2.62.59v4.662a21.62,21.62,0,0,0,2.62-.6" transform="translate(-695.997 -565.082)" fill="#dc8e00"/>
        <path id="Path_180428" data-name="Path 180428" d="M864.715,507.719a2.855,2.855,0,0,0,1.4-2.159q.032-2.338.067-4.672a2.851,2.851,0,0,1-1.394,2.159l-.191.133v4.626l.122-.087" transform="translate(-828.552 -480.058)" fill="#dc8e00"/>
        <path id="Path_180429" data-name="Path 180429" d="M424.961,590.234q-.779-.161-1.477-.356v4.692q.668.183,1.409.337c.409.084.824.16,1.242.227V590.45c-.394-.064-.787-.135-1.173-.216" transform="translate(-405.831 -565.348)" fill="#ffde00"/>
        <path id="Path_180430" data-name="Path 180430" d="M792.261,560.538V555.91a11.407,11.407,0,0,1-3.146,1.392v4.646a11.126,11.126,0,0,0,3.146-1.418" transform="translate(-756.219 -532.792)" fill="#ca7700"/>
        <path id="Path_180431" data-name="Path 180431" d="M323.88,544.283v4.726a8.9,8.9,0,0,0,1.793,1.035v-4.7a8.67,8.67,0,0,1-1.793-1.058" transform="translate(-310.379 -521.649)" fill="#ffff89"/>
        <path id="Path_180432" data-name="Path 180432" d="M366.892,569.7v4.707a16.364,16.364,0,0,0,2.359.826v-4.691a16.134,16.134,0,0,1-2.359-.838" transform="translate(-351.598 -546.009)" fill="#fffa13"/>
        <path id="Path_180433" data-name="Path 180433" d="M291.13,491.846q-.033,2.336-.067,4.673a2.9,2.9,0,0,0,1.368,2.233v-4.726a2.854,2.854,0,0,1-1.3-2.178" transform="translate(-278.93 -471.393)" fill="#fffa13"/>
        <path id="Path_180434" data-name="Path 180434" d="M669.191,385.7c3.293.682,5.489,1.847,6.285,3.129l.479-.158c-.891-1.3-3.155-2.468-6.505-3.161-.783-.163-1.588-.29-2.4-.388l-.508.168c.9.094,1.79.231,2.652.409" transform="translate(-638.754 -369.107)" fill="#f0a400"/>
        <path id="Path_180435" data-name="Path 180435" d="M303.7,510.878c-3.694-.765-6.008-2.136-6.513-3.6l-.508.168c.629,1.477,3.018,2.847,6.763,3.63q.608.126,1.233.225l.479-.157q-.736-.109-1.455-.258" transform="translate(-284.316 -486.182)" fill="#f0a400"/>
        <path id="Path_180436" data-name="Path 180436" d="M812.723,484.528a1.6,1.6,0,0,0-.183-.468,1.442,1.442,0,0,1,.1.494Z" transform="translate(-778.668 -463.93)" fill="#fff511"/>
        <path id="Path_180437" data-name="Path 180437" d="M425.477,546.269a13,13,0,0,1-2.937-.939,12.275,12.275,0,0,0,3.2,1.064,21.214,21.214,0,0,0,2.595.357l.243-.081a21.411,21.411,0,0,1-3.112-.4" transform="translate(-404.926 -522.652)" fill="#fff511"/>
        <path id="Path_180438" data-name="Path 180438" d="M294.469,382.291c2.611-1.916,8.345-2.787,13.68-2.223l.508-.168c-5.7-.672-11.906.235-14.7,2.287-1.281.939-1.649,1.977-1.228,2.968l.508-.168c-.313-.9.063-1.841,1.227-2.695" transform="translate(-280.369 -363.892)" fill="#f0b500"/>
        <path id="Path_180439" data-name="Path 180439" d="M310.235,387.77c1.708-1.257,5.179-1.924,8.7-1.809l2.551-.841c-5.343-.565-11.077.306-13.68,2.223-1.164.854-1.54,1.793-1.227,2.695l2.663-.871a2.217,2.217,0,0,1,1-1.389" transform="translate(-293.701 -368.943)" fill="#ffff89"/>
        <path id="Path_180440" data-name="Path 180440" d="M382.426,468.456a2.3,2.3,0,0,0-.487.611l.068-.023a2.083,2.083,0,0,1,.419-.588" transform="translate(-366.018 -448.975)" fill="#ca7700"/>
        <path id="Path_180441" data-name="Path 180441" d="M384.278,418.359a3.461,3.461,0,0,0-.453.385,2.081,2.081,0,0,0-.421.588l8.1-2.673.194-.064a14.276,14.276,0,0,0-7.422,1.764" transform="translate(-367.423 -399.262)" fill="#ffff89"/>
        <path id="Path_180442" data-name="Path 180442" d="M504.359,473.735c1.482-1.089,1.747-2.307.972-3.429l-.479.158c.646,1.042.368,2.163-1,3.166-2.809,2.06-9.225,2.912-14.885,2.07l-.479.157c5.993.954,12.883.068,15.863-2.123" transform="translate(-468.126 -450.748)" fill="#ca7700"/>
        <path id="Path_180443" data-name="Path 180443" d="M512.629,476.907c-2,1.423-6.358,2.078-10.449,1.681l-2.2.726c5.658.842,12.079-.01,14.885-2.07,1.368-1,1.648-2.124,1-3.166l-2.3.758c.274.693-.009,1.414-.931,2.072" transform="translate(-479.133 -454.362)" fill="#ffe400"/>
        <path id="Path_180444" data-name="Path 180444" d="M572.439,495.339l-.083.028-10.309,3.4-.243.081c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.811" transform="translate(-538.384 -474.74)" fill="#ffe400"/>
        <path id="Path_180445" data-name="Path 180445" d="M493.525,449.128a9.825,9.825,0,0,1,4.452.3c.451.155,1.007.164,1.221.29v.319l-2.037.29a3.06,3.06,0,0,0-.7-.327,4.769,4.769,0,0,0-2.222-.135c-1.528.215-1.841.963-1.262,1.562a4.657,4.657,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.674a1.437,1.437,0,0,0,0-.726l.93.081,1.019-.125a.928.928,0,0,1,.173.846c-.213.63-1.233,1.1-2.868,1.333a10.775,10.775,0,0,1-3.988-.184,5.117,5.117,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.161-1.46,4.278,4.278,0,0,1,2.719-1.128" transform="translate(-470.126 -430.329)" fill="#ca7700"/>
        <path id="Path_180446" data-name="Path 180446" d="M490.715,439.979a9.827,9.827,0,0,1,4.453.3,4.395,4.395,0,0,1,1.219.616l-2.037.29a3.12,3.12,0,0,0-.7-.327,4.766,4.766,0,0,0-2.222-.135c-1.527.215-1.841.963-1.26,1.562a4.654,4.654,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.674a.588.588,0,0,0-.086-.5l1.01-.145,1.019-.125a.926.926,0,0,1,.175.845c-.213.63-1.231,1.1-2.868,1.334a10.78,10.78,0,0,1-3.988-.184,5.113,5.113,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.163-1.461,4.289,4.289,0,0,1,2.727-1.125" transform="translate(-467.428 -421.561)" fill="#ffff89"/>
        <path id="Path_180447" data-name="Path 180447" d="M493.417,441.471a9.824,9.824,0,0,1,4.452.3,4.392,4.392,0,0,1,1.221.616l-2.037.29a3.075,3.075,0,0,0-.7-.327,4.751,4.751,0,0,0-2.222-.135c-1.528.215-1.841.963-1.262,1.561a4.666,4.666,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.674a.59.59,0,0,0-.084-.5l1.01-.145,1.019-.125a.928.928,0,0,1,.173.846c-.213.63-1.233,1.1-2.868,1.333a10.794,10.794,0,0,1-3.988-.184,5.117,5.117,0,0,1-2.363-1.151,1.042,1.042,0,0,1-.163-1.461,4.285,4.285,0,0,1,2.718-1.127" transform="translate(-470.018 -422.991)" fill="#f0b500"/>
        <path id="Path_180448" data-name="Path 180448" d="M316.519,365.632q-.033,2.337-.067,4.672a2.852,2.852,0,0,1-1.4,2.157c-3.194,2.345-10.851,3.194-17.1,1.9-4.421-.916-6.953-2.66-6.929-4.432q.033-2.337.067-4.672c-.025,1.771,2.507,3.515,6.929,4.432,6.251,1.3,13.907.446,17.1-1.9a2.852,2.852,0,0,0,1.394-2.159" transform="translate(-278.893 -350.067)" fill="#f0b500"/>
        <path id="Path_180449" data-name="Path 180449" d="M311.142,253.647c-6.258-1.3-13.909-.446-17.1,1.9s-.717,5.295,5.53,6.59,13.907.446,17.1-1.9.716-5.295-5.53-6.591" transform="translate(-280.445 -242.518)" fill="#f0b500"/>
        <path id="Path_180450" data-name="Path 180450" d="M323.982,258.954c-5.967-1.237-13.286-.425-16.339,1.815s-.685,5.058,5.286,6.3,13.286.425,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-293.541 -247.631)" fill="#fff511"/>
        <path id="Path_180451" data-name="Path 180451" d="M391.313,291.965c4.618.929,6.447,3.049,4.087,4.724s-8.016,2.29-12.634,1.36-6.447-3.049-4.088-4.724,8.016-2.29,12.634-1.36" transform="translate(-361.905 -279.408)" fill="#ffff89"/>
        <path id="Path_180452" data-name="Path 180452" d="M385.734,282.81c4.535.942,6.334,3.089,4.014,4.791s-7.873,2.323-12.41,1.381-6.333-3.089-4.015-4.791,7.875-2.323,12.409-1.381" transform="translate(-356.79 -270.628)" fill="#ca7700"/>
        <path id="Path_180453" data-name="Path 180453" d="M393.656,290.611c4.481.915,6.258,3,3.967,4.646s-7.78,2.253-12.26,1.339-6.258-3-3.967-4.646,7.78-2.253,12.261-1.339" transform="translate(-364.539 -278.116)" fill="#fff511"/>
        <path id="Path_180454" data-name="Path 180454" d="M728.892,467.655v-4.646a21.8,21.8,0,0,1-2.62.589v4.66a21.621,21.621,0,0,0,2.62-.6" transform="translate(-695.997 -443.755)" fill="#dc8e00"/>
        <path id="Path_180455" data-name="Path 180455" d="M864.715,381.162a2.852,2.852,0,0,0,1.4-2.158q.032-2.336.067-4.672a2.852,2.852,0,0,1-1.394,2.159l-.191.134v4.625c.041-.029.083-.057.122-.087" transform="translate(-828.552 -358.766)" fill="#dc8e00"/>
        <path id="Path_180456" data-name="Path 180456" d="M424.961,463.645q-.779-.163-1.477-.356v4.691q.668.182,1.409.337c.409.086.824.16,1.242.226V463.86c-.394-.064-.787-.135-1.173-.215" transform="translate(-405.831 -444.023)" fill="#ffde00"/>
        <path id="Path_180457" data-name="Path 180457" d="M792.261,434.049v-4.625a11.407,11.407,0,0,1-3.146,1.392v4.646a11.136,11.136,0,0,0,3.146-1.418" transform="translate(-756.219 -411.566)" fill="#ca7700"/>
        <path id="Path_180458" data-name="Path 180458" d="M323.88,417.7v4.726a8.887,8.887,0,0,0,1.793,1.035v-4.7a8.682,8.682,0,0,1-1.793-1.058" transform="translate(-310.379 -400.325)" fill="#ffff89"/>
        <path id="Path_180459" data-name="Path 180459" d="M366.892,443.145v4.7a16.374,16.374,0,0,0,2.359.828v-4.689a16.2,16.2,0,0,1-2.359-.838" transform="translate(-351.598 -424.717)" fill="#fffa13"/>
        <path id="Path_180460" data-name="Path 180460" d="M291.13,365.256q-.033,2.336-.067,4.672a2.9,2.9,0,0,0,1.368,2.233v-4.726a2.852,2.852,0,0,1-1.3-2.178" transform="translate(-278.93 -350.067)" fill="#fffa13"/>
        <path id="Path_180461" data-name="Path 180461" d="M669.191,258.863c3.293.682,5.489,1.847,6.285,3.129l.479-.158c-.891-1.3-3.155-2.468-6.505-3.161-.783-.161-1.588-.29-2.4-.386l-.508.167c.9.1,1.79.231,2.652.41" transform="translate(-638.754 -247.546)" fill="#f0a400"/>
        <path id="Path_180462" data-name="Path 180462" d="M303.7,384.289c-3.694-.765-6.008-2.137-6.513-3.6l-.508.167c.629,1.477,3.018,2.847,6.763,3.63q.608.125,1.233.225l.479-.158q-.736-.11-1.455-.257" transform="translate(-284.316 -364.857)" fill="#f0a400"/>
        <path id="Path_180463" data-name="Path 180463" d="M812.723,357.936a1.592,1.592,0,0,0-.183-.466,1.437,1.437,0,0,1,.1.494Z" transform="translate(-778.668 -342.604)" fill="#fff511"/>
        <path id="Path_180464" data-name="Path 180464" d="M425.477,419.787a13.031,13.031,0,0,1-2.937-.941,12.26,12.26,0,0,0,3.2,1.064,21.161,21.161,0,0,0,2.595.357l.243-.08a21.419,21.419,0,0,1-3.112-.4" transform="translate(-404.926 -401.428)" fill="#fff511"/>
        <path id="Path_180465" data-name="Path 180465" d="M294.469,255.771c2.611-1.916,8.345-2.787,13.68-2.223l.508-.167c-5.7-.672-11.906.235-14.7,2.285-1.281.939-1.649,1.977-1.228,2.968l.508-.167c-.313-.9.063-1.841,1.227-2.695" transform="translate(-280.369 -242.634)" fill="#f0b500"/>
        <path id="Path_180466" data-name="Path 180466" d="M310.235,261.192c1.708-1.257,5.179-1.924,8.7-1.809l2.551-.841c-5.343-.563-11.077.306-13.68,2.223-1.164.854-1.54,1.792-1.227,2.695l2.663-.871a2.219,2.219,0,0,1,1-1.389" transform="translate(-293.701 -247.629)" fill="#ffff89"/>
        <path id="Path_180467" data-name="Path 180467" d="M382.426,341.9a2.3,2.3,0,0,0-.487.611l.068-.023a2.086,2.086,0,0,1,.419-.588" transform="translate(-366.018 -327.681)" fill="#ca7700"/>
        <path id="Path_180468" data-name="Path 180468" d="M384.278,291.837a3.413,3.413,0,0,0-.453.385,2.091,2.091,0,0,0-.421.588l8.1-2.671.194-.064a14.276,14.276,0,0,0-7.422,1.764" transform="translate(-367.423 -278.002)" fill="#ffff89"/>
        <path id="Path_180469" data-name="Path 180469" d="M504.359,347.145c1.482-1.087,1.747-2.307.972-3.429l-.479.158c.646,1.042.368,2.163-1,3.166-2.809,2.06-9.225,2.912-14.885,2.07l-.479.158c5.993.954,12.883.068,15.863-2.124" transform="translate(-468.126 -329.422)" fill="#ca7700"/>
        <path id="Path_180470" data-name="Path 180470" d="M512.629,350.351c-2,1.423-6.358,2.078-10.449,1.681l-2.2.726c5.658.842,12.079-.01,14.885-2.07,1.368-1,1.648-2.124,1-3.166l-2.3.758c.274.692-.009,1.415-.931,2.072" transform="translate(-479.133 -333.069)" fill="#ffe400"/>
        <path id="Path_180471" data-name="Path 180471" d="M572.439,368.573l-.083.028L562.046,372l-.243.08c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.81" transform="translate(-538.384 -353.246)" fill="#ffe400"/>
        <path id="Path_180472" data-name="Path 180472" d="M493.525,322.574a9.824,9.824,0,0,1,4.452.3c.451.155,1.007.164,1.221.29v.319l-2.037.29a3.1,3.1,0,0,0-.7-.327,4.759,4.759,0,0,0-2.222-.135c-1.528.215-1.841.962-1.262,1.562a4.665,4.665,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.674a1.437,1.437,0,0,0,0-.726l.93.081,1.019-.125a.927.927,0,0,1,.173.846c-.213.63-1.233,1.1-2.868,1.333a10.77,10.77,0,0,1-3.988-.183,5.123,5.123,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.161-1.461,4.279,4.279,0,0,1,2.719-1.127" transform="translate(-470.126 -309.038)" fill="#ca7700"/>
        <path id="Path_180473" data-name="Path 180473" d="M490.728,313.559a9.814,9.814,0,0,1,4.453.3,4.406,4.406,0,0,1,1.219.616l-2.037.29a3.111,3.111,0,0,0-.7-.327,4.762,4.762,0,0,0-2.222-.135c-1.527.215-1.841.963-1.26,1.562a4.655,4.655,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.674a.579.579,0,0,0-.086-.5l1.01-.145,1.019-.125a.926.926,0,0,1,.175.845c-.212.63-1.231,1.1-2.868,1.334a10.775,10.775,0,0,1-3.988-.184,5.11,5.11,0,0,1-2.363-1.15,1.042,1.042,0,0,1-.163-1.46,4.284,4.284,0,0,1,2.718-1.127" transform="translate(-467.441 -300.397)" fill="#ffff89"/>
        <path id="Path_180474" data-name="Path 180474" d="M493.417,314.88a9.821,9.821,0,0,1,4.452.3,4.381,4.381,0,0,1,1.221.616l-2.037.29a3.065,3.065,0,0,0-.7-.327,4.759,4.759,0,0,0-2.222-.135c-1.528.215-1.841.963-1.262,1.562a4.656,4.656,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.674a.59.59,0,0,0-.084-.5l1.01-.145,1.019-.125a.926.926,0,0,1,.173.845c-.213.63-1.233,1.1-2.868,1.333a10.782,10.782,0,0,1-3.988-.184,5.119,5.119,0,0,1-2.363-1.151A1.042,1.042,0,0,1,490.69,316a4.284,4.284,0,0,1,2.718-1.127" transform="translate(-470.018 -301.663)" fill="#f0b500"/>
        <path id="Path_180475" data-name="Path 180475" d="M316.519,239.076q-.033,2.337-.067,4.673a2.854,2.854,0,0,1-1.4,2.157c-3.194,2.343-10.851,3.194-17.1,1.9-4.421-.916-6.953-2.661-6.929-4.432q.033-2.336.067-4.672c-.025,1.773,2.507,3.516,6.929,4.432,6.251,1.3,13.907.446,17.1-1.9a2.851,2.851,0,0,0,1.394-2.159" transform="translate(-278.893 -228.775)" fill="#f0b500"/>
        <path id="Path_180476" data-name="Path 180476" d="M311.142,127.162c-6.258-1.3-13.909-.446-17.1,1.9s-.717,5.295,5.53,6.59,13.907.446,17.1-1.9.716-5.295-5.53-6.59" transform="translate(-280.445 -121.294)" fill="#f0b500"/>
        <path id="Path_180477" data-name="Path 180477" d="M323.982,132.347c-5.967-1.238-13.286-.425-16.339,1.813s-.685,5.058,5.286,6.3,13.286.425,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-293.541 -126.288)" fill="#fff511"/>
        <path id="Path_180478" data-name="Path 180478" d="M391.313,165.375c4.618.928,6.447,3.049,4.087,4.724s-8.016,2.289-12.634,1.36-6.447-3.049-4.088-4.724,8.016-2.29,12.634-1.36" transform="translate(-361.905 -158.083)" fill="#ffff89"/>
        <path id="Path_180479" data-name="Path 180479" d="M385.734,156.221c4.535.942,6.334,3.089,4.014,4.791s-7.873,2.323-12.41,1.381S371,159.3,373.323,157.6s7.875-2.323,12.409-1.381" transform="translate(-356.79 -149.303)" fill="#ca7700"/>
        <path id="Path_180480" data-name="Path 180480" d="M393.656,164.036c4.481.913,6.258,3,3.967,4.646s-7.78,2.253-12.26,1.339-6.258-3-3.967-4.646,7.78-2.252,12.261-1.339" transform="translate(-364.539 -156.805)" fill="#fff511"/>
        <path id="Path_180481" data-name="Path 180481" d="M728.892,341.1v-4.646a21.953,21.953,0,0,1-2.62.589v4.66a21.527,21.527,0,0,0,2.62-.6" transform="translate(-695.997 -322.463)" fill="#dc8e00"/>
        <path id="Path_180482" data-name="Path 180482" d="M864.715,254.54a2.853,2.853,0,0,0,1.4-2.157q.032-2.337.067-4.673a2.85,2.85,0,0,1-1.394,2.159c-.062.045-.126.09-.191.134v4.626l.122-.087" transform="translate(-828.552 -237.408)" fill="#dc8e00"/>
        <path id="Path_180483" data-name="Path 180483" d="M424.961,337.053q-.779-.161-1.477-.354v4.691q.668.183,1.409.337c.409.084.824.16,1.242.226V337.27c-.394-.064-.787-.135-1.173-.216" transform="translate(-405.831 -322.697)" fill="#ffde00"/>
        <path id="Path_180484" data-name="Path 180484" d="M792.261,307.461v-4.628a11.4,11.4,0,0,1-3.146,1.392v4.646a11.152,11.152,0,0,0,3.146-1.418" transform="translate(-756.219 -290.24)" fill="#ca7700"/>
        <path id="Path_180485" data-name="Path 180485" d="M323.88,291.1v4.726a8.929,8.929,0,0,0,1.793,1.035v-4.7a8.692,8.692,0,0,1-1.793-1.058" transform="translate(-310.379 -278.998)" fill="#ffff89"/>
        <path id="Path_180486" data-name="Path 180486" d="M366.892,316.553v4.7a16.4,16.4,0,0,0,2.359.826v-4.689a16.24,16.24,0,0,1-2.359-.839" transform="translate(-351.598 -303.389)" fill="#fffa13"/>
        <path id="Path_180487" data-name="Path 180487" d="M291.13,238.666q-.033,2.337-.067,4.673a2.9,2.9,0,0,0,1.368,2.233v-4.726a2.853,2.853,0,0,1-1.3-2.178" transform="translate(-278.93 -228.741)" fill="#fffa13"/>
        <path id="Path_180488" data-name="Path 180488" d="M669.191,132.447c3.293.682,5.489,1.847,6.285,3.129l.479-.157c-.891-1.3-3.155-2.468-6.505-3.161-.783-.163-1.588-.29-2.4-.386l-.508.167c.9.094,1.79.231,2.652.409" transform="translate(-638.754 -126.388)" fill="#f0a400"/>
        <path id="Path_180489" data-name="Path 180489" d="M303.7,257.7c-3.694-.766-6.008-2.137-6.513-3.6l-.508.167c.629,1.478,3.018,2.847,6.763,3.63q.608.126,1.233.225l.479-.158q-.736-.109-1.455-.257" transform="translate(-284.316 -243.531)" fill="#f0a400"/>
        <path id="Path_180490" data-name="Path 180490" d="M812.723,231.453a1.592,1.592,0,0,0-.183-.466,1.432,1.432,0,0,1,.1.494Z" transform="translate(-778.668 -221.381)" fill="#fff511"/>
        <path id="Path_180491" data-name="Path 180491" d="M425.477,293.2a12.986,12.986,0,0,1-2.937-.941,12.27,12.27,0,0,0,3.2,1.064,21.178,21.178,0,0,0,2.595.357l.243-.081a21.411,21.411,0,0,1-3.112-.4" transform="translate(-404.926 -280.102)" fill="#fff511"/>
        <path id="Path_180492" data-name="Path 180492" d="M294.469,129.177c2.611-1.916,8.345-2.787,13.68-2.223l.508-.167c-5.7-.672-11.906.234-14.7,2.285-1.281.941-1.649,1.977-1.228,2.968l.508-.167c-.313-.9.063-1.841,1.227-2.695" transform="translate(-280.369 -121.305)" fill="#f0b500"/>
        <path id="Path_180493" data-name="Path 180493" d="M310.235,134.556c1.708-1.257,5.179-1.924,8.7-1.809l2.551-.842c-5.343-.565-11.077.306-13.68,2.223-1.164.854-1.54,1.792-1.227,2.695l2.663-.871a2.22,2.22,0,0,1,1-1.389" transform="translate(-293.701 -126.257)" fill="#ffff89"/>
        <path id="Path_180494" data-name="Path 180494" d="M382.426,215.31a2.291,2.291,0,0,0-.487.611l.068-.023a2.079,2.079,0,0,1,.419-.588" transform="translate(-366.018 -206.356)" fill="#ca7700"/>
        <path id="Path_180495" data-name="Path 180495" d="M384.278,165.176a3.414,3.414,0,0,0-.453.385,2.081,2.081,0,0,0-.421.588l8.1-2.673.194-.064a14.272,14.272,0,0,0-7.422,1.763" transform="translate(-367.423 -156.607)" fill="#ffff89"/>
        <path id="Path_180496" data-name="Path 180496" d="M504.359,220.556c1.482-1.089,1.747-2.307.972-3.429l-.479.157c.646,1.042.368,2.163-1,3.166-2.809,2.06-9.225,2.912-14.885,2.07l-.479.158c5.993.954,12.883.068,15.863-2.124" transform="translate(-468.126 -208.098)" fill="#ca7700"/>
        <path id="Path_180497" data-name="Path 180497" d="M512.629,223.762c-2,1.423-6.358,2.078-10.449,1.681l-2.2.726c5.658.842,12.079-.01,14.885-2.07,1.368-1,1.648-2.124,1-3.166l-2.3.758c.274.692-.009,1.415-.931,2.072" transform="translate(-479.133 -211.744)" fill="#ffe400"/>
        <path id="Path_180498" data-name="Path 180498" d="M572.439,242.122l-.083.028-10.309,3.4-.243.081c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.81" transform="translate(-538.384 -232.053)" fill="#ffe400"/>
        <path id="Path_180499" data-name="Path 180499" d="M493.525,195.97a9.821,9.821,0,0,1,4.452.3c.451.155,1.007.164,1.221.29v.319l-2.037.29a3.124,3.124,0,0,0-.7-.327,4.769,4.769,0,0,0-2.222-.135c-1.528.215-1.841.963-1.262,1.562a4.665,4.665,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.674a1.438,1.438,0,0,0,0-.726l.93.081,1.019-.125a.925.925,0,0,1,.173.845c-.213.63-1.233,1.1-2.868,1.333a10.782,10.782,0,0,1-3.988-.184,5.119,5.119,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.161-1.461,4.28,4.28,0,0,1,2.719-1.127" transform="translate(-470.126 -187.699)" fill="#ca7700"/>
        <path id="Path_180500" data-name="Path 180500" d="M490.728,186.977a9.83,9.83,0,0,1,4.453.3,4.413,4.413,0,0,1,1.219.616l-2.037.29a3.092,3.092,0,0,0-.7-.327,4.76,4.76,0,0,0-2.222-.136c-1.527.215-1.841.963-1.26,1.562a4.653,4.653,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.672a.579.579,0,0,0-.086-.5l1.01-.145,1.019-.125a.927.927,0,0,1,.175.846c-.212.629-1.231,1.1-2.868,1.333a10.768,10.768,0,0,1-3.988-.184,5.117,5.117,0,0,1-2.363-1.151A1.042,1.042,0,0,1,488,188.1a4.286,4.286,0,0,1,2.718-1.128" transform="translate(-467.441 -179.08)" fill="#ffff89"/>
        <path id="Path_180501" data-name="Path 180501" d="M493.417,188.333a9.817,9.817,0,0,1,4.452.3,4.39,4.39,0,0,1,1.221.616l-2.037.29a3.087,3.087,0,0,0-.7-.327,4.764,4.764,0,0,0-2.222-.135c-1.528.215-1.841.963-1.262,1.562a4.655,4.655,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.674a.592.592,0,0,0-.084-.5l1.01-.145,1.019-.125a.926.926,0,0,1,.173.845c-.213.63-1.233,1.1-2.868,1.334a10.781,10.781,0,0,1-3.988-.184,5.114,5.114,0,0,1-2.363-1.151,1.042,1.042,0,0,1-.163-1.461,4.284,4.284,0,0,1,2.718-1.127" transform="translate(-470.018 -180.38)" fill="#f0b500"/>
        <path id="Path_180502" data-name="Path 180502" d="M316.519,112.488q-.033,2.337-.067,4.673a2.854,2.854,0,0,1-1.4,2.159c-3.194,2.343-10.851,3.194-17.1,1.9-4.421-.916-6.953-2.661-6.929-4.432q.033-2.337.067-4.672c-.025,1.771,2.507,3.516,6.929,4.432,6.251,1.3,13.907.446,17.1-1.9a2.852,2.852,0,0,0,1.394-2.159" transform="translate(-278.893 -107.451)" fill="#f0b500"/>
        <path id="Path_180503" data-name="Path 180503" d="M311.142.605c-6.258-1.3-13.909-.446-17.1,1.9s-.717,5.295,5.53,6.59,13.907.446,17.1-1.9.716-5.295-5.53-6.59" transform="translate(-280.445 0)" fill="#f0b500"/>
        <path id="Path_180504" data-name="Path 180504" d="M323.982,5.737C318.015,4.5,310.7,5.311,307.643,7.55s-.685,5.058,5.286,6.3,13.286.425,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-293.541 -4.944)" fill="#fff511"/>
        <path id="Path_180505" data-name="Path 180505" d="M391.313,38.785c4.618.929,6.447,3.049,4.087,4.724s-8.016,2.289-12.634,1.36-6.447-3.049-4.088-4.724,8.016-2.289,12.634-1.36" transform="translate(-361.905 -36.756)" fill="#ffff89"/>
        <path id="Path_180506" data-name="Path 180506" d="M385.734,29.632c4.535.942,6.334,3.088,4.014,4.791s-7.873,2.323-12.41,1.381-6.333-3.089-4.015-4.791,7.875-2.323,12.409-1.381" transform="translate(-356.79 -27.978)" fill="#ca7700"/>
        <path id="Path_180507" data-name="Path 180507" d="M393.656,37.47c4.481.913,6.258,3,3.967,4.646s-7.78,2.253-12.26,1.34-6.258-3-3.967-4.646,7.78-2.253,12.261-1.339" transform="translate(-364.539 -35.504)" fill="#fff511"/>
        <path id="Path_180508" data-name="Path 180508" d="M728.892,214.545V209.9a21.667,21.667,0,0,1-2.62.589v4.662a21.713,21.713,0,0,0,2.62-.6" transform="translate(-695.997 -201.17)" fill="#dc8e00"/>
        <path id="Path_180509" data-name="Path 180509" d="M864.715,127.949a2.853,2.853,0,0,0,1.4-2.159q.032-2.336.067-4.672a2.85,2.85,0,0,1-1.394,2.159c-.062.045-.126.09-.191.134v4.625l.122-.087" transform="translate(-828.552 -116.081)" fill="#dc8e00"/>
        <path id="Path_180510" data-name="Path 180510" d="M424.961,210.5q-.779-.161-1.477-.356v4.691q.668.183,1.409.337c.409.084.824.16,1.242.226v-4.684c-.394-.064-.787-.135-1.173-.216" transform="translate(-405.831 -201.405)" fill="#ffde00"/>
        <path id="Path_180511" data-name="Path 180511" d="M792.261,180.868v-4.623a11.365,11.365,0,0,1-3.146,1.392v4.646a11.144,11.144,0,0,0,3.146-1.416" transform="translate(-756.219 -168.916)" fill="#ca7700"/>
        <path id="Path_180512" data-name="Path 180512" d="M323.88,164.514v4.724a8.927,8.927,0,0,0,1.793,1.037v-4.7a8.679,8.679,0,0,1-1.793-1.057" transform="translate(-310.379 -157.673)" fill="#ffff89"/>
        <path id="Path_180513" data-name="Path 180513" d="M366.892,189.964v4.7a16.367,16.367,0,0,0,2.359.826V190.8a16.214,16.214,0,0,1-2.359-.839" transform="translate(-351.598 -182.064)" fill="#fffa13"/>
        <path id="Path_180514" data-name="Path 180514" d="M291.13,112.078q-.033,2.336-.067,4.673a2.9,2.9,0,0,0,1.368,2.233v-4.726a2.853,2.853,0,0,1-1.3-2.178" transform="translate(-278.93 -107.417)" fill="#fffa13"/>
        <path id="Path_180515" data-name="Path 180515" d="M669.191,5.823c3.293.682,5.489,1.847,6.285,3.129l.479-.157c-.891-1.3-3.155-2.468-6.505-3.161-.783-.163-1.588-.29-2.4-.388l-.508.167c.9.1,1.79.231,2.652.409" transform="translate(-638.754 -5.029)" fill="#f0a400"/>
        <path id="Path_180516" data-name="Path 180516" d="M303.7,131.144c-3.694-.767-6.008-2.137-6.513-3.6l-.508.167c.629,1.478,3.018,2.847,6.763,3.63q.608.126,1.233.225l.479-.157q-.736-.109-1.455-.257" transform="translate(-284.316 -122.239)" fill="#f0a400"/>
        <path id="Path_180517" data-name="Path 180517" d="M812.723,104.653a1.592,1.592,0,0,0-.183-.466,1.432,1.432,0,0,1,.1.494Z" transform="translate(-778.668 -99.854)" fill="#fff511"/>
        <path id="Path_180518" data-name="Path 180518" d="M425.477,166.608a12.975,12.975,0,0,1-2.937-.941,12.294,12.294,0,0,0,3.2,1.066,21.321,21.321,0,0,0,2.595.357l.243-.081a21.43,21.43,0,0,1-3.112-.4" transform="translate(-404.926 -158.778)" fill="#fff511"/>
        <path id="Path_180519" data-name="Path 180519" d="M294.469,2.661C297.08.744,302.814-.127,308.149.438l.508-.167c-5.7-.672-11.906.235-14.7,2.285-1.281.939-1.649,1.977-1.228,2.968l.508-.167c-.313-.9.063-1.841,1.227-2.695" transform="translate(-280.369 -0.05)" fill="#f0b500"/>
        <path id="Path_180520" data-name="Path 180520" d="M310.235,8.017c1.708-1.257,5.179-1.924,8.7-1.809l2.551-.842c-5.343-.563-11.077.308-13.68,2.223-1.164.854-1.54,1.792-1.227,2.695l2.663-.878a2.22,2.22,0,0,1,1-1.389" transform="translate(-293.701 -4.98)" fill="#ffff89"/>
        <path id="Path_180521" data-name="Path 180521" d="M382.426,88.72a2.286,2.286,0,0,0-.487.611l.068-.023a2.084,2.084,0,0,1,.419-.588" transform="translate(-366.018 -85.031)" fill="#ca7700"/>
        <path id="Path_180522" data-name="Path 180522" d="M384.278,38.586a3.442,3.442,0,0,0-.453.385,2.082,2.082,0,0,0-.421.588l8.1-2.673.194-.064a14.274,14.274,0,0,0-7.422,1.763" transform="translate(-367.423 -35.281)" fill="#ffff89"/>
        <path id="Path_180523" data-name="Path 180523" d="M504.359,94c1.482-1.089,1.747-2.307.972-3.429l-.479.157c.646,1.042.368,2.163-1,3.166-2.809,2.06-9.225,2.912-14.885,2.07l-.479.157c5.993.954,12.883.068,15.863-2.123" transform="translate(-468.126 -86.806)" fill="#ca7700"/>
        <path id="Path_180524" data-name="Path 180524" d="M512.629,97.171c-2,1.423-6.358,2.078-10.449,1.681l-2.2.726c5.658.841,12.079-.01,14.885-2.07,1.368-1,1.648-2.124,1-3.166l-2.3.758c.274.692-.009,1.415-.931,2.072" transform="translate(-479.133 -90.418)" fill="#ffe400"/>
        <path id="Path_180525" data-name="Path 180525" d="M572.439,115.534l-.083.028-10.309,3.4-.243.081c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.81" transform="translate(-538.384 -110.729)" fill="#ffe400"/>
        <path id="Path_180526" data-name="Path 180526" d="M25.528,1212.383q-.033,2.337-.067,4.673a2.851,2.851,0,0,1-1.4,2.157c-3.2,2.345-10.852,3.194-17.1,1.9-4.42-.918-6.954-2.661-6.927-4.434q.035-2.336.067-4.672c-.026,1.773,2.508,3.516,6.929,4.432,6.251,1.3,13.907.445,17.1-1.9a2.851,2.851,0,0,0,1.394-2.159" transform="translate(-0.034 -1161.606)" fill="#f0b500"/>
        <path id="Path_180527" data-name="Path 180527" d="M20.139,1100.5c-6.258-1.3-13.907-.446-17.1,1.9s-.717,5.295,5.53,6.59,13.907.446,17.1-1.9.716-5.295-5.53-6.59" transform="translate(-1.571 -1054.154)" fill="#f0b500"/>
        <path id="Path_180528" data-name="Path 180528" d="M32.957,1105.688c-5.967-1.238-13.286-.426-16.339,1.813s-.685,5.058,5.286,6.3,13.286.425,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-14.647 -1059.152)" fill="#fff511"/>
        <path id="Path_180529" data-name="Path 180529" d="M100.323,1138.678c4.616.929,6.447,3.049,4.087,4.724s-8.016,2.29-12.634,1.361-6.447-3.049-4.088-4.724,8.016-2.29,12.634-1.36" transform="translate(-83.045 -1090.91)" fill="#ffff89"/>
        <path id="Path_180530" data-name="Path 180530" d="M94.778,1129.561c4.535.942,6.334,3.089,4.015,4.791s-7.873,2.323-12.41,1.381-6.334-3.089-4.015-4.791,7.875-2.322,12.41-1.38" transform="translate(-77.964 -1082.166)" fill="#ca7700"/>
        <path id="Path_180531" data-name="Path 180531" d="M102.593,1137.338c4.481.913,6.258,2.995,3.967,4.646s-7.78,2.253-12.261,1.339-6.258-2.995-3.967-4.646,7.78-2.252,12.261-1.338" transform="translate(-85.607 -1089.633)" fill="#fff511"/>
        <path id="Path_180532" data-name="Path 180532" d="M437.9,1314.472v-4.646a21.929,21.929,0,0,1-2.62.589v4.66a21.847,21.847,0,0,0,2.62-.6" transform="translate(-417.139 -1255.357)" fill="#dc8e00"/>
        <path id="Path_180533" data-name="Path 180533" d="M573.691,1227.88a2.849,2.849,0,0,0,1.4-2.157q.033-2.338.067-4.674a2.85,2.85,0,0,1-1.394,2.159c-.062.045-.125.09-.191.134v4.625c.042-.029.083-.058.122-.087" transform="translate(-549.659 -1170.271)" fill="#dc8e00"/>
        <path id="Path_180534" data-name="Path 180534" d="M133.937,1310.394q-.779-.161-1.477-.355v4.692q.668.183,1.409.337c.409.084.824.16,1.241.226v-4.683c-.4-.064-.787-.135-1.173-.216" transform="translate(-126.938 -1255.56)" fill="#ffde00"/>
        <path id="Path_180535" data-name="Path 180535" d="M501.27,1280.762v-4.625a11.406,11.406,0,0,1-3.145,1.392v4.646a11.159,11.159,0,0,0,3.145-1.419" transform="translate(-477.36 -1223.068)" fill="#ca7700"/>
        <path id="Path_180536" data-name="Path 180536" d="M32.856,1264.376v4.721a8.933,8.933,0,0,0,1.793,1.035v-4.7a8.638,8.638,0,0,1-1.793-1.056" transform="translate(-31.486 -1211.794)" fill="#ffff89"/>
        <path id="Path_180537" data-name="Path 180537" d="M75.868,1289.857v4.7a16.554,16.554,0,0,0,2.359.826V1290.7a16.091,16.091,0,0,1-2.359-.838" transform="translate(-72.705 -1236.218)" fill="#fffa13"/>
        <path id="Path_180538" data-name="Path 180538" d="M.106,1212l-.067,4.672a2.9,2.9,0,0,0,1.368,2.233v-4.726a2.855,2.855,0,0,1-1.3-2.177" transform="translate(-0.037 -1161.603)" fill="#fffa13"/>
        <path id="Path_180539" data-name="Path 180539" d="M378.167,1105.719c3.293.682,5.49,1.847,6.287,3.129l.477-.157c-.891-1.3-3.155-2.468-6.505-3.161-.783-.162-1.588-.29-2.4-.388l-.508.167c.9.094,1.79.231,2.652.41" transform="translate(-359.862 -1059.184)" fill="#f0a400"/>
        <path id="Path_180540" data-name="Path 180540" d="M12.68,1231.039c-3.694-.767-6.008-2.137-6.513-3.6l-.508.173c.629,1.478,3.018,2.847,6.763,3.63q.608.125,1.233.225l.479-.158q-.738-.109-1.455-.257" transform="translate(-5.424 -1176.395)" fill="#f0a400"/>
        <path id="Path_180541" data-name="Path 180541" d="M521.7,1204.683a1.55,1.55,0,0,0-.183-.466,1.447,1.447,0,0,1,.1.494Z" transform="translate(-499.775 -1154.139)" fill="#fff511"/>
        <path id="Path_180542" data-name="Path 180542" d="M134.486,1266.534a12.951,12.951,0,0,1-2.935-.941,12.239,12.239,0,0,0,3.2,1.064,21.28,21.28,0,0,0,2.595.357l.245-.081a21.488,21.488,0,0,1-3.112-.4" transform="translate(-126.067 -1212.963)" fill="#fff511"/>
        <path id="Path_180543" data-name="Path 180543" d="M3.593,1102.518c2.612-1.916,8.345-2.787,13.68-2.223l.508-.167c-5.695-.672-11.906.235-14.7,2.285-1.276.939-1.643,1.983-1.224,2.968l.508-.167c-.311-.9.064-1.84,1.227-2.694" transform="translate(-1.622 -1054.169)" fill="#f0b500"/>
        <path id="Path_180544" data-name="Path 180544" d="M19.246,1107.894c1.71-1.257,5.179-1.924,8.7-1.809l2.551-.842c-5.342-.565-11.075.306-13.68,2.223-1.164.854-1.54,1.792-1.227,2.695l2.663-.871a2.219,2.219,0,0,1,1-1.389" transform="translate(-14.842 -1059.118)" fill="#ffff89"/>
        <path id="Path_180545" data-name="Path 180545" d="M91.368,1188.579a2.328,2.328,0,0,0-.488.611l.068-.023a2.087,2.087,0,0,1,.419-.589" transform="translate(-87.092 -1139.151)" fill="#ca7700"/>
        <path id="Path_180546" data-name="Path 180546" d="M93.288,1138.482a3.536,3.536,0,0,0-.453.384,2.092,2.092,0,0,0-.419.589l8.1-2.673.194-.064a14.3,14.3,0,0,0-7.422,1.762" transform="translate(-88.564 -1089.438)" fill="#ffff89"/>
        <path id="Path_180547" data-name="Path 180547" d="M213.3,1193.894c1.481-1.088,1.746-2.307.972-3.429l-.477.157c.646,1.043.367,2.163-1,3.167-2.809,2.06-9.225,2.912-14.885,2.07l-.479.159c5.992.954,12.881.068,15.863-2.124" transform="translate(-189.2 -1140.959)" fill="#ca7700"/>
        <path id="Path_180548" data-name="Path 180548" d="M221.571,1197.1c-2,1.423-6.358,2.078-10.451,1.681l-2.2.726c5.658.842,12.079-.01,14.885-2.07,1.368-1.005,1.646-2.124,1-3.166l-2.3.758c.272.693-.01,1.415-.931,2.072" transform="translate(-200.207 -1144.608)" fill="#ffe400"/>
        <path id="Path_180549" data-name="Path 180549" d="M281.347,1215.426l-.083.028-10.309,3.4-.245.081c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.81" transform="translate(-259.425 -1164.882)" fill="#ffe400"/>
        <path id="Path_180550" data-name="Path 180550" d="M202.546,1169.306a9.808,9.808,0,0,1,4.452.3c.451.155,1.007.164,1.221.29v.319l-2.037.291a3.116,3.116,0,0,0-.7-.327,4.768,4.768,0,0,0-2.222-.135c-1.528.214-1.841.962-1.262,1.562a4.666,4.666,0,0,0,3.618.871c.888-.125,1.354-.339,1.467-.674a1.433,1.433,0,0,0,0-.726l.931.081,1.019-.125a.929.929,0,0,1,.175.846c-.212.629-1.233,1.1-2.87,1.333a10.783,10.783,0,0,1-3.986-.184,5.116,5.116,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.163-1.46,4.285,4.285,0,0,1,2.719-1.127" transform="translate(-191.279 -1120.558)" fill="#ca7700"/>
        <path id="Path_180551" data-name="Path 180551" d="M199.773,1160.312a9.828,9.828,0,0,1,4.453.3,4.4,4.4,0,0,1,1.221.616l-2.037.291a3.074,3.074,0,0,0-.7-.327,4.754,4.754,0,0,0-2.222-.137c-1.527.215-1.841.962-1.26,1.562a4.656,4.656,0,0,0,3.618.871c.888-.125,1.354-.338,1.467-.674a.588.588,0,0,0-.086-.5l1.01-.145,1.019-.125a.929.929,0,0,1,.175.846c-.212.629-1.231,1.1-2.868,1.333a10.758,10.758,0,0,1-3.988-.185,5.112,5.112,0,0,1-2.363-1.151,1.042,1.042,0,0,1-.163-1.461,4.287,4.287,0,0,1,2.718-1.126" transform="translate(-188.616 -1111.939)" fill="#ffff89"/>
        <path id="Path_180552" data-name="Path 180552" d="M202.412,1161.636a9.808,9.808,0,0,1,4.452.3,4.386,4.386,0,0,1,1.221.615l-2.037.291a3.074,3.074,0,0,0-.7-.327,4.768,4.768,0,0,0-2.222-.135c-1.528.215-1.841.963-1.262,1.563a4.667,4.667,0,0,0,3.618.871c.888-.125,1.353-.339,1.467-.674a.59.59,0,0,0-.086-.5l1.01-.145,1.019-.125a.927.927,0,0,1,.175.847c-.212.63-1.231,1.1-2.868,1.334a10.76,10.76,0,0,1-3.986-.184,5.111,5.111,0,0,1-2.363-1.15,1.041,1.041,0,0,1-.163-1.461,4.283,4.283,0,0,1,2.719-1.127" transform="translate(-191.145 -1113.207)" fill="#f0b500"/>
        <path id="Path_180553" data-name="Path 180553" d="M25.5,1085.895q-.033,2.337-.067,4.673a2.849,2.849,0,0,1-1.4,2.157c-3.2,2.344-10.852,3.194-17.1,1.9-4.42-.917-6.954-2.661-6.929-4.433q.035-2.336.067-4.672c-.026,1.771,2.507,3.516,6.929,4.433,6.251,1.3,13.907.446,17.1-1.9a2.853,2.853,0,0,0,1.394-2.159" transform="translate(-0.001 -1040.378)" fill="#f0b500"/>
        <path id="Path_180554" data-name="Path 180554" d="M20.139,973.944c-6.258-1.3-13.907-.446-17.1,1.9s-.717,5.295,5.53,6.59,13.907.446,17.1-1.9.716-5.3-5.53-6.59" transform="translate(-1.571 -932.862)" fill="#f0b500"/>
        <path id="Path_180555" data-name="Path 180555" d="M32.957,979.111c-5.967-1.237-13.286-.425-16.339,1.813s-.685,5.058,5.286,6.3,13.286.426,16.337-1.813.685-5.058-5.286-6.295" transform="translate(-14.647 -937.84)" fill="#fff511"/>
        <path id="Path_180556" data-name="Path 180556" d="M100.323,1012.124c4.616.929,6.447,3.049,4.087,4.725s-8.016,2.289-12.634,1.36-6.447-3.049-4.088-4.724,8.016-2.289,12.634-1.361" transform="translate(-83.045 -969.619)" fill="#ffff89"/>
        <path id="Path_180557" data-name="Path 180557" d="M94.778,1003c4.535.942,6.334,3.09,4.015,4.791s-7.873,2.323-12.41,1.382-6.334-3.09-4.015-4.791,7.875-2.323,12.41-1.381" transform="translate(-77.964 -960.872)" fill="#ca7700"/>
        <path id="Path_180558" data-name="Path 180558" d="M102.593,1010.77c4.481.913,6.258,2.995,3.967,4.646s-7.78,2.253-12.261,1.339-6.258-2.995-3.967-4.646,7.78-2.253,12.261-1.339" transform="translate(-85.607 -968.327)" fill="#fff511"/>
        <path id="Path_180559" data-name="Path 180559" d="M437.9,1187.882v-4.646a21.735,21.735,0,0,1-2.62.589v4.653a21.636,21.636,0,0,0,2.62-.6" transform="translate(-417.139 -1134.031)" fill="#dc8e00"/>
        <path id="Path_180560" data-name="Path 180560" d="M573.691,1101.29a2.849,2.849,0,0,0,1.4-2.157q.033-2.337.067-4.673a2.852,2.852,0,0,1-1.394,2.159c-.062.045-.125.09-.191.134v4.626l.122-.087" transform="translate(-549.659 -1048.946)" fill="#dc8e00"/>
        <path id="Path_180561" data-name="Path 180561" d="M133.937,1183.8q-.779-.161-1.477-.355v4.691q.668.182,1.409.337c.409.084.824.159,1.241.226v-4.677c-.4-.064-.787-.135-1.173-.216" transform="translate(-126.938 -1134.235)" fill="#ffde00"/>
        <path id="Path_180562" data-name="Path 180562" d="M501.27,1154.21v-4.625a11.392,11.392,0,0,1-3.145,1.392v4.646a11.154,11.154,0,0,0,3.145-1.419" transform="translate(-477.36 -1101.779)" fill="#ca7700"/>
        <path id="Path_180563" data-name="Path 180563" d="M32.856,1137.852v4.724a8.925,8.925,0,0,0,1.793,1.037v-4.7a8.689,8.689,0,0,1-1.793-1.057" transform="translate(-31.486 -1090.534)" fill="#ffff89"/>
        <path id="Path_180564" data-name="Path 180564" d="M75.868,1163.3v4.7a16.4,16.4,0,0,0,2.359.826v-4.691a16.1,16.1,0,0,1-2.359-.839" transform="translate(-72.705 -1114.926)" fill="#fffa13"/>
        <path id="Path_180565" data-name="Path 180565" d="M.106,1085.414q-.033,2.336-.067,4.672a2.9,2.9,0,0,0,1.368,2.24V1087.6a2.855,2.855,0,0,1-1.3-2.178" transform="translate(-0.037 -1040.277)" fill="#fffa13"/>
        <path id="Path_180566" data-name="Path 180566" d="M378.167,979.128c3.293.682,5.49,1.847,6.287,3.129l.477-.158c-.891-1.3-3.155-2.468-6.505-3.161-.783-.162-1.588-.29-2.4-.388l-.508.167c.9.1,1.79.231,2.652.409" transform="translate(-359.862 -937.857)" fill="#f0a400"/>
        <path id="Path_180567" data-name="Path 180567" d="M12.68,1104.448c-3.694-.765-6.008-2.137-6.513-3.6l-.508.167c.629,1.478,3.018,2.847,6.763,3.63q.608.126,1.233.225l.479-.157q-.738-.109-1.455-.257" transform="translate(-5.424 -1055.068)" fill="#f0a400"/>
        <path id="Path_180568" data-name="Path 180568" d="M521.7,1078.131a1.54,1.54,0,0,0-.183-.466,1.445,1.445,0,0,1,.1.494Z" transform="translate(-499.775 -1032.85)" fill="#fff511"/>
        <path id="Path_180569" data-name="Path 180569" d="M134.486,1139.945a12.919,12.919,0,0,1-2.935-.941,12.244,12.244,0,0,0,3.2,1.064,21.281,21.281,0,0,0,2.595.357l.245-.081a21.471,21.471,0,0,1-3.112-.4" transform="translate(-126.067 -1091.638)" fill="#fff511"/>
        <path id="Path_180570" data-name="Path 180570" d="M3.593,975.926c2.612-1.917,8.345-2.787,13.68-2.223l.508-.167c-5.695-.672-11.906.234-14.7,2.285-1.276.939-1.643,1.973-1.224,2.968l.508-.167c-.311-.9.064-1.841,1.227-2.695" transform="translate(-1.622 -932.842)" fill="#f0b500"/>
        <path id="Path_180571" data-name="Path 180571" d="M19.246,981.318c1.71-1.257,5.179-1.924,8.7-1.809l2.551-.842c-5.342-.563-11.075.306-13.68,2.223-1.164.853-1.54,1.792-1.227,2.694l2.663-.878a2.22,2.22,0,0,1,1-1.389" transform="translate(-14.842 -937.807)" fill="#ffff89"/>
        <path id="Path_180572" data-name="Path 180572" d="M91.368,1062.023a2.312,2.312,0,0,0-.488.611l.068-.022a2.093,2.093,0,0,1,.419-.587" transform="translate(-87.092 -1017.858)" fill="#ca7700"/>
        <path id="Path_180573" data-name="Path 180573" d="M93.288,1011.925a3.445,3.445,0,0,0-.453.385,2.1,2.1,0,0,0-.419.588l8.1-2.673.194-.064a14.277,14.277,0,0,0-7.422,1.762" transform="translate(-88.564 -968.144)" fill="#ffff89"/>
        <path id="Path_180574" data-name="Path 180574" d="M213.3,1067.3c1.481-1.088,1.746-2.307.972-3.429l-.477.158c.646,1.042.367,2.163-1,3.166-2.809,2.062-9.225,2.912-14.885,2.072l-.479.157c5.992.954,12.881.068,15.863-2.122" transform="translate(-189.2 -1019.634)" fill="#ca7700"/>
        <path id="Path_180575" data-name="Path 180575" d="M221.571,1070.475c-2,1.423-6.358,2.078-10.451,1.681l-2.2.726c5.658.841,12.079-.01,14.885-2.072,1.368-1,1.646-2.124,1-3.166l-2.3.758c.272.693-.01,1.416-.931,2.072" transform="translate(-200.207 -1023.246)" fill="#ffe400"/>
        <path id="Path_180576" data-name="Path 180576" d="M281.347,1088.873l-.083.028-10.309,3.4-.245.081c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.811" transform="translate(-259.425 -1043.592)" fill="#ffe400"/>
        <path id="Path_180577" data-name="Path 180577" d="M202.546,1042.713a9.8,9.8,0,0,1,4.452.3c.451.155,1.007.162,1.221.291v.319l-2.037.29a3.091,3.091,0,0,0-.7-.327,4.765,4.765,0,0,0-2.222-.135c-1.528.215-1.841.962-1.262,1.562a4.666,4.666,0,0,0,3.618.871c.888-.125,1.354-.338,1.467-.673a1.434,1.434,0,0,0,0-.726l.931.082,1.019-.125a.928.928,0,0,1,.175.846c-.212.629-1.233,1.1-2.87,1.333a10.8,10.8,0,0,1-3.986-.184,5.112,5.112,0,0,1-2.363-1.152,1.04,1.04,0,0,1-.163-1.46,4.279,4.279,0,0,1,2.719-1.128" transform="translate(-191.279 -999.229)" fill="#ca7700"/>
        <path id="Path_180578" data-name="Path 180578" d="M199.773,1033.708a9.822,9.822,0,0,1,4.453.3,4.412,4.412,0,0,1,1.221.615l-2.037.29a3.106,3.106,0,0,0-.7-.326,4.778,4.778,0,0,0-2.222-.135c-1.527.215-1.841.963-1.26,1.562a4.653,4.653,0,0,0,3.618.871c.888-.125,1.354-.338,1.467-.672a.589.589,0,0,0-.086-.5l1.01-.145,1.019-.125a.928.928,0,0,1,.175.845c-.212.63-1.231,1.1-2.868,1.334a10.8,10.8,0,0,1-3.988-.185,5.111,5.111,0,0,1-2.363-1.151,1.042,1.042,0,0,1-.163-1.461,4.285,4.285,0,0,1,2.718-1.128" transform="translate(-188.616 -990.599)" fill="#ffff89"/>
        <path id="Path_180579" data-name="Path 180579" d="M202.412,1035.084a9.818,9.818,0,0,1,4.452.3,4.38,4.38,0,0,1,1.221.616l-2.037.29a3.091,3.091,0,0,0-.7-.327,4.759,4.759,0,0,0-2.222-.135c-1.528.215-1.841.962-1.262,1.562a4.666,4.666,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.674a.588.588,0,0,0-.086-.5l1.01-.145,1.019-.125a.927.927,0,0,1,.175.845c-.212.63-1.231,1.1-2.868,1.334a10.791,10.791,0,0,1-3.986-.184,5.114,5.114,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.163-1.461,4.281,4.281,0,0,1,2.719-1.126" transform="translate(-191.145 -991.918)" fill="#f0b500"/>
        <path id="Path_180580" data-name="Path 180580" d="M25.5,959.1q-.033,2.337-.067,4.672a2.85,2.85,0,0,1-1.4,2.159c-3.2,2.343-10.852,3.194-17.1,1.9-4.42-.916-6.954-2.66-6.929-4.432q.035-2.338.067-4.672c-.026,1.771,2.507,3.516,6.929,4.432,6.251,1.3,13.907.446,17.1-1.9a2.851,2.851,0,0,0,1.394-2.159" transform="translate(-0.001 -918.852)" fill="#f0b500"/>
        <path id="Path_180581" data-name="Path 180581" d="M20.139,847.32c-6.258-1.3-13.907-.446-17.1,1.9s-.717,5.295,5.53,6.591,13.907.446,17.1-1.9.716-5.295-5.53-6.59" transform="translate(-1.571 -811.503)" fill="#f0b500"/>
        <path id="Path_180582" data-name="Path 180582" d="M32.957,852.521c-5.967-1.237-13.286-.425-16.339,1.813s-.685,5.058,5.286,6.3,13.286.426,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-14.647 -816.514)" fill="#fff511"/>
        <path id="Path_180583" data-name="Path 180583" d="M100.323,885.534c4.616.929,6.447,3.049,4.087,4.724s-8.016,2.29-12.634,1.36-6.447-3.049-4.088-4.724,8.016-2.289,12.634-1.36" transform="translate(-83.045 -848.293)" fill="#ffff89"/>
        <path id="Path_180584" data-name="Path 180584" d="M94.778,876.38c4.535.942,6.334,3.089,4.015,4.791s-7.873,2.323-12.41,1.381-6.334-3.09-4.015-4.791,7.875-2.323,12.41-1.381" transform="translate(-77.964 -839.514)" fill="#ca7700"/>
        <path id="Path_180585" data-name="Path 180585" d="M102.593,884.133c4.481.915,6.258,3,3.967,4.646s-7.78,2.253-12.261,1.339-6.258-3-3.967-4.646,7.78-2.253,12.261-1.34" transform="translate(-85.607 -846.956)" fill="#fff511"/>
        <path id="Path_180586" data-name="Path 180586" d="M437.9,1061.293v-4.646a22.061,22.061,0,0,1-2.62.59v4.66a21.554,21.554,0,0,0,2.62-.6" transform="translate(-417.139 -1012.706)" fill="#dc8e00"/>
        <path id="Path_180587" data-name="Path 180587" d="M573.691,974.7a2.849,2.849,0,0,0,1.4-2.159q.033-2.336.067-4.672a2.85,2.85,0,0,1-1.394,2.159c-.062.045-.125.09-.191.134v4.626l.122-.087" transform="translate(-549.659 -927.621)" fill="#dc8e00"/>
        <path id="Path_180588" data-name="Path 180588" d="M133.937,1057.212q-.779-.161-1.477-.356v4.691q.668.181,1.409.337c.409.084.824.16,1.241.227v-4.685c-.4-.062-.787-.135-1.173-.215" transform="translate(-126.938 -1012.906)" fill="#ffde00"/>
        <path id="Path_180589" data-name="Path 180589" d="M501.27,1027.618v-4.625a11.419,11.419,0,0,1-3.145,1.392v4.646a11.164,11.164,0,0,0,3.145-1.418" transform="translate(-477.36 -980.452)" fill="#ca7700"/>
        <path id="Path_180590" data-name="Path 180590" d="M32.856,1011.264v4.724a8.907,8.907,0,0,0,1.793,1.035v-4.7a8.691,8.691,0,0,1-1.793-1.058" transform="translate(-31.486 -969.209)" fill="#ffff89"/>
        <path id="Path_180591" data-name="Path 180591" d="M75.868,1036.822v4.7a16.448,16.448,0,0,0,2.359.826v-4.692a16.126,16.126,0,0,1-2.359-.838" transform="translate(-72.705 -993.704)" fill="#fffa13"/>
        <path id="Path_180592" data-name="Path 180592" d="M.106,958.825Q.072,961.161.039,963.5a2.9,2.9,0,0,0,1.368,2.233v-4.724a2.856,2.856,0,0,1-1.3-2.178" transform="translate(-0.037 -918.952)" fill="#fffa13"/>
        <path id="Path_180593" data-name="Path 180593" d="M378.167,852.538c3.293.682,5.49,1.847,6.287,3.129l.477-.158c-.891-1.3-3.155-2.468-6.505-3.161-.783-.163-1.588-.29-2.4-.388l-.508.167c.9.1,1.79.231,2.652.409" transform="translate(-359.862 -816.531)" fill="#f0a400"/>
        <path id="Path_180594" data-name="Path 180594" d="M12.68,977.858c-3.694-.765-6.008-2.137-6.513-3.6l-.508.168c.629,1.478,3.018,2.847,6.763,3.63q.608.125,1.233.225l.479-.157q-.738-.11-1.455-.257" transform="translate(-5.424 -933.742)" fill="#f0a400"/>
        <path id="Path_180595" data-name="Path 180595" d="M521.7,951.509a1.545,1.545,0,0,0-.183-.468,1.448,1.448,0,0,1,.1.494Z" transform="translate(-499.775 -911.492)" fill="#fff511"/>
        <path id="Path_180596" data-name="Path 180596" d="M134.486,1013.388a12.98,12.98,0,0,1-2.935-.939,12.268,12.268,0,0,0,3.2,1.064,21.161,21.161,0,0,0,2.595.357l.245-.08a21.473,21.473,0,0,1-3.112-.4" transform="translate(-126.067 -970.345)" fill="#fff511"/>
        <path id="Path_180597" data-name="Path 180597" d="M3.475,849.336c2.612-1.916,8.345-2.787,13.68-2.223l.508-.167c-5.695-.672-11.906.234-14.7,2.285-1.282.941-1.649,1.977-1.228,2.967l.508-.168c-.311-.9.064-1.841,1.227-2.694" transform="translate(-1.504 -811.516)" fill="#f0b500"/>
        <path id="Path_180598" data-name="Path 180598" d="M19.246,854.75c1.71-1.257,5.179-1.924,8.7-1.809L30.5,852.1c-5.342-.565-11.075.306-13.68,2.223-1.164.854-1.54,1.793-1.227,2.695l2.663-.871a2.221,2.221,0,0,1,1-1.389" transform="translate(-14.842 -816.503)" fill="#ffff89"/>
        <path id="Path_180599" data-name="Path 180599" d="M91.368,935.469a2.305,2.305,0,0,0-.488.611l.068-.022a2.087,2.087,0,0,1,.419-.588" transform="translate(-87.092 -896.567)" fill="#ca7700"/>
        <path id="Path_180600" data-name="Path 180600" d="M93.288,885.372a3.489,3.489,0,0,0-.453.385,2.093,2.093,0,0,0-.419.588l8.1-2.673.194-.064a14.279,14.279,0,0,0-7.422,1.764" transform="translate(-88.564 -846.853)" fill="#ffff89"/>
        <path id="Path_180601" data-name="Path 180601" d="M213.3,940.855c1.481-1.087,1.746-2.305.972-3.429l-.477.158c.646,1.043.367,2.163-1,3.167-2.809,2.06-9.225,2.913-14.885,2.07l-.479.157c5.992.954,12.881.068,15.863-2.124" transform="translate(-189.2 -898.443)" fill="#ca7700"/>
        <path id="Path_180602" data-name="Path 180602" d="M221.571,943.919c-2,1.423-6.358,2.077-10.451,1.681l-2.2.726c5.658.842,12.079-.01,14.885-2.07,1.368-1,1.646-2.124,1-3.167l-2.3.758c.272.692-.01,1.415-.931,2.072" transform="translate(-200.207 -901.954)" fill="#ffe400"/>
        <path id="Path_180603" data-name="Path 180603" d="M281.381,962.28l-.083.026-10.308,3.4-.244.08c3.829.29,7.8-.353,9.665-1.7.794-.581,1.1-1.2.971-1.811" transform="translate(-259.459 -922.264)" fill="#ffe400"/>
        <path id="Path_180604" data-name="Path 180604" d="M202.546,916.13a9.829,9.829,0,0,1,4.452.3c.451.157,1.007.164,1.221.29v.319l-2.037.29a3.1,3.1,0,0,0-.7-.327,4.763,4.763,0,0,0-2.222-.135c-1.528.215-1.841.963-1.262,1.561a4.655,4.655,0,0,0,3.618.871c.888-.125,1.354-.338,1.467-.674a1.43,1.43,0,0,0,0-.726l.931.081,1.019-.125a.929.929,0,0,1,.175.846c-.212.63-1.233,1.1-2.87,1.333a10.757,10.757,0,0,1-3.986-.184,5.108,5.108,0,0,1-2.363-1.15,1.041,1.041,0,0,1-.163-1.46,4.282,4.282,0,0,1,2.719-1.127" transform="translate(-191.279 -877.911)" fill="#ca7700"/>
        <path id="Path_180605" data-name="Path 180605" d="M199.786,907.128a9.82,9.82,0,0,1,4.453.305,4.4,4.4,0,0,1,1.221.614l-2.037.29a3.086,3.086,0,0,0-.7-.327,4.761,4.761,0,0,0-2.222-.136c-1.527.215-1.841.964-1.26,1.562a4.651,4.651,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.674a.59.59,0,0,0-.086-.5l1.01-.145,1.019-.125a.928.928,0,0,1,.175.845c-.212.63-1.231,1.1-2.868,1.334a10.8,10.8,0,0,1-3.988-.184,5.114,5.114,0,0,1-2.363-1.151,1.044,1.044,0,0,1-.163-1.461,4.289,4.289,0,0,1,2.719-1.126" transform="translate(-188.63 -869.283)" fill="#ffff89"/>
        <path id="Path_180606" data-name="Path 180606" d="M202.412,908.48a9.814,9.814,0,0,1,4.452.3,4.373,4.373,0,0,1,1.221.615l-2.037.291a3.1,3.1,0,0,0-.7-.327,4.762,4.762,0,0,0-2.222-.135c-1.528.215-1.841.963-1.262,1.562a4.664,4.664,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.673a.587.587,0,0,0-.086-.5l1.01-.145,1.019-.125a.928.928,0,0,1,.175.845c-.212.63-1.231,1.1-2.868,1.334a10.786,10.786,0,0,1-3.986-.184,5.121,5.121,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.163-1.461,4.285,4.285,0,0,1,2.719-1.127" transform="translate(-191.145 -870.578)" fill="#f0b500"/>
        <path id="Path_180607" data-name="Path 180607" d="M25.5,832.646q-.033,2.337-.067,4.673a2.851,2.851,0,0,1-1.4,2.158c-3.2,2.345-10.852,3.194-17.1,1.9C2.51,840.46-.024,838.715,0,836.943q.035-2.338.067-4.673c-.023,1.773,2.509,3.516,6.93,4.43,6.251,1.3,13.907.445,17.1-1.9a2.852,2.852,0,0,0,1.394-2.159" transform="translate(-0.001 -797.66)" fill="#f0b500"/>
        <path id="Path_180608" data-name="Path 180608" d="M20.139,720.766c-6.258-1.295-13.907-.446-17.1,1.9s-.717,5.295,5.53,6.59,13.907.446,17.1-1.9.716-5.295-5.53-6.591" transform="translate(-1.571 -690.213)" fill="#f0b500"/>
        <path id="Path_180609" data-name="Path 180609" d="M32.957,725.951c-5.967-1.238-13.286-.425-16.339,1.813s-.685,5.058,5.286,6.3,13.286.427,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-14.647 -695.207)" fill="#fff511"/>
        <path id="Path_180610" data-name="Path 180610" d="M100.323,759.012c4.616.929,6.447,3.049,4.087,4.724s-8.016,2.289-12.634,1.362-6.447-3.049-4.088-4.724,8.016-2.29,12.634-1.36" transform="translate(-83.045 -727.034)" fill="#ffff89"/>
        <path id="Path_180611" data-name="Path 180611" d="M94.778,749.819c4.535.942,6.334,3.089,4.015,4.791s-7.873,2.323-12.41,1.381S80.05,752.9,82.368,751.2s7.875-2.323,12.41-1.381" transform="translate(-77.964 -718.215)" fill="#ca7700"/>
        <path id="Path_180612" data-name="Path 180612" d="M102.593,757.764c4.481.915,6.258,3,3.967,4.646s-7.78,2.252-12.261,1.339-6.258-3-3.967-4.646,7.78-2.253,12.261-1.339" transform="translate(-85.607 -725.842)" fill="#fff511"/>
        <path id="Path_180613" data-name="Path 180613" d="M437.9,934.563v-4.646a21.887,21.887,0,0,1-2.62.59v4.662a21.789,21.789,0,0,0,2.62-.6" transform="translate(-417.139 -891.246)" fill="#dc8e00"/>
        <path id="Path_180614" data-name="Path 180614" d="M573.691,848.109a2.849,2.849,0,0,0,1.4-2.157q.033-2.338.067-4.673a2.85,2.85,0,0,1-1.394,2.159c-.062.045-.125.09-.191.134V848.2l.122-.087" transform="translate(-549.659 -806.293)" fill="#dc8e00"/>
        <path id="Path_180615" data-name="Path 180615" d="M133.937,930.661q-.779-.161-1.477-.356V935q.668.183,1.409.337c.409.084.824.159,1.241.226v-4.684c-.4-.064-.787-.135-1.173-.216" transform="translate(-126.938 -891.618)" fill="#ffde00"/>
        <path id="Path_180616" data-name="Path 180616" d="M501.27,901.027V896.4a11.417,11.417,0,0,1-3.145,1.392v4.646a11.156,11.156,0,0,0,3.145-1.419" transform="translate(-477.36 -859.125)" fill="#ca7700"/>
        <path id="Path_180617" data-name="Path 180617" d="M32.856,884.707v4.726a8.912,8.912,0,0,0,1.793,1.035v-4.7a8.662,8.662,0,0,1-1.793-1.057" transform="translate(-31.486 -847.916)" fill="#ffff89"/>
        <path id="Path_180618" data-name="Path 180618" d="M75.868,909.984v4.7a16.444,16.444,0,0,0,2.359.826v-4.691a16.188,16.188,0,0,1-2.359-.838" transform="translate(-72.705 -872.142)" fill="#fffa13"/>
        <path id="Path_180619" data-name="Path 180619" d="M.106,832.27q-.033,2.336-.067,4.674a2.9,2.9,0,0,0,1.368,2.233v-4.726a2.855,2.855,0,0,1-1.3-2.178" transform="translate(-0.037 -797.66)" fill="#fffa13"/>
        <path id="Path_180620" data-name="Path 180620" d="M378.167,725.949c3.293.682,5.49,1.847,6.287,3.129l.477-.158c-.891-1.3-3.155-2.468-6.505-3.16-.783-.163-1.588-.29-2.4-.388l-.508.168a26.539,26.539,0,0,1,2.652.409" transform="translate(-359.862 -695.206)" fill="#f0a400"/>
        <path id="Path_180621" data-name="Path 180621" d="M12.68,851.3c-3.694-.765-6.008-2.137-6.513-3.6l-.508.168c.629,1.477,3.018,2.847,6.763,3.629q.608.126,1.233.225l.479-.158q-.738-.111-1.455-.259" transform="translate(-5.424 -812.451)" fill="#f0a400"/>
        <path id="Path_180622" data-name="Path 180622" d="M521.7,824.95a1.542,1.542,0,0,0-.183-.466,1.447,1.447,0,0,1,.1.494Z" transform="translate(-499.775 -790.198)" fill="#fff511"/>
        <path id="Path_180623" data-name="Path 180623" d="M134.486,886.8a12.946,12.946,0,0,1-2.935-.939,12.248,12.248,0,0,0,3.2,1.064,21.414,21.414,0,0,0,2.595.357l.245-.081a21.335,21.335,0,0,1-3.112-.4" transform="translate(-126.067 -849.021)" fill="#fff511"/>
        <path id="Path_180624" data-name="Path 180624" d="M3.593,722.715c2.612-1.917,8.345-2.788,13.68-2.223l.508-.168c-5.695-.672-11.906.235-14.7,2.285-1.276.945-1.643,1.98-1.224,2.969l.508-.169c-.311-.9.064-1.841,1.227-2.694" transform="translate(-1.622 -690.159)" fill="#f0b500"/>
        <path id="Path_180625" data-name="Path 180625" d="M19.246,728.195c1.71-1.257,5.179-1.924,8.7-1.809l2.551-.84c-5.342-.565-11.075.306-13.68,2.223-1.164.854-1.54,1.793-1.227,2.695l2.663-.871a2.22,2.22,0,0,1,1-1.389" transform="translate(-14.842 -695.21)" fill="#ffff89"/>
        <path id="Path_180626" data-name="Path 180626" d="M91.368,808.879a2.3,2.3,0,0,0-.488.611l.068-.023a2.088,2.088,0,0,1,.419-.588" transform="translate(-87.092 -775.242)" fill="#ca7700"/>
        <path id="Path_180627" data-name="Path 180627" d="M93.288,758.783a3.515,3.515,0,0,0-.453.385,2.088,2.088,0,0,0-.419.588l8.1-2.673.194-.064a14.282,14.282,0,0,0-7.422,1.764" transform="translate(-88.564 -725.529)" fill="#ffff89"/>
        <path id="Path_180628" data-name="Path 180628" d="M213.3,814.021c1.481-1.087,1.746-2.307.972-3.429l-.477.158c.646,1.042.367,2.163-1,3.167-2.809,2.06-9.225,2.912-14.885,2.072l-.479.158c5.992.954,12.881.068,15.863-2.124" transform="translate(-189.2 -776.883)" fill="#ca7700"/>
        <path id="Path_180629" data-name="Path 180629" d="M221.571,817.33c-2,1.423-6.358,2.078-10.451,1.681l-2.2.726c5.658.841,12.079-.01,14.885-2.072,1.368-1,1.646-2.124,1-3.167l-2.3.758c.272.692-.01,1.415-.931,2.071" transform="translate(-200.207 -780.628)" fill="#ffe400"/>
        <path id="Path_180630" data-name="Path 180630" d="M281.381,835.691l-.083.027-10.308,3.4-.244.081c3.829.29,7.8-.353,9.665-1.7.794-.581,1.1-1.2.971-1.812" transform="translate(-259.459 -800.939)" fill="#ffe400"/>
        <path id="Path_180631" data-name="Path 180631" d="M202.546,789.538a9.822,9.822,0,0,1,4.452.3c.451.155,1.007.164,1.221.29v.319l-2.037.29a3.106,3.106,0,0,0-.7-.327,4.764,4.764,0,0,0-2.222-.135c-1.528.215-1.841.962-1.262,1.562a4.657,4.657,0,0,0,3.618.871c.888-.125,1.354-.338,1.467-.674a1.433,1.433,0,0,0,0-.726l.931.081,1.019-.125a.929.929,0,0,1,.175.846c-.212.629-1.233,1.1-2.87,1.333a10.774,10.774,0,0,1-3.986-.184,5.114,5.114,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.163-1.46,4.281,4.281,0,0,1,2.719-1.127" transform="translate(-191.279 -756.583)" fill="#ca7700"/>
        <path id="Path_180632" data-name="Path 180632" d="M199.773,780.576a9.825,9.825,0,0,1,4.453.3,4.393,4.393,0,0,1,1.221.616l-2.037.29a3.06,3.06,0,0,0-.7-.327,4.756,4.756,0,0,0-2.222-.136c-1.527.215-1.841.963-1.26,1.562a4.655,4.655,0,0,0,3.618.871c.888-.125,1.354-.338,1.467-.674a.588.588,0,0,0-.086-.5l1.01-.145,1.019-.125a.929.929,0,0,1,.175.846c-.212.63-1.231,1.1-2.868,1.333a10.8,10.8,0,0,1-3.988-.184,5.115,5.115,0,0,1-2.363-1.152,1.042,1.042,0,0,1-.163-1.46,4.287,4.287,0,0,1,2.718-1.127" transform="translate(-188.616 -747.994)" fill="#ffff89"/>
        <path id="Path_180633" data-name="Path 180633" d="M202.413,781.892a9.817,9.817,0,0,1,4.452.3,4.384,4.384,0,0,1,1.221.616l-2.037.29a3.083,3.083,0,0,0-.7-.327,4.761,4.761,0,0,0-2.222-.135c-1.528.215-1.841.963-1.262,1.562a4.654,4.654,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.674a.589.589,0,0,0-.086-.5l1.01-.145,1.019-.125a.927.927,0,0,1,.175.845c-.213.63-1.231,1.1-2.868,1.334a10.781,10.781,0,0,1-3.986-.184,5.11,5.11,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.163-1.461,4.284,4.284,0,0,1,2.719-1.127" transform="translate(-191.146 -749.254)" fill="#f0b500"/>
        <path id="Path_180634" data-name="Path 180634" d="M25.528,706.057q-.033,2.338-.067,4.672a2.85,2.85,0,0,1-1.4,2.159c-3.2,2.345-10.852,3.194-17.1,1.9-4.42-.913-6.954-2.655-6.927-4.434q.035-2.337.067-4.672c-.026,1.771,2.508,3.516,6.929,4.432,6.251,1.3,13.907.446,17.1-1.9a2.851,2.851,0,0,0,1.394-2.157" transform="translate(-0.034 -676.335)" fill="#f0b500"/>
        <path id="Path_180635" data-name="Path 180635" d="M20.139,594.162c-6.258-1.3-13.907-.446-17.1,1.9s-.717,5.295,5.53,6.59,13.907.446,17.1-1.9.716-5.295-5.53-6.59" transform="translate(-1.571 -568.872)" fill="#f0b500"/>
        <path id="Path_180636" data-name="Path 180636" d="M32.957,599.343c-5.967-1.237-13.286-.425-16.339,1.815s-.685,5.058,5.286,6.295,13.286.425,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-14.647 -573.864)" fill="#fff511"/>
        <path id="Path_180637" data-name="Path 180637" d="M100.323,632.345c4.616.929,6.447,3.049,4.087,4.724s-8.016,2.289-12.634,1.36-6.447-3.049-4.088-4.724,8.016-2.29,12.634-1.362" transform="translate(-83.045 -605.633)" fill="#ffff89"/>
        <path id="Path_180638" data-name="Path 180638" d="M94.778,623.227c4.535.942,6.334,3.089,4.015,4.791s-7.873,2.323-12.41,1.381-6.334-3.09-4.015-4.791,7.875-2.323,12.41-1.382" transform="translate(-77.964 -596.887)" fill="#ca7700"/>
        <path id="Path_180639" data-name="Path 180639" d="M102.593,631.034c4.481.913,6.258,2.995,3.967,4.646s-7.78,2.253-12.261,1.34-6.258-3-3.967-4.646,7.78-2.253,12.261-1.339" transform="translate(-85.607 -604.384)" fill="#fff511"/>
        <path id="Path_180640" data-name="Path 180640" d="M437.9,808.08v-4.646a21.834,21.834,0,0,1-2.62.589v4.662a21.694,21.694,0,0,0,2.62-.6" transform="translate(-417.139 -770.023)" fill="#dc8e00"/>
        <path id="Path_180641" data-name="Path 180641" d="M573.691,721.587a2.85,2.85,0,0,0,1.4-2.158q.033-2.336.067-4.672a2.848,2.848,0,0,1-1.394,2.157c-.062.046-.125.09-.191.134v4.625c.042-.029.083-.058.122-.087" transform="translate(-549.659 -685.034)" fill="#dc8e00"/>
        <path id="Path_180642" data-name="Path 180642" d="M133.937,804.069q-.779-.161-1.477-.356V808.4q.668.183,1.409.337c.409.084.824.16,1.241.226v-4.683c-.4-.064-.787-.135-1.173-.216" transform="translate(-126.938 -770.291)" fill="#ffde00"/>
        <path id="Path_180643" data-name="Path 180643" d="M501.27,774.438v-4.625a11.381,11.381,0,0,1-3.145,1.392v4.646a11.154,11.154,0,0,0,3.145-1.419" transform="translate(-477.36 -737.8)" fill="#ca7700"/>
        <path id="Path_180644" data-name="Path 180644" d="M32.856,758.05v4.724a8.9,8.9,0,0,0,1.793,1.035V759.1a8.689,8.689,0,0,1-1.793-1.057" transform="translate(-31.486 -726.523)" fill="#ffff89"/>
        <path id="Path_180645" data-name="Path 180645" d="M75.868,783.534v4.7a16.369,16.369,0,0,0,2.359.826v-4.691a16.143,16.143,0,0,1-2.359-.838" transform="translate(-72.705 -750.951)" fill="#fffa13"/>
        <path id="Path_180646" data-name="Path 180646" d="M.106,705.68l-.067,4.672a2.9,2.9,0,0,0,1.368,2.233v-4.724a2.855,2.855,0,0,1-1.3-2.178" transform="translate(-0.037 -676.334)" fill="#fffa13"/>
        <path id="Path_180647" data-name="Path 180647" d="M378.167,599.36c3.293.682,5.49,1.847,6.287,3.129l.477-.158c-.891-1.3-3.155-2.468-6.505-3.161-.783-.162-1.588-.291-2.4-.388l-.508.167c.9.1,1.79.231,2.652.409" transform="translate(-359.862 -573.882)" fill="#f0a400"/>
        <path id="Path_180648" data-name="Path 180648" d="M12.68,724.712c-3.694-.765-6.008-2.136-6.513-3.6l-.508.167c.629,1.476,3.018,2.847,6.763,3.629q.608.127,1.233.225l.479-.158q-.738-.11-1.455-.258" transform="translate(-5.424 -691.124)" fill="#f0a400"/>
        <path id="Path_180649" data-name="Path 180649" d="M521.7,698.361a1.54,1.54,0,0,0-.183-.466,1.447,1.447,0,0,1,.1.494Z" transform="translate(-499.775 -668.873)" fill="#fff511"/>
        <path id="Path_180650" data-name="Path 180650" d="M134.486,760.211a13,13,0,0,1-2.935-.941,12.259,12.259,0,0,0,3.2,1.064,21.268,21.268,0,0,0,2.595.357l.245-.08a21.534,21.534,0,0,1-3.112-.4" transform="translate(-126.067 -727.695)" fill="#fff511"/>
        <path id="Path_180651" data-name="Path 180651" d="M3.593,596.193c2.612-1.916,8.345-2.787,13.68-2.223l.508-.167c-5.695-.672-11.906.235-14.7,2.285-1.276.934-1.643,1.977-1.224,2.966l.508-.167c-.311-.9.064-1.841,1.227-2.695" transform="translate(-1.622 -568.9)" fill="#f0b500"/>
        <path id="Path_180652" data-name="Path 180652" d="M19.246,601.585c1.71-1.257,5.179-1.923,8.7-1.809l2.551-.842c-5.342-.563-11.075.308-13.68,2.223-1.164.854-1.54,1.791-1.227,2.694l2.663-.871a2.218,2.218,0,0,1,1-1.389" transform="translate(-14.842 -573.865)" fill="#ffff89"/>
        <path id="Path_180653" data-name="Path 180653" d="M91.368,682.29a2.3,2.3,0,0,0-.488.611l.068-.023a2.083,2.083,0,0,1,.419-.588" transform="translate(-87.092 -653.917)" fill="#ca7700"/>
        <path id="Path_180654" data-name="Path 180654" d="M93.288,632.19a3.494,3.494,0,0,0-.453.385,2.086,2.086,0,0,0-.419.588l8.1-2.671.194-.064a14.275,14.275,0,0,0-7.422,1.763" transform="translate(-88.564 -604.201)" fill="#ffff89"/>
        <path id="Path_180655" data-name="Path 180655" d="M213.3,687.57c1.481-1.087,1.746-2.305.972-3.429l-.477.158c.646,1.042.367,2.163-1,3.166-2.809,2.062-9.225,2.912-14.885,2.07l-.479.158c5.992.954,12.881.068,15.863-2.124" transform="translate(-189.2 -655.691)" fill="#ca7700"/>
        <path id="Path_180656" data-name="Path 180656" d="M221.571,690.774c-2,1.423-6.358,2.077-10.451,1.681l-2.2.726c5.658.842,12.079-.01,14.885-2.07,1.368-1,1.646-2.124,1-3.166l-2.3.758c.272.692-.01,1.416-.931,2.072" transform="translate(-200.207 -659.336)" fill="#ffe400"/>
        <path id="Path_180657" data-name="Path 180657" d="M281.347,709.137l-.083.027-10.309,3.4-.245.08c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.81" transform="translate(-259.425 -679.647)" fill="#ffe400"/>
        <path id="Path_180658" data-name="Path 180658" d="M634.318,1212.417q-.034,2.337-.067,4.673a2.851,2.851,0,0,1-1.4,2.157c-3.195,2.345-10.851,3.194-17.1,1.9-4.42-.916-6.953-2.661-6.927-4.432q.033-2.336.068-4.672c-.026,1.773,2.507,3.516,6.929,4.432,6.249,1.3,13.907.446,17.1-1.9a2.85,2.85,0,0,0,1.394-2.159" transform="translate(-583.446 -1161.639)" fill="#f0b500"/>
        <path id="Path_180659" data-name="Path 180659" d="M628.909,1100.5c-6.258-1.3-13.908-.446-17.1,1.9s-.718,5.295,5.53,6.59,13.907.446,17.1-1.9.717-5.295-5.53-6.59" transform="translate(-584.965 -1054.154)" fill="#f0b500"/>
        <path id="Path_180660" data-name="Path 180660" d="M641.713,1105.688c-5.967-1.238-13.286-.426-16.339,1.813s-.685,5.058,5.287,6.3,13.286.425,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-598.027 -1059.152)" fill="#fff511"/>
        <path id="Path_180661" data-name="Path 180661" d="M709.079,1138.678c4.616.929,6.446,3.049,4.087,4.724s-8.016,2.29-12.634,1.361-6.447-3.049-4.088-4.724,8.016-2.29,12.634-1.36" transform="translate(-666.425 -1090.91)" fill="#ffff89"/>
        <path id="Path_180662" data-name="Path 180662" d="M703.534,1129.561c4.534.942,6.333,3.089,4.015,4.791s-7.873,2.323-12.41,1.381-6.333-3.089-4.015-4.791,7.873-2.322,12.41-1.38" transform="translate(-661.344 -1082.166)" fill="#ca7700"/>
        <path id="Path_180663" data-name="Path 180663" d="M711.365,1137.338c4.481.913,6.258,2.995,3.967,4.646s-7.78,2.253-12.26,1.339-6.258-2.995-3.966-4.646,7.779-2.252,12.26-1.338" transform="translate(-669.005 -1089.633)" fill="#fff511"/>
        <path id="Path_180664" data-name="Path 180664" d="M1046.659,1314.472v-4.646a22,22,0,0,1-2.62.589v4.66a21.893,21.893,0,0,0,2.62-.6" transform="translate(-1000.517 -1255.357)" fill="#dc8e00"/>
        <path id="Path_180665" data-name="Path 180665" d="M1182.446,1227.88a2.85,2.85,0,0,0,1.4-2.157q.034-2.338.067-4.674a2.847,2.847,0,0,1-1.394,2.159c-.063.045-.126.09-.191.134v4.625l.122-.087" transform="translate(-1133.038 -1170.271)" fill="#dc8e00"/>
        <path id="Path_180666" data-name="Path 180666" d="M742.588,1310.394q-.78-.161-1.477-.355v4.692q.668.183,1.407.337c.409.084.824.16,1.241.226v-4.683c-.394-.064-.787-.135-1.173-.216" transform="translate(-710.217 -1255.56)" fill="#ffde00"/>
        <path id="Path_180667" data-name="Path 180667" d="M1110.024,1280.762v-4.625a11.394,11.394,0,0,1-3.145,1.392v4.646a11.135,11.135,0,0,0,3.145-1.419" transform="translate(-1060.738 -1223.068)" fill="#ca7700"/>
        <path id="Path_180668" data-name="Path 180668" d="M641.612,1264.376v4.721a8.973,8.973,0,0,0,1.793,1.035v-4.7a8.669,8.669,0,0,1-1.793-1.056" transform="translate(-614.866 -1211.794)" fill="#ffff89"/>
        <path id="Path_180669" data-name="Path 180669" d="M684.623,1289.857v4.7a16.458,16.458,0,0,0,2.359.826V1290.7a16.023,16.023,0,0,1-2.359-.838" transform="translate(-656.084 -1236.218)" fill="#fffa13"/>
        <path id="Path_180670" data-name="Path 180670" d="M608.863,1212q-.034,2.336-.068,4.672a2.9,2.9,0,0,0,1.367,2.233v-4.726a2.852,2.852,0,0,1-1.3-2.177" transform="translate(-583.416 -1161.603)" fill="#fffa13"/>
        <path id="Path_180671" data-name="Path 180671" d="M987.06,1105.719c3.293.682,5.491,1.847,6.286,3.129l.479-.157c-.891-1.3-3.156-2.468-6.505-3.161-.782-.162-1.586-.29-2.4-.388l-.508.167c.9.094,1.79.231,2.651.41" transform="translate(-943.374 -1059.184)" fill="#f0a400"/>
        <path id="Path_180672" data-name="Path 180672" d="M621.437,1231.039c-3.7-.767-6.008-2.137-6.512-3.6l-.508.167c.629,1.478,3.019,2.847,6.763,3.63.4.085.816.16,1.233.225l.477-.158c-.49-.073-.977-.159-1.456-.257" transform="translate(-588.804 -1176.395)" fill="#f0a400"/>
        <path id="Path_180673" data-name="Path 180673" d="M1130.454,1204.683a1.6,1.6,0,0,0-.183-.466,1.447,1.447,0,0,1,.1.494Z" transform="translate(-1083.155 -1154.139)" fill="#fff511"/>
        <path id="Path_180674" data-name="Path 180674" d="M743.209,1266.534a12.96,12.96,0,0,1-2.937-.941,12.252,12.252,0,0,0,3.2,1.064,21.275,21.275,0,0,0,2.595.357l.243-.081a21.539,21.539,0,0,1-3.111-.4" transform="translate(-709.413 -1212.963)" fill="#fff511"/>
        <path id="Path_180675" data-name="Path 180675" d="M612.215,1102.518c2.612-1.916,8.345-2.787,13.68-2.223l.508-.167c-5.7-.672-11.906.235-14.7,2.285-1.282.941-1.65,1.978-1.228,2.968l.508-.167c-.313-.9.064-1.841,1.227-2.695" transform="translate(-584.869 -1054.169)" fill="#f0b500"/>
        <path id="Path_180676" data-name="Path 180676" d="M628.016,1107.894c1.709-1.257,5.18-1.924,8.7-1.809l2.551-.842c-5.342-.565-11.075.306-13.68,2.223-1.164.854-1.538,1.792-1.227,2.695l2.663-.871a2.22,2.22,0,0,1,1-1.389" transform="translate(-598.235 -1059.118)" fill="#ffff89"/>
        <path id="Path_180677" data-name="Path 180677" d="M700.052,1188.579a2.326,2.326,0,0,0-.487.611l.067-.023a2.113,2.113,0,0,1,.419-.589" transform="translate(-670.402 -1139.151)" fill="#ca7700"/>
        <path id="Path_180678" data-name="Path 180678" d="M702.079,1138.48a3.555,3.555,0,0,0-.453.383,2.113,2.113,0,0,0-.419.589l8.1-2.673.193-.064a14.282,14.282,0,0,0-7.422,1.762" transform="translate(-671.977 -1089.436)" fill="#ffff89"/>
        <path id="Path_180679" data-name="Path 180679" d="M822.056,1193.894c1.482-1.088,1.746-2.307.972-3.429l-.479.157c.646,1.043.368,2.163-1,3.167-2.807,2.06-9.225,2.912-14.885,2.07l-.479.159c5.992.954,12.883.068,15.863-2.124" transform="translate(-772.579 -1140.959)" fill="#ca7700"/>
        <path id="Path_180680" data-name="Path 180680" d="M830.363,1197.1c-2,1.423-6.358,2.078-10.451,1.681l-2.2.726c5.658.842,12.079-.01,14.885-2.07,1.368-1.005,1.648-2.124,1-3.166l-2.3.758c.274.693-.009,1.415-.931,2.072" transform="translate(-783.62 -1144.608)" fill="#ffe400"/>
        <path id="Path_180681" data-name="Path 180681" d="M890.17,1215.426l-.083.028-10.309,3.4-.243.081c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.81" transform="translate(-842.87 -1164.882)" fill="#ffe400"/>
        <path id="Path_180682" data-name="Path 180682" d="M811.288,1169.308a9.82,9.82,0,0,1,4.452.3c.451.155,1.007.164,1.221.291v.319l-2.037.29a3.165,3.165,0,0,0-.7-.327,4.781,4.781,0,0,0-2.224-.135c-1.528.215-1.84.963-1.262,1.562a4.665,4.665,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.673a1.424,1.424,0,0,0,0-.726l.932.081,1.019-.125a.928.928,0,0,1,.174.846c-.214.629-1.232,1.1-2.869,1.333a10.773,10.773,0,0,1-3.987-.184,5.122,5.122,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.162-1.461,4.282,4.282,0,0,1,2.717-1.127" transform="translate(-774.645 -1120.559)" fill="#ca7700"/>
        <path id="Path_180683" data-name="Path 180683" d="M808.479,1160.312a9.828,9.828,0,0,1,4.453.3,4.411,4.411,0,0,1,1.22.616l-2.037.291a3.087,3.087,0,0,0-.7-.327,4.757,4.757,0,0,0-2.222-.137c-1.527.215-1.84.962-1.26,1.562a4.657,4.657,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.674a.588.588,0,0,0-.086-.5l1.01-.145,1.019-.125a.928.928,0,0,1,.176.846c-.214.629-1.233,1.1-2.87,1.333a10.749,10.749,0,0,1-3.986-.185,5.108,5.108,0,0,1-2.364-1.151,1.041,1.041,0,0,1-.163-1.461,4.287,4.287,0,0,1,2.719-1.126" transform="translate(-771.948 -1111.939)" fill="#ffff89"/>
        <path id="Path_180684" data-name="Path 180684" d="M811.1,1161.637a9.812,9.812,0,0,1,4.452.3,4.4,4.4,0,0,1,1.221.616l-2.037.291a3.084,3.084,0,0,0-.7-.327,4.763,4.763,0,0,0-2.224-.135c-1.528.215-1.84.962-1.262,1.562a4.665,4.665,0,0,0,3.619.871c.888-.125,1.355-.338,1.467-.673a.579.579,0,0,0-.086-.5l1.01-.145,1.019-.125a.927.927,0,0,1,.173.847c-.213.63-1.231,1.1-2.868,1.334a10.784,10.784,0,0,1-3.988-.185,5.118,5.118,0,0,1-2.363-1.149,1.042,1.042,0,0,1-.162-1.461,4.283,4.283,0,0,1,2.719-1.127" transform="translate(-774.458 -1113.208)" fill="#f0b500"/>
        <path id="Path_180685" data-name="Path 180685" d="M634.318,1085.895q-.034,2.337-.067,4.673a2.848,2.848,0,0,1-1.4,2.157c-3.195,2.344-10.851,3.194-17.1,1.9-4.42-.917-6.953-2.661-6.927-4.433q.033-2.336.068-4.672c-.026,1.771,2.507,3.516,6.929,4.433,6.249,1.3,13.907.446,17.1-1.9a2.852,2.852,0,0,0,1.394-2.159" transform="translate(-583.446 -1040.378)" fill="#f0b500"/>
        <path id="Path_180686" data-name="Path 180686" d="M628.909,973.944c-6.258-1.3-13.908-.446-17.1,1.9s-.718,5.295,5.53,6.59,13.907.446,17.1-1.9.717-5.3-5.53-6.59" transform="translate(-584.965 -932.862)" fill="#f0b500"/>
        <path id="Path_180687" data-name="Path 180687" d="M641.713,979.111c-5.967-1.237-13.286-.425-16.339,1.813s-.685,5.058,5.287,6.3,13.286.426,16.337-1.813.685-5.058-5.286-6.295" transform="translate(-598.027 -937.84)" fill="#fff511"/>
        <path id="Path_180688" data-name="Path 180688" d="M709.079,1012.124c4.616.929,6.446,3.049,4.087,4.725s-8.016,2.289-12.634,1.36-6.447-3.049-4.088-4.724,8.016-2.289,12.634-1.361" transform="translate(-666.425 -969.619)" fill="#ffff89"/>
        <path id="Path_180689" data-name="Path 180689" d="M703.534,1003c4.534.942,6.333,3.09,4.015,4.791s-7.873,2.323-12.41,1.382-6.333-3.09-4.015-4.791,7.873-2.323,12.41-1.381" transform="translate(-661.344 -960.872)" fill="#ca7700"/>
        <path id="Path_180690" data-name="Path 180690" d="M711.365,1010.77c4.481.913,6.258,2.995,3.967,4.646s-7.78,2.253-12.26,1.339-6.258-2.995-3.966-4.646,7.779-2.253,12.26-1.339" transform="translate(-669.005 -968.327)" fill="#fff511"/>
        <path id="Path_180691" data-name="Path 180691" d="M1046.659,1187.882v-4.646a21.766,21.766,0,0,1-2.62.589v4.653a21.639,21.639,0,0,0,2.62-.6" transform="translate(-1000.517 -1134.031)" fill="#dc8e00"/>
        <path id="Path_180692" data-name="Path 180692" d="M1182.446,1101.29a2.851,2.851,0,0,0,1.4-2.157q.034-2.337.067-4.673a2.849,2.849,0,0,1-1.394,2.159q-.094.068-.191.134v4.626l.122-.087" transform="translate(-1133.038 -1048.946)" fill="#dc8e00"/>
        <path id="Path_180693" data-name="Path 180693" d="M742.588,1183.8q-.78-.161-1.477-.355v4.691q.668.182,1.407.337c.409.084.824.159,1.241.226v-4.677c-.394-.064-.787-.135-1.173-.216" transform="translate(-710.217 -1134.235)" fill="#ffde00"/>
        <path id="Path_180694" data-name="Path 180694" d="M1110.024,1154.21v-4.625a11.373,11.373,0,0,1-3.145,1.392v4.646a11.137,11.137,0,0,0,3.145-1.419" transform="translate(-1060.738 -1101.779)" fill="#ca7700"/>
        <path id="Path_180695" data-name="Path 180695" d="M641.612,1137.852v4.724a8.972,8.972,0,0,0,1.793,1.037v-4.7a8.707,8.707,0,0,1-1.793-1.057" transform="translate(-614.866 -1090.534)" fill="#ffff89"/>
        <path id="Path_180696" data-name="Path 180696" d="M684.623,1163.3v4.7a16.332,16.332,0,0,0,2.359.826v-4.691a16.085,16.085,0,0,1-2.359-.839" transform="translate(-656.084 -1114.926)" fill="#fffa13"/>
        <path id="Path_180697" data-name="Path 180697" d="M608.863,1085.414q-.034,2.336-.068,4.672a2.9,2.9,0,0,0,1.367,2.233v-4.724a2.853,2.853,0,0,1-1.3-2.178" transform="translate(-583.416 -1040.277)" fill="#fffa13"/>
        <path id="Path_180698" data-name="Path 180698" d="M987.06,979.128c3.293.682,5.491,1.847,6.286,3.129l.479-.158c-.891-1.3-3.156-2.468-6.505-3.161-.782-.162-1.586-.29-2.4-.388l-.508.167c.9.1,1.79.231,2.651.409" transform="translate(-943.374 -937.857)" fill="#f0a400"/>
        <path id="Path_180699" data-name="Path 180699" d="M621.437,1104.448c-3.7-.765-6.008-2.137-6.512-3.6l-.508.167c.629,1.478,3.019,2.847,6.763,3.63.4.084.816.158,1.233.225l.477-.157c-.49-.073-.977-.158-1.456-.257" transform="translate(-588.804 -1055.068)" fill="#f0a400"/>
        <path id="Path_180700" data-name="Path 180700" d="M1130.454,1078.131a1.582,1.582,0,0,0-.183-.466,1.445,1.445,0,0,1,.1.494Z" transform="translate(-1083.155 -1032.85)" fill="#fff511"/>
        <path id="Path_180701" data-name="Path 180701" d="M743.209,1139.945a12.925,12.925,0,0,1-2.937-.941,12.243,12.243,0,0,0,3.2,1.064,21.326,21.326,0,0,0,2.595.357l.243-.081a21.487,21.487,0,0,1-3.111-.4" transform="translate(-709.413 -1091.638)" fill="#fff511"/>
        <path id="Path_180702" data-name="Path 180702" d="M612.215,975.926c2.612-1.917,8.345-2.787,13.68-2.223l.508-.167c-5.7-.672-11.906.234-14.7,2.285-1.282.941-1.65,1.977-1.228,2.968l.508-.167c-.313-.9.064-1.841,1.227-2.695" transform="translate(-584.869 -932.842)" fill="#f0b500"/>
        <path id="Path_180703" data-name="Path 180703" d="M628.016,981.318c1.709-1.257,5.18-1.924,8.7-1.809l2.551-.842c-5.342-.563-11.075.306-13.68,2.223-1.164.853-1.538,1.792-1.227,2.694l2.663-.878a2.222,2.222,0,0,1,1-1.389" transform="translate(-598.235 -937.807)" fill="#ffff89"/>
        <path id="Path_180704" data-name="Path 180704" d="M700.052,1062.023a2.319,2.319,0,0,0-.487.611l.067-.022a2.112,2.112,0,0,1,.419-.587" transform="translate(-670.402 -1017.858)" fill="#ca7700"/>
        <path id="Path_180705" data-name="Path 180705" d="M702.079,1011.924a3.473,3.473,0,0,0-.453.385,2.114,2.114,0,0,0-.419.588l8.1-2.673.193-.064a14.267,14.267,0,0,0-7.422,1.762" transform="translate(-671.977 -968.142)" fill="#ffff89"/>
        <path id="Path_180706" data-name="Path 180706" d="M822.056,1067.3c1.482-1.088,1.746-2.307.972-3.429l-.479.158c.646,1.042.368,2.163-1,3.166-2.807,2.062-9.225,2.912-14.885,2.072l-.479.157c5.992.954,12.883.068,15.863-2.122" transform="translate(-772.579 -1019.634)" fill="#ca7700"/>
        <path id="Path_180707" data-name="Path 180707" d="M830.363,1070.475c-2,1.423-6.358,2.078-10.451,1.681l-2.2.726c5.658.841,12.079-.01,14.885-2.072,1.368-1,1.648-2.124,1-3.166l-2.3.758c.274.693-.009,1.416-.931,2.072" transform="translate(-783.62 -1023.246)" fill="#ffe400"/>
        <path id="Path_180708" data-name="Path 180708" d="M890.17,1088.873l-.083.028-10.309,3.4-.243.081c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.811" transform="translate(-842.87 -1043.592)" fill="#ffe400"/>
        <path id="Path_180709" data-name="Path 180709" d="M811.288,1042.714a9.815,9.815,0,0,1,4.452.3c.451.155,1.007.163,1.221.29v.319l-2.037.291a3.113,3.113,0,0,0-.7-.327,4.776,4.776,0,0,0-2.224-.135c-1.528.215-1.84.963-1.262,1.562a4.667,4.667,0,0,0,3.618.871c.888-.125,1.355-.338,1.467-.674a1.419,1.419,0,0,0,0-.726l.932.081,1.019-.125a.93.93,0,0,1,.174.846c-.214.629-1.232,1.1-2.869,1.333a10.788,10.788,0,0,1-3.987-.184,5.13,5.13,0,0,1-2.363-1.151,1.041,1.041,0,0,1-.162-1.46,4.276,4.276,0,0,1,2.717-1.128" transform="translate(-774.645 -999.23)" fill="#ca7700"/>
        <path id="Path_180710" data-name="Path 180710" d="M808.479,1033.708a9.822,9.822,0,0,1,4.453.3,4.41,4.41,0,0,1,1.22.615l-2.037.29a3.126,3.126,0,0,0-.7-.326,4.778,4.778,0,0,0-2.222-.135c-1.527.215-1.84.963-1.26,1.562a4.653,4.653,0,0,0,3.618.871c.888-.125,1.353-.338,1.467-.672a.589.589,0,0,0-.086-.5l1.01-.145,1.019-.125a.926.926,0,0,1,.176.845c-.214.63-1.233,1.1-2.87,1.334a10.786,10.786,0,0,1-3.986-.185,5.108,5.108,0,0,1-2.364-1.151,1.041,1.041,0,0,1-.163-1.461,4.286,4.286,0,0,1,2.719-1.128" transform="translate(-771.948 -990.599)" fill="#ffff89"/>
        <path id="Path_180711" data-name="Path 180711" d="M811.1,1035.084a9.823,9.823,0,0,1,4.452.3,4.406,4.406,0,0,1,1.221.616l-2.037.291a3.088,3.088,0,0,0-.7-.327,4.763,4.763,0,0,0-2.224-.135c-1.528.215-1.84.963-1.262,1.562a4.668,4.668,0,0,0,3.619.871c.888-.125,1.355-.339,1.467-.674a.579.579,0,0,0-.086-.5l1.01-.145,1.019-.125a.928.928,0,0,1,.173.845c-.213.63-1.231,1.1-2.868,1.334a10.8,10.8,0,0,1-3.988-.184,5.124,5.124,0,0,1-2.363-1.151,1.042,1.042,0,0,1-.162-1.46,4.282,4.282,0,0,1,2.719-1.127" transform="translate(-774.458 -991.919)" fill="#f0b500"/>
        <path id="Path_180712" data-name="Path 180712" d="M634.318,959.1q-.034,2.337-.067,4.672a2.85,2.85,0,0,1-1.4,2.159c-3.195,2.343-10.851,3.194-17.1,1.9-4.42-.916-6.953-2.66-6.927-4.432q.033-2.338.068-4.672c-.026,1.771,2.507,3.516,6.929,4.432,6.249,1.3,13.907.446,17.1-1.9a2.849,2.849,0,0,0,1.394-2.159" transform="translate(-583.446 -918.852)" fill="#f0b500"/>
        <path id="Path_180713" data-name="Path 180713" d="M628.909,847.32c-6.258-1.3-13.908-.446-17.1,1.9s-.718,5.295,5.53,6.591,13.907.446,17.1-1.9.717-5.295-5.53-6.59" transform="translate(-584.965 -811.503)" fill="#f0b500"/>
        <path id="Path_180714" data-name="Path 180714" d="M641.713,852.521c-5.967-1.237-13.286-.425-16.339,1.813s-.685,5.058,5.287,6.3,13.286.426,16.337-1.813.685-5.058-5.286-6.3" transform="translate(-598.027 -816.514)" fill="#fff511"/>
        <path id="Path_180715" data-name="Path 180715" d="M709.079,885.534c4.616.929,6.446,3.049,4.087,4.724s-8.016,2.29-12.634,1.36-6.447-3.049-4.088-4.724,8.016-2.289,12.634-1.36" transform="translate(-666.425 -848.293)" fill="#ffff89"/>
        <path id="Path_180716" data-name="Path 180716" d="M703.534,876.38c4.534.942,6.333,3.089,4.015,4.791s-7.873,2.323-12.41,1.381-6.333-3.09-4.015-4.791,7.873-2.323,12.41-1.381" transform="translate(-661.344 -839.514)" fill="#ca7700"/>
        <path id="Path_180717" data-name="Path 180717" d="M711.365,884.133c4.481.915,6.258,3,3.967,4.646s-7.78,2.253-12.26,1.339-6.258-3-3.966-4.646,7.779-2.253,12.26-1.34" transform="translate(-669.005 -846.956)" fill="#fff511"/>
        <path id="Path_180718" data-name="Path 180718" d="M1046.659,1061.293v-4.646a22.123,22.123,0,0,1-2.62.59v4.66a21.611,21.611,0,0,0,2.62-.6" transform="translate(-1000.517 -1012.706)" fill="#dc8e00"/>
        <path id="Path_180719" data-name="Path 180719" d="M1182.446,974.7a2.849,2.849,0,0,0,1.4-2.159q.034-2.336.067-4.672a2.849,2.849,0,0,1-1.394,2.159c-.062.045-.126.09-.191.134v4.626l.122-.087" transform="translate(-1133.038 -927.621)" fill="#dc8e00"/>
        <path id="Path_180720" data-name="Path 180720" d="M742.588,1057.212q-.78-.161-1.477-.356v4.691q.668.181,1.407.337c.409.084.824.16,1.241.227v-4.685c-.394-.062-.787-.135-1.173-.215" transform="translate(-710.217 -1012.906)" fill="#ffde00"/>
        <path id="Path_180721" data-name="Path 180721" d="M1110.024,1027.618v-4.625a11.409,11.409,0,0,1-3.145,1.392v4.646a11.139,11.139,0,0,0,3.145-1.418" transform="translate(-1060.738 -980.452)" fill="#ca7700"/>
        <path id="Path_180722" data-name="Path 180722" d="M641.612,1011.264v4.724a8.954,8.954,0,0,0,1.793,1.035v-4.7a8.718,8.718,0,0,1-1.793-1.058" transform="translate(-614.866 -969.209)" fill="#ffff89"/>
        <path id="Path_180723" data-name="Path 180723" d="M684.623,1036.822v4.7a16.382,16.382,0,0,0,2.359.826v-4.692a16.023,16.023,0,0,1-2.359-.838" transform="translate(-656.084 -993.704)" fill="#fffa13"/>
        <path id="Path_180724" data-name="Path 180724" d="M608.863,958.825q-.034,2.336-.068,4.672a2.9,2.9,0,0,0,1.367,2.233v-4.724a2.852,2.852,0,0,1-1.3-2.178" transform="translate(-583.416 -918.952)" fill="#fffa13"/>
        <path id="Path_180725" data-name="Path 180725" d="M987.06,852.538c3.293.682,5.491,1.847,6.286,3.129l.479-.158c-.891-1.3-3.156-2.468-6.505-3.161-.782-.163-1.586-.29-2.4-.388l-.508.167c.9.1,1.79.231,2.651.409" transform="translate(-943.374 -816.531)" fill="#f0a400"/>
        <path id="Path_180726" data-name="Path 180726" d="M621.437,977.858c-3.7-.765-6.008-2.137-6.512-3.6l-.508.168c.629,1.478,3.019,2.847,6.763,3.63.4.084.816.16,1.233.225l.477-.157c-.49-.073-.977-.158-1.456-.257" transform="translate(-588.804 -933.742)" fill="#f0a400"/>
        <path id="Path_180727" data-name="Path 180727" d="M1130.454,951.509a1.586,1.586,0,0,0-.183-.468,1.448,1.448,0,0,1,.1.494Z" transform="translate(-1083.155 -911.492)" fill="#fff511"/>
        <path id="Path_180728" data-name="Path 180728" d="M743.209,1013.388a12.974,12.974,0,0,1-2.937-.939,12.266,12.266,0,0,0,3.2,1.064,21.2,21.2,0,0,0,2.595.357l.243-.08a21.5,21.5,0,0,1-3.111-.4" transform="translate(-709.413 -970.345)" fill="#fff511"/>
        <path id="Path_180729" data-name="Path 180729" d="M612.215,849.336c2.612-1.916,8.345-2.787,13.68-2.223l.508-.167c-5.7-.672-11.906.234-14.7,2.285-1.282.941-1.65,1.977-1.228,2.967l.508-.168c-.313-.9.064-1.841,1.227-2.694" transform="translate(-584.869 -811.516)" fill="#f0b500"/>
        <path id="Path_180730" data-name="Path 180730" d="M628.016,854.75c1.709-1.257,5.18-1.924,8.7-1.809l2.551-.841c-5.342-.565-11.075.306-13.68,2.223-1.164.854-1.538,1.793-1.227,2.695l2.663-.871a2.222,2.222,0,0,1,1-1.389" transform="translate(-598.235 -816.503)" fill="#ffff89"/>
        <path id="Path_180731" data-name="Path 180731" d="M700.052,935.469a2.31,2.31,0,0,0-.487.611l.067-.022a2.118,2.118,0,0,1,.419-.588" transform="translate(-670.402 -896.567)" fill="#ca7700"/>
        <path id="Path_180732" data-name="Path 180732" d="M702.079,885.37a3.515,3.515,0,0,0-.453.385,2.117,2.117,0,0,0-.419.588l8.1-2.673.193-.064a14.267,14.267,0,0,0-7.422,1.764" transform="translate(-671.977 -846.852)" fill="#ffff89"/>
        <path id="Path_180733" data-name="Path 180733" d="M822.056,940.855c1.482-1.087,1.746-2.305.972-3.429l-.479.158c.646,1.043.368,2.163-1,3.167-2.807,2.06-9.225,2.913-14.885,2.07l-.479.157c5.992.954,12.883.068,15.863-2.124" transform="translate(-772.579 -898.443)" fill="#ca7700"/>
        <path id="Path_180734" data-name="Path 180734" d="M830.363,943.919c-2,1.423-6.358,2.077-10.451,1.681l-2.2.726c5.658.842,12.079-.01,14.885-2.07,1.368-1,1.648-2.124,1-3.167l-2.3.758c.274.692-.009,1.415-.931,2.072" transform="translate(-783.62 -901.954)" fill="#ffe400"/>
        <path id="Path_180735" data-name="Path 180735" d="M890.17,962.28l-.083.026-10.309,3.4-.243.08c3.829.29,7.8-.353,9.665-1.7.795-.581,1.1-1.2.971-1.81" transform="translate(-842.87 -922.264)" fill="#ffe400"/>
        <path id="Path_180736" data-name="Path 180736" d="M484.753,48.462l.064.075.034.045.025.052.021.086.757.179.94.239-.009-.051-.011-.046-.011-.046-.009-.039-.005-.036-.007-.043-.013-.066-.013-.054-.023-.068-.025-.076-.019-.052-.034-.089-.023-.043-.025-.051-.025-.051-.014-.017-.009-.014-.006-.011-.009-.014-.01-.014-.011-.021-.012-.021-.017-.02-.014-.017-.014-.017-.014-.017-.018-.02-.021-.023-.018-.02-.017-.02-.025-.026-.2-.157-.046-.029-.038-.023-.03-.017-.038-.023-.03-.017-.034-.02-.064-.024-.052-.028-.052-.028-.058-.026-.048-.024-.044-.021-.058-.026-.066-.025-.054-.023-.06-.02-.046-.017-.05-.02-.071-.023-.095-.029-.077-.022-.077-.022-.1-.024-.084-.02-.059-.014-.049-.012-.055-.011-.045-.009-.045-.009-.056-.011-.055-.011-.056-.011-.056-.011-.052-.008-.052-.008L483.9,47.2l-.072-.011-.058-.006-.058-.006-.068-.008-.069-.008-.075-.006-.065,0h-.05l-.054,0h-.044l-.059-.006h-.061l-.089,0h-.061l-.074,0h-.067l-.077,0-.057,0-.076,0-.081,0-.08,0-.064,0-.071.005-.078.007-.078.007-.067.008-.052,0-.08.012-.074.01-.056.01-.054.01-.065.012-.056.01-.046.012-.057.01-.369.093-.055.018-.046.012-.049.017-.049.017-.056.018-.058.023-.042.015-.052.022-.045.02-.046.02-.036.018-.038.018-.045.02-.041.023-.033.022-.034.022-.048.025-.034.022-.032.017-.039.019-.034.022-.034.022-.023.023-.039.032-.027.02-.039.032-.03.025-.025.029-.035.035-.03.038-.042.037-.032.036-.028.034-.021.032-.017.022-.02.04-.024.037-.012.025-.017.035-.007.034-.012.038-.017.043-.01.033-.006.037.008.033,0,.032-.005.037v.04l0,.043,0,.029.012.05.012.05.01.042.017.04.013.036.01.028.021.043.015.031.026.046.024.038.037.032.022.029.031.036.026.033.039.042.038.034.042.037.038.034.046.04.035.025.038.034.048.035.064.04.051.03.051.03.066.034.082.039.073.032.064.026.068.029.075.027.083.034.079.03.09.032.1.033.09.031.05.016.07.024.07.024.07.024.126.038.1.029.091.026.08.025.095.029.09.026.086.023.071.019.075.022.111.028.1.024.1.027.11.028.11.028.11.028.11.028.105.025.145.034.1.025.109.028.129.03.1.024.11.023.089.02.1.024.128.029.088.02.124.027.158.033.132.027.073.016.1.021.084.017.084.017.087.02.118.023.107.022.092.018.13.027.082.017.1.021.1.021.092.018.115.023.115.022.115.022.1.018.006-.052.015-.084.015-.134.013-.091.009-.094.009-.094.014-.116.006-.065,0-.02.055,0,.045,0,.035,0,.055,0,.039,0,.049.006.082.012.043.008.043.008.046.01.024.005.027.008.033.006.033.006.027.008.041.012.041.012.045.015.035.014.052.021.038.017.035.014.038.017.033.019.033.019.029.016.032.019.033.019.129.1.021.022.025.025.021.022.019.027.011.021.015.024.015.024.009.026.009.026.011.033.009.026,0,.028,0,.04,0,.03,0,.042-.006.027-.008.031-.014.033-.017.043-.017.03-.019.035-.031.039-.035.036-.056.041-.029.022-.044.035-.048.032-.047.028-.039.021-.039.021-.048.02-.049.02-.037.016-.185.06-.045.01-.051.012-.045.01-.039.009-.049.008-.034.007-.037,0-.049.008-.075.008-.059.007-.062,0-.057,0-.057,0h-.155l-.092,0-.1-.007-.069-.007-.074-.01-.078-.013-.068-.015-.1-.02-.091-.022-.085-.023-.068-.025-.068-.025-.095-.038-.079-.039-.069-.038-.054-.034-.1-.077-.052-.051-.04-.043-.038-.048-.026-.039-.034-.07-.008-.032-.005-.037-1.492.453.012.022.012.022.02.028.016.025.021.028.016.038.029.034.027.026.029.034.031.029.031.029.133.1.052.031.06.037.044.025.048.028.048.028.058.029.066.035.077.036.082.034.111.041.07.025.084.028.076.022.061.019.066.022.057.016.047.015.039.009.053.013.053.013.078.018.063.013.069.012.063.013.069.012.055.007.085.01.085.01.075.009.07.006.07.006.126.009.122.006.082,0,.082,0h.272l.1,0,.083,0,.079-.005.094-.006.11-.008.1-.008.075-.008.074-.008.08-.01.091-.014.082-.015.093-.019.1-.021.1-.021.106-.027.095-.024.062-.017.067-.018.212-.071.07-.028.054-.022.064-.026.072-.033.074-.037.087-.046.075-.042.058-.036.06-.041.066-.043.069-.052.056-.043.043-.035.052-.046.043-.047.041-.042.045-.051.044-.063.028-.046.036-.065.03-.062.029-.074.009-.035.006-.026.006-.026.009-.035,0-.034,0-.041,0-.029,0-.029,0-.032-.009-.025,0-.039,0-.027-.005-.035-.013-.04-.012-.04-.018-.051-.015-.036-.015-.036-.022-.041-.024-.037-.021-.034-.033-.05-.029-.035-.041-.044-.033-.038-.042-.039-.039-.036-.05-.045-.1-.078-.046-.03-.05-.033-.043-.027-.06-.034-.069-.035-.062-.029-.088-.037-.085-.035-.081-.032-.077-.029-.072-.021-.1-.032-.144-.05-.158-.055-.134-.042-.107-.033-.117-.034-.135-.042-.117-.034-.132-.039-.132-.039-.132-.039-.134-.034-.187-.049-.133-.037-.145-.036-.113-.03-.113-.03-.16-.041-.157-.039-.12-.028-.121-.028-.134-.032-.185-.052-.155-.035-.16-.039-.136-.033-.157-.036-.155-.035-.119-.026-.169-.038-.12-.026-.11-.024-.11-.025-.142-.031L483.6,48.8l-.142-.029-.179-.036,0,.031-.014.055-.008.053-.014.068-.012.076-.008.053-.012.076,0,.075,0,.069-.009.092-.016.073,0,.044h-.034l-.038,0-.038,0-.119-.01-.089-.014-.1-.018-.083-.015-.106-.026-.066-.016-.064-.021-.06-.018-.084-.028-.077-.03-.081-.037-.068-.037-.06-.031-.071-.046-.08-.06-.045-.04-.036-.034-.04-.036-.025-.032-.025-.032-.018-.033-.014-.031-.019-.034-.017-.039-.008-.045-.012-.049.005-.036,0-.044.019-.053.023-.05.025-.041.032-.043.036-.04.042-.041.051-.048.058-.04.049-.03.046-.025.059-.028.059-.028.1-.034.1-.03.1-.027.107-.024L482.61,48l.133-.018.14-.011.111-.008.166-.006.153.006.141.009.127.013.094.013.1.016.092.018.085.02.085.02.057.018.09.03.08.029.084.032.077.034.083.044.069.04.067.045.068.052Z" transform="translate(-459.772 -45.179)" fill="#ca7700"/>
        <path id="Path_180737" data-name="Path 180737" d="M474.325,46.882l.065.075.034.045.025.052.021.086.763.18.948.241-.009-.051-.011-.046-.011-.046-.01-.039-.005-.036-.007-.043-.013-.066-.013-.054-.023-.068-.025-.076-.019-.052L476,46.893l-.023-.043-.025-.051-.025-.051-.014-.017-.01-.014-.006-.011-.01-.014-.01-.014-.011-.022-.012-.022-.018-.02-.014-.017-.014-.017-.014-.017-.018-.02-.022-.023-.018-.02-.017-.02-.026-.026-.2-.158-.046-.029-.038-.023-.03-.017-.038-.023-.03-.017-.034-.02-.064-.025-.052-.028-.052-.028-.059-.026L475,46.039l-.044-.021-.059-.026-.067-.026-.055-.023-.06-.021-.047-.017-.051-.02-.071-.023-.1-.029-.078-.022-.078-.022-.1-.024-.084-.02-.06-.014-.049-.012-.056-.011L473.9,45.7l-.045-.009-.056-.011-.056-.011-.056-.011-.056-.011-.052-.008-.052-.008-.067-.011-.073-.011-.059-.006-.059-.006-.069-.008-.069-.008-.076-.006-.065,0h-.051l-.055,0h-.045l-.059-.006h-.061l-.089,0h-.062l-.075,0h-.068l-.078,0-.058,0-.076,0-.082,0-.081,0-.065,0-.072.005-.078.007-.079.007-.068.008-.053,0-.081.012-.075.01-.057.01-.055.01-.065.012-.057.01-.046.012-.057.01-.373.093-.056.018-.047.012-.049.017-.049.017-.056.018-.059.024-.042.015-.052.022-.045.02-.046.02-.037.018L470.2,46l-.046.02-.041.023-.034.022-.034.022-.048.025-.034.022-.032.017-.039.019-.034.022-.035.022-.023.023-.039.032-.028.02-.039.032-.03.025-.026.029-.035.035-.031.039-.043.037-.032.036-.029.034-.022.032-.017.022-.02.04-.024.037-.012.025-.017.035-.007.034-.013.039-.017.043-.01.034-.006.037.008.033,0,.032-.006.037v.04l0,.044,0,.029.012.05.012.05.01.042.018.04.013.037.01.028.021.043.015.031.026.046.024.038.037.033.022.03.031.036.026.033.04.042.038.034.042.037.038.034.046.04.035.025.038.034.049.035.065.04.052.03.051.03.067.035.082.039.073.033.065.027.069.029.075.027.084.034.08.031.091.032.1.033.091.032.051.016.071.024.07.024.07.024.127.038.1.03.091.026.081.025.1.029.091.026.087.023.072.019.076.022.112.029.1.024.1.027.111.028.111.028.111.028.111.028.106.025.146.035.106.025.11.028.131.03.1.025.111.023.09.02.1.025.129.03.089.02.125.027.159.033.133.028.074.016.1.021.084.017.084.017.088.02.119.023.108.022.093.018.131.027.083.017.1.021.1.021.092.018.117.023.116.023.116.023.1.019.007-.052.016-.084.015-.134.013-.092.009-.094.009-.094.014-.117.006-.065,0-.02.056,0,.046,0,.035,0,.055,0,.039,0,.05.006.083.012.043.008.043.008.047.011.024.005.027.008.033.006.033.006.027.008.041.012.041.012.045.015.035.014.053.021.039.017.035.014.039.017.033.019.033.019.029.016.033.019.033.019.13.1.021.022.025.025.021.022.019.027.011.021.015.024.015.024.009.026.009.026.012.034.009.026,0,.028,0,.04,0,.03,0,.042-.006.027-.008.031-.014.033-.018.043-.018.03-.02.035-.032.039-.035.036-.057.041-.029.022-.045.035-.049.032-.047.028-.039.021-.039.021-.049.02-.049.02-.037.016-.187.061-.045.01-.051.012-.045.01-.039.009-.049.008-.034.007-.038,0-.049.008-.076.008-.059.007-.062,0-.057,0-.058,0h-.156l-.092,0-.1-.007-.07-.007-.074-.01-.078-.013-.068-.015-.1-.02-.091-.022-.085-.024-.069-.025-.069-.025-.1-.038-.08-.039-.069-.038-.055-.034-.105-.078-.052-.052-.04-.043-.038-.048-.026-.039-.034-.07-.008-.032-.005-.037-1.505.454.012.022.012.022.021.028.017.025.021.028.017.039.029.035.027.026.029.035.031.029.031.029.134.1.052.031.061.037.044.025.048.028.048.028.058.029.067.035.077.036.083.034.112.042.07.025.085.029.077.023.062.019.066.022.058.016.047.015.039.009.053.013.053.013.078.018.064.014.07.012.064.014.069.012.055.008.086.01.086.01.075.009.071.007.071.006.127.009.123.006.083,0,.082,0h.274l.1,0,.084,0,.08-.005.1-.006.111-.007.1-.008.075-.008.075-.008.08-.01.092-.014.082-.015.094-.019.1-.021.1-.021.107-.027.1-.024.062-.017.067-.018.213-.072.071-.028.054-.022.065-.026.073-.033.075-.038.087-.046.076-.042.059-.036.06-.041.067-.043.07-.052.056-.043.043-.035.053-.046.043-.047.041-.042.045-.052.045-.064.028-.046.036-.065.03-.063.03-.075.009-.036.006-.026.006-.026.009-.036,0-.034,0-.041,0-.029,0-.029,0-.032-.009-.025,0-.04,0-.027L479.2,49.5l-.013-.04-.013-.04-.018-.051-.015-.036-.015-.036-.022-.041-.024-.037-.021-.034-.034-.05-.029-.035-.041-.044-.033-.038-.043-.039-.039-.036-.051-.045-.1-.078-.047-.03-.051-.033-.043-.027-.06-.034-.07-.035-.062-.03-.089-.038-.086-.035-.081-.032-.078-.029-.073-.021-.1-.032-.145-.05-.159-.055-.135-.042-.107-.033-.118-.034-.136-.042-.118-.035-.132-.039-.133-.039-.133-.039-.135-.035-.188-.049-.135-.037-.146-.036-.114-.03-.114-.03-.162-.041-.159-.04-.121-.028-.122-.028L475,47.643l-.187-.052-.156-.035-.162-.039-.137-.033-.158-.036-.156-.036-.12-.026-.17-.038-.121-.026-.111-.025-.111-.025-.143-.031-.108-.022-.143-.029-.181-.037,0,.031-.014.055-.008.053-.014.068-.012.076-.008.053-.012.076,0,.076,0,.069-.009.093-.017.073,0,.044h-.034l-.038,0-.038,0-.12-.01-.09-.014-.105-.018-.084-.016-.107-.026-.067-.016-.064-.021-.06-.018-.084-.029-.077-.031-.082-.037-.069-.037-.06-.031-.072-.046-.081-.06-.045-.04-.037-.034-.041-.037-.026-.032-.025-.032-.018-.033-.014-.031-.019-.034-.017-.039-.008-.046-.012-.049.005-.036,0-.045.02-.053.023-.05.025-.041.032-.043.036-.04.043-.042.052-.048.059-.04.049-.03.047-.025.059-.029.059-.028.1-.034.1-.031.1-.027.108-.024.093-.016.134-.018.142-.011.111-.008.167-.005.154.006.142.009.128.013.095.013.1.016.092.018.086.02.085.02.058.018.091.031.081.029.085.032.078.034.084.045.07.04.068.045.069.053Z" transform="translate(-449.736 -43.659)" fill="#ffff89"/>
        <path id="Path_180738" data-name="Path 180738" d="M479.161,47.591l.064.075.034.045.025.052.021.086.76.18.944.24L481,48.218l-.011-.046-.011-.046-.009-.039-.005-.036-.007-.043-.013-.066-.013-.054-.023-.068-.025-.076-.019-.052-.034-.089-.023-.043-.025-.051-.025-.051-.014-.017-.01-.014-.006-.011-.01-.014-.01-.014-.011-.022-.012-.021-.017-.02-.014-.017-.014-.017-.014-.017-.017-.02-.021-.023-.018-.02-.018-.02-.025-.026-.2-.158-.046-.029-.038-.023-.03-.017-.038-.023-.03-.017-.034-.02-.064-.025-.052-.028-.052-.028-.058-.026-.048-.025-.044-.021-.058-.026-.067-.026-.054-.023-.06-.021-.046-.017-.051-.02-.071-.023-.1-.029-.077-.022-.077-.022-.1-.024-.084-.02-.06-.014-.049-.012-.055-.011-.045-.009L478.7,46.4l-.056-.011-.056-.011-.056-.011-.056-.011-.052-.008-.052-.008-.067-.011-.073-.011-.058-.006-.058-.006-.069-.008-.069-.008-.075-.006-.065,0h-.051l-.054,0h-.045l-.059-.006h-.061l-.089,0h-.061l-.074,0h-.067l-.077,0-.058,0-.076,0-.082,0-.08,0-.065,0-.071.005-.078.007-.078.007-.067.008-.053,0-.08.012-.074.01-.057.01-.054.01-.065.012-.057.01-.046.012L476,46.4l-.371.093-.056.018-.047.012-.049.017-.049.017-.056.018-.059.023-.042.015-.052.022-.045.02-.046.02-.037.018-.038.018-.045.02-.041.023-.034.022-.034.022-.048.025-.034.022-.032.017-.039.019-.034.022-.034.022-.023.023-.039.032-.027.02-.039.032-.03.025-.026.029-.035.035-.031.039-.042.037-.032.036-.028.034-.021.032-.017.022-.02.04-.024.037-.012.025-.017.035-.007.034-.013.038-.017.043-.01.034-.006.037.008.033,0,.032-.006.037v.04l0,.044,0,.029.012.05.012.05.01.042.017.04.013.036.01.028.022.043.015.031.026.046.024.038.037.033.022.03.031.036.026.033.039.042.038.034.042.037.038.034.046.04.035.025.038.034.049.035.064.04.051.03.051.03.067.035.082.039.073.033.065.026.068.029.075.027.084.034.079.031.09.032.1.033.09.032.051.016.07.024.07.024.07.024.126.038.1.029.091.026.08.025.1.029.091.026.086.023.071.019.076.022.112.028.1.024.1.027.111.028.111.028.111.028.11.028.106.025.145.035.105.025.109.028.13.03.1.025.111.023.089.02.1.024.129.03.089.02.125.027.158.033.133.028.074.016.1.021.084.017.084.017.087.02.118.023.107.022.093.018.131.027.083.017.1.021.1.021.092.018.116.023.116.023.116.022.1.019.007-.052.015-.084.015-.134.012-.092.009-.094.009-.094.014-.117.006-.065,0-.02.055,0,.045,0,.035,0,.055,0,.039,0,.049.006.082.012.043.008.043.008.047.01.023.005.027.008.033.006.033.006.027.008.041.012.041.012.045.015.035.014.052.021.039.017.035.014.039.017.033.019.033.019.029.016.033.019.033.019.13.1.021.022.025.025.021.022.019.027.011.021.015.024.015.024.009.026.009.026.011.033.009.026,0,.028,0,.04,0,.03,0,.042-.006.027-.008.031-.014.033-.018.043-.017.03-.02.035-.031.039-.035.036-.057.041-.029.022-.044.035-.048.032-.047.028-.039.021-.039.021-.049.02-.049.02-.037.016-.187.061-.045.01-.051.012-.045.01-.039.009-.049.008-.034.007-.038,0-.049.008-.075.008-.06.007-.062,0-.057,0-.057,0h-.155l-.092,0-.1-.007-.07-.007-.074-.01-.078-.013-.068-.015-.1-.02-.091-.022-.085-.024-.068-.025-.068-.025-.095-.038-.079-.039-.069-.038-.055-.034-.1-.077-.052-.052-.04-.043-.038-.048-.026-.039-.034-.07-.008-.032-.005-.037-1.5.453.012.022.012.022.02.028.017.025.021.028.017.038.029.035.027.026.029.034.031.029.031.029.133.1.052.031.06.037.044.025.048.028.048.028.058.029.066.035.077.036.083.034.112.042.07.025.085.029.076.022.062.019.066.022.057.016.047.015.039.009.053.013.053.013.078.018.064.014.069.012.063.013.069.012.055.008.085.01.085.01.075.009.071.006.071.007.127.009.122.006.082,0,.082,0h.273l.1,0,.084,0,.08-.005.095-.006.11-.007.1-.008.075-.008.075-.008.08-.01.092-.014.082-.015.094-.019.1-.021.1-.021.106-.027.1-.024.062-.017.067-.018.212-.071.071-.028.054-.022.065-.026.072-.033.075-.037.087-.046.076-.042.059-.036.06-.041.066-.043.069-.052.056-.043.043-.035.052-.046.043-.047.041-.042.045-.052.045-.064.028-.046.036-.065.03-.063.03-.075.009-.036.006-.026.006-.026.009-.036,0-.034,0-.041,0-.029,0-.029,0-.032-.009-.025,0-.039,0-.027-.005-.035L484,50.16l-.013-.04-.018-.051-.015-.036L483.944,50l-.022-.041-.024-.037-.02-.034-.034-.05-.03-.035-.04-.044-.033-.038-.043-.039-.039-.036-.05-.045-.1-.078-.047-.03-.05-.033-.043-.027-.06-.034-.069-.035-.062-.03-.088-.038L483,49.257l-.081-.032-.077-.029-.073-.021-.1-.032-.145-.05-.159-.055L482.237,49l-.107-.033-.117-.034-.136-.042-.118-.035-.132-.039-.132-.039-.133-.039-.134-.035-.188-.049-.134-.037-.146-.036-.113-.03-.114-.03-.161-.041-.158-.04-.121-.028-.121-.028-.134-.032-.186-.052-.156-.035-.161-.039-.136-.033-.157-.036-.156-.035-.12-.026-.17-.038-.121-.026-.11-.025-.11-.025-.143-.031-.107-.022-.142-.029-.18-.037,0,.031-.014.055-.008.053-.014.068-.012.076-.008.053-.012.076,0,.076,0,.069-.009.092-.017.073,0,.044h-.034l-.038,0-.038,0-.119-.01-.09-.014-.1-.018-.083-.016-.107-.026-.067-.016-.064-.021-.06-.018-.084-.029-.077-.03-.081-.037-.069-.037-.06-.031-.071-.046-.081-.06-.045-.04-.037-.034-.04-.037-.025-.032-.025-.032-.018-.033-.014-.031-.019-.034-.017-.039-.008-.046L476,47.812l.005-.036,0-.044.019-.053.023-.05.025-.041.032-.043.037-.04.042-.041.051-.048.058-.04.049-.03.046-.025.059-.029.059-.028.1-.034.1-.03.1-.027.108-.024.093-.016.134-.018.141-.011.111-.008.167-.005.154.006.142.009.128.013.094.013.1.016.092.018.085.02.085.02.058.018.091.03.08.029.084.032.078.034.084.044.069.04.068.045.069.052Z" transform="translate(-454.39 -44.342)" fill="#f0b500"/>
        <path id="Path_180739" data-name="Path 180739" d="M183.622,638.309l.064.075.034.045.025.052.021.086.757.179.94.239-.009-.051-.011-.046-.011-.046-.009-.039-.005-.036-.007-.043-.013-.066-.013-.054-.023-.068-.025-.076-.019-.052-.034-.089-.023-.043-.025-.051-.025-.051-.014-.017-.01-.014-.006-.011-.01-.014-.01-.014-.011-.021-.011-.021-.017-.02-.014-.017-.014-.017-.014-.017-.017-.02-.021-.023-.017-.02-.017-.02-.025-.026-.2-.157-.046-.029-.038-.023-.03-.017-.038-.023-.03-.017-.034-.02-.064-.024-.052-.028-.052-.028-.058-.026-.048-.025-.044-.021-.058-.026-.066-.026-.054-.023-.06-.021-.046-.017-.05-.02-.071-.023-.095-.029-.077-.022-.077-.022-.1-.024-.083-.02-.059-.014-.049-.012-.055-.01-.045-.009-.045-.009-.055-.011-.056-.011-.055-.011-.056-.01-.052-.008-.052-.008-.066-.011-.072-.011-.058-.006-.058-.006-.068-.008-.069-.008-.075-.006-.065,0h-.05l-.054,0h-.044l-.059-.006h-.061l-.089,0h-.061l-.074,0h-.067l-.077,0-.057,0-.075,0-.081,0-.08,0-.064,0-.071.005-.078.007-.078.007-.067.008-.053,0-.08.011-.074.01-.057.01-.054.01-.065.012-.056.01-.046.012-.057.01-.369.093-.055.018-.046.012-.049.017-.049.017-.056.018-.059.023-.042.015-.052.022-.045.02-.046.02-.036.018-.038.018-.045.02-.041.024-.034.021-.034.022-.048.025-.034.022-.032.017-.039.019-.034.022-.034.022-.023.023-.039.032-.027.02-.039.032-.03.025-.025.029-.035.035-.031.038-.042.037-.032.036-.028.034-.021.032-.017.022-.019.04-.024.037-.012.025-.017.035-.007.034-.013.038-.017.043-.01.033-.006.037.008.033,0,.032-.006.037v.04l0,.044,0,.029.012.05.012.05.01.042.017.04.013.036.01.028.021.043.015.031.026.046.024.038.037.032.022.03.031.036.026.033.039.042.038.034.042.037.038.034.046.04.035.025.038.034.048.035.064.04.051.03.051.03.066.034.082.039.073.032.064.026.068.029.075.027.083.034.079.03.09.032.1.033.09.032.05.016.07.024.07.024.07.024.126.038.1.029.091.026.08.025.095.029.09.026.086.023.071.019.075.022.111.028.1.024.1.027.111.028.11.028.11.028.11.028.105.025.145.034.1.025.109.028.13.03.1.024.11.023.089.02.1.024.128.029.088.02.124.027.158.033.132.027.073.016.1.021.084.017.083.017.087.02.118.023.107.022.092.018.13.027.082.017.1.021.1.021.091.017.116.023.115.022.115.023.1.018.007-.052.015-.084.015-.134.013-.091.009-.094.009-.094.014-.116.006-.065,0-.02.055,0,.045,0,.035,0,.055,0,.039,0,.049.006.082.012.043.008.043.008.046.01.023.005.027.008.033.006.033.006.027.008.041.012.041.012.044.015.035.014.052.021.038.017.035.014.038.017.033.019.033.019.029.016.032.019.033.019.129.1.021.022.025.025.021.022.019.027.011.021.015.024.015.024.009.026.009.026.011.033.009.026,0,.027,0,.04,0,.029,0,.042-.006.026-.008.031-.014.033-.018.043-.017.03-.019.035-.031.039-.035.036-.056.041-.029.022-.044.035-.048.032-.047.028-.039.021-.039.021-.048.02-.049.02-.037.016-.186.061-.045.01-.051.012-.045.01-.039.009-.049.008-.033.007-.037,0-.049.008-.075.008-.059.007-.062,0-.057,0-.057,0h-.155l-.092,0-.1-.007-.07-.007-.074-.01-.078-.013-.068-.015-.1-.02-.091-.021-.084-.023-.068-.025-.068-.025-.095-.037-.079-.039-.069-.038-.054-.034-.1-.077-.052-.052-.04-.042-.038-.048-.026-.039-.034-.07-.008-.032-.005-.037-1.492.453.012.022.012.022.02.028.016.025.021.028.017.038.029.034.027.026.029.035.031.029.031.029.133.1.052.031.06.037.044.025.048.028.048.028.058.029.066.035.077.036.082.034.111.042.07.025.084.029.076.022.061.019.066.022.057.016.047.015.039.009.053.013.053.013.078.018.063.013.069.011.063.013.069.011.055.007.085.01.085.01.075.009.07.006.07.006.126.009.122.006.082,0,.082,0h.272l.1,0,.083,0,.079-.005.094-.006.11-.008.1-.008.075-.008.074-.008.08-.01.091-.014.082-.015.093-.019.1-.021.1-.021.106-.027.095-.024.062-.017.067-.018.211-.071.07-.028.054-.022.064-.026.072-.033.074-.037.087-.046.075-.042.058-.036.06-.041.066-.043L188,641.9l.056-.043.043-.035.052-.046.043-.047.041-.042.045-.051.044-.063.028-.046.036-.065.03-.062.03-.074.009-.035.006-.026.006-.026.009-.035,0-.034,0-.041,0-.029,0-.029,0-.032-.009-.025,0-.04,0-.027-.005-.035-.013-.04-.012-.04-.018-.05-.015-.036-.015-.036-.022-.041-.024-.037-.02-.034-.034-.05-.029-.035-.04-.044-.033-.038-.042-.039-.039-.036-.05-.045-.1-.078-.046-.03-.05-.033-.043-.027-.06-.034-.069-.035-.062-.029-.088-.037-.085-.035-.081-.032-.077-.029-.072-.021-.1-.032-.144-.05-.158-.055-.134-.041-.107-.033-.117-.034-.135-.042-.117-.035-.131-.039-.132-.039-.132-.039-.134-.034-.187-.049-.133-.037-.145-.036-.113-.03-.113-.03-.16-.04-.157-.04-.12-.028-.121-.028-.134-.032-.185-.052-.155-.035-.16-.039-.136-.033-.157-.036-.155-.035-.119-.026-.169-.038-.12-.026-.109-.025-.11-.024-.142-.031-.107-.022-.142-.029-.179-.037,0,.031-.014.055-.008.053-.014.068-.012.076-.008.053-.012.076,0,.076,0,.069-.009.092-.016.073,0,.044h-.034l-.038,0-.038,0-.119-.01-.089-.014-.1-.018-.083-.015-.106-.026-.066-.016-.064-.021-.06-.018-.083-.028-.077-.03-.081-.037-.068-.037-.06-.031-.071-.046-.081-.06-.045-.04-.036-.034-.04-.036-.025-.032-.025-.032-.018-.033-.014-.031-.019-.034-.017-.039-.008-.046-.012-.049.005-.036,0-.044.019-.053.023-.05.025-.041.032-.043.036-.04.042-.041.051-.048.058-.04.049-.03.046-.025.059-.028.059-.029.1-.034.1-.03.1-.027.107-.024.092-.016.133-.018.14-.012.11-.008.166-.006.153.006.141.009.127.013.094.013.1.016.091.018.085.02.085.02.057.018.09.03.08.029.084.032.077.034.083.044.069.04.067.045.068.052Z" transform="translate(-171.193 -610.497)" fill="#ca7700"/>
        <path id="Path_180740" data-name="Path 180740" d="M173.193,636.729l.065.075.034.045.026.052.021.086.763.18.948.241-.009-.051-.011-.047-.011-.046-.009-.039L175,637.19l-.007-.043-.013-.066-.013-.054-.023-.068-.025-.076-.019-.052-.034-.089-.023-.043-.025-.051-.025-.051-.014-.017-.01-.014-.006-.011-.01-.014-.01-.014-.011-.021-.011-.022-.018-.02-.014-.017-.014-.017-.014-.017-.018-.02-.022-.023-.018-.02-.018-.02-.026-.026-.2-.158-.046-.029-.038-.023-.03-.017-.038-.023-.03-.017-.034-.02-.064-.025-.052-.028-.052-.028-.058-.026-.048-.025-.044-.021-.059-.026-.067-.026-.055-.023-.06-.021-.047-.017-.051-.021-.072-.023-.1-.029-.077-.022-.078-.022-.1-.024-.084-.02-.06-.014-.049-.012-.056-.011-.045-.009-.045-.009-.056-.011-.056-.011-.056-.011-.056-.011-.052-.008-.052-.008-.067-.011-.073-.011-.059-.006-.059-.006-.069-.008-.069-.008-.076-.006-.065,0h-.051l-.055,0h-.045l-.059-.006h-.061l-.09,0h-.062l-.075,0H171.3l-.078,0-.058,0-.076,0-.082,0-.081,0-.065,0-.071.005-.079.007-.079.007-.068.008-.053,0-.081.012-.075.01-.057.01-.055.01-.065.012-.057.01-.046.012-.057.01-.373.093-.056.018-.047.012-.049.017-.049.017-.056.019-.059.024-.042.015-.052.021-.045.02-.047.02-.037.018-.039.018-.045.02-.041.023-.034.022-.034.022-.048.025-.034.022L168.8,636l-.039.019-.034.022-.035.022-.023.023-.039.032-.028.02-.039.032-.03.025-.026.029-.035.035-.031.039-.043.037-.032.036-.029.034-.022.032-.017.022-.02.04-.024.037-.012.025-.017.035-.008.034-.013.039-.017.043-.01.033-.006.037.008.033,0,.032-.006.037v.04l0,.044,0,.029.012.05.012.05.01.042.018.04.013.036.01.028.022.043.015.031.026.046.024.038.037.033.022.03.031.036.026.033.04.042.038.034.042.038.038.034.046.04.035.025.038.034.049.035.064.04.052.03.051.03.067.034.082.039.073.033.065.026.069.029.075.027.084.034.079.031.091.032.1.033.09.032.051.016.07.024.07.024.07.024.127.038.1.03.091.026.081.025.1.029.091.026.087.023.072.019.076.022.112.029.1.024.1.027.111.028.111.028.111.028.111.028.106.025.146.035.106.025.11.028.131.03.1.025.111.023.09.02.1.025.129.03.089.02.125.027.159.033.134.028.074.016.1.021.084.017.084.017.088.02.119.023.108.022.093.018.131.027.083.017.1.021.1.021.092.018.117.023.116.023.116.022.1.019.007-.052.016-.084.015-.134.013-.092.009-.095.009-.094.014-.117.006-.065,0-.02.055,0,.046,0,.036,0,.055,0,.039,0,.049.006.083.012.043.008.043.008.047.011.024.005.027.008.033.006.033.006.027.008.041.012.041.012.045.015.035.014.053.021.039.017.035.014.039.017.033.019.033.019.029.016.033.019.033.019.13.1.021.022.025.025.021.022.019.027.011.021.015.024.015.024.009.026.009.026.012.034.009.026,0,.028,0,.04,0,.03,0,.042-.006.027-.008.031-.014.033-.018.043-.018.03-.02.035-.032.039-.035.036-.057.041-.029.022-.045.035-.049.032-.047.028-.039.021-.039.021-.049.02-.049.02-.037.016-.187.061-.045.01-.051.012-.045.01-.039.009-.049.008-.034.007-.038,0-.05.008-.076.008-.06.007-.062,0-.058,0-.058,0h-.156l-.092,0-.1-.007-.07-.007-.074-.01-.078-.013-.069-.015-.1-.02-.091-.022-.085-.024-.069-.025-.069-.025-.1-.038-.08-.039-.069-.038-.055-.034-.105-.078-.052-.052-.04-.043-.038-.048-.026-.039-.034-.071-.008-.032-.005-.037-1.505.454.012.022.012.022.021.028.016.025.021.028.017.038.029.035.027.026.029.035.031.029.031.029.134.1.052.031.061.037.044.025.048.028.048.028.058.029.067.035.077.036.083.034.112.042.07.025.085.029.077.023.062.019.066.022.058.016.047.015.039.009.053.012.053.013.078.018.064.013.07.012.064.014.069.012.055.008.086.01.086.011.075.009.071.007.071.006.127.009.123.006.083,0,.082,0h.274l.1,0,.084,0,.08-.005.1-.006.111-.008.1-.008.075-.008.075-.008.08-.01.092-.014.082-.015.094-.019.1-.021.1-.021.107-.027.1-.024.062-.017.067-.018.213-.072.071-.028.054-.022.065-.026.072-.032.075-.037.087-.046.076-.042.059-.036.06-.041.067-.043.07-.052.057-.043.043-.035.053-.046.043-.047.041-.042.045-.052.045-.064.028-.046.036-.065.03-.063.03-.075.009-.036.006-.026.006-.026.009-.035,0-.034,0-.042,0-.029,0-.029,0-.032-.009-.025,0-.04,0-.027-.005-.035-.013-.04-.013-.04-.018-.051-.015-.036-.015-.036-.022-.042-.024-.037-.021-.034-.034-.05-.029-.035-.041-.044-.033-.038-.043-.039-.039-.036-.051-.045-.1-.078-.047-.03-.05-.033-.043-.028-.06-.034-.07-.035-.062-.03-.089-.038-.086-.035-.081-.032-.078-.029-.073-.021-.1-.032-.145-.05-.159-.055-.135-.042-.108-.033-.118-.035-.136-.042-.118-.035-.132-.039-.133-.039-.133-.039-.135-.035-.188-.049-.135-.037-.146-.036-.114-.03-.114-.03-.161-.041-.159-.04-.121-.028-.122-.028-.135-.033-.187-.052-.156-.035-.162-.039-.137-.033-.158-.036-.156-.036-.12-.026-.17-.038-.121-.026-.11-.025-.111-.025-.143-.031-.108-.022-.143-.029L171.71,637l0,.031-.014.055-.008.053-.014.068-.012.076-.008.053-.012.076,0,.076,0,.07-.009.093-.017.073,0,.044h-.034l-.038,0-.038,0-.12-.01-.09-.014-.1-.018-.084-.016-.107-.026-.067-.016-.064-.021-.06-.018-.084-.029-.077-.031-.082-.037-.069-.037-.06-.031-.072-.046-.081-.06-.045-.04-.037-.034-.041-.037-.026-.032-.025-.032-.018-.033-.015-.031-.019-.034-.017-.039-.007-.046-.012-.049.005-.036,0-.045.02-.053.024-.05.025-.041.032-.043.037-.04.043-.042.052-.048.059-.04.049-.03.046-.025.059-.029.059-.028.1-.034.1-.031.1-.027.108-.024.093-.016.134-.018.141-.011.111-.008.167-.006.155.006.142.009.128.013.095.013.1.016.092.018.086.02.086.02.058.018.091.03.081.029.085.032.078.034.084.044.07.04.068.045.069.053Z" transform="translate(-161.158 -608.977)" fill="#ffff89"/>
        <path id="Path_180741" data-name="Path 180741" d="M178.03,637.439l.065.074.034.045.026.052.021.086.76.18.944.24-.009-.051-.011-.046-.011-.046-.009-.039-.005-.036-.007-.043-.013-.066-.013-.054-.023-.068-.025-.076-.019-.052-.034-.089-.023-.043-.025-.051-.025-.051-.014-.017-.01-.014-.006-.011-.01-.014-.01-.014-.011-.022-.011-.022-.018-.02-.014-.017-.014-.017-.014-.017-.018-.02-.021-.023-.018-.02-.017-.02-.025-.026-.2-.157-.046-.029-.038-.023-.03-.017-.038-.023-.03-.017-.034-.02-.064-.025-.052-.028-.052-.028-.058-.026-.048-.024-.044-.022-.058-.026-.067-.026-.054-.023-.06-.02-.046-.017-.051-.02-.071-.023-.1-.029-.077-.022-.077-.022-.1-.024-.084-.02-.059-.014-.049-.012-.056-.011-.045-.01-.045-.009-.056-.011-.056-.011-.056-.011-.056-.011-.052-.008-.052-.008-.067-.011-.073-.011-.058-.006-.058-.006-.069-.008-.069-.008-.075-.006-.065,0h-.051l-.054,0h-.045l-.059-.006h-.061l-.089,0h-.061l-.074,0h-.067l-.077,0-.058,0-.076,0-.082,0-.08,0-.065,0-.071.005-.078.007-.078.007-.067.008-.053,0-.08.012-.074.01-.057.01-.055.01-.065.012-.057.01-.046.012-.057.01-.371.093-.055.018-.047.012-.049.017-.049.016-.056.018-.059.023-.042.015-.052.022-.045.02-.046.02-.037.018-.038.018-.045.02-.041.023-.034.022-.034.022-.048.025-.034.022-.032.017-.039.018-.034.022-.034.022-.023.023-.039.032-.028.02-.039.032-.03.025-.026.029-.035.035-.031.039-.042.037-.032.036-.028.034-.021.032-.017.022-.019.04-.024.037-.012.025-.017.035-.008.034-.013.039-.017.043-.01.033-.006.037.008.033,0,.032-.006.037v.04l0,.044,0,.029.012.05.012.05.01.042.017.04.013.036.01.028.021.043.015.031.026.046.024.038.037.033.022.03.031.036.026.033.039.042.038.034.042.037.038.034.046.04.035.025.038.034.049.035.064.04.051.03.051.03.067.034.082.039.073.033.065.026.069.029.075.027.084.034.079.03.09.032.1.033.09.031.051.017.07.024.07.024.07.024.126.038.1.029.091.026.08.025.1.029.091.026.086.023.071.019.076.022.112.028.1.024.1.027.111.028.111.028.111.028.11.028.106.025.145.035.105.025.109.028.13.03.1.025.111.023.089.02.1.024.129.029.089.02.125.027.158.033.133.027.074.016.1.021.084.017.084.017.087.02.118.023.107.022.093.017.131.027.083.017.1.021.1.021.092.018.116.023.116.022.115.023.1.019.007-.052.015-.084.015-.134.013-.091.009-.094.009-.094.014-.117.006-.065,0-.02.055,0,.045,0,.035,0,.055,0,.039,0,.049.006.082.012.043.008.043.008.047.01.024.005.027.008.033.006.033.006.027.008.041.012.041.012.045.015.035.014.052.021.039.017.035.014.039.017.033.019.033.019.029.016.033.019.033.019.13.1.021.022.025.025.021.022.019.027.011.021.015.024.015.024.009.026.009.026.011.033.009.026,0,.028,0,.04,0,.03,0,.042-.006.027-.008.031-.014.033-.018.043-.017.03-.02.035-.031.039-.035.036-.057.041-.029.022-.044.035-.048.032-.047.028-.039.021-.039.021-.049.02-.049.02-.037.016-.187.061-.045.01-.051.012-.045.01-.039.009-.049.007-.034.007-.038,0-.049.008-.075.008-.059.007-.062,0-.057,0-.057,0h-.155l-.092,0-.1-.007-.07-.007-.074-.01-.078-.013-.068-.015-.1-.02-.091-.022-.085-.024-.068-.025-.068-.025-.095-.038-.079-.039-.069-.038-.055-.034-.1-.077-.052-.051-.04-.043-.038-.048-.026-.039-.034-.07-.008-.032-.005-.037-1.5.453.012.022.012.022.021.028.016.025.021.028.017.038.029.035.027.026.029.034.031.029.031.029.133.1.052.031.06.037.044.025.048.028.048.028.058.029.066.035.077.036.083.034.112.042.07.025.085.029.076.023.062.019.066.022.057.016.047.015.039.009.053.013.053.013.078.018.064.014.069.012.063.014.069.012.055.008.085.01.085.01.075.01.071.006.071.007.127.009.122.006.082,0,.082,0h.273l.1,0,.084,0,.08-.006.095-.006.11-.008.1-.008.075-.008.075-.009.08-.01.092-.014.082-.015.094-.019.1-.021.1-.021.106-.027.1-.024.062-.017.067-.018.212-.072.071-.028.054-.022.065-.026.072-.033L182,641.3l.087-.046.076-.042.059-.036.06-.041.066-.043.069-.052.056-.043.043-.035.052-.046.043-.047.041-.042.045-.052.045-.063.028-.046.036-.065.03-.063.03-.075.009-.035.006-.026.006-.026.009-.035,0-.034,0-.041,0-.029,0-.029,0-.032-.009-.025,0-.039,0-.027-.005-.035-.013-.04-.013-.04-.018-.051-.015-.036-.015-.036-.022-.041-.024-.037-.02-.034-.034-.05-.029-.035-.041-.044-.033-.038-.043-.039-.039-.036-.05-.045-.1-.078-.047-.03-.05-.033-.043-.027-.06-.034-.069-.035-.062-.029-.088-.038-.085-.035-.081-.032-.078-.029-.073-.021-.1-.032-.145-.049-.159-.055-.135-.042-.107-.033-.117-.034-.136-.042-.118-.034-.132-.039-.132-.039-.133-.039-.135-.034-.187-.049-.134-.037-.146-.036-.113-.03-.113-.03-.161-.041-.158-.04-.121-.028-.121-.028-.134-.033-.186-.052-.156-.035-.161-.039-.136-.033-.157-.036-.156-.035-.12-.026-.17-.038-.12-.026-.11-.025-.11-.025-.143-.031-.107-.022-.142-.029-.18-.036,0,.031-.014.055-.008.053-.014.068-.012.076-.008.053-.012.076,0,.075,0,.069-.009.092-.017.073,0,.044h-.034l-.038,0-.038,0-.119-.01-.09-.014-.1-.018-.083-.016-.107-.026-.067-.016-.064-.021-.06-.018-.084-.029-.077-.03-.081-.037-.069-.037-.06-.031-.071-.046-.081-.06-.045-.04-.037-.034-.041-.036-.025-.032-.025-.032-.018-.033-.014-.031-.019-.034-.017-.039-.008-.046-.012-.049.005-.036,0-.045.02-.053.023-.05.025-.041.032-.043.037-.04.042-.041.051-.048.058-.04.049-.03.046-.025.059-.029.059-.028.1-.034.1-.03.1-.027.108-.024.093-.016.134-.018.141-.011.111-.008.167-.005.154.006.142.009.128.013.094.013.1.016.092.018.085.02.085.02.058.018.091.031.08.029.084.032.078.034.084.044.069.04.067.045.069.053Z" transform="translate(-165.812 -609.66)" fill="#f0b500"/>
        <path id="Path_180742" data-name="Path 180742" d="M806.793,897.85l.064.074.034.045.026.052.021.086.757.179.94.239-.009-.051-.012-.046-.011-.046-.01-.039-.005-.036-.007-.043-.013-.066-.013-.054-.023-.068-.025-.076-.019-.052-.034-.089-.023-.043-.025-.051-.025-.051-.014-.017-.01-.014-.006-.011-.009-.014-.01-.014-.011-.021-.012-.022-.017-.02-.014-.017-.014-.017-.013-.017-.017-.02-.022-.023-.017-.02-.018-.02-.025-.026-.2-.157-.046-.029-.037-.023-.03-.017-.038-.023-.029-.017-.034-.02-.064-.024-.052-.028-.052-.028-.058-.026-.048-.024-.044-.022-.058-.026-.066-.025-.054-.023-.06-.021-.046-.017-.05-.02-.071-.023-.1-.029-.077-.021-.077-.022-.1-.024-.083-.02-.059-.014-.049-.012-.055-.011-.045-.01-.045-.009-.055-.011-.056-.011-.055-.011-.056-.011-.052-.008L806,896.6l-.066-.011-.072-.011-.058-.006-.058-.006-.068-.008-.069-.008-.075-.006-.065,0h-.05l-.054,0h-.044l-.059-.006-.061,0-.089,0h-.061l-.074,0h-.067l-.077,0-.057,0-.075,0-.081,0-.08,0-.064,0-.071.005-.078.007-.078.007-.067.008-.052,0-.08.011-.074.01-.057.01-.054.01-.065.012-.056.01-.046.012-.057.01-.369.093-.055.018-.046.012-.049.017-.049.016-.056.018-.058.023-.042.015-.051.022-.045.02-.046.02-.037.018-.038.018-.045.02-.041.023-.034.022-.034.022-.048.025-.034.022-.032.017-.039.018-.034.022-.034.022-.023.023-.039.032-.027.02-.039.032-.03.025-.025.028-.035.035-.031.039-.042.037-.032.036-.028.033-.021.032-.017.022-.019.04-.024.037-.012.025-.017.035-.007.034-.013.038-.017.043-.01.034-.006.037.008.033,0,.032-.006.036v.04l0,.043,0,.029.012.049.012.05.01.042.018.04.013.036.01.028.021.043.015.031.026.046.024.038.037.033.022.03.031.036.026.033.039.042.038.034.042.037.038.034.046.04.035.025.037.034.048.035.064.039.051.03.051.03.066.034.082.039.073.032.064.026.068.029.075.027.083.034.078.031.09.031.1.033.09.031.05.016.07.024.07.024.069.024.126.038.1.029.091.026.08.025.095.029.09.026.086.023.071.019.076.022.111.028.1.024.1.027.111.028.11.028.11.028.11.028.105.025.145.034.1.025.109.028.13.03.1.024.11.023.089.02.1.025.128.029.089.02.124.027.158.033.132.028.073.016.1.021.083.017.084.017.087.02.118.023.107.022.093.018.13.027.083.017.1.021.1.02.091.017.116.023.115.022.115.022.1.018.007-.052.015-.084.015-.134.013-.091.009-.094.009-.094.014-.117.006-.065,0-.02.055,0,.045,0,.035,0,.055,0,.039,0,.049.006.082.012.043.008.043.008.046.01.024.005.027.008.033.006.033.006.027.008.041.012.041.012.045.015.035.014.052.021.039.017.035.014.038.017.033.019.033.019.029.016.033.018.033.019.129.1.021.022.025.025.021.022.019.027.011.021.015.024.015.024.009.026.009.026.011.033.009.026,0,.027,0,.04,0,.029,0,.042-.006.027-.008.031-.014.033-.017.043-.017.03-.02.035-.031.039-.035.036-.056.04-.029.022-.044.035-.048.032-.047.028-.039.021-.039.021-.048.02-.048.02-.037.016-.186.061-.045.01-.051.012-.045.01-.039.009-.049.008-.033.007-.038,0-.049.008-.075.008-.059.006-.061,0-.057,0-.057,0H808.5l-.092,0-.1-.007-.069-.007-.074-.01-.078-.013-.068-.015-.1-.02-.09-.022-.085-.023-.068-.025-.068-.025-.094-.038-.079-.039-.069-.038-.054-.034-.1-.077-.052-.051-.04-.043-.038-.048-.026-.039-.033-.07-.008-.031-.005-.037-1.492.453.012.022.012.022.021.028.016.025.021.028.017.038.029.034.027.026.029.034.031.029.031.029.132.1.052.031.06.037.044.025.048.028.048.028.058.029.066.035.077.036.082.034.111.042.069.025.084.028.076.023.061.019.066.022.057.016.047.015.039.009.053.013.053.013.078.018.063.013.069.011.063.014.069.012.055.008.085.01.085.01.075.009.07.006.07.006.126.009.122.006.082,0,.082,0h.272l.1,0,.083,0,.079-.005.094-.006.11-.007.1-.008.075-.008.075-.009.079-.01.091-.014.081-.015.093-.019.1-.021.1-.021.106-.027.095-.024.062-.017.067-.018.212-.071.07-.028.053-.022.064-.026.072-.032.074-.037.087-.046.075-.042.058-.036.06-.041.066-.043.069-.052.056-.043.042-.035.052-.046.043-.047.041-.042.045-.051.044-.064.028-.046.036-.064.03-.062.029-.074.009-.035.006-.026.006-.026.009-.035,0-.034,0-.041,0-.029,0-.029,0-.032-.009-.025,0-.039,0-.027-.005-.035-.012-.04-.013-.04-.018-.051-.015-.036-.015-.036-.022-.041-.024-.037-.02-.034-.034-.05-.029-.035-.04-.044-.033-.038-.042-.039-.039-.036-.05-.045-.1-.078-.046-.03-.05-.033-.043-.027-.06-.034-.069-.035-.062-.029-.088-.037-.085-.035-.081-.032-.077-.029-.072-.021-.1-.032-.144-.05-.158-.055-.134-.042-.107-.033-.117-.034-.135-.042-.117-.035-.131-.039-.132-.039-.132-.039-.134-.034-.187-.049-.134-.037-.145-.036-.112-.03-.113-.029-.16-.041-.157-.039-.12-.028-.121-.028-.134-.032-.185-.052-.155-.035-.16-.039-.136-.033-.157-.035-.155-.035-.119-.025-.169-.038-.12-.026-.109-.025-.11-.025-.142-.031-.107-.022-.142-.029-.179-.036,0,.031-.014.055-.008.053-.014.068-.012.076-.007.053-.012.076.005.075,0,.069-.009.092-.016.073,0,.044h-.034l-.038,0-.038,0-.119-.01-.089-.014-.1-.018-.083-.015-.106-.026-.066-.016-.064-.021-.059-.018-.084-.028-.077-.031-.081-.037-.068-.037-.06-.031-.071-.046-.081-.06-.044-.04-.037-.034-.04-.036-.025-.032-.025-.032-.018-.033-.014-.031-.019-.034-.017-.039-.008-.045-.012-.049.005-.036,0-.044.019-.053.023-.05.025-.041.032-.043.036-.04.042-.041.051-.048.058-.04.049-.03.046-.025.059-.028.059-.029.1-.034.1-.031.1-.027.107-.024.092-.016.133-.018.14-.012.111-.007.166-.006.153.006.141.009.127.013.094.013.1.016.091.018.085.02.085.02.057.018.09.03.08.029.084.032.077.033.083.044.069.04.067.045.068.052Z" transform="translate(-768.387 -859.246)" fill="#ca7700"/>
        <path id="Path_180743" data-name="Path 180743" d="M796.365,896.271l.065.075.034.045.026.052.021.087.763.18.948.241-.009-.051-.011-.046-.011-.046-.01-.039-.005-.036-.007-.043-.013-.066-.013-.054-.023-.068-.025-.076-.019-.052-.034-.089-.023-.043-.025-.051-.025-.051-.014-.017-.01-.014-.006-.011-.01-.014-.01-.014-.012-.022-.012-.022-.017-.02-.014-.017-.014-.017-.014-.017-.018-.02-.021-.023-.018-.02-.018-.02-.025-.026-.2-.158-.046-.029-.038-.023-.03-.017-.038-.023-.03-.017-.034-.02-.064-.025-.052-.028-.052-.028-.059-.026-.048-.025-.044-.021-.059-.026-.067-.025-.055-.023-.06-.021-.046-.017-.051-.02-.072-.023-.1-.029-.078-.022-.078-.022-.1-.024-.084-.02-.06-.014-.049-.012-.056-.011-.045-.01-.045-.009-.056-.01-.056-.011-.056-.011-.056-.011-.052-.008-.052-.008-.067-.011-.073-.011-.059-.006-.059-.006-.069-.008-.069-.008-.076-.006-.065,0h-.051l-.055,0-.045,0-.059-.006h-.061l-.09,0h-.061l-.075,0h-.068l-.078,0-.058,0-.076,0-.082,0-.081,0-.065,0-.072,0-.078.007-.079.007-.068.008-.053,0L793.6,895l-.075.01-.057.01-.055.009-.065.012-.057.01-.046.012-.058.01-.372.093-.056.018-.047.012-.049.017-.049.017-.056.018-.059.024-.042.015-.052.022-.045.02-.046.02-.037.018-.038.018-.045.02-.041.023-.034.022-.034.022-.048.025-.034.022-.032.017-.039.019-.035.022-.034.022-.023.023-.04.032-.028.02-.039.032-.03.025-.026.029-.035.035-.031.039-.043.037-.032.036-.028.034-.021.032-.017.022-.02.04-.024.037-.013.025-.017.035-.008.034-.013.039-.017.043-.01.034-.006.037.009.033,0,.032-.006.037v.04l0,.043,0,.029.012.05.012.05.01.042.017.04.013.036.01.028.022.043.015.031.026.046.024.038.037.033.022.029.031.036.026.033.04.042.038.034.042.037.038.034.046.041.035.025.038.034.049.035.064.04.052.03.051.03.067.034.082.039.073.032.065.027.069.029.075.027.084.034.079.031.091.032.1.033.09.031.051.017.071.025.07.024.07.024.127.038.1.03.092.026.081.025.1.029.091.026.086.023.072.019.076.022.112.029.1.024.1.027.112.028.111.028.111.028.111.028.106.025.146.034.106.025.11.028.13.03.105.025.111.023.09.02.1.025.129.03.089.02.125.027.159.033.133.028.074.016.1.021.084.017.084.017.088.02.119.023.107.022.093.018.131.027.083.017.1.021.1.021.092.018.116.023.116.023.116.023.1.019.007-.052.015-.084.016-.134.013-.092.009-.094.009-.095.014-.117.006-.065,0-.02.055,0,.046,0,.036,0,.055,0,.039,0,.05.006.082.012.043.008.043.008.047.01.024.005.027.008.033.006.033.006.027.008.041.012.041.012.045.015.035.014.053.021.039.017.035.014.039.017.033.019.033.019.029.016.033.019.033.019.13.1.021.023.025.025.021.022.019.027.011.021.015.024.015.024.009.026.009.026.011.034.009.026,0,.027,0,.04,0,.03,0,.042-.006.027-.008.031-.014.033-.018.043-.018.03-.02.035-.032.039-.036.036-.057.041-.029.022-.044.035-.049.032-.047.028-.039.021-.039.021-.049.02-.049.02-.037.016-.187.06-.045.01-.051.012-.045.01-.039.009-.049.008-.034.007-.038,0-.05.008-.075.008-.06.006-.062,0-.057,0-.058,0h-.156l-.092,0-.1-.007-.07-.007-.074-.01-.078-.013-.069-.015-.1-.02-.091-.021-.085-.024-.069-.025-.069-.025-.1-.038-.08-.039-.069-.038-.055-.034-.105-.078-.052-.052-.04-.043-.038-.048-.026-.039-.034-.07-.008-.032-.005-.037-1.505.453.012.022.012.022.021.028.016.026.021.028.017.039.029.034.027.026.029.034.031.029.031.029.134.1.052.031.06.037.044.025.048.028.048.028.058.029.066.035.077.036.083.034.112.042.07.025.085.029.077.022.062.019.066.022.058.016.048.015.039.009.053.013.053.013.078.018.064.014.07.011.064.014.069.012.055.007.086.01.086.01.075.009.071.007.071.006.127.009.123.006.083,0,.083,0h.274l.1,0,.084,0,.08-.005.1-.006.111-.008.1-.008.075-.009.075-.008.08-.01.092-.014.082-.015.094-.019.1-.021.1-.021.107-.027.1-.024.062-.017.067-.018.213-.072.071-.028.054-.022.065-.026.073-.033.075-.037.087-.046.076-.042.059-.036.06-.041.067-.043.07-.052.057-.044.043-.035.053-.046.043-.047.041-.042.045-.052.045-.064.028-.046.036-.065.03-.063.03-.075.009-.035.006-.026.006-.026.01-.036,0-.034,0-.042,0-.029,0-.029,0-.032-.009-.026,0-.04,0-.027-.005-.035-.013-.04-.013-.041-.018-.051-.015-.036-.015-.036-.022-.041-.024-.037-.02-.034-.034-.05-.029-.035-.041-.044-.033-.038-.043-.039-.039-.036-.051-.045-.1-.078-.047-.03-.051-.033-.043-.027-.06-.034-.07-.035-.062-.03-.089-.038-.086-.035-.081-.032-.078-.029-.073-.021-.1-.032-.145-.05-.159-.055-.135-.042-.107-.033-.118-.035-.136-.042-.118-.035-.133-.039-.133-.039-.133-.039-.135-.034-.188-.049-.135-.037-.146-.036-.114-.03-.114-.03-.162-.041-.159-.04-.121-.029-.122-.028-.135-.033-.187-.053-.156-.035-.162-.039-.137-.033-.158-.036-.156-.035-.12-.026-.17-.038-.121-.026-.11-.025-.111-.025-.143-.031-.107-.022-.143-.029-.181-.037,0,.031-.015.055-.008.053-.014.068-.012.076-.008.053-.012.076,0,.076,0,.069-.009.093-.017.073,0,.044h-.034l-.038,0-.038,0-.12-.01-.09-.013-.105-.018-.084-.016-.107-.026-.067-.016-.065-.021-.06-.018-.084-.029-.077-.031-.082-.037-.069-.038L793.6,897l-.071-.046-.081-.06-.045-.04-.037-.034-.04-.037-.026-.032-.026-.032-.018-.033-.015-.031-.019-.034-.017-.039-.007-.046-.012-.049.005-.036,0-.044.02-.053.024-.05.025-.041.032-.043.036-.04.043-.042.052-.048.059-.04.049-.03.047-.025.059-.029.059-.029.1-.034.1-.03.1-.027.108-.024.093-.016.134-.018.141-.011.112-.008.167-.005.154.006.142.009.128.013.095.013.1.017.092.018.086.02.086.02.058.018.091.03.08.029.085.032.078.034.084.044.07.04.068.045.069.053Z" transform="translate(-758.352 -857.726)" fill="#ffff89"/>
        <path id="Path_180744" data-name="Path 180744" d="M801.2,896.98l.065.075.034.045.026.052.021.086.76.179.944.24-.009-.051-.011-.046-.011-.046-.01-.039-.005-.036L803,897.4l-.013-.066-.013-.054-.023-.067-.025-.076-.019-.052-.034-.089-.023-.043-.025-.051-.025-.051-.014-.017-.01-.014-.006-.011-.01-.014-.01-.014-.011-.021-.012-.022-.017-.02-.014-.017-.014-.017-.014-.017-.018-.02-.021-.023-.017-.02-.017-.02-.026-.026-.2-.158-.046-.029-.038-.023-.03-.017-.038-.023-.03-.017-.034-.02-.064-.025-.052-.028-.052-.028-.058-.026-.048-.025-.044-.021-.059-.026-.066-.026-.055-.023-.06-.021-.047-.017-.051-.02-.071-.023-.1-.029-.077-.022-.078-.022-.1-.024-.084-.02-.06-.014-.049-.012-.055-.011-.045-.01-.045-.009-.056-.01-.055-.011-.056-.011-.056-.011-.052-.008-.052-.008-.067-.011-.073-.011-.058-.007-.058-.006-.069-.008-.069-.008-.075-.006-.065,0h-.05l-.054,0h-.044l-.059-.006h-.061l-.089,0h-.061l-.074,0h-.067l-.077,0-.058,0-.076,0-.082,0-.08,0-.065,0-.071.005-.078.007-.078.007-.067.008-.053,0-.08.011-.075.01-.056.01-.055.01-.065.012-.057.01-.046.011-.057.01-.371.093-.055.018-.047.012-.049.017-.049.017-.056.018-.059.024-.042.015-.052.022-.045.02-.046.02-.037.018-.038.019-.045.02-.041.023-.034.022-.034.022-.048.025-.034.022-.032.017-.039.019-.034.022-.034.022-.023.023-.039.032-.027.02-.039.032-.03.025-.026.029-.035.035-.031.039-.043.037-.032.036-.028.034-.021.032-.017.022-.02.04-.024.037-.012.025-.017.035-.007.034-.013.038-.017.043-.01.033-.006.037.008.033,0,.032-.006.037v.04l0,.044,0,.029.012.05.012.049.01.042.018.04.013.036.01.028.021.043.015.031.026.046.024.038.037.033.022.029.031.036.026.033.039.042.038.034.042.037.038.034.046.04.035.025.037.034.049.035.064.04.051.03.051.03.066.035.082.039.073.033.065.026.068.029.075.027.083.034.079.03.09.032.1.033.09.032.05.016.07.024.07.024.07.024.126.038.1.029.091.027.08.025.1.029.091.026.086.023.071.019.076.022.112.029.1.024.1.027.111.028.111.028.111.028.11.028.106.025.145.035.105.025.109.028.13.03.1.025.111.023.089.02.1.024.129.03.089.02.125.027.158.033.133.028.074.016.1.021.084.017.084.017.087.02.118.023.107.022.093.018.131.027.083.017.1.021.1.021.092.018.116.023.116.023.115.022.1.019.007-.052.016-.084.015-.134.013-.092.009-.094.009-.094.014-.117.006-.065,0-.021.055,0,.045,0,.035,0,.055,0,.039,0,.05.006.082.012.043.008.043.008.047.01.024.005.027.008.033.006.034.007.027.008.041.012.041.012.045.015.035.014.053.021.038.017.035.014.039.017.033.019.033.019.029.016.033.019.033.018.13.1.021.022.025.025.021.022.019.027.011.021.015.024.015.024.009.026.009.026.012.033.009.026,0,.028,0,.04,0,.03,0,.042-.006.027-.008.031-.013.033-.018.043-.017.031-.019.035-.032.039-.035.036-.057.041-.029.022-.044.035-.048.032-.047.027-.039.021-.039.021-.049.02-.049.02-.037.016-.186.06-.045.01-.051.012-.045.011-.039.008-.049.008-.034.007-.038,0-.049.008-.075.008-.059.007-.062,0-.057,0-.058,0h-.156l-.092,0-.1-.007-.07-.007-.074-.01-.078-.013-.068-.015-.1-.02-.091-.022-.085-.023-.069-.025-.068-.025-.095-.038-.079-.039-.069-.038-.055-.034-.1-.077-.052-.051-.04-.043-.038-.048-.026-.039-.034-.07-.008-.032-.005-.037-1.5.453.012.022.012.022.021.029.016.025.021.028.017.039.029.035.027.026.029.035.031.029.031.029.133.1.052.031.06.037.044.025.048.028.048.028.058.029.067.035.077.036.083.034.112.042.07.025.085.028.076.022.062.019.066.022.057.016.047.015.039.009.053.013.053.013.078.018.064.013.069.012.063.013.069.011.055.008.085.01.085.011.075.01.071.006.071.006.127.009.122.006.082,0,.082,0h.273l.1,0,.084,0,.08-.006.095-.006.11-.008.1-.008.075-.008.075-.008.08-.01.092-.014L804,901.2l.093-.019.1-.021.1-.021.106-.027.1-.023.062-.017.067-.018.213-.071.07-.028.054-.022.065-.026.072-.033.075-.037.087-.046.075-.042.059-.036.06-.041.066-.043.069-.052.056-.043.043-.035.052-.046.043-.047.041-.042.045-.052.045-.063.028-.046.036-.065.03-.063.03-.075.009-.035.006-.026.006-.026.01-.036,0-.034,0-.041,0-.029,0-.029,0-.032-.009-.025,0-.04,0-.027-.005-.035-.013-.04-.013-.04-.018-.051-.015-.036-.015-.036-.022-.041-.024-.037-.02-.034-.034-.05-.029-.035-.041-.044-.033-.038-.042-.039-.039-.036-.05-.045-.1-.078-.046-.03-.05-.033-.043-.027-.06-.034-.069-.035-.062-.03-.089-.037-.085-.035-.081-.032-.078-.029-.073-.021-.1-.032-.144-.05-.159-.055-.135-.042-.107-.033-.117-.034-.136-.042-.118-.034-.132-.039-.132-.039-.133-.039-.134-.035-.187-.049-.134-.037-.146-.036-.113-.03-.113-.03-.161-.041-.158-.04-.121-.028-.121-.028-.134-.033-.186-.052-.156-.035-.161-.039-.136-.033-.157-.036-.155-.035-.12-.026-.17-.038-.12-.026-.11-.024-.11-.025-.143-.031-.107-.022-.142-.029-.18-.037,0,.031-.014.055-.008.053-.014.068-.013.076-.008.053-.012.076.005.075,0,.069-.009.092-.016.073,0,.044h-.034l-.038,0-.038,0-.119-.01-.09-.014-.1-.018-.083-.016-.107-.026-.066-.016-.064-.021-.06-.018-.084-.029-.077-.03-.081-.037-.069-.037-.06-.031-.071-.046-.081-.06-.045-.04-.037-.034-.04-.037-.025-.032-.025-.032-.018-.033-.015-.031-.019-.034-.017-.039-.007-.046-.012-.049.005-.036,0-.045.019-.053.023-.05.025-.041.032-.043.036-.04.042-.041.051-.048.058-.04.049-.03.046-.025.059-.029.059-.028.1-.034.1-.031.1-.027.108-.024.093-.016.134-.018.141-.011.111-.008.167-.005.154.006.142.009.128.013.094.013.1.016.092.018.085.02.085.02.058.018.091.03.08.029.084.032.078.034.084.044.069.04.067.045.069.053Z" transform="translate(-763.006 -858.409)" fill="#f0b500"/>
      </g>
    </g>
  </g>
</svg>
