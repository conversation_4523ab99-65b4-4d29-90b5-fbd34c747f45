import React from 'react';

function DarkSmartCoins() {
  return (
    <div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        width="82.937"
        height="18.451"
        viewBox="0 0 82.937 18.451"
      >
        <defs>
          <clipPath id="clip-path">
            <rect
              id="Rectangle_63454"
              data-name="Rectangle 63454"
              width="82.937"
              height="18.451"
              fill="none"
            />
          </clipPath>
          <clipPath id="clip-path-2">
            <path
              id="Path_205426"
              data-name="Path 205426"
              d="M4.836,0A4.838,4.838,0,0,0,.11,4.167a4.074,4.074,0,0,0,.334,2.2A17.255,17.255,0,0,0,1.4,8.355a49.7,49.7,0,0,0,3.4,5.229c.47-.6.947-1.24,1.389-1.857a2.334,2.334,0,1,1-3.442.765L.2,12.471a4.7,4.7,0,0,0,3.114,5.7A4.81,4.81,0,0,0,9.6,13.428,11.173,11.173,0,0,0,8.105,9.74,49.945,49.945,0,0,0,4.864,4.822c-.456.616-.943,1.233-1.382,1.863a2.335,2.335,0,1,1,3.7-1.537,2.476,2.476,0,0,1-.259.772c.386.552,1.148,1.746,1.5,2.326A7.746,7.746,0,0,0,9.671,4.625,4.829,4.829,0,0,0,5.7.077,4.56,4.56,0,0,0,4.892,0Z"
              transform="translate(0)"
              fill="none"
            />
          </clipPath>
          <radialGradient
            id="radial-gradient"
            cx="0.509"
            cy="-0.08"
            r="0.888"
            gradientTransform="matrix(0, 1, 1.693, 0, -1.486, 0.833)"
            gradientUnits="objectBoundingBox"
          >
            <stop offset="0" stop-color="#e84b23" />
            <stop offset="0.151" stop-color="#e84a23" />
            <stop offset="0.257" stop-color="#e74823" />
            <stop offset="0.35" stop-color="#ec5130" />
            <stop offset="0.435" stop-color="#f36648" />
            <stop offset="0.514" stop-color="#ff806c" />
            <stop offset="0.59" stop-color="#ff9487" />
            <stop offset="0.663" stop-color="#ffc3bc" />
            <stop offset="0.731" stop-color="#ffe4e0" />
            <stop offset="0.797" stop-color="#fff" />
            <stop offset="1" stop-color="#fff" />
          </radialGradient>
        </defs>
        <g
          id="Group_121898"
          data-name="Group 121898"
          transform="translate(0 0)"
        >
          <g
            id="Group_121899"
            data-name="Group 121899"
            transform="translate(0 0)"
          >
            <g
              id="Group_121898-2"
              data-name="Group 121898"
              transform="translate(0 0)"
              clip-path="url(#clip-path)"
            >
              <path
                id="Path_205422"
                data-name="Path 205422"
                d="M50.224,43.565H48.29l-1.285-2.971q-.046-.124-.178-.457t-.294-.758q-.163-.425-.333-.874t-.31-.82v2q0,.542-.008,1.021t-.023.758l-.077,2.1H43.431l.619-10.477h2.2l2.414,6.035q.108.278.286.781T49.3,41q.17-.572.348-1.075t.3-.8l2.414-6.035h2.151l.619,10.477h-2.4l-.093-2.1q-.016-.294-.023-.774t-.008-1.014q0-.534.008-1.068t.008-.921q-.124.325-.271.735t-.3.8q-.155.387-.294.72t-.217.518Z"
                transform="translate(-32.964 -25.113)"
                fill="#fff"
              />
              <path
                id="Path_205423"
                data-name="Path 205423"
                d="M99.223,33.088H101.5l3.838,10.477h-2.585l-.774-2.522H98.589l-.8,2.522H95.246l3.977-10.477m2.182,6.066q-.155-.449-.317-.936t-.31-.952q-.147-.465-.271-.874t-.217-.689q-.093.294-.217.7t-.271.874q-.147.464-.309.944t-.317.929h2.228"
                transform="translate(-72.291 -25.113)"
                fill="#fff"
              />
              <path
                id="Path_205424"
                data-name="Path 205424"
                d="M140.964,32.99q.139,0,.557-.008t.928-.008q.511,0,.991-.008t.743-.008a4.851,4.851,0,0,1,2.971.758,3.03,3.03,0,0,1,.511,3.876,3.141,3.141,0,0,1-1.3,1,2.931,2.931,0,0,1,.82.7,6.32,6.32,0,0,1,.681,1.045q.325.612.658,1.4t.735,1.733H146.52q-.5-1.083-.836-1.911t-.635-1.393a3.5,3.5,0,0,0-.58-.851.94.94,0,0,0-.689-.286h-.263v4.442h-2.553Zm2.553,4.441h.65a1.347,1.347,0,0,0,.921-.333,1.32,1.32,0,0,0,.379-1.045,2.076,2.076,0,0,0-.07-.549,1.225,1.225,0,0,0-.224-.449,1.072,1.072,0,0,0-.426-.309,1.667,1.667,0,0,0-.658-.116h-.572Z"
                transform="translate(-106.99 -25.016)"
                fill="#fff"
              />
              <path
                id="Path_205425"
                data-name="Path 205425"
                d="M180.469,33.088l-.006,1.873H178.05v8.6H175.5v-8.6H173V33.088Z"
                transform="translate(-131.309 -25.113)"
                fill="#fff"
              />
            </g>
          </g>
          <g
            id="Group_121901"
            data-name="Group 121901"
            transform="translate(0 0)"
          >
            <g
              id="Group_121900"
              data-name="Group 121900"
              clip-path="url(#clip-path-2)"
            >
              <rect
                id="Rectangle_63455"
                data-name="Rectangle 63455"
                width="11.79"
                height="19.955"
                transform="translate(-2.057 0.115) rotate(-4.434)"
                fill="url(#radial-gradient)"
              />
            </g>
          </g>
          <g
            id="Group_121903"
            data-name="Group 121903"
            transform="translate(0 0)"
          >
            <g
              id="Group_121902"
              data-name="Group 121902"
              transform="translate(0 0)"
              clip-path="url(#clip-path)"
            >
              <path
                id="Path_205427"
                data-name="Path 205427"
                d="M212.605,7.806a3.8,3.8,0,0,0-1.215-.186,3.266,3.266,0,0,0-3.442,3.345v1.747l2.54,3.919V11.416c0-.1,0-.2.014-.294.051-.535.265-1.058.887-1.058s.877.663.877,1v1.234c0,.308.4.26,1.186.26,1.092,0,1.28-.07,1.28-.308v-1.28a3.185,3.185,0,0,0-2.128-3.16m.918,11.414c-1.044,0-1.256-.024-1.256.308v1.234a.868.868,0,0,1-.877.95c-.622,0-.836-.489-.887-1.041-.01-.1-.014-.207-.014-.311v-1.9l-2.54-3.885V20.9a3.208,3.208,0,0,0,2.494,3.213,4.085,4.085,0,0,0,.947.108,3.06,3.06,0,0,0,3.343-3.321V19.67c0-.427-.212-.451-1.21-.451"
                transform="translate(-157.831 -5.783)"
                fill="#fc4714"
              />
              <path
                id="Path_205428"
                data-name="Path 205428"
                d="M244.518,37.2a3.155,3.155,0,0,1-3.464,3.416,3.243,3.243,0,0,1-3.44-3.416V32.6a3.452,3.452,0,0,1,6.9,0ZM242,36.511V33.238c0-.735-.166-1.566-.949-1.566-.759,0-.949.831-.949,1.566v3.274c0,.9.19,1.589.949,1.589.807,0,.949-.711.949-1.589"
                transform="translate(-180.348 -22.166)"
                fill="#fc4714"
              />
              <path
                id="Path_205429"
                data-name="Path 205429"
                d="M272.105,11.3c.071.854.166,1.447-1.376,1.637a1.474,1.474,0,0,1-1.447-1.708c.071-.759.284-1.471,1.447-1.423a1.5,1.5,0,0,1,1.376,1.494m-.071,14.162c0,.427-.048.427-1.329.427-1.186,0-1.21,0-1.21-.38V14.884c0-.38.095-.4,1.21-.4,1.234,0,1.329,0,1.329.427Z"
                transform="translate(-204.376 -7.442)"
                fill="#fc4714"
              />
              <path
                id="Path_205430"
                data-name="Path 205430"
                d="M292.062,40.439c0,.214-.214.237-1.186.237-1.139,0-1.376-.024-1.376-.19V33.227c0-.712-.047-1.494-.712-1.494-.783,0-1.091.641-1.091,1.423v7.259c0,.261-.095.261-1.352.261-1.067,0-1.186-.024-1.186-.237V29.479c0-.214.19-.19,1.067-.19,1.091,0,1.257-.071,1.257.356,0,.617,0,.807.166.546a2.3,2.3,0,0,1,1.637-.9c1.566,0,2.776.925,2.776,3.44Z"
                transform="translate(-216.433 -22.227)"
                fill="#fc4714"
              />
              <path
                id="Path_205431"
                data-name="Path 205431"
                d="M323.352,37.677a3.3,3.3,0,0,1-6.6-.308c0-.166.19-.166,1.091-.166.878,0,1.233-.024,1.233.118,0,.641.214,1.186.925,1.186s.925-.427.925-1.02c0-2.135-4.1-1.21-4.1-5.124a3.092,3.092,0,0,1,3.179-3.06,3.231,3.231,0,0,1,3.179,3.345c0,.142-.285.119-1.139.119-.972,0-1.257-.047-1.257-.166,0-.783-.356-1.21-.83-1.21-.522,0-.831.356-.831,1.02,0,1.613,4.223,1.376,4.223,5.267"
                transform="translate(-240.416 -22.241)"
                fill="#fc4714"
              />
            </g>
          </g>
        </g>
      </svg>
    </div>
  );
}

export default DarkSmartCoins;
