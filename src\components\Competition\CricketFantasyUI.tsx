'use client';
import { useSearchParams } from 'next/navigation';
import type { Dispatch, SetStateAction } from 'react';

import { useCompetition } from '@/helpers/context/competitionContext';
import type { PlayersByRole } from '@/helpers/context/createTeamContext';
import { useTeam } from '@/helpers/context/createTeamContext';

import PlayerCard from './PlayerCard';
import { generateUniqueId } from '@/lib/utils';


type CricketFantasyUIProps = {
  activeTab?: keyof PlayersByRole;
  setActiveTab?: Dispatch<SetStateAction<keyof PlayersByRole>>;
};

const CricketFantasyUI: React.FC<CricketFantasyUIProps> = ({
  setActiveTab,
}) => {
  const {
    state: {
      playersByRole: { BAT, WKP, BOW, ALL },
      reserveState: { reservePlayers, reservePlayersLimit },
      playerTypeLimits: {
        BAT: BATSMANLIMIT,
        WKP: WKPLIMIT,
        BOW: BOWLIMIT,
        ALL: ALLROUNDERLIMIT,
      },
    },
    setShowPlayerTabel,
    activeTab,
    activePlayerPosition,
    setActivePlayerPosition,
    setOpenReserveModal
  } = useTeam();



  const { eventDetailsResponse, } = useCompetition();
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  const searchParams = useSearchParams();
  const add_more = searchParams.get('add_more');
  const dreamTeamId = searchParams.get('dreamTeamId');

  const isPlayerLocked =
    add_more !== 'true' &&
    (eventDetailsResponse?.result?.eventDetails?.status === 'inprogress' ||
      eventDetailsResponse?.result?.eventDetails?.status === 'innings break' ||
      eventDetailsResponse?.result?.eventDetails?.status === 'drink' ||
      eventDetailsResponse?.result?.eventDetails?.status === 'Strategic Timeout' ||
      eventDetailsResponse?.result?.eventDetails?.status === 'Stumps');

  return (
    <>
      <div className="bg-[url('/fantasy/images/competitions/cricketBg.png')] bg-no-repeat bg-center bg-cover relative rounded">
        <div className="max-w-6xl mx-auto md:p-8 p-3">
          <div className="flex flex-col gap-8">
            {/* Section Labels */}
            <div className="text-center">
              <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
                Wicket-keepers: {WKP?.length}/{WKPLIMIT}
              </span>
            </div>



            {/* Wicket Keeper */}
            <div className="flex justify-center md:gap-8 gap-4 flex-wrap">
              {WKP?.length === 0 && (
                <PlayerCard
                  setActiveTab={setActiveTab!}
                  tabSection="WKP"
                  setShowPlayerTabel={setShowPlayerTabel}
                  isActive={activeTab === 'WKP'}
                />
              )}
              {WKP?.toSorted((a, b) => a?.id - b?.id).map((player) => (
                <PlayerCard
                  player={player}
                  key={player.id}
                  setActiveTab={setActiveTab!}
                  tabSection="WKP"
                  setShowPlayerTabel={setShowPlayerTabel}
                  isPlayerLocked={isPlayerLocked}
                />
              ))}
            </div>

            {/* Batsmen Section */}
            <div className="text-center">
              <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
                Batsmen: {BAT?.length}/{BATSMANLIMIT}
              </span>
            </div>

            {/* Batsmen Row */}
            <div className="flex justify-center md:gap-8 gap-5 flex-wrap">
              {BAT?.toSorted((a, b) => Number(a?.id) - Number(b?.id))?.map(
                (player) => (
                  <PlayerCard
                    key={player.id} // Add a unique key prop to improve rendering efficiency
                    player={player}
                    setActiveTab={setActiveTab!}
                    tabSection="BAT"
                    setShowPlayerTabel={setShowPlayerTabel}
                    isPlayerLocked={isPlayerLocked}
                  />
                ),
              )}

              {Array.from({
                length: Math.max(0, BATSMANLIMIT - (BAT?.length || 0)),
              }).map((_, index) => (
                <PlayerCard
                  key={generateUniqueId()} // Ensure unique key for each item
                  setActiveTab={setActiveTab!}
                  tabSection="BAT"
                  setShowPlayerTabel={setShowPlayerTabel}
                  isActive={activeTab === 'BAT'}
                />
              ))}
            </div>

            {/* All Rounder Section */}

            <div className="text-center">
              <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
                All Rounders : {ALL?.length}/{ALLROUNDERLIMIT}
              </span>
            </div>

            <div className="flex justify-center md:gap-8 gap-5 flex-wrap">
              {ALL?.toSorted((a, b) => Number(a?.id) - Number(b?.id))?.map(
                (player) => (
                  <PlayerCard
                    key={player.id}
                    player={player}
                    setActiveTab={setActiveTab!}
                    tabSection="ALL"
                    setShowPlayerTabel={setShowPlayerTabel}
                    isPlayerLocked={isPlayerLocked}
                  />
                ),
              )}
              {Array.from({
                length: Math.max(0, ALLROUNDERLIMIT - (ALL?.length || 0)),
              }).map((_, index) => (
                <PlayerCard
                  key={generateUniqueId()} // Ensure unique key for each item
                  setActiveTab={setActiveTab!}
                  tabSection="ALL"
                  setShowPlayerTabel={setShowPlayerTabel}
                  isActive={activeTab === 'ALL'}
                />
              ))}
            </div>

            {/* Bowlers Section */}
            <div className="text-center">
              <span className="bg-[rgba(255,255,255,0.5)] rounded-md text-secondary-100 px-3 py-1 text-sm text-black">
                Bowlers: {BOW?.length}/{BOWLIMIT}
              </span>
            </div>
            <div className="flex justify-center md:gap-8 gap-5 flex-wrap">
              {BOW?.toSorted((a, b) => Number(a?.id) - Number(b?.id))?.map(
                (player) => (
                  <PlayerCard
                    player={player}
                    key={player.id}
                    setActiveTab={setActiveTab!}
                    tabSection="BOW"
                    setShowPlayerTabel={setShowPlayerTabel}
                    isPlayerLocked={isPlayerLocked}
                  />
                ),
              )}
              {Array.from({
                length: Math.max(0, BOWLIMIT - (BOW?.length || 0)),
              }).map(() => (
                <PlayerCard
                  key={generateUniqueId()} // Unique key for placeholders
                  setActiveTab={setActiveTab!}
                  tabSection="BOW"
                  isActive={activeTab === 'BOW'}
                  setShowPlayerTabel={setShowPlayerTabel}
                />
              ))}
            </div>




          </div>
        </div>
      </div>
      {
        reservePlayers.length > 0 && dreamTeamId && <div className="bg-[url('/fantasy/images/reserveCricketBG.png')] bg-no-repeat bg-center bg-cover relative rounded mt-2">
          {/* Reserve Section */}
          <div className="text-center">
            <div className="w-full rounded-t-md bg-secondary-100 text-white px-3 py-1 text-sm text-black">
              RESERVES
            </div>
          </div>
          <div className="flex justify-center md:gap-8 gap-5 flex-wrap my-10 pb-4">
            {reservePlayers?.map((player, index) => (
              <div key={index} className="flex flex-col gap-2 items-center">
                {player ? (
                  <PlayerCard
                    key={player.id}
                    player={player}
                    setActiveTab={() => { }}
                    tabSection="BAT"
                    setShowPlayerTabel={() => { }}
                    isActive={false}
                    isReserveType={true}
                    setOpenReserveModal={setOpenReserveModal}
                    playerIndex={index}
                  />
                ) : (
                  <PlayerCard
                    setActiveTab={() => { }}
                    tabSection="BAT"
                    setShowPlayerTabel={() => { }}
                    isActive={index === activePlayerPosition && eventStatus === 'upcoming'}
                    activePlayerPosition={index}
                    setActivePlayerPosition={setActivePlayerPosition}
                    isReserveType={true}
                    setOpenReserveModal={setOpenReserveModal}
                    playerIndex={index}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      }


    </>

  );
};

export default CricketFantasyUI;
