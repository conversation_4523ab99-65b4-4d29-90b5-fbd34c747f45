import { X } from 'lucide-react';

export type Suggestion = {
  id: string;
  name: string;
};

interface IconProps {
  className?: string;
}

const CloseIcon: React.FC<IconProps> = ({ className }) => (
  <X className={className} size={18} />
);

interface SearchAutocompleteProps {
  query: string;
  suggestions: Suggestion[];
  placeholder?: string;
  onQueryChange: (query: string) => void;
  onSuggestionClick: (suggestion: Suggestion) => void;
  onClearSearch: () => void;
}

export default function SearchAutocomplete({
  query,
  suggestions = [],
  placeholder = 'Search...',
  onQueryChange,
  onSuggestionClick,
  onClearSearch,
}: Readonly<SearchAutocompleteProps>) {
  return (
    <div className="relative w-full">
      <div className="relative flex items-center">
        <div
          // className={`${query ? 'top-[60%]' : 'top-1/2 '} absolute left-3 transform -translate-y-1/2 text-gray-400`}
          className={` top-[60%] absolute left-3 transform -translate-y-1/2 text-gray-400`}
        >
          {/* {query ? ( */}
          <button onClick={onClearSearch}>
            <CloseIcon className="hover:text-gray-600" />
          </button>
          {/* ) : (
            <SearchIcon />
          )} */}
        </div>
        <input
          type="text"
          value={query}
          onChange={(e) => onQueryChange(e.target.value)}
          placeholder={placeholder}
          className="w-full pl-10 pr-3 py-2 text-sm border rounded-md focus:outline-none focus:border-secondary-100"
        />
      </div>
      {suggestions && suggestions?.length > 0 && (
        <ul className="absolute z-50 w-full max-h-[172px] h-auto overflow-auto mt-1 bg-white border rounded-md shadow-lg ">
          {suggestions?.map((suggestion) => (
            <button
              key={suggestion?.id}
              className="px-4 py-2 text-sm cursor-pointer hover:bg-gray-100"
              onClick={() => onSuggestionClick(suggestion)}
            >
              {suggestion?.name}
            </button>
          ))}
        </ul>
      )}
    </div>
  );
}
