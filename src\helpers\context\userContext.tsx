'use client';
import { useQuery } from '@tanstack/react-query';
import { createContext, useContext, useMemo } from 'react';

import { userProfile } from '@/lib/queryKeys';

import { Token } from '../../../db/db';
import type { UserProfileData } from '../../../types';
import { useAuthContext } from './authContext';
import { Config } from './config';

type UserContextType = {
  user: UserProfileData;
};

const userContext = createContext<UserContextType | undefined>(undefined);

const fetchUserProfile = async () => {
  // Fetch user profile from API
  const authToken = Token;
  if (authToken) {
    const res = await fetch(`${Config.baseURL}user/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${authToken}`,
      },
    });

    const data = await res.json();

    return data;
  }
};

type Address = {
  country: string | number | null;
  state: string | number | null;
};

type UserProfilePayload = {
  firstName?: string;
  lastName?: string;
  dob?: string;
  nickName?: string; // Optional if nickName might not always be present
  gender?: string;
  address?: Address;
  phone?: number | string;
  phoneCountryId?: string | number;
  MediaId?: number;
};

type UserProfileResponse = {
  success: boolean;
  message: string;
  // Add additional properties based on the API response
};

export const updateUserProfile = async (
  payload: UserProfilePayload,
): Promise<UserProfileResponse> => {
  const authToken = Token;
  const res = await fetch(`${Config.baseURL}user/editProfile`, {
    method: 'PUT',
    body: JSON.stringify(payload),
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${authToken}`,
    },
  });

  if (!res.ok) {
    throw new Error('Failed to update profile');
  }

  const data: UserProfileResponse = await res.json();
  return data;
};

const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const { token } = useAuthContext();

  const { data } = useQuery({
    queryKey: [userProfile, token],
    queryFn: fetchUserProfile,
    enabled: !!Token,
  });

  const userState: UserContextType = useMemo(
    () => ({
      user: data?.data,
    }),
    [data?.data],
  );
  return (
    <userContext.Provider value={userState}>{children}</userContext.Provider>
  );
};

export const useUserProfileContext = () => {
  const userCtx = useContext(userContext);
  if (!userCtx) {
    throw new Error('useContext must be used within a UserContextProvider');
  }
  return userCtx;
};

export default UserProvider;
