import React from 'react';

import { cn, generateUniqueId } from '@/lib/utils';

import { Card, CardContent } from '../card';
import { Label } from '../label';
import LabelButton from '../LabelButton';
import { RadioGroupItem } from '../radio-group';

export type Variant = 'Platinum' | 'Diamond' | 'Gold' | 'Silver' | 'Bronze';

interface PlanCardProps {
  planName: string;
  price: string;
  billingFrequency: string;
  features: string[];
  selectedPlan: string;
  value: string;
  selectedPlanStyle?: string;
  badge?: string;
  variant: Variant;
  isActive?: boolean;
  isPopularPlan?: boolean;
  purchasedStatus?: string;
}

const PlanCard: React.FC<PlanCardProps> = ({
  planName,
  price,
  billingFrequency,
  features,
  selectedPlan,
  value,
  selectedPlanStyle = '',
  badge,
  variant,
  isActive,
  isPopularPlan,
  purchasedStatus,
}) => {
  const isSelected = selectedPlan === value;

  let backgroundColor = '';

  if (purchasedStatus === 'hold') {
    backgroundColor = 'bg-[#eb8f4a]';
  } else if (purchasedStatus === 'cancelled') {
    backgroundColor = 'bg-red-500';
  } else {
    backgroundColor = 'bg-green-500';
  }

  return (
    <Card
      className={`${isSelected ? selectedPlanStyle : ''} relative transition-all duration-100 ease-in-out`}
    >
      <CardContent className="p-4">
        <div className="flex items-center space-x-2">
          <RadioGroupItem value={value} id={value} checked={isSelected} />
          <Label htmlFor={value} className="flex-grow">
            <div className="flex flex-col md:flex-row justify-between items-center max-799:items-start max-799:gap-y-2">
              <LabelButton text={planName} variant={variant} />
              <span className="text-xl font-bold">
                ${parseFloat(price).toFixed(2)}
                <span className="text-sm font-normal">/{billingFrequency}</span>
              </span>
            </div>
          </Label>
        </div>

        <p className="text-sm text-muted-foreground mt-0 md:mt-2 ml-[22px]">
          Billed {billingFrequency}
        </p>

        {badge && !isActive && (
          <div className="absolute top-[-11px] right-0">
            <div className="bg-orange-500 text-white text-xs px-[9px] py-[3px] rounded flex space-x-1 justify-center items-center">
              <span className="text-[11.42px]">{badge}</span>
            </div>
          </div>
        )}

        {isActive && (
          <div className="absolute top-[-11px] right-0">
            <div
              className={cn(
                backgroundColor,
                'text-white text-xs px-[9px] py-[3px] rounded flex space-x-1 justify-center items-center',
              )}
            >
              <span className="text-[11.42px]">{badge}</span>
            </div>
          </div>
        )}

        {badge && !isActive && isPopularPlan && (
          <div className="absolute top-[-11px] right-0">
            <div className="bg-secondary-300 text-white text-xs px-[9px] py-[3px] rounded flex space-x-1 justify-center items-center">
              <span className="text-[11.42px]">{badge}</span>
            </div>
          </div>
        )}

        <div
          className={`
            overflow-hidden transition-[max-height,opacity] duration-300 ease-in-out
            ${isSelected ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'}
          `}
        >
          <ul className="mt-4 space-y-2">
            {features.map((feature, index) => (
              <li
                key={generateUniqueId()}
                className="flex items-center transform transition-transform duration-300 ease-in-out"
                style={{
                  transitionDelay: `${index * 50}ms`,
                }}
              >
                <div>
                  <svg
                    className="w-[22px] h-[22px] mr-2 text-secondary-100 bg-[#E7E9EC] md:bg-white rounded-full"
                    fill="none"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <p>{feature}</p>
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default PlanCard;
