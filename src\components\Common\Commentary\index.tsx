'use client';
import Select, { components, SingleValue } from 'react-select';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import MatchInfoCard from './MatchCardInfo';
import { useCricketCommentary } from '@/helpers/context/commentry/cricket';
import { StatsDropdown } from './StatsDropdown';
import { ReactSelectOptionType } from '../../../../types';
import CommentryCard from './CommentryCard';
import { generateUniqueId } from '@/lib/utils';
import CricketScoreboardHeader from './CricketScoreboardHeader';

export const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const Commentary = () => {
  const {
    state: { commentary, matchDetails, option, commentaryList },
    selectedOption,
    setSelectedOption,
    setTeamId,
  } = useCricketCommentary();

  return (
    <div className="md:p-4 p-2 overflow-hidden">
      <div
        style={{
          marginLeft: -60,
          marginRight: -60,
          marginTop: -30,
        }}
      >
        <CricketScoreboardHeader />
      </div>
      <div className="md:flex grid grid-cols-2 space-x-2 mt-2">
        <div className="md:w-[226px]  w-[170px]">
          <Select
            className="React desktop-odds-select"
            options={option}
            classNamePrefix="select"
            placeholder="Sort"
            isSearchable={false}
            components={{ DropdownIndicator }}
            defaultValue={selectedOption}
            onChange={(newValue: SingleValue<ReactSelectOptionType>) => {
              if (newValue) {
                setSelectedOption(newValue);
                if (newValue.value !== 'preview') {
                  setTeamId(Number(newValue.value));
                }
              }
            }}
          />
        </div>
        {selectedOption.value !== 'preview' && (
          <div>
            <StatsDropdown />
          </div>
        )}
      </div>

      {selectedOption.value === 'preview' && (
        <div className="mt-4">
          <MatchInfoCard
            details={matchDetails}
            commentaryList={commentaryList?.result?.result || []}
          />
        </div>
      )}

      <div className="bg-white rounded">
        {selectedOption.value !== 'preview' && (
          <CommentryCard commentary={commentaryList?.result?.result || []} />
        )}
      </div>
    </div>
  );
};

export default Commentary;
