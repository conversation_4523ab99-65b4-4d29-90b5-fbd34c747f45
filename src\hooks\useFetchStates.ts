// hooks/useFetchCountries.ts
import { useInfiniteQuery } from '@tanstack/react-query';

const fetchStates = async (
  offset: number,
  search: string,
  countryId: number,
) => {
  const response = await fetch(
    `https://smartb.com.au/api/public/state/country/${countryId}?limit=20&offset=${offset}${search && '&search=${search}'}`,
  );
  if (!response.ok) {
    throw new Error('Network response was not ok');
  }
  return response.json();
};

export const useFetchStates = () => {
  return useInfiniteQuery({
    queryKey: ['states'],
    queryFn: ({ pageParam = 0 }) => fetchStates(pageParam, '', 10), // Default to 0 if pageParam is undefined
    initialPageParam: 0, // Start with offset of 0
    getNextPageParam: (lastPage, allPages) => {
      const totalItem = lastPage?.result?.count;
      const allItems = allPages?.length * 20;
      if (allItems >= totalItem) {
        return undefined;
      }
      return allPages?.length * 20;
    },
  });
};
