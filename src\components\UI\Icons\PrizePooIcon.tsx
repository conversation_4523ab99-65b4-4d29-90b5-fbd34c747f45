import React from 'react';

const PrizePooIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="60"
      height="60"
      viewBox="0 0 60 60"
    >
      <defs>
        <linearGradient
          id="linear-gradient"
          x1="0.5"
          y1="1"
          x2="0.5"
          gradientUnits="objectBoundingBox"
        >
          <stop offset="0" stopColor="#bb401e" />
          <stop offset="0.152" stopColor="#fc4714" />
          <stop offset="0.675" stopColor="#fc7914" />
          <stop offset="1" stopColor="#fed1c4" />
        </linearGradient>
      </defs>
      <g id="PrizePooIcon" transform="translate(-9043 -12408)">
        <rect
          id="Rectangle_62060"
          data-name="Rectangle 62060"
          width="60"
          height="60"
          rx="30"
          transform="translate(9043 12408)"
          fill="url(#linear-gradient)"
        />
        <g
          id="Group_116720"
          data-name="Group 116720"
          transform="translate(9058.068 12420.715)"
        >
          <path
            id="Path_179179"
            data-name="Path 179179"
            d="M35.175,76.572H15.4c-.06.768-.09,1.537-.09,2.324,0,12.294,5.143,17.268,9.974,17.268S35.26,91.191,35.26,78.9C35.265,78.109,35.234,77.34,35.175,76.572Z"
            transform="translate(-10.358 -76.572)"
            fill="none"
            stroke="#fff"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            id="Path_179180"
            data-name="Path 179180"
            d="M68.512,87.087H73.46s.887,7.618-8.166,12.133"
            transform="translate(-43.62 -83.571)"
            fill="none"
            stroke="#fff"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            id="Path_179181"
            data-name="Path 179181"
            d="M5.473,87.087H.525S-.363,94.705,8.689,99.22"
            transform="translate(-0.501 -83.571)"
            fill="none"
            stroke="#fff"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            id="Path_179182"
            data-name="Path 179182"
            d="M32.677,134.772c0,3.116-.73,4.9-2.837,6.231"
            transform="translate(-20.026 -115.301)"
            fill="none"
            stroke="#fff"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            id="Path_179183"
            data-name="Path 179183"
            d="M51.954,134.772c0,3.116.73,4.9,2.837,6.231"
            transform="translate(-34.745 -115.301)"
            fill="none"
            stroke="#fff"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <rect
            id="Rectangle_57626"
            data-name="Rectangle 57626"
            width="12.331"
            height="7.117"
            transform="translate(8.764 25.702)"
            fill="none"
            stroke="#fff"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            id="Path_179184"
            data-name="Path 179184"
            d="M35.689,179.018H15.778v-2.165a2.164,2.164,0,0,1,2.165-2.165H33.529a2.165,2.165,0,0,1,2.165,2.165Z"
            transform="translate(-10.668 -141.865)"
            fill="none"
            stroke="#fff"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <g
            id="Group_116728"
            data-name="Group 116728"
            transform="translate(-585.068 -5522.715)"
          >
            <path
              id="Path_203909"
              data-name="Path 203909"
              d="M2.357,1.786l-.014.078-.011.046-.018.049-.039.078.223.332.272.435L2.8,2.758l.021-.042.021-.042.018-.035.018-.032.021-.039.032-.06.025-.049.028-.064.032-.071.021-.049.035-.085.014-.042L3.1,2.1l.018-.049,0-.018,0-.014,0-.011,0-.014,0-.014.007-.021.007-.021,0-.021,0-.018,0-.018,0-.018,0-.021,0-.025,0-.021,0-.021,0-.028V1.56l0-.035,0-.028,0-.021,0-.028,0-.021,0-.025-.014-.035L3.138,1.33l-.007-.035L3.12,1.259l-.007-.032L3.106,1.2,3.1,1.164l-.014-.039-.011-.032-.014-.032-.011-.025-.011-.028L3.018.973,2.993.927,2.972.892,2.951.856,2.923.814,2.9.779,2.88.754,2.866.733,2.848.711,2.834.694,2.82.676,2.8.655,2.785.634,2.767.612,2.749.591,2.732.574,2.714.556,2.689.531,2.665.506,2.643.489,2.622.471,2.6.45,2.573.429,2.544.407,2.52.39,2.5.379,2.477.365,2.46.354,2.438.337,2.414.323,2.378.3,2.354.287,2.322.273,2.294.259,2.262.241,2.237.231,2.205.217,2.17.2,2.135.188,2.106.178,2.074.167,2.039.157,2,.146,1.972.139,1.947.132,1.908.125,1.873.118l-.028,0-.028,0-.032,0L1.756.1H1.732L1.7.1h-.2L1.474.1H1.449l-.028,0-.028,0-.032,0L1.325.121,1.3.125,1.269.132,1.24.139,1.212.146,1.187.153,1.163.16,1.134.167,1.106.178,1.081.188,1.057.2,1.025.21,1,.22.979.227.954.234.929.245.9.255.883.27.852.287.83.3.8.316.774.33.749.347.717.369.686.393.65.415.618.439.59.46.565.482.548.5.519.524.491.549.474.566.449.591.428.616.4.644.375.676.353.7.332.729.318.757.3.782.279.81.258.842.237.877.223.9.2.945.18.987l-.018.035-.014.035L.134,1.09l-.011.025L.11,1.153.1,1.181l-.014.042-.011.035,0,.035-.007.028-.007.035L.049,1.39l-.007.042,0,.035,0,.039,0,.035,0,.042v.028l0,.035v.039l0,.046,0,.035,0,.035.007.042L.053,1.9l.011.042.011.035.011.039L.1,2.051.113,2.1l.014.042.018.046.021.049.018.046.011.025.014.035.014.035.014.035.028.06.021.046.021.042L.325,2.6l.021.046.021.042.021.039.018.032.018.035.028.049.025.042L.5,2.931.53,2.98l.028.049.028.049.028.049.028.046.039.064.028.046.028.049.035.057L.8,3.436l.032.046.025.039.028.046.035.057.025.039.035.053.046.067.039.057.021.032.028.042.***************.025.039.035.049.032.046.028.039.039.057.025.035.028.042.028.042.028.039.035.049.035.049.035.049.032.042L1.6,4.521l.053-.067.081-.11L1.8,4.27l.057-.078.057-.078.071-.1.039-.053.011-.018.021.018.018.014.014.011.021.018.014.014.018.018.028.032.014.018.014.018.014.021.007.011.007.014.011.014.011.014.007.014.011.021.011.021.011.025.007.021.011.032.007.025.007.021.007.025,0,.025,0,.025,0,.021,0,.025,0,.025v.12l0,.025,0,.028,0,.025-.007.028-.007.021L2.3,4.779,2.3,4.8l-.011.025-.011.025-.014.032-.011.025-.014.025-.021.035L2.2,4.995l-.025.035-.018.021-.021.025L2.11,5.1l-.032.032-.025.021-.028.025L1.99,5.2l-.035.021-.042.025-.025.011-.039.018-.039.014L1.774,5.3l-.028.007-.028.007-.032,0-.032,0-.025,0H1.516l-.025,0-.028,0-.025,0-.021,0-.025-.007-.018,0-.018-.007-.025-.007L1.3,5.274l-.028-.011L1.24,5.249l-.025-.014-.025-.014L1.163,5.2l-.035-.025L1.092,5.15l-.035-.032-.025-.025-.025-.028L.982,5.033.961,5,.933,4.959.908,4.917.887,4.874.873,4.835.859,4.8.841,4.74.83,4.687.823,4.638l0-.042V4.5l.007-.057L.834,4.4l.011-.049.011-.039L.88,4.245l.014-.028.018-.032L.074,4.178.067,4.2.06,4.221l-.007.028-.007.025L.039,4.3l-.014.035-.007.035,0,.028-.007.035,0,.032L0,4.5v.113l0,.039,0,.046,0,.032,0,.035,0,.035.007.039.007.046.011.049.014.049.021.064.014.039.018.046.018.039L.141,5.2l.014.035.014.028.011.025.011.018L.2,5.334l.014.025.021.035.018.028.021.028L.3,5.479l.021.028.018.021.028.032.028.032.025.028.025.025.025.025.046.042.046.039.032.025L.622,5.8l.039.028L.7,5.857l.032.021L.774,5.9l.035.021.035.018.042.021.049.025.046.021.035.014.035.014.039.014.046.014.042.011.049.011.053.011.053.011.06.007.053.007.035,0,.039,0h.131l.046,0,.035,0,.042,0,.049-.007L1.88,6.1l.064-.014L2,6.072l.046-.014L2.1,6.041l.053-.018L2.209,6l.049-.021L2.3,5.959l.049-.025.046-.028.042-.025.049-.032.057-.042.039-.032.053-.046.049-.046.057-.057L2.764,5.6l.018-.021L2.8,5.556l.025-.028L2.845,5.5l.025-.035.018-.025L2.9,5.415l.018-.028.011-.025.021-.035L2.969,5.3l.018-.032L3,5.231l.018-.039.021-.049.014-.035.014-.035.014-.042L3.1,4.991l.011-.035L3.12,4.9l.007-.039.007-.049.007-.042,0-.046,0-.042,0-.053v-.1l0-.039,0-.042,0-.035-.007-.046-.011-.049-.011-.042L3.1,4.224l-.018-.053L3.06,4.122l-.018-.046-.014-.039L3,3.984,2.969,3.9,2.93,3.814l-.035-.071-.028-.057-.032-.06L2.8,3.556,2.767,3.5l-.035-.067L2.7,3.362l-.035-.067-.039-.064-.053-.088L2.53,3.076l-.042-.067-.032-.053L2.424,2.9l-.046-.074-.046-.074L2.3,2.7l-.035-.053-.039-.06L2.173,2.5l-.046-.067-.046-.071L2.043,2.3,2,2.235l-.046-.067-.035-.049-.049-.071L1.831,2,1.8,1.952l-.032-.046-.042-.06L1.693,1.8,1.65,1.747,1.6,1.676,1.58,1.7l-.035.042-.032.042-.042.053-.046.06-.032.042L1.346,2l-.039.064-.039.057L1.216,2.2l-.046.057-.025.035L1.131,2.28,1.117,2.27,1.1,2.259,1.06,2.224l-.028-.032L1,2.153.975,2.121.947,2.075.929,2.047.915,2.015.9,1.987.883,1.945.869,1.9.855,1.853.848,1.807.841,1.768l0-.053V1.648l0-.042,0-.035,0-.039L.855,1.5l.007-.032.011-.032.011-.028.011-.032L.908,1.34.929,1.3l.021-.042.021-.028L1,1.2l.035-.039.035-.035L1.1,1.093l.035-.028L1.17,1.04l.039-.025L1.255.987,1.3.966,1.336.952,1.368.941,1.407.93,1.445.92,1.5.913l.057,0h.057l.057,0L1.717.92l.***************.049.018L1.965,1l.**************.***************.***************.***************.***************.018.042.018.046L2.35,1.5l.***************,0,.053v.06Z"
              transform="translate(597.909 5528.387)"
              fill="#fff"
            />
            <g
              id="Ellipse_14829"
              data-name="Ellipse 14829"
              transform="translate(594 5526)"
              fill="none"
              stroke="#fff"
              strokeWidth="1"
            >
              <circle cx="5.5" cy="5.5" r="5.5" stroke="none" />
              <circle cx="5.5" cy="5.5" r="5" fill="none" />
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
};

export default PrizePooIcon;
