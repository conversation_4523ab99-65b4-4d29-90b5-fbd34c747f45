'use client';
import type {
  captainViceCaptains,
  careerStatsOpponents,
  careerStatsSSL,
  careerStatsVenues,
  fantasyStatsState,
  PlayerProfileDetails,
  seasonStatsSummaryStats,
  seasonStatsUpcomingMatches,
  Team,
  TeamData,
  TeamSpendData,
} from '../types';
import { Category } from '../types/commentry';
import type { Player } from '../types/competitions';

export const teams: Team[] = [
  {
    id: 1,
    coach: 'John08',
    teamName: 'Team 1',
    teamValue: '$6,623',
  },
  { id: 2, coach: 'John08', teamName: 'Team 2', teamValue: '$6,623' },
  { id: 3, coach: 'Alex99', teamName: 'Team 2', teamValue: '$8,382' },
  { id: 4, coach: 'Chris<PERSON>', teamName: 'Team 1', teamValue: '$3,587' },
  { id: 5, coach: '<PERSON>_<PERSON>', teamName: 'Team 1', teamValue: '$3,545' },
  { id: 6, coach: '<PERSON><PERSON><PERSON>', teamName: 'Team 1', teamValue: '$3,575' },
  { id: 7, coach: 'Emma<PERSON>', teamName: 'Team 2', teamValue: '$6,623' },
  { id: 8, coach: '<PERSON>K', teamName: 'Team 1', teamValue: '$8,382' },
  { id: 9, coach: 'SophiaB', teamName: 'Team 1', teamValue: '$3,587' },
  { id: 10, coach: '<PERSON>J', teamName: 'Team 1', teamValue: '$3,545' },
  { id: 11, coach: 'AvaM', teamName: 'Team 2', teamValue: '$3,575' },
  { id: 12, coach: 'LucasL', teamName: 'Team 1', teamValue: '$8,382' },
  { id: 13, coach: 'OliviaP', teamName: 'Team 1', teamValue: '$3,587' },
  { id: 14, coach: 'BenjaminC', teamName: 'Team 1', teamValue: '$3,545' },
  { id: 15, coach: 'IsabellaH', teamName: 'Team 1', teamValue: '$3,575' },
  { id: 16, coach: 'JamesW', teamName: 'Team 2', teamValue: '$6,623' },
];

export const teamSpendData: TeamSpendData[] = [
  { position: 'WKP', totalSpend: '$720,100', average: '$720,100' },
  { position: 'BAT', totalSpend: '$720,100', average: '$720,100' },
  { position: 'BOW', totalSpend: '$720,100', average: '$720,100' },
];
export const upcomingMatch: seasonStatsUpcomingMatches[] = [
  {
    opponent: 'New Zealand',
    venue: 'Sydney Oval',
    rounds: '1st and 2nd',
    date: new Date('2022-12-15'),
  },
  {
    opponent: 'Australia',
    venue: 'Melbourne Cricket Ground',
    rounds: '1st and 2nd',
    date: new Date('2022-12-15'),
  },
  {
    opponent: 'Sri Lanka',
    venue: 'Sri Lanka Cricket Stadium',
    rounds: '1st and 2nd',
    date: new Date('2022-12-15'),
  },
  {
    opponent: 'West Indies',
    venue: 'Wankhede Stadium',
    rounds: '1st and 2nd',
    date: new Date('2022-12-15'),
  },
  {
    opponent: 'India',
    venue: 'Eden Gardens',
    rounds: '1st and 2nd',
    date: new Date('2022-12-15'),
  },
];

export const seasonSummaryStateData: seasonStatsSummaryStats[] = [
  {
    rd: 1,
    opponent: 'Australia',
    venue: 'Melbourne Cricket Ground',
    score: 210,
    totalScore: 320,
    avg: 25.3,
    price: 18000,
    priceChange: -5000,
    points: 100,
    teams: 5,
  },
  {
    rd: 2,
    opponent: 'Australia',
    venue: 'Melbourne Cricket Ground',
    score: 210,
    totalScore: 320,
    avg: 25.3,
    price: 18000,
    priceChange: -5000,
    points: 100,
    teams: 5,
  },
  {
    rd: 3,
    opponent: 'Australia',
    venue: 'Melbourne Cricket Ground',
    score: 210,
    totalScore: 320,
    avg: 25.3,
    price: 18000,
    priceChange: -5000,
    points: 100,
    teams: 5,
  },
  {
    rd: 4,
    opponent: 'Australia',
    venue: 'Melbourne Cricket Ground',
    score: 210,
    totalScore: 320,
    avg: 25.3,
    price: 18000,
    priceChange: -5000,
    points: 100,
    teams: 5,
  },
  {
    rd: 5,
    opponent: 'Australia',
    venue: 'Melbourne Cricket Ground',
    score: 210,
    totalScore: 320,
    avg: 25.3,
    price: 18000,
    priceChange: -5000,
    points: 100,
    teams: 5,
  },
];

export const teamTableData: TeamData[] = [
  {
    position: 1,
    teamName: 'Brisbane Heat',
    matches: 10,
    wins: 7,
    losses: 1,
    ties: 0,
    noResults: 2,
    netRunRate: 0.972,
    deductions: 0,
    points: 16,
  },
  {
    position: 2,
    teamName: 'Sydney Sixers',
    matches: 10,
    wins: 6,
    losses: 2,
    ties: 0,
    noResults: 2,
    netRunRate: 0.972,
    deductions: 0,
    points: 14,
  },
  {
    position: 3,
    teamName: 'Perth Scorchers',
    matches: 10,
    wins: 5,
    losses: 3,
    ties: 0,
    noResults: 2,
    netRunRate: 0.972,
    deductions: 0,
    points: 13,
  },
  {
    position: 4,
    teamName: 'Adelaide Strikers',
    matches: 10,
    wins: 4,
    losses: 4,
    ties: 0,
    noResults: 2,
    netRunRate: 0.972,
    deductions: 0,
    points: 11,
  },
  {
    position: 5,
    teamName: 'Hobart Hurricanes',
    matches: 10,
    wins: 4,
    losses: 4,
    ties: 0,
    noResults: 2,
    netRunRate: 0.972,
    deductions: 0,
    points: 8,
  },
  {
    position: 6,
    teamName: 'Melbourne Stars',
    matches: 10,
    wins: 2,
    losses: 6,
    ties: 0,
    noResults: 2,
    netRunRate: 0.972,
    deductions: 0,
    points: 8,
  },
  {
    position: 7,
    teamName: 'Melbourne Renegades',
    matches: 10,
    wins: 1,
    losses: 7,
    ties: 0,
    noResults: 2,
    netRunRate: 0.972,
    deductions: 0,
    points: 6,
  },
  {
    position: 8,
    teamName: 'Sydney Thunder',
    matches: 10,
    wins: 1,
    losses: 7,
    ties: 0,
    noResults: 2,
    netRunRate: 0.972,
    deductions: 0,
    points: 4,
  },
];

export const fantasyStatsStateData: fantasyStatsState[] = [
  {
    rd: 1,
    opponent: 'Adelaide Strikers',
    run: 3,
    sr: 3,
    rb: 3,
    wickets: 3,
    maiden: 3,
    dot: 3,
    economy: 3,
    catches: 3,
    runOut: 3,
    stumping: 3,
    directHit: 3,
    allRounder: 3,
  },
  {
    rd: 2,
    opponent: 'Sydney Sixers',
    run: 15,
    sr: 120,
    rb: 25,
    wickets: 2,
    maiden: 1,
    dot: 12,
    economy: 6.5,
    catches: 1,
    runOut: 0,
    stumping: 0,
    directHit: 1,
    allRounder: 10,
  },
  {
    rd: 3,
    opponent: 'Melbourne Stars',
    run: 40,
    sr: 135,
    rb: 30,
    wickets: 4,
    maiden: 2,
    dot: 18,
    economy: 5.5,
    catches: 2,
    runOut: 1,
    stumping: 1,
    directHit: 2,
    allRounder: 25,
  },
  {
    rd: 4,
    opponent: 'Brisbane Heat',
    run: 10,
    sr: 80,
    rb: 12,
    wickets: 1,
    maiden: 0,
    dot: 10,
    economy: 8.2,
    catches: 0,
    runOut: 0,
    stumping: 0,
    directHit: 1,
    allRounder: 8,
  },
  {
    rd: 5,
    opponent: 'Perth Scorchers',
    run: 55,
    sr: 150,
    rb: 37,
    wickets: 5,
    maiden: 3,
    dot: 20,
    economy: 4.8,
    catches: 3,
    runOut: 2,
    stumping: 0,
    directHit: 0,
    allRounder: 30,
  },
];

export const careerStatsOpponentsData: careerStatsOpponents[] = [
  {
    opponent: 'Team A',
    timesPlayed: 15,
    highScore: 120,
    lowScore: 78,
    avg: 94.7,
  },
  {
    opponent: 'Team B',
    timesPlayed: 10,
    highScore: 105,
    lowScore: 65,
    avg: 84.3,
  },
  {
    opponent: 'Team C',
    timesPlayed: 8,
    highScore: 112,
    lowScore: 80,
    avg: 92.5,
  },
  {
    opponent: 'Team D',
    timesPlayed: 12,
    highScore: 130,
    lowScore: 90,
    avg: 102.8,
  },
  {
    opponent: 'Team E',
    timesPlayed: 20,
    highScore: 98,
    lowScore: 55,
    avg: 76.4,
  },
];

export const careerStatsVenuesData: careerStatsVenues[] = [
  {
    venue: 'Stadium X',
    timesPlayed: 18,
    highScore: 115,
    lowScore: 70,
    avg: 92.4,
  },
  {
    venue: 'Arena Y',
    timesPlayed: 12,
    highScore: 125,
    lowScore: 85,
    avg: 98.3,
  },
  {
    venue: 'Field Z',
    timesPlayed: 9,
    highScore: 110,
    lowScore: 68,
    avg: 88.9,
  },
  {
    venue: 'Dome A',
    timesPlayed: 20,
    highScore: 135,
    lowScore: 90,
    avg: 106.7,
  },
  {
    venue: 'Ground B',
    timesPlayed: 14,
    highScore: 102,
    lowScore: 60,
    avg: 82.6,
  },
];

export const careerStatsSSLData: careerStatsSSL[] = [
  {
    year: 2020,
    startingPrice: 50,
    endPrice: 75,
    priceChange: 25,
    matchesPlayed: 15,
    avgScore: 87.3,
  },
  {
    year: 2021,
    startingPrice: 60,
    endPrice: 80,
    priceChange: 20,
    matchesPlayed: 18,
    avgScore: 91.2,
  },
  {
    year: 2022,
    startingPrice: 65,
    endPrice: 90,
    priceChange: 25,
    matchesPlayed: 20,
    avgScore: 94.5,
  },
  {
    year: 2023,
    startingPrice: 70,
    endPrice: 95,
    priceChange: 25,
    matchesPlayed: 17,
    avgScore: 88.9,
  },
  {
    year: 2024,
    startingPrice: 75,
    endPrice: 100,
    priceChange: 25,
    matchesPlayed: 22,
    avgScore: 92.1,
  },
];

export const captainViceCaptainsData: captainViceCaptains[] = [
  {
    id: 1,
    name: 'John Doe',
    type: 'Captain',
    teamName: 'Team A',
    captain: 2.1,
    viceCaptain: 2.7,
  },
  {
    id: 2,
    name: 'Jane Smith',
    type: 'Vice-Captain',
    teamName: 'Team B',
    captain: 2.2,
    viceCaptain: 2.8,
  },
  {
    id: 3,
    name: 'Bob Johnson',
    type: 'Captain',
    teamName: 'Team C',
    captain: 2.3,
    viceCaptain: 2.9,
  },
  {
    id: 4,
    name: 'Alice Williams',
    type: 'Vice-Captain',
    teamName: 'Team D',
    captain: 2.4,
    viceCaptain: 3.0,
  },
  {
    id: 5,
    name: 'Charlie Brown',
    type: 'Captain',
    teamName: 'Team E',
    captain: 2.5,
    viceCaptain: 3.1,
  },
  {
    id: 6,
    name: 'Eve Green',
    type: 'Vice-Captain',
    teamName: 'Team F',
    captain: 2.6,
    viceCaptain: 3.2,
  },
  {
    id: 7,
    name: 'Frank White',
    type: 'Captain',
    teamName: 'Team G',
    captain: 2.7,
    viceCaptain: 3.3,
  },
  {
    id: 8,
    name: 'Grace Taylor',
    type: 'Vice-Captain',
    teamName: 'Team H',
    captain: 2.8,
    viceCaptain: 3.4,
  },
  {
    id: 9,
    name: 'Harry Black',
    type: 'Captain',
    teamName: 'Team I',
    captain: 2.9,
    viceCaptain: 3.5,
  },
  {
    id: 10,
    name: 'Ivy Lewis',
    type: 'Vice-Captain',
    teamName: 'Team J',
    captain: 3.0,
    viceCaptain: 3.6,
  },
];

export const PlayerProfileDetailsData: PlayerProfileDetails[] = [
  {
    id: 1,
    name: 'John Doe',
    type: 'Captain',
    teamName: 'Team A',
    lastScore: 85,
    totalScore: 120,
    avg: 50.4,
    matches: 15,
    threeGamesAvg: 60.3,
    fiveGamesAvg: 55.2,
    selectionPercent: 75.5,
    price: '$171,400 -$4k',
  },
  {
    id: 2,
    name: 'Jane Smith',
    type: 'Vice-Captain',
    teamName: 'Team B',
    lastScore: 73,
    totalScore: 110,
    avg: 47.8,
    matches: 12,
    threeGamesAvg: 58.2,
    fiveGamesAvg: 52.9,
    selectionPercent: 65.8,
    price: '$159,000 -$3k',
  },
  {
    id: 3,
    name: 'Michael Brown',
    type: 'Player',
    teamName: 'Team C',
    lastScore: 65,
    totalScore: 98,
    avg: 43.7,
    matches: 10,
    threeGamesAvg: 50.1,
    fiveGamesAvg: 48.3,
    selectionPercent: 60.0,
    price: '$143,500 -$2k',
  },
  {
    id: 4,
    name: 'Emily Davis',
    type: 'Player',
    teamName: 'Team D',
    lastScore: 82,
    totalScore: 115,
    avg: 49.9,
    matches: 14,
    threeGamesAvg: 56.4,
    fiveGamesAvg: 53.0,
    selectionPercent: 72.2,
    price: '$168,300 -$3.5k',
  },
  {
    id: 5,
    name: 'David Wilson',
    type: 'Captain',
    teamName: 'Team E',
    lastScore: 90,
    totalScore: 135,
    avg: 55.6,
    matches: 17,
    threeGamesAvg: 64.0,
    fiveGamesAvg: 60.7,
    selectionPercent: 78.4,
    price: '$184,700 -$5k',
  },
  {
    id: 6,
    name: 'Sophia Miller',
    type: 'Vice-Captain',
    teamName: 'Team F',
    lastScore: 68,
    totalScore: 100,
    avg: 46.5,
    matches: 13,
    threeGamesAvg: 54.3,
    fiveGamesAvg: 50.0,
    selectionPercent: 63.3,
    price: '$151,200 -$2.5k',
  },
  {
    id: 7,
    name: 'Chris Johnson',
    type: 'Player',
    teamName: 'Team G',
    lastScore: 77,
    totalScore: 109,
    avg: 47.3,
    matches: 11,
    threeGamesAvg: 57.9,
    fiveGamesAvg: 52.8,
    selectionPercent: 68.1,
    price: '$162,900 -$3k',
  },
  {
    id: 8,
    name: 'Ava Jones',
    type: 'Player',
    teamName: 'Team H',
    lastScore: 74,
    totalScore: 113,
    avg: 48.0,
    matches: 13,
    threeGamesAvg: 55.8,
    fiveGamesAvg: 53.6,
    selectionPercent: 66.0,
    price: '$160,400 -$3.1k',
  },
  {
    id: 9,
    name: 'Daniel White',
    type: 'Captain',
    teamName: 'Team I',
    lastScore: 95,
    totalScore: 145,
    avg: 57.4,
    matches: 18,
    threeGamesAvg: 66.5,
    fiveGamesAvg: 61.9,
    selectionPercent: 80.5,
    price: '$189,500 -$5.2k',
  },
  {
    id: 10,
    name: 'Olivia Martin',
    type: 'Vice-Captain',
    teamName: 'Team J',
    lastScore: 69,
    totalScore: 106,
    avg: 47.1,
    matches: 12,
    threeGamesAvg: 54.7,
    fiveGamesAvg: 51.1,
    selectionPercent: 64.8,
    price: '$154,000 -$2.8k',
  },
  {
    id: 11,
    name: 'Ethan Lee',
    type: 'Player',
    teamName: 'Team K',
    lastScore: 79,
    totalScore: 118,
    avg: 48.9,
    matches: 15,
    threeGamesAvg: 59.2,
    fiveGamesAvg: 54.5,
    selectionPercent: 69.9,
    price: '$166,200 -$3.2k',
  },
  {
    id: 12,
    name: 'Emma Garcia',
    type: 'Player',
    teamName: 'Team L',
    lastScore: 76,
    totalScore: 112,
    avg: 47.6,
    matches: 13,
    threeGamesAvg: 56.1,
    fiveGamesAvg: 52.3,
    selectionPercent: 67.1,
    price: '$161,700 -$3k',
  },
  {
    id: 13,
    name: 'James Rodriguez',
    type: 'Captain',
    teamName: 'Team M',
    lastScore: 93,
    totalScore: 140,
    avg: 56.0,
    matches: 16,
    threeGamesAvg: 65.3,
    fiveGamesAvg: 60.2,
    selectionPercent: 79.2,
    price: '$186,000 -$4.8k',
  },
  {
    id: 14,
    name: 'Mia Martinez',
    type: 'Vice-Captain',
    teamName: 'Team N',
    lastScore: 72,
    totalScore: 108,
    avg: 46.9,
    matches: 13,
    threeGamesAvg: 54.1,
    fiveGamesAvg: 50.9,
    selectionPercent: 64.4,
    price: '$153,800 -$2.7k',
  },
  {
    id: 15,
    name: 'William Hernandez',
    type: 'Player',
    teamName: 'Team O',
    lastScore: 84,
    totalScore: 119,
    avg: 49.3,
    matches: 12,
    threeGamesAvg: 59.5,
    fiveGamesAvg: 54.0,
    selectionPercent: 70.8,
    price: '$165,400 -$3.3k',
  },
  {
    id: 16,
    name: 'Isabella Brown',
    type: 'Player',
    teamName: 'Team P',
    lastScore: 75,
    totalScore: 111,
    avg: 47.8,
    matches: 14,
    threeGamesAvg: 55.6,
    fiveGamesAvg: 52.5,
    selectionPercent: 66.5,
    price: '$159,900 -$3k',
  },
  {
    id: 17,
    name: 'Mason Clark',
    type: 'Captain',
    teamName: 'Team Q',
    lastScore: 88,
    totalScore: 132,
    avg: 54.0,
    matches: 15,
    threeGamesAvg: 63.8,
    fiveGamesAvg: 58.4,
    selectionPercent: 76.9,
    price: '$180,500 -$4.6k',
  },
  {
    id: 18,
    name: 'Ava Lopez',
    type: 'Vice-Captain',
    teamName: 'Team R',
    lastScore: 70,
    totalScore: 107,
    avg: 46.7,
    matches: 11,
    threeGamesAvg: 53.4,
    fiveGamesAvg: 51.0,
    selectionPercent: 63.5,
    price: '$152,300 -$2.6k',
  },
  {
    id: 19,
    name: 'Lucas Walker',
    type: 'Player',
    teamName: 'Team S',
    lastScore: 81,
    totalScore: 117,
    avg: 48.6,
    matches: 13,
    threeGamesAvg: 57.5,
    fiveGamesAvg: 53.2,
    selectionPercent: 68.7,
    price: '$164,200 -$3.1k',
  },
  {
    id: 20,
    name: 'Sophia Hill',
    type: 'Player',
    teamName: 'Team T',
    lastScore: 78,
    totalScore: 113,
    avg: 47.2,
    matches: 12,
    threeGamesAvg: 56.8,
    fiveGamesAvg: 51.8,
    selectionPercent: 65.9,
    price: '$160,000 -$3.2k',
  },
  {
    id: 21,
    name: 'Benjamin Harris',
    type: 'Captain',
    teamName: 'Team U',
    lastScore: 91,
    totalScore: 136,
    avg: 55.3,
    matches: 16,
    threeGamesAvg: 64.1,
    fiveGamesAvg: 60.5,
    selectionPercent: 78.2,
    price: '$183,000 -$4.9k',
  },
  {
    id: 22,
    name: 'Ella Allen',
    type: 'Vice-Captain',
    teamName: 'Team V',
    lastScore: 71,
    totalScore: 104,
    avg: 46.2,
    matches: 11,
    threeGamesAvg: 53.0,
    fiveGamesAvg: 50.6,
    selectionPercent: 63.2,
    price: '$151,000 -$2.5k',
  },
  {
    id: 23,
    name: 'Henry Nelson',
    type: 'Player',
    teamName: 'Team W',
    lastScore: 83,
    totalScore: 118,
    avg: 49.0,
    matches: 14,
    threeGamesAvg: 58.6,
    fiveGamesAvg: 53.9,
    selectionPercent: 71.5,
    price: '$166,800 -$3.4k',
  },
  {
    id: 24,
    name: 'Grace Carter',
    type: 'Player',
    teamName: 'Team X',
    lastScore: 73,
    totalScore: 109,
    avg: 46.8,
    matches: 10,
    threeGamesAvg: 54.9,
    fiveGamesAvg: 52.7,
    selectionPercent: 66.2,
    price: '$157,500 -$2.9k',
  },
  {
    id: 25,
    name: 'Oliver Wood',
    type: 'Captain',
    teamName: 'Team Y',
    lastScore: 89,
    totalScore: 133,
    avg: 53.5,
    matches: 15,
    threeGamesAvg: 63.4,
    fiveGamesAvg: 58.1,
    selectionPercent: 76.0,
    price: '$178,000 -$4.3k',
  },
  {
    id: 26,
    name: 'Lily Adams',
    type: 'Vice-Captain',
    teamName: 'Team Z',
    lastScore: 74,
    totalScore: 108,
    avg: 47.4,
    matches: 12,
    threeGamesAvg: 54.5,
    fiveGamesAvg: 51.4,
    selectionPercent: 64.6,
    price: '$155,000 -$2.7k',
  },
  {
    id: 27,
    name: 'Jack Evans',
    type: 'Player',
    teamName: 'Team AA',
    lastScore: 76,
    totalScore: 112,
    avg: 47.8,
    matches: 13,
    threeGamesAvg: 56.0,
    fiveGamesAvg: 52.1,
    selectionPercent: 67.4,
    price: '$158,500 -$3.0k',
  },
  {
    id: 28,
    name: 'Luna Kelly',
    type: 'Player',
    teamName: 'Team BB',
    lastScore: 80,
    totalScore: 116,
    avg: 48.5,
    matches: 14,
    threeGamesAvg: 57.4,
    fiveGamesAvg: 53.0,
    selectionPercent: 69.3,
    price: '$162,000 -$3.3k',
  },
  {
    id: 29,
    name: 'Logan Scott',
    type: 'Captain',
    teamName: 'Team CC',
    lastScore: 92,
    totalScore: 137,
    avg: 55.4,
    matches: 15,
    threeGamesAvg: 63.9,
    fiveGamesAvg: 60.3,
    selectionPercent: 77.8,
    price: '$181,000 -$4.7k',
  },
  {
    id: 30,
    name: 'Zoe Bailey',
    type: 'Vice-Captain',
    teamName: 'Team DD',
    lastScore: 72,
    totalScore: 105,
    avg: 46.5,
    matches: 12,
    threeGamesAvg: 54.3,
    fiveGamesAvg: 50.8,
    selectionPercent: 63.9,
    price: '$152,800 -$2.6k',
  },
];

export const GENDER_OPTIONS = [
  { label: 'Male', value: 'm' },
  { label: 'Female', value: 'f' },
  { label: 'Non-binary', value: 'nb' },
  { label: 'Prefer not to say', value: 'pnts' },
];

interface ScoreData {
  lastScore: number;
  totalScore: number;
  lastThreeMatch: number;
  lastFiveMatch: number;
  totalPlayed: number;
  avg: number;
  sel: number;
  playerCurrentSalary: number;
  playerLastSalary: number;
}

const createPlayer = (
  basePlayer: Omit<
    Player,
    | 'rank'
    | 'type'
    | 'team'
    | 'lastScore'
    | 'total'
    | 'avg'
    | 'matches'
    | 'selectionPercent'
    | 'price'
    | 'avatar'
    | 'avgThreeGames'
  > & { scoreData: ScoreData },
): Player => {
  const roleType =
    basePlayer.role === 'Batter'
      ? ['BAT']
      : basePlayer.role === 'Bowler'
        ? ['BOW']
        : basePlayer.role === 'Batting Allrounder'
          ? ['BAT', 'BOW']
          : basePlayer.role === 'Bowling Allrounder'
            ? ['BOW', 'BAT']
            : basePlayer.role === 'WK-Batter'
              ? ['WK']
              : ['BAT'];

  const lastThreeMatch = basePlayer.scoreData.lastThreeMatch;
  const avgThreeGames = lastThreeMatch / 3;

  return {
    ...basePlayer,
    rank: 1,
    type: roleType,
    team: basePlayer.teamName,
    lastScore: basePlayer.scoreData.lastScore,
    total: basePlayer.scoreData.totalScore,
    avg: basePlayer.scoreData.avg,
    matches: basePlayer.scoreData.totalPlayed,
    selectionPercent: basePlayer.scoreData.sel,
    price: `$${basePlayer.scoreData.playerCurrentSalary}`,
    avatar: '/fantasy/images/player/DefaultPlayer.svg',
    avgThreeGames,
  };
};

let token;

if (typeof window !== 'undefined') {
  // Access localStorage only in the browser
  const storedToken = localStorage.getItem('auth_token');
  token = storedToken?.replace(/"/g, '');
}

export const Token = token ?? null;

export const getDefaultProfileImage = () =>
  '/fantasy/images/player/DefaultPlayer.svg';

export const getDefaultCountryFlag = () => '/fantasy/images/defaultCountry.png';

export const getDefaultUserImage = () =>
  '/fantasy/images/user/defaultSmartUser.png';

export const getDefaultTeamImage = () =>
  '/fantasy/images/player/defaultTeam.png';

export const playerShape = {
  id: {
    name: 'id',
    type: 'number',
    description: 'The unique identifier for the player',
    required: true,
  },
  role: {
    name: 'role',
    type: "'Batter' | 'Batting Allrounder' | 'WK-Batter' | 'Bowler' | 'Bowling Allrounder'",
    description: 'The role of the player in the team',
    required: true,
  },
  squadsId: {
    name: 'squadsId',
    type: 'number',
    description: 'The ID of the squad the player belongs to',
    required: true,
  },
  tournamentId: {
    name: 'tournamentId',
    type: 'number',
    description: 'The ID of the tournament the player is participating in',
    required: true,
  },
  playerId: {
    name: 'playerId',
    type: 'number',
    description: 'The ID of the player',
    required: true,
  },
  name: {
    name: 'name',
    type: 'string',
    description: 'The name of the player',
    required: true,
  },
  scoreData: {
    name: 'scoreData',
    type: 'PlayerStats',
    description: 'The statistics of the player',
    required: true,
  },
  teamName: {
    name: 'teamName',
    type: 'string',
    description: "The name of the player's team",
    required: true,
  },
  image: {
    name: 'image',
    type: 'string | null',
    description: "The URL of the player's image",
    required: false,
  },
  playerValue: {
    name: 'playerValue',
    type: 'number | null',
    description: 'The value of the player in the tournament',
    required: false,
  },
  isAdded: {
    name: 'isAdded',
    type: 'boolean',
    description: 'Indicates if the player is added to the team',
    required: false,
  },
  positionType: {
    name: 'positionType',
    type: "'wicketKeeper' | 'bowler' | 'batsman' | 'allRounder' | 'captain' | 'viceCaptain'",
    description: 'The position type of the player',
    required: false,
  },
  isCaiptain: {
    name: 'isCaiptain',
    type: 'boolean',
    description: 'Indicates if the player is the captain',
    required: false,
  },
  isViceCaiptain: {
    name: 'isViceCaiptain',
    type: 'boolean',
    description: 'Indicates if the player is the vice-captain',
    required: false,
  },
};

// Sample data
export const categories: Category[] = [
  {
    id: 'batting',
    name: 'Batting',
    children: [
      { id: 'RUN', name: 'RUN' },
      { id: '4sB', name: 'Four Bonus' },
      { id: '6sB', name: 'Six Bonus' },
    ],
  },
  {
    id: 'rebounding',
    name: 'REBOUNDING',
    children: [
      { id: 'defensive-rebound', name: 'Defensive Rebound' },
      { id: 'offensive-rebound', name: 'Offensive Rebound' },
    ],
  },
  {
    id: 'defencing',
    name: 'DEFENCING',
    children: [
      { id: 'steal', name: 'Steal' },
      { id: 'block', name: 'Block' },
      { id: 'defensive-stop', name: 'Defensive Stop' },
    ],
  },
  {
    id: 'general-play',
    name: 'GENERAL PLAY',
    children: [
      { id: 'double-double', name: 'Double-Double' },
      { id: 'triple-double', name: 'Triple-Double' },
      { id: 'turnover', name: 'Turnover' },
      { id: 'field-goal-made', name: 'Field Goal Made' },
      { id: 'field-goal-missed', name: 'Field Goal Missed' },
    ],
  },
  {
    id: 'shooting-efficiency',
    name: 'SHOOTING EFFICIENCY BONUSES',
    children: [
      { id: 'higher-field-goal', name: 'Higher Field Goal' },
      { id: 'higher-free-throw', name: 'Higher Free Throw' },
    ],
  },
  {
    id: 'fouling-penalties',
    name: 'FOULING PENALTIES',
    children: [
      { id: 'personal-foul', name: 'Personal Foul' },
      { id: 'technical-foul', name: 'Technical Foul' },
      { id: 'ejection', name: 'Ejection' },
    ],
  },
  {
    id: 'overtime-bonuses',
    name: 'OVERTIME BONUSES',
    children: [{ id: 'clutch-performance', name: 'Clutch Performance' }],
  },
];
