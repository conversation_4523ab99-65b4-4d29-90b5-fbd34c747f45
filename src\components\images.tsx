import React from 'react';

export const NextSlideArrow = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      {...props}
    >
      <g
        id="Group_29194"
        data-name="Group 29194"
        transform="translate(14309 -11825.479) rotate(-90)"
      >
        <rect
          id="Rectangle_2837"
          data-name="Rectangle 2837"
          width="16"
          height="16"
          transform="translate(-11841.479 -14309)"
          fill="none"
        />
        <path
          id="Path_12085"
          data-name="Path 12085"
          d="M4,9l4.435,4.435L12.869,9"
          transform="translate(-11841.848 -14312.5)"
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
        />
      </g>
    </svg>
  );
};
export const PrvSlideArrow = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      {...props}
    >
      <g
        id="Group_29195"
        data-name="Group 29195"
        transform="translate(-14293 11841.479) rotate(90)"
      >
        <rect
          id="Rectangle_2837"
          data-name="Rectangle 2837"
          width="16"
          height="16"
          transform="translate(-11841.479 -14309)"
          fill="none"
        />
        <path
          id="Path_12085"
          data-name="Path 12085"
          d="M4,9l4.435,4.435L12.869,9"
          transform="translate(-11841.848 -14312.5)"
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
        />
      </g>
    </svg>
  );
};
export const FillDropDown = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="11.176"
      height="6.586"
      viewBox="0 0 11.176 6.586"
      {...props}
    >
      <path
        id="Path_20868"
        data-name="Path 20868"
        d="M4.881.293a1,1,0,0,1,1.414,0l4.586,4.586a1,1,0,0,1-.707,1.707H1A1,1,0,0,1,.295,4.879Z"
        transform="translate(11.176 6.586) rotate(180)"
        fill="#363636"
      />
    </svg>
  );
};
export const BattingIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      {...props}
    >
      <g
        id="Group_43978"
        data-name="Group 43978"
        transform="translate(16265 -5115)"
      >
        <rect
          id="Rectangle_18744"
          data-name="Rectangle 18744"
          width="16"
          height="16"
          transform="translate(-16265 5115)"
          fill="none"
        />
        <g
          id="Group_40521"
          data-name="Group 40521"
          transform="translate(-16264.555 5115.444)"
        >
          <g
            id="Group_40522"
            data-name="Group 40522"
            transform="translate(0 0)"
          >
            <path
              id="Path_31727"
              data-name="Path 31727"
              d="M10.964,3.858l1.121.233.934.934.233,1.121c.02.1.07.184,0,.254l-8.67,8.67c-.07.07-.164.039-.254,0C2.8,14.4,2.711,14.308,2.04,12.783c-.04-.091-.07-.184,0-.255l8.67-8.669c.07-.07.157-.02.254,0m2.274.945L17.052.99a.2.2,0,0,0,0-.28L16.4.058a.2.2,0,0,0-.28,0L12.3,3.87Z"
              transform="translate(-1.998 0.001)"
              fill="#fafafa"
              fillRule="evenodd"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};
export const BowlingIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      {...props}
    >
      <g
        id="Group_47312"
        data-name="Group 47312"
        transform="translate(16265 -5115)"
      >
        <rect
          id="Rectangle_18744"
          data-name="Rectangle 18744"
          width="16"
          height="16"
          transform="translate(-16265 5115)"
          fill="none"
        />
        <g
          id="Group_40521"
          data-name="Group 40521"
          transform="translate(-16262.405 5117.594)"
        >
          <g
            id="Group_40522"
            data-name="Group 40522"
            transform="translate(0 0)"
          >
            <path
              id="Path_31727"
              data-name="Path 31727"
              d="M1.421,16.489a4.847,4.847,0,1,0,0-6.856,4.842,4.842,0,0,0,0,6.856m.556-.59a4.035,4.035,0,1,1,5.707-5.707c.071.071.142.148.209.226a4.035,4.035,0,0,0-5.69,5.69c-.078-.067-.155-.138-.226-.209"
              transform="translate(-0.001 -8.215)"
              fill="#fff"
              fillRule="evenodd"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};
export const WinArrowIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="6.586"
      height="11.176"
      viewBox="0 0 6.586 11.176"
      {...props}
    >
      <path
        id="Path_12104"
        data-name="Path 12104"
        d="M6.293.707a1,1,0,0,1,1.414,0l4.586,4.586A1,1,0,0,1,11.586,7H2.414a1,1,0,0,1-.707-1.707Z"
        transform="translate(-0.414 12.588) rotate(-90)"
        fill="#fc4714"
      />
    </svg>
  );
};
export const MobileMenuIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="28"
      height="28"
      viewBox="0 0 28 28"
      {...props}
    >
      <g
        id="Group_80620"
        data-name="Group 80620"
        transform="translate(-12 -61)"
      >
        <g
          id="Mobile_responsive_-_Menu_Tab"
          data-name="Mobile responsive - Menu Tab"
          transform="translate(12 61)"
        >
          <rect
            id="Rectangle_6967"
            data-name="Rectangle 6967"
            width="28"
            height="28"
            fill="none"
          />
          <path
            id="Path_12123"
            data-name="Path 12123"
            d="M5,6H19M5,12H19M5,18H19"
            transform="translate(2.5 3)"
            fill="none"
            stroke="#003764"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
        </g>
      </g>
    </svg>
  );
};
export const CheckedRadioIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      id="Radio_Button"
      data-name="Radio Button"
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      {...props}
    >
      <defs>
        <filter
          id="Ellipse_1238"
          x="1.5"
          y="2.5"
          width="13"
          height="13"
          filterUnits="userSpaceOnUse"
        >
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="0.5" result="blur" />
          <feFlood floodOpacity="0.161" />
          <feComposite operator="in" in2="blur" />
          <feComposite in="SourceGraphic" />
        </filter>
      </defs>
      <g
        id="Rectangle_96"
        data-name="Rectangle 96"
        fill="none"
        stroke="#d4d6d8"
        strokeWidth="1"
      >
        <rect width="16" height="16" rx="8" stroke="none" />
        <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" fill="none" />
      </g>
      <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#Ellipse_1238)">
        <circle
          id="Ellipse_1238-2"
          data-name="Ellipse 1238"
          cx="5"
          cy="5"
          r="5"
          transform="translate(3 3)"
          fill="#0095ff"
        />
      </g>
    </svg>
  );
};
export const InfoIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="28"
      height="28"
      viewBox="0 0 28 28"
      {...props}
    >
      <g
        id="Group_29201"
        data-name="Group 29201"
        transform="translate(3343 -4217)"
      >
        <rect
          id="Rectangle_6963"
          data-name="Rectangle 6963"
          width="28"
          height="28"
          transform="translate(-3343 4217)"
          fill="none"
        />
        <g
          id="Group_29189"
          data-name="Group 29189"
          transform="translate(-3341 4219)"
        >
          <circle
            id="Ellipse_1207"
            data-name="Ellipse 1207"
            cx="10"
            cy="10"
            r="10"
            transform="translate(2 2)"
            fill="none"
            stroke="#000"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            id="Path_13729"
            data-name="Path 13729"
            d="M12,7h.01"
            fill="none"
            stroke="#000"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            id="Path_13730"
            data-name="Path 13730"
            d="M10,11h2v5"
            fill="none"
            stroke="#000"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            id="Path_13731"
            data-name="Path 13731"
            d="M10,16h4"
            fill="none"
            stroke="#000"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
        </g>
      </g>
    </svg>
  );
};
export const VerificationCrossIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      {...props}
    >
      <g
        id="Group_29166"
        data-name="Group 29166"
        transform="translate(11897 14339)"
      >
        <rect
          id="Rectangle_2932"
          data-name="Rectangle 2932"
          width="20"
          height="20"
          transform="translate(-11897 -14339)"
          fill="none"
        />
        <path
          id="Path_12107"
          data-name="Path 12107"
          d="M5.714,11.429V5.714m0,0V0m0,5.714h5.714m-5.714,0H0"
          transform="translate(-11895.215 -14329.133) rotate(-45)"
          fill="none"
          stroke="#000"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        />
      </g>
    </svg>
  );
};
export const VerificationCheckedIcon = (
  props: React.SVGProps<SVGSVGElement>,
) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      {...props}
    >
      <g
        id="Group_114438"
        data-name="Group 114438"
        transform="translate(4719 -4222)"
      >
        <rect
          id="Rectangle_6968"
          data-name="Rectangle 6968"
          width="20"
          height="20"
          transform="translate(-4719 4222)"
          fill="none"
        />
        <path
          id="Path_12120"
          data-name="Path 12120"
          d="M4,10.286l4.286,4.286L15.429,6"
          transform="translate(-4719.071 4222.071)"
          fill="none"
          stroke="#1c9a6c"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        />
      </g>
    </svg>
  );
};
export const VerificationDeleteIcon = (
  props: React.SVGProps<SVGSVGElement>,
) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="19"
      height="19"
      viewBox="0 0 19 19"
      {...props}
    >
      <g
        id="Group_114434"
        data-name="Group 114434"
        transform="translate(11544 14319)"
      >
        <rect
          id="Rectangle_2967"
          data-name="Rectangle 2967"
          width="19"
          height="19"
          rx="9.5"
          transform="translate(-11544 -14319)"
          fill="#e7e9ec"
        />
        <g
          id="Group_24052"
          data-name="Group 24052"
          transform="translate(-11540.81 -14315.81)"
        >
          <path
            id="Path_12113"
            data-name="Path 12113"
            d="M4,6H14.1l-1,8.972A1.262,1.262,0,0,1,11.844,16.1H6.251A1.262,1.262,0,0,1,5,14.972Z"
            transform="translate(-2.738 -3.476)"
            fill="none"
            stroke="#4455c7"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            id="Path_12114"
            data-name="Path 12114"
            d="M6.849,2.724A1.262,1.262,0,0,1,7.99,2h3.591a1.262,1.262,0,0,1,1.142.724l.848,1.8H6Z"
            transform="translate(-3.476 -2)"
            fill="none"
            stroke="#4455c7"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            id="Path_12115"
            data-name="Path 12115"
            d="M2,6H14.619"
            transform="translate(-2 -3.476)"
            fill="none"
            stroke="#4455c7"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            id="Path_12116"
            data-name="Path 12116"
            d="M10,11v3.155"
            transform="translate(-4.952 -5.321)"
            fill="none"
            stroke="#4455c7"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            id="Path_12117"
            data-name="Path 12117"
            d="M14,11v3.155"
            transform="translate(-6.428 -5.321)"
            fill="none"
            stroke="#4455c7"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </g>
    </svg>
  );
};
export const VerifyDocIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="13.851"
      height="13.851"
      viewBox="0 0 13.851 13.851"
      {...props}
    >
      <path
        id="Path_22868"
        data-name="Path 22868"
        d="M7.926,1a6.926,6.926,0,1,0,6.926,6.926A6.926,6.926,0,0,0,7.926,1Zm3,5.754a.63.63,0,1,0-.967-.806L7.253,9.2l-1.4-1.4a.63.63,0,1,0-.89.89l1.889,1.889a.63.63,0,0,0,.929-.042Z"
        transform="translate(-1 -1)"
        fill="#1c9a6c"
        fillRule="evenodd"
      />
    </svg>
  );
};
export const VerifyInProgressIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      id="Group_114456"
      data-name="Group 114456"
      xmlns="http://www.w3.org/2000/svg"
      width="17"
      height="17"
      viewBox="0 0 17 17"
      {...props}
    >
      <rect
        id="Rectangle_6978"
        data-name="Rectangle 6978"
        width="17"
        height="17"
        fill="none"
      />
      <path
        id="Path_183392"
        data-name="Path 183392"
        d="M6.644,0a6.644,6.644,0,1,0,6.644,6.644A6.644,6.644,0,0,0,6.644,0m0,12.338a5.694,5.694,0,1,1,0-11.389V6.644l4.025,4.025a5.677,5.677,0,0,1-4.025,1.67"
        transform="translate(1.857 1.856)"
        fill="#fff"
      />
    </svg>
  );
};
export const VerifiedIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="17"
      height="17"
      viewBox="0 0 17 17"
      {...props}
    >
      <g
        id="Group_113990"
        data-name="Group 113990"
        transform="translate(4719 -4222)"
      >
        <g
          id="Rectangle_6968"
          data-name="Rectangle 6968"
          transform="translate(-4719 4222)"
          fill="none"
          stroke="#fff"
          strokeWidth="1.5"
        >
          <rect width="17" height="17" rx="8.5" stroke="none" />
          <rect
            x="0.75"
            y="0.75"
            width="15.5"
            height="15.5"
            rx="7.75"
            fill="none"
          />
        </g>
        <path
          id="Path_12120"
          data-name="Path 12120"
          d="M4,9.062l3.062,3.062L12.166,6"
          transform="translate(-4718.583 4221.322)"
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
        />
      </g>
    </svg>
  );
};
export const PlayerCaptainViewIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="46"
      height="26"
      viewBox="0 0 46 26"
      {...props}
    >
      <defs>
        <linearGradient
          id="linear-gradient"
          x1="0.5"
          x2="0.5"
          y2="1"
          gradientUnits="objectBoundingBox"
        >
          <stop offset="0" stopColor="#003764" stopOpacity="0.902" />
          <stop offset="1" stopColor="#090b0d" />
        </linearGradient>
      </defs>
      <g
        id="Group_104087"
        data-name="Group 104087"
        transform="translate(-139.04 26.215) rotate(-180)"
      >
        <path
          id="Rectangle_2837"
          data-name="Rectangle 2837"
          d="M0,0H46a0,0,0,0,1,0,0V6A20,20,0,0,1,26,26H20A20,20,0,0,1,0,6V0A0,0,0,0,1,0,0Z"
          transform="translate(-185.04 0.215)"
          opacity="0.74"
          fill="url(#linear-gradient)"
        />
        <path
          id="Path_12085"
          data-name="Path 12085"
          d="M0,0,6.682,6.832,13.363,0"
          transform="translate(-168.904 8.883)"
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        />
      </g>
    </svg>
  );
};
export const ModalHeaderDownIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 40 40"
      {...props}
    >
      <g
        id="Group_104087"
        data-name="Group 104087"
        transform="translate(-145.224 39.827) rotate(-180)"
      >
        <rect
          id="Rectangle_2837"
          data-name="Rectangle 2837"
          width="40"
          height="40"
          transform="translate(-185.224 -0.173)"
          fill="none"
          opacity="0.74"
        />
        <path
          id="Path_12085"
          data-name="Path 12085"
          d="M0,0,10.072,10.3,20.145,0"
          transform="translate(-155.411 25.089) rotate(180)"
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="3"
        />
      </g>
    </svg>
  );
};
