'use client';
// PlayerActions component
const PlayerActions: React.FC<{
  showSubstituteButton: boolean;
  showRemoveButton: boolean;
  dreamTeamId: string | null;
  addMore: boolean;
  onSubstitute: () => void;
  onSetCaptain: () => void;
  onSetViceCaptain: () => void;
  onRemove: () => void;
}> = ({
  showSubstituteButton,
  showRemoveButton,
  addMore,
  onSubstitute,
  onSetCaptain,
  onSetViceCaptain,
  onRemove,
}) => (
  <div
    className="absolute -bottom-6 items-center justify-center -left-[40px] group-hover:flex z-10"
    style={{
      width: 'max-content',
    }}
  >
    <div className="flex text-white text-base font-normal gap-x-1">
      {showSubstituteButton && (
        <button
          className="bg-secondary-100 px-2 rounded-md cursor-pointer"
          style={{ width: 'max-content' }}
          onClick={onSubstitute}
        >
          Substitute
        </button>
      )}
      <button
        className="bg-secondary-100 px-2 rounded-md cursor-pointer disabled:cursor-not-allowed"
        style={{ width: 'max-content' }}
        disabled={!addMore}
        onClick={onSetCaptain}
      >
        Captain
      </button>
      <button
        className="bg-secondary-100 max-w-fit px-2 rounded-md cursor-pointer disabled:cursor-not-allowed"
        style={{ width: 'max-content' }}
        disabled={!addMore}
        onClick={onSetViceCaptain}
      >
        Vice-captain
      </button>
      {showRemoveButton && (
        <button
          className="bg-negative-200 max-w-fit px-2 rounded-md cursor-pointer disabled:cursor-not-allowed disabled:bg-[#C9C9C9]"
          style={{ width: 'max-content' }}
          onClick={onRemove}
        >
          Remove
        </button>
      )}
    </div>
  </div>
);

export default PlayerActions;
