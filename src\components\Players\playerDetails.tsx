'use client';

import './playerDetails.scss';

import {
  <PERSON><PERSON>,
  <PERSON>overContent,
  PopoverHandler,
} from '@material-tailwind/react';
import { useQuery } from '@tanstack/react-query';
import { Search } from 'lucide-react';
import Image from 'next/image';
import React, { useCallback, useEffect, useState } from 'react';
import Select, { components } from 'react-select';

import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { Config } from '@/helpers/context/config';
import useScreen from '@/hooks/useScreen';
import { quyerKeys } from '@/lib/queryKeys';

import type {
  FetchLeagueDetailsResponse,
  FetchTournamentSeasonDetailsResponse,
} from '../../../types';
import { ModalHeaderDownIcon, PlayerCaptainViewIcon } from '../images';
import { Drawer, DrawerContent } from '../UI/drawer';
import SearchAutocomplete from '../UI/SearchAutocomplete';
import Captain<PERSON><PERSON><PERSON>aptain from './captainViceCaptain';
import PlayerDetailsTable from './playerDetailsTable';
import PlayerFilterSection from './playerFilterSection';

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const sortOption = [
  {
    value: 'Player (alphabetically A-Z)',
    label: 'Player (alphabetically A-Z)',
  },
  {
    value: 'Player (alphabetically Z-A)',
    label: 'Player (alphabetically Z-A)',
  },
  { value: 'Team (alphabetically A-Z)', label: 'Team (alphabetically A-Z)' },
  { value: 'Team (alphabetically Z-A)', label: 'Team (alphabetically Z-A)' },
];

type Suggestion = {
  id: string;
  name: string;
};

type LeagueMenuData =
  | {
      value: any;
      label: any;
    }[]
  | undefined;

type LeagueSeasonMenuData =
  | {
      value: any;
      label: any;
    }[]
  | undefined;

const fetchLeagueDetails = async (): Promise<FetchLeagueDetailsResponse> => {
  const res = await axiosInstance.get<FetchLeagueDetailsResponse>(
    Config.baseURL + `allsport/tournament?SportId=4`,
  );
  return res?.data;
};

const fetchLeagueSeasonDetails = async (
  tournament_id: number | null,
): Promise<FetchTournamentSeasonDetailsResponse> => {
  const res = await axiosInstance.get<FetchTournamentSeasonDetailsResponse>(
    Config.baseURL + `allsport/season?SportId=4?&tournamentId=${tournament_id}`,
  );
  return res?.data;
};

type FiltersState = {
  teams: Record<string, boolean>; // Using a Record for dynamic teams
  position: Record<string, boolean>;
};

const teamsList = [
  'Adelaide Strikers',
  'Melbourne Stars',
  'Brisbane Heat',
  'Perth Scrochers',
  'Hobart Hurricanes',
  'Sydney Sixers',
  'Melbourne Renegades',
  'Sydney Thunder',
]; // Dynamic team list

const positionsList = [
  'Wicket-keepers',
  'Batsmen',
  'Bowlers',
  'Dual position players',
];

const PlayerDetails = () => {
  const { width } = useScreen();
  const [selectedLeague, setSelectedLeague] = useState(null);
  const [selectedSeason, setSelectedSeason] = useState(null);
  const [selectedSort, setSelectedSort] = useState<string | null>(null);
  const [searchModal, setSearchModal] = useState<boolean>(false);
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [playerShowFilter, setPlayerShowFilter] = useState<boolean>(false);
  const [isMobileFilter, setIsMobileFilter] = useState(false);
  const [isMobileCaptainView, setIsMobileCaptainView] = useState(false);
  const [leagueOption, setLeagueOption] = useState<LeagueMenuData | undefined>(
    undefined,
  );
  const [seasonOption, setSeasonOption] = useState<
    LeagueSeasonMenuData | undefined
  >(undefined);
  const [mockSuggestions, setMockSuggestions] = useState<Suggestion[]>([]);
  const [filters, setFilters] = useState<FiltersState>({
    teams: teamsList.reduce((acc, team) => ({ ...acc, [team]: false }), {}),
    position: positionsList.reduce(
      (acc, position) => ({ ...acc, [position]: false }),
      {},
    ),
  });

  const handleSearchModal = () => {
    setSearchModal(true);
  };

  const handleQueryChange = useCallback((newQuery: string) => {
    setQuery(newQuery);

    // If the user has typed something, filter the suggestions and show the results
    if (newQuery?.length > 0) {
      const filteredSuggestions = mockSuggestions?.filter((suggestion) =>
        suggestion?.name?.toLowerCase()?.includes(newQuery?.toLowerCase()),
      );
      setSuggestions(filteredSuggestions);
    } else {
      // If the user has cleared the input, hide the suggestions
      setSuggestions([]);
    }
  }, []);

  const handleSuggestionClick = useCallback((suggestion: Suggestion) => {
    setQuery(suggestion?.name);
    setSuggestions([]);
    setSearchModal(false);
    alert(`id : ${suggestion?.id} name : ${suggestion?.name}`);
    // Implement any additional logic here, e.g., triggering a search
  }, []);

  // Resets the search query and suggestions when the user clicks the clear button
  const handleClearSearch = useCallback(() => {
    setQuery('');
    setSuggestions([]);
    setSearchModal(false);
  }, []);

  const { data: leagueList, isLoading } = useQuery({
    queryFn: () => fetchLeagueDetails(),
    queryKey: [quyerKeys.getLeagueList],
  });

  useEffect(() => {
    const leagueData = leagueList?.result?.rows?.map((item: any) => {
      return {
        value: item?.id,
        label: item?.name,
      };
    });
    setLeagueOption(leagueData);
    setSelectedLeague(leagueData?.[0]?.value);
  }, [leagueList?.result?.rows?.length]);

  const { data: leagueSeasonList, isLoading: isSeasonLoading } = useQuery({
    queryFn: () => fetchLeagueSeasonDetails(selectedLeague),
    queryKey: [quyerKeys.getLeagueSeason, selectedLeague],
    enabled: !!selectedLeague,
  });

  useEffect(() => {
    const leagueSeasonData = leagueSeasonList?.result?.rows?.map(
      (item: any) => {
        return {
          value: item?.id,
          label: item?.name,
        };
      },
    );
    setSeasonOption(leagueSeasonData);
    setSelectedSeason(
      leagueSeasonData?.length! > 0 ? leagueSeasonData?.[0]?.value : null,
    );
  }, [selectedLeague, leagueSeasonList?.result?.rows?.length]);

  return (
    <>
      <div className="player-details-section max-799:mx-3">
        {!searchModal ? (
          <div className="flex items-center gap-[9px] flex-wrap relative">
            <button
              className="bg-white border border-black-300 rounded-lg p-2 cursor-pointer max-799:hidden"
              onClick={() => handleSearchModal()}
            >
              <Search size={20} className="text-gray-400" />
            </button>
            <div className="min-w-[345px] max-w-[345px] ">
              <Select
                className="React league-select"
                value={leagueOption?.find((item: any) => {
                  return item?.value === selectedLeague;
                })}
                onChange={(e: any) => {
                  setSelectedLeague(e?.value);
                  setSelectedSeason(null);
                  setSeasonOption([]);
                }}
                options={leagueOption}
                classNamePrefix="select"
                placeholder="league"
                isSearchable={true}
                isLoading={isLoading}
                components={{ DropdownIndicator }}
              />
            </div>
            <div className="min-w-[200px] max-w-[200px] ">
              <Select
                className="React season-select"
                value={seasonOption?.find((item: any) => {
                  return item?.value === selectedSeason;
                })}
                onChange={(e: any) => setSelectedSeason(e?.value)}
                options={seasonOption}
                classNamePrefix="select"
                placeholder="season stats"
                isLoading={isSeasonLoading}
                isSearchable={true}
                components={{ DropdownIndicator }}
              />
            </div>
            <div className="min-w-[85px] max-w-[85px] ">
              <Select
                className="React sort-select"
                value={sortOption?.find((item: any) => {
                  return item?.value === selectedSort;
                })}
                onChange={(e: any) => setSelectedSort(e?.value || null)}
                options={sortOption}
                classNamePrefix="select"
                placeholder="Sort"
                isSearchable={false}
                components={{ DropdownIndicator }}
              />
            </div>
            {width >= 800 ? (
              <div className="cursor-pointer max-799:hidden">
                <Popover
                  placement="bottom-end"
                  open={playerShowFilter}
                  handler={setPlayerShowFilter}
                >
                  <PopoverHandler>
                    <Image
                      src={'/fantasy/images/icons/filterPlayers.svg'}
                      alt="filter"
                      width={38}
                      height={38}
                      unoptimized={true}
                    />
                  </PopoverHandler>
                  {/* @ts-expect-error */}
                  <PopoverContent className="z-50 w-[584px] bg-black-400 p-0">
                    <PlayerFilterSection
                      setFilters={setFilters}
                      filters={filters}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            ) : (
              <div className="cursor-pointer hidden max-799:block">
                <Image
                  onClick={() => setIsMobileFilter(true)}
                  src={'/fantasy/images/icons/filterPlayers.svg'}
                  alt="filter"
                  width={38}
                  height={38}
                  unoptimized={true}
                />

                {/* Player Mobile view filter */}
                <Drawer open={isMobileFilter} onOpenChange={setIsMobileFilter}>
                  <DrawerContent className="overflow-hidden bg-gradient-to-b from-[#E5EAEF] to-[#93A0AD]">
                    <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 flex items-center justify-between">
                      <div>
                        <span className="text-[22.4px] leading-[24px] font-veneerCleanSoft text-white font-normal">
                          Filter
                        </span>
                      </div>
                      <button
                        className="flex items-center gap-1.5"
                        onClick={() => setIsMobileFilter(false)}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          className="h-5 w-5"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                    <div className="mt-[44px] pt-[27px] px-3 max-h-[calc(100vh-238px)] overflow-y-auto">
                      <PlayerFilterSection
                        setFilters={setFilters}
                        filters={filters}
                      />
                    </div>
                  </DrawerContent>
                </Drawer>
              </div>
            )}
          </div>
        ) : (
          <div className="w-[695px] max-799:hidden">
            <SearchAutocomplete
              query={query}
              suggestions={suggestions}
              placeholder="Search Player..."
              onQueryChange={handleQueryChange}
              onSuggestionClick={handleSuggestionClick}
              onClearSearch={handleClearSearch}
            />
            {/* <div className="relative w-full">
              <Search className="absolute top-1/2 left-3 -translate-y-1/2 h-5 w-5 text-gray-500 z-[1] stroke-black-600" />
              <Input
                type="text"
                placeholder="Search"
                className="pl-10" // Add padding to avoid overlap with the icon
                color="indigo"
                labelProps={{
                  className: '',
                }}
              />
            </div> */}
          </div>
        )}
        <div></div>
      </div>
      <div className="mt-[9px] flex items-start gap-[21px]">
        <div className="w-[calc(100%-510px)] max-1024:w-3/5 max-799:w-full">
          <PlayerDetailsTable
            selectedSort={selectedSort}
            selectedLeague={selectedLeague}
            selectedSeason={selectedSeason}
            setMockSuggestions={setMockSuggestions}
          />
        </div>
        <div className="w-[510px] max-1024:w-2/5 max-799:hidden">
          <CaptainViceCaptain
            selectedLeague={selectedLeague}
            selectedSeason={selectedSeason}
          />
        </div>
      </div>
      <div className="fixed hidden max-799:flex justify-center items-center bottom-0 right-0 left-0 z-10 mt-5">
        <PlayerCaptainViewIcon onClick={() => setIsMobileCaptainView(true)} />
      </div>

      {/* Captain Mobile view filter */}
      {width <= 800 && (
        <Drawer
          open={isMobileCaptainView}
          onOpenChange={setIsMobileCaptainView}
        >
          <DrawerContent className="overflow-hidden bg-gradient-to-b from-[#E5EAEF] to-[#93A0AD]">
            <div className="absolute bg-primary-200 p-2 max-799:p-0 font-bold text-xl text-white w-full z-50 flex items-center justify-center h-[47px]">
              <button
                className="flex items-center gap-1.5"
                onClick={() => setIsMobileCaptainView(false)}
              >
                <ModalHeaderDownIcon />
              </button>
            </div>
            <div className="mt-[44px] overflow-y-auto">
              <CaptainViceCaptain
                selectedLeague={selectedLeague}
                selectedSeason={selectedSeason}
              />
            </div>
          </DrawerContent>
        </Drawer>
      )}
    </>
  );
};

export default PlayerDetails;
