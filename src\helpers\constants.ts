export const SOCCER_PLAYER_STATS_LEGEND = [
  // General
  {
    column: 'General',
    label: 'G<PERSON>',
    text: 'Goals',
  },
  {
    column: 'General',
    label: 'A',
    text: 'Assists',
  },
  {
    column: 'General',
    label: 'APR',
    text: 'Appearances',
  },
  {
    column: 'General',
    label: 'MP',
    text: 'Minutes Played',
  },
  {
    column: 'General',
    label: 'YC',
    text: 'Yellow Cards',
  },
  {
    column: 'General',
    label: 'RC',
    text: 'Red Cards',
  },

  // Attack
  {
    column: 'Attack',
    label: 'S',
    text: 'Shots',
  },
  {
    column: 'Attack',
    label: 'ST',
    text: 'Shots on Target',
  },
  {
    column: 'Attack',
    label: 'HW',
    text: 'Hit Woodwork',
  },
  {
    column: 'Attack',
    label: 'OFF',
    text: 'Offsides',
  },
  {
    column: 'Attack',
    label: 'T',
    text: 'Touches',
  },
  {
    column: 'Attack',
    label: 'P',
    text: 'Passes',
  },
  {
    column: 'Attack',
    label: 'C',
    text: 'Cross<PERSON>',
  },

  // Defence
  {
    column: 'Defence',
    label: 'INT',
    text: 'Interceptions',
  },
  {
    column: 'Defence',
    label: 'B',
    text: 'Blocks',
  },
  {
    column: 'Defence',
    label: 'TAC',
    text: 'Tackles',
  },
  {
    column: 'Defence',
    label: 'CLR',
    text: 'Clearances',
  },
  {
    column: 'Defence',
    label: 'OG',
    text: 'Own Goals',
  },
  {
    column: 'Defence',
    label: 'ERR',
    text: 'Errors Leading to Goal',
  },
  {
    column: 'Defence',
    label: 'PC',
    text: 'Penalties Conceded',
  },

  // Goalkeeper
  {
    column: 'Goalkeeper',
    label: 'CS',
    text: 'Clean Sheets',
  },
  {
    column: 'Goalkeeper',
    label: 'GC',
    text: 'Goals Conceded',
  },
  {
    column: 'Goalkeeper',
    label: 'SAV',
    text: 'Saves',
  },
  {
    column: 'Goalkeeper',
    label: 'PS',
    text: 'Penalties Saved',
  },
  {
    column: 'Goalkeeper',
    label: 'GK',
    text: 'Goal Kicks',
  },
];

export const SOCCER_SMARTPLAY_STATS_LEGEND = [
  // Scoring
  {
    column: 'Scoring',
    label: 'GSF',
    text: 'Goal Scored (Forward)', 
  },
  {
    column: 'Scoring',
    label: 'GSM',
    text: 'Goal Scored (Midfielder)',
  },
  {
    column: 'Scoring',
    label: 'GSD', 
    text: 'Goal Scored (Defender)',
  },
  {
    column: 'Scoring',
    label: 'GSG',
    text: 'Goal Scored (Goalkeeper)',
  },
  {
    column: 'Scoring',
    label: 'A',
    text: 'Assist',
  },
  {
    column: 'Scoring',
    label: 'ST',
    text: 'Shot on Target',
  },

  // Bonus
  {
    column: 'Bonus',
    label: 'HT',
    text: 'Hat Trick',
  },
  {
    column: 'Bonus',
    label: 'MG',
    text: 'Match-Winning Goal',
  },
  {
    column: 'Bonus',
    label: '10TAC',
    text: '10+ Tackles in a game',
  },
  {
    column: 'Bonus',
    label: '5SAV',
    text: '5+ Saves in a game',
  },

  // Defensive
  {
    column: 'Defensive',
    label: 'CSD',
    text: 'Clean Sheet (Defender)',
  },
  {
    column: 'Defensive',
    label: 'CSG',
    text: 'Clean Sheet (Goalkeeper)',
  },
  {
    column: 'Defensive',
    label: 'TAC',
    text: 'Tackle Won',
  },
  {
    column: 'Defensive',
    label: 'INT',
    text: 'Interception',
  },
  {
    column: 'Defensive',
    label: 'BS',
    text: 'Blocked Shot',
  },
  {
    column: 'Defensive',
    label: 'SAV',
    text: 'Save',
  },
  {
    column: 'Defensive',
    label: 'PS',
    text: 'Penalty Save',
  },

  // General Play
  {
    column: 'General Play',
    label: 'PCR',
    text: 'Pass Completion Rate',
  },
  {
    column: 'General Play',
    label: 'KP',
    text: 'Key Pass',
  },
  {
    column: 'General Play',
    label: 'DC',
    text: 'Dribble Completed',
  },
  {
    column: 'General Play',
    label: 'MP10',
    text: 'Minutes Played (per 10 minutes played)',
  },
  {
    column: 'General Play',
    label: 'MP30',
    text: 'Minutes Played (if subbed off before the 30th minute (without injury))',
  },

  // Penalty
  {
    column: 'Penalty',
    label: 'OG',
    text: 'Own Goal',
  },
  {
    column: 'Penalty',
    label: 'YC',
    text: 'Yellow Cards',
  },
  {
    column: 'Penalty',
    label: 'RC',
    text: 'Red Card',
  },
  {
    column: 'Penalty',
    label: 'PC',
    text: 'Penalty Conceded',
  },
  {
    column: 'Penalty',
    label: 'MP',
    text: 'Missed Penalty',
  },
];
