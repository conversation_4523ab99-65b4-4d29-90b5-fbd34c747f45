import React from 'react';

import { Card, CardContent } from '@/components/UI/card';

import type { CouponData } from '../../../../../../types';

export interface ResponseCouponDetailsData {
  data: {
    status: boolean;
    data: CouponData;
  };
}

interface CouponCodeDetailsProps {
  response: ResponseCouponDetailsData;
}

const DiscountDetails: React.FC<CouponCodeDetailsProps> = ({ response }) => {
  if (!response.data.status) {
    return (
      <Card className="w-full bg-gray-100">
        <CardContent className="p-4">
          <p className="text-red-500">
            Failed to apply the coupon code. Please try again.
          </p>
        </CardContent>
      </Card>
    );
  }

  const couponData = response.data.data;
  const discountText = couponData.isPercentage
    ? `${couponData.discountAmount}% off`
    : `$${couponData.discountAmount} off`;

  return (
    <Card className="w-full bg-gray-100">
      <CardContent className="p-4">
        {/* Discount Header */}
        <div className="flex justify-between items-center mb-4">
          <div>
            <div className="flex items-center gap-2">
              <p className="text-orange-500 font-semibold">{couponData.name}</p>
            </div>
            <div className="text-sm text-gray-600">
              <p>Valid: {new Date(couponData.expireAt).toLocaleDateString()}</p>
            </div>
            <div className="text-sm text-gray-600 flex space-x-1">
              <span>{discountText}</span>
            </div>
          </div>
          {/* 
          <div className="bg-white px-3 py-2 rounded shadow-sm">
            <p className="text-gray-800 font-medium">{couponData.code}</p>
          </div> */}
          <div className="coupon-number">
            <div className="coupon-card border-element">
              <span className="circle-bottom"></span>
              <span className="text-black-800"> {couponData.code}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DiscountDetails;
