'use client';
import type { ColumnDef } from '@tanstack/react-table';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { useCompetition } from '@/helpers/context/competitionContext';
import { useTeam } from '@/helpers/context/createTeamContext';
import useScreen from '@/hooks/useScreen';
import { getAllPlayers } from '@/lib/utils';

import type { Player } from '../../../types/competitions';
import { PlayerCaptainViewIcon } from '../images';
import { Button } from '../UI/button';
import DataTable from '../UI/DataTabel';

import { upcomingPlayerListColumn } from '../UI/DataTabel/upcomingPlayerlistColumns';
import CricketFantasyUI from './CricketFantasyUI';
import {
  completedCricketPlayerListColumn,
  liveCricketPlayerListColumn,
} from '../UI/DataTabel/columns/createTeamColumn';
import PlayerSelectionUI from './PlayerSelectionUI';
import SharePopup from '../PopUp/Share/SharePopup';
import ShareIcon from '../Icons/Share/ShareIcon';
import { Config } from '@/helpers/context/config';
import CompetitionDetailsHeader from './CompetitionDetailsHeader';
import DreamTeamLoader from '../Loading/DreamTeamLoader';

const PreviewTeam = () => {
  const {
    eventDetailsResponse,
    dreamTeamResponse,
    activeTeam,
    setActiveTeam,
    isDreamTeamResponseLoading,
    eventTimeStatus: { isLockout, isLive, isCompleted },
    playerByRole: playerListPlayersByRole,
  } = useCompetition();

  const {
    resetTeam,
    activeTab: plyaerRoleTab,
    setActiveTab: setPlayerRoleTab,
    setBudget,
    getDreamTeam: dispatchDreamTean,
    setShowPlayerTabel,
    openReserveModal,
    showPlayerTabel,
    state: { reserveState: { reservePlayers, reservePlayersLimit }, remainingBudget },
  } = useTeam();

  const searchParams = useSearchParams();
  const pathname = usePathname();

  const event_id = searchParams.get('event_id');
  const sport_id = searchParams.get('sport_id');
  const tournament_id = searchParams.get('tournament_id');
  const seasonId = searchParams.get('seasonId');
  const competition_id = searchParams.get('competition_id');
  const comp_Type = searchParams.get('comp_Type');
  const dreamTeamId = searchParams.get('dreamTeamId');

  useEffect(() => {
    if (dreamTeamResponse) {
      dispatchDreamTean(dreamTeamResponse);
    }

    return () => resetTeam();
  }, [dreamTeamResponse, dreamTeamId]);

  const [activeTab, setActiveTab] = useState<string | number>(1);
  const playersByRole = dreamTeamResponse && getAllPlayers(dreamTeamResponse);
  const [captain] = dreamTeamResponse?.result?.captain! || [];
  const [viceCaptain] = dreamTeamResponse?.result?.viceCaptain! || [];
  const flattenedPlayerByRole = Object.values(playersByRole || {})
    ?.map((player) => {
      if (player?.playerId === captain?.playerId) {
        return { ...player, positionType: 'captain' };
      }

      if (player?.playerId === viceCaptain?.playerId) {
        return { ...player, positionType: 'viceCaptain' };
      }

      return player;
    })
    ?.sort((a, b) => a.name.localeCompare(b.name));

  const allLiveScore = dreamTeamResponse?.result?.totalLivePoints;

  const router = useRouter();

  let playerColumn: ColumnDef<Player>[] = [];
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  switch (eventStatus) {
    case 'finished':
      playerColumn = completedCricketPlayerListColumn;
      break;
    case 'innings break':
    case 'inprogress':
    case 'drink':
      playerColumn = liveCricketPlayerListColumn;
      break;
    case 'upcoming':
      playerColumn = upcomingPlayerListColumn;
      break;
  }

  const { width } = useScreen();

  const query = {
    event_id,
    sport_id,
    tournament_id,
    seasonId,
    competition_id,
    dreamTeamId: `${activeTeam?.id}`,
    comp_Type,
  };

  useEffect(() => {
    if (activeTeam?.id && comp_Type === 'my') {
      // @ts-expect-error
      router.push(`${pathname}?${new URLSearchParams(query).toString()}`);
    }
  }, [activeTeam]);

  const dreamTeams = eventDetailsResponse?.result?.dreamTeams;

  // useEffect(() => {
  //   if (dreamTeams && !activeTeam?.id && dreamTeamId) {
  //     setActiveTeam(() => dreamTeams.find((team) => team.id === +dreamTeamId));
  //   }

  //   if (dreamTeams && !activeTeam?.id && !dreamTeamId) {
  //     setActiveTeam(() => dreamTeams[0]);
  //   }

  // }, [dreamTeamId, dreamTeams]);



  useEffect(() => {
    if (dreamTeams) {
      setActiveTeam(() => dreamTeams[0]);
    }
  }, [dreamTeams]);

  const [showSharePopup, setShowSharePopup] = useState(false);

  return (
    <div className="bg-white px-0">
      <div>
        {/* <ul className="flex justify-start items-center  w-full overflow-x-scroll no-scrollbar">
          {eventDetailsResponse?.result?.dreamTeams?.map((dreamTeam) => (
            <button
              className={`h-[41px] w-full  px-4 flex justify-center items-center text-center cursor-pointer whitespace-nowrap text-[#93A0AD] ${dreamTeam.id === activeTeam?.id
                ? 'bg-[#4455C7] !text-white border-b-2 border-orange-500'
                : 'bg-[#E7E9EC]'
                }`}
              key={dreamTeam?.id}
              onClick={() => {
                setActiveTeam(dreamTeam);
                router.push(
                  // @ts-expect-error
                  `${pathname}?${new URLSearchParams(query).toString()}`,
                );
              }}
            >
              {dreamTeam?.name?.toLocaleUpperCase() ?? 'TEAM'}
            </button>
          ))}
        </ul> */}
        <div className="px-[33px] max-799:px-0 bg-off-white-200 mb-[40px]">
          {/* Competition Header */}
          <div className="mt-5">
            <CompetitionDetailsHeader
              stats={{
                selectedPlayer: '11',
                remainingSalary:
                  eventStatus === 'upcoming'
                    ? remainingBudget
                    : dreamTeamResponse?.result?.totalSpendValue!,

                allLiveScore: allLiveScore,
              }}
              status={eventDetailsResponse?.result?.eventDetails?.status}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              dreamTeamResponse={dreamTeamResponse}
            />
          </div>

          <div className="flex space-x-2 mt-2 flex-col lg:flex-row w-full">
            <div className="w-full">
              {activeTab === 1 ? (
                <>
                  {isDreamTeamResponseLoading ? <DreamTeamLoader /> : <CricketFantasyUI />}
                </>
              ) : (
                <div className="shadow-[0px_1px_9px_0px_#0000002e]">
                  <DataTable
                    // @ts-expect-error
                    columns={playerColumn}
                    data={flattenedPlayerByRole || []}
                    stickyColumns={width <= 700 ? [0] : []}
                    initialColumnVisibility={{ teamName: false }}
                    isLoading={isDreamTeamResponseLoading}
                  />
                  <div className="w-full text-center bg-secondary-100 text-white px-3 py-1 text-sm text-black">
                    RESERVES
                  </div>
                  <DataTable
                    // @ts-expect-error
                    columns={playerColumn}
                    data={reservePlayers || []}
                    stickyColumns={width <= 700 ? [0] : []}
                    hideHeader={true}
                    hideCellVisibility={true}
                    initialColumnVisibility={{ teamName: false }}
                    isLoading={isDreamTeamResponseLoading}
                    skeletonRows={reservePlayersLimit || 4}
                  />

                </div>
              )}
            </div>
            <div className="w-[95%]">
              {/* <RightSideDetails dreamTeamId={activeTeam?.id} /> */}
              <PlayerSelectionUI
                stats={{
                  remainingSalary: 100000,
                }}
                activeTab={plyaerRoleTab}
                setActiveTab={setPlayerRoleTab}
                playerByRole={playerListPlayersByRole}
              />
            </div>
          </div>
        </div>
      </div>

      {eventDetailsResponse?.result?.eventDetails?.status === 'upcoming' &&
        !isLockout &&
        !isLive &&
        !isCompleted && !openReserveModal && !showPlayerTabel && (
          <div className="bg-gray-50 z-[1200] fixed bottom-0 left-0 right-0 rounded-t-xl shadow-xl">
            <div className="absolute -top-[25px] left-1/2 transform -translate-x-1/2 hidden"></div>
            <div className="flex w-full space-x-2 pb-4 md:space-x-0 md:mt-0 justify-around items-center">
              <div className="space-x-2 mt-5 flex flex-wrap gap-2 justify-center">
                <Button
                  size="sm"
                  disabled={
                    eventDetailsResponse?.result?.eventConfiguration
                      ?.eventType === 'free' &&
                    eventDetailsResponse?.result?.dreamTeams?.length > 0
                  }
                  variant="ghost"
                  className="!bg-[#335F83] text-white w-40 !disabled:cursor-not-allowed"
                  onClick={() => {
                    setBudget(
                      eventDetailsResponse?.result?.eventDetails?.CricketSeason
                        ?.fantasy_sport_salary_cap,
                    );
                    resetTeam();
                    router.push(
                      `/competitions/${eventDetailsResponse?.result?.eventConfiguration?.eventId}?event_id=${eventDetailsResponse?.result?.eventConfiguration?.eventId}&sport_id=4&tournament_id=${eventDetailsResponse?.result?.eventDetails?.CricketTournamentId}&competition_id=${eventDetailsResponse?.result?.eventConfiguration?.id}&add_more=true`,
                    );
                  }}
                >
                  {eventDetailsResponse?.result?.eventConfiguration
                    ?.eventType === 'free' &&
                    eventDetailsResponse?.result?.dreamTeams?.length > 0
                    ? 'Team Entered'
                    : 'Add one more team'}
                </Button>
                <SharePopup
                  isOpen={showSharePopup}
                  onClose={() => setShowSharePopup(false)}
                >
                  <button
                    type="button"
                    className="cursor-pointer border-none bg-transparent p-0 flex items-center justify-center"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setShowSharePopup(true);
                    }}
                  >
                    <ShareIcon width={35} height={35} />
                  </button>
                </SharePopup>
              </div>

              <div className="fixed bottom-[71px] left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center ">
                <PlayerCaptainViewIcon
                  className="md:hidden block"
                  onClick={() => setShowPlayerTabel(true)}
                />
              </div>
            </div>
          </div>
        )}

      {eventDetailsResponse?.result?.eventDetails?.status !== 'upcoming' && (
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center ">
          <PlayerCaptainViewIcon
            className="md:hidden block"
            onClick={() => setShowPlayerTabel(true)}
          />
        </div>
      )}
    </div>
  );
};

export default PreviewTeam;
