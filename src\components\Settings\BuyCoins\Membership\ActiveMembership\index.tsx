'use client';
import { yupResolver } from '@hookform/resolvers/yup';
import moment from 'moment-timezone';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import Select, { components } from 'react-select';
import * as Yup from 'yup';

import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { Button } from '@/components/UI/button';
import CustomDialog from '@/components/UI/CustomDialog';
import { Label } from '@/components/UI/label';
import { RadioGroup, RadioGroupItem } from '@/components/UI/radio-group';
import RHFDatePicker from '@/components/UI/RHFDatePicker';
import { useUserMemeberShip } from '@/helpers/context/userMembershipContext';
import { usePlanContext } from '@/helpers/context/userPlanContext';

export const memberOption = [
  { value: 'ManageMembership', label: 'Manage membership' },
  { value: 'HoldMembership', label: 'Hold membership' },
  { value: 'UpgradeMembership', label: 'Upgrade membership' },
  { value: 'CancelMembership', label: 'Cancel membership' },
];
export const cancelledMembershipOption = [
  { value: 'ManageMembership', label: 'Manage membership' },
  { value: 'UpgradeMembership', label: 'Upgrade membership' },
];

import './activeMemebership.scss';

import MasterCardIcon from '@/components/UI/Icons/MasterCardIcon';

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const membershipSchema = Yup.object().shape({
  startDate: Yup.date().nullable().required('Start date is required'),

  endDate: Yup.date()
    .nullable()
    .required('End date is required')
    .test(
      'endDate-after-startDate',
      'End date must be after start date',
      function (value) {
        const { startDate } = this.parent; // Access other field values
        return !startDate || !value || new Date(value) > new Date(startDate);
      },
    ),

  durationType: Yup.mixed<'infinite' | 'duration'>()
    .oneOf(
      ['infinite', 'duration'],
      'Duration must be either "infinite" or "duration"',
    )
    .required('Duration is required')
    .default('duration'),
});

type DurationType = 'infinite' | 'duration';

export type MembershipFormData = {
  startDate: Date;
  endDate: Date;
  durationType: DurationType;
};
const ActiveMembership = () => {
  const {
    cancelMembershipModal,

    resumeMembershipModal,

    selectedMembership,
    setActiveMembership,
    handelCanceActiveMembership,
    handleCancelMembershipModal,
    handleMembershipChanges,
    handleMembershipHold,
    handleResumeMembership,
    handleResumeMembershipClose,
    handleResumeMembershipPlan,
    setSelectedMembership,
  } = useUserMemeberShip();

  const { currentPlan, isResumePending, isHoldPending } = usePlanContext();

  const [durationType, setDurationType] = useState<DurationType>('duration');

  const {
    handleSubmit,
    setValue,
    control,
    watch,
    formState: { errors },
  } = useForm<MembershipFormData>({
    resolver:
      durationType === 'duration' ? yupResolver(membershipSchema) : undefined,
  });

  let planClassnames;
  if (currentPlan?.status === 'active') {
    planClassnames = 'w-[9px] h-[9px] rounded-[50%] bg-success-100 block';
  }
  if (currentPlan?.status === 'hold') {
    planClassnames = 'w-[9px] h-[9px] rounded-[50%] bg-[#FFC69A] block';
  }
  if (currentPlan?.status === 'cancelled') {
    planClassnames = 'w-[9px] h-[9px] rounded-[50%] bg-red-500 block';
  }

  useEffect(() => {
    setValue('durationType', 'duration');
  }, []);

  useEffect(() => {
    setDurationType(watch('durationType'));
  }, [watch('durationType')]);

  const planOptions =
    currentPlan?.status === 'cancelled' || !currentPlan?.planQueueStatus
      ? cancelledMembershipOption
      : memberOption;

  return (
    <>
      <div className="mt-7">
        <div className="flex items-center justify-between max-799:flex-col max-799:items-start gap-y-1.5">
          <p className="text-[22.4px] leading-[27px] font-semibold font-inter text-black-100">
            Membership
          </p>
          <div className="min-w-[207px] max-639:min-w-max max-w-[207px] max-639:max-w-none max-639:w-full">
            <Select
              className="React membership-select"
              value={planOptions?.find((item: any) => {
                return item?.value === selectedMembership;
              })}
              onChange={(e: any) => handleMembershipChanges(e?.value)}
              options={planOptions}
              classNamePrefix="select"
              placeholder="Odds"
              isSearchable={false}
              components={{ DropdownIndicator }}
            />
          </div>
        </div>
        <div className="p-[33px] details-duration max-799:px-[12px] max-799:py-[19px] mt-[14px] rounded-md shadow-[0px_1px_9px_0px_#0000002b] bg-white">
          {selectedMembership === 'HoldMembership' ? (
            <div className="">
              <h2 className="text-[16px] max-799:text-[12px] leading-[19px] max-799:leading-[16px] font-inter font-normal text-black-100 mb-[19px]">
                Need a break? Just follow these steps:
              </h2>

              <RadioGroup
                className="space-y-4"
                value={watch('durationType')}
                onValueChange={(durationType) => {
                  if (durationType === 'duration') {
                    setValue('durationType', durationType);
                  }

                  if (durationType === 'infinite') {
                    setValue('durationType', durationType);
                  }
                }}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="duration" id="duration" />
                  <Label
                    htmlFor="duration"
                    className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-normal font-inter text-black-100"
                  >
                    Hold duration
                  </Label>
                </div>

                {watch('durationType') === 'duration' && (
                  <div className="pl-6 space-y-4">
                    <div className="grid grid-cols-2 max-868:grid-cols-1 gap-4">
                      <div className="flex items-center gap-3">
                        <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100 max-868:w-[90px]">
                          Start date
                        </p>
                        <div className="relative">
                          <RHFDatePicker
                            control={control}
                            name="startDate"
                            placeHolder="dd/mm/yyyy"
                            disablePast
                          />

                          {errors?.startDate?.message && (
                            <p className="text-red-500 absolute -bottom-5 text-[14px] leading-[16px] font-inter">
                              {errors?.startDate?.message}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold text-black-100 max-868:w-[90px]">
                          End date
                        </p>
                        <div className="relative">
                          <RHFDatePicker
                            control={control}
                            name="endDate"
                            placeHolder="dd/mm/yyyy"
                            disablePast
                          />

                          {errors?.endDate?.message && (
                            <p className="text-red-500 absolute -bottom-5 text-[14px] leading-[16px] font-inter">
                              {errors?.endDate?.message}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-2 mt-2">
                  <RadioGroupItem value="infinite" id="infinite" />
                  <Label
                    htmlFor="infinite"
                    className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-normal font-inter text-black-100"
                  >
                    Keep my membership on hold until I choose to resume it
                    manually
                  </Label>
                </div>
              </RadioGroup>

              <div className="mt-[26px] max-799:mt-[22px] text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-normal text-black-600">
                The membership will be marked as hold. During this time, no
                charges will be processed until your membership is resumed.
              </div>
            </div>
          ) : (
            <div>
              <div className="flex items-center justify-between mb-3 pb-3 border-b border-black-300">
                <p className="w-1/2 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold">
                  Membership Type
                </p>
                <p className="w-1/2 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] max-799:text-end font-inter font-normal">
                  Smartplay - {currentPlan?.PlanName ?? '-'}
                </p>
              </div>
              <div className="flex items-center justify-between mb-3 pb-3 border-b border-black-300">
                <p className="w-1/2 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold">
                  Amount
                </p>
                <p className="w-1/2 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] max-799:text-end font-inter font-normal">
                  {currentPlan?.amount && (
                    <>${parseFloat(currentPlan.amount).toFixed(2)}/week</>
                  )}
                </p>
              </div>
              <div className="flex items-center justify-between mb-3 pb-3 border-b border-black-300">
                <p className="w-1/2 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold">
                  Status
                </p>
                <p className="w-1/2 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-normal flex items-center max-799:justify-end gap-2">
                  <span className={planClassnames}></span>
                  {currentPlan?.status === 'active' && (
                    <span className="text-success-100">Active</span>
                  )}

                  {currentPlan?.status === 'hold' && (
                    <span className="text-[#FFC69A]">Hold</span>
                  )}

                  {currentPlan?.status === 'cancelled' && (
                    <span className="text-red-500">Cancelled</span>
                  )}
                </p>
              </div>
              {currentPlan?.status === 'hold' && (
                <div className="flex items-center justify-between mb-3 pb-3 border-b border-black-300">
                  <p className="w-1/2 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold">
                    Hold duration
                  </p>
                  <p className="w-1/2 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-normal flex items-center max-799:justify-end gap-2">
                    {moment(currentPlan?.SubscriptionHold?.startDate).format(
                      'DD/MM/YYYY',
                    )}
                    {currentPlan?.SubscriptionHold?.endDate && (
                      <div className="flex items-center gap-2">
                        <span>to</span>
                        {moment(currentPlan?.SubscriptionHold?.endDate).format(
                          'DD/MM/YYYY',
                        )}
                      </div>
                    )}
                  </p>
                </div>
              )}
              {/* {currentPlan?.status !== 'hold' && ( */}
              <div className="flex items-center justify-between mb-3 pb-3 border-b border-black-300">
                <p className="w-1/2 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold">
                  {currentPlan?.status === 'cancelled'
                    ? 'End Date'
                    : 'Renewal Date'}
                </p>
                <p className="w-1/2 text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] max-799:text-end font-inter font-normal">
                  {currentPlan?.expireAt
                    ? moment(currentPlan?.expireAt).format('DD/MM/YYYY')
                    : '-'}
                </p>
              </div>
              {/* )} */}
              <div className="flex items-start justify-between max-799:flex-col max-799:gap-y-2">
                <p className="w-1/2 max-799:w-full text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-semibold">
                  Payment method
                </p>
                <p className="w-1/2 max-799:w-full text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-normal">
                  <div className="p-[18px] rounded-[5px] bg-gray-300 shadow-[0px_3px_6px_#0000001c]">
                    <p className="flex items-center gap-1.5 mb-1">
                      <span className="card-img">
                        <MasterCardIcon />
                      </span>
                      <span className="card-name text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-inter font-normal text-black-100">
                        **** {currentPlan?.card?.card_number}
                      </span>
                    </p>
                    <p className="text-[14px] leading-[16px] font-inter font-normal text-black-100">
                      Expiry: {currentPlan?.card?.card_expiry}
                    </p>
                  </div>
                </p>
              </div>
            </div>
          )}
        </div>
        <div className="mt-[34px] max-639:mt-[23px] text-center">
          {selectedMembership === 'HoldMembership' ? (
            <Button
              className="max-639:w-full"
              onClick={handleSubmit(handleMembershipHold)}
              disabled={isHoldPending}
            >
              Put membership on hold
            </Button>
          ) : (
            <Button
              className="max-639:w-full"
              onClick={() => {
                if (currentPlan?.status !== 'hold') {
                  setSelectedMembership('UpgradeMembership');
                  setActiveMembership(false);
                } else {
                  handleResumeMembership();
                }
              }}
            >
              {currentPlan?.status === 'hold'
                ? 'Resume membership'
                : 'Upgrade membership'}
            </Button>
          )}
        </div>
      </div>

      {/* cancel Modal code */}

      <CustomDialog
        isOpen={cancelMembershipModal}
        onClose={handleCancelMembershipModal}
        maxWidth={680}
        className="cancel-modal"
        outerClickClose={true}
      >
        <div className="text-center">
          <p className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[33px] max-799:mb-[23px]">
            Are you sure you want to cancel your membership?
          </p>
          <div className="mb-[18px]">
            <Button className="w-full" onClick={handelCanceActiveMembership}>
              Cancel active membership
            </Button>
          </div>
          <div>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => handleCancelMembershipModal()}
            >
              Not now
            </Button>
          </div>
        </div>
      </CustomDialog>

      {/* resume Modal code */}

      <CustomDialog
        isOpen={resumeMembershipModal}
        onClose={handleResumeMembershipClose}
        maxWidth={680}
        className="cancel-modal"
        outerClickClose={true}
      >
        <div className="text-center">
          <p className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[18px] max-799:mb-[13px]">
            Are you sure you want to resume your membership?
          </p>
          <p className="text-[16px] max-799:text-[12px] leading-[22.4px] max-799:leading-[17px] font-normal font-inter text-black-100 mb-[31px] max-799:mb-[23px]">
            Resuming will reactivate your subscription, and charges will apply
            on your next billing date.
          </p>
          <div className="mb-[18px]">
            <Button
              className="w-full"
              onClick={handleResumeMembershipPlan}
              disabled={isResumePending}
            >
              Resume membership
            </Button>
          </div>
          <div>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => handleResumeMembershipClose()}
            >
              Not now
            </Button>
          </div>
        </div>
      </CustomDialog>
    </>
  );
};

export default ActiveMembership;
