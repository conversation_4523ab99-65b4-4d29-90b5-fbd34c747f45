import React from 'react';
import { motion } from 'framer-motion';
import { generateUniqueId } from '@/lib/utils';

interface Tab {
  id: any;
  name: string;
  icon?: React.ReactNode;
  count?: number;
  maxCount?: number;
  disabled?: boolean;
}

interface TabsComponentProps {
  tabs: Tab[];
  activeTab: string | number;
  setActiveTab: (id: any) => void;
  variant?: 'default' | 'contained';
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const SimpleTabs: React.FC<TabsComponentProps> = ({
  tabs,
  activeTab,
  setActiveTab,
  variant = 'default',
  size = 'medium',
  className = '',
}) => {
  const handleTabClick = (tab: Tab) => {
    if (!tab.disabled) {
      setActiveTab(tab.id);
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'small':
        return 'text-sm py-1 px-2';
      case 'large':
        return 'text-lg py-3 px-4';
      default:
        return 'text-base py-2 px-3';
    }
  };

  const getVariantClasses = (variant: string, isActive: boolean) => {
    if (variant === 'contained') {
      return isActive
        ? 'bg-primary-600 text-white'
        : 'bg-gray-100 text-gray-600 hover:bg-gray-200';
    }
    return isActive
      ? 'bg-primary-200 text-white'
      : 'text-primary-200 hover:bg-gray-100';
  };

  return (
    <div className={`w-full ${className} bg-black-300 rounded-lg`}>
      <div className="overflow-x-auto" style={{ scrollbarWidth: 'thin' }}>
        <div className="flex items-center justify-between">
          {tabs?.map((tab) => {
            const isActive = activeTab === tab.id;
            const sizeClasses = getSizeClasses(size);

            return (
              <button
                key={tab.id}
                className={`relative flex-1 rounded-md transition-all duration-200 min-w-[94px] ${tab.disabled
                  ? 'opacity-50 cursor-not-allowed'
                  : 'cursor-pointer'
                  } ${sizeClasses}`}
                onClick={() => handleTabClick(tab)}
              >
                {isActive && (
                  <motion.span
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="absolute inset-0 z-10 bg-primary-200 rounded-md"
                    transition={{ type: 'spring', bounce: 0.2, duration: 0.6 }}
                  />
                )}
                <div className="relative z-20 md:flex items-center justify-center gap-2 text-center block py-1 md:py-0 space-x-1 w-max mx-auto">
                  {tab.icon && (
                    <span className={isActive ? 'active-icon' : ''}>
                      {tab.icon}
                    </span>
                  )}
                  <span
                    className={`font-medium md:text-base text-sm truncate w-fit ${isActive ? 'text-white' : 'text-gray-600'}`}
                  >
                    {tab.name}
                  </span>
                  {tab.count !== undefined && (
                    <span
                      className={`text-sm ${isActive ? 'text-white' : 'text-gray-600'}`}
                    >
                      {tab.count}
                      {tab.maxCount !== undefined && ` / ${tab.maxCount}`}
                    </span>
                  )}
                </div>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default SimpleTabs;
