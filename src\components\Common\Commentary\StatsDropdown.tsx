'use client';

import * as React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/UI/dropdown-menu';
import { Button } from '@/components/UI/button';
import { ChevronDown } from 'lucide-react';
import { ReactSelectOptionType } from '../../../../types';
import { useCricketCommentary } from '@/helpers/context/commentry/cricket';
import DropDownArrowIcon from '@/components/UI/Icons/DropDownArrowIcon';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/UI/select';
import useScreen from '@/hooks/useScreen';
export function StatsDropdown() {
  const value = useCricketCommentary();
  const [selectedOption, setSelectedOption] =
    React.useState<ReactSelectOptionType>(value.state.HIGHLIGHTOPTIONS[0]);

  const { width } = useScreen();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Button
          variant="default"
          className="md:w-[240px] w-[164px] h-[38px] justify-between relative"
        >
          {selectedOption.label}
          <DropDownArrowIcon position="up" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-[600px]  p-0 menu-content"
        align={width < 800 ? 'center' : 'start'}
      >
        <div className="grid lg:grid-cols-3 grid-cols-1  overflow-scroll no-scrollbar md:h-[400px]">
          <div>
            {/* Desktop */}
            <div className="p-2 pl-5 font-semibold">HIGHLIGHTS</div>
            <div className="hidden md:block">
              {value?.state.HIGHLIGHTOPTIONS.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  className="flex items-center px-4 py-2 cursor-pointer"
                  onSelect={() => setSelectedOption(option)}
                >
                  <span
                    className={`${
                      selectedOption.value === option.value
                        ? 'bg-black-400'
                        : ''
                    } px-2 py-1 rounded w-full`}
                  >
                    {option.label}
                  </span>
                </DropdownMenuItem>
              ))}
            </div>
            {/* Mobile View */}
            <div className="pl-5 block md:hidden">
              <Select defaultValue="">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Full Commentary" />
                </SelectTrigger>
                <SelectContent className="border-none">
                  <SelectGroup>
                    {value?.state?.HIGHLIGHTOPTIONS.map((option) => {
                      return (
                        <SelectItem value={option.value}>
                          {option.label}
                        </SelectItem>
                      );
                    })}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div>
            <div className="p-2 pl-5 font-semibold">BATTER</div>

            {/* Desktop View */}
            <div className="md:block hidden">
              {value?.state.selectedBatterPlayers?.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  className="flex items-center px-4 py-2 cursor-pointer"
                  onSelect={() => setSelectedOption(option)}
                >
                  <span
                    className={`${
                      selectedOption.value === option.value
                        ? 'bg-black-400'
                        : ''
                    } px-2 py-1 rounded w-full`}
                  >
                    {option.label}
                  </span>
                </DropdownMenuItem>
              ))}
            </div>

            {/* Mobile View */}
            <div className="pl-5 block md:hidden">
              <Select defaultValue="">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Tilak Verma" />
                </SelectTrigger>
                <SelectContent className="border-none">
                  <SelectGroup>
                    {value?.state?.awayTeamBowlerPlayers?.map((option) => {
                      return (
                        <SelectItem value={option.value}>
                          {option.label}
                        </SelectItem>
                      );
                    })}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div>
            <div className="p-2 pl-5 font-semibold">BOWLER</div>
            {/* Desktop View */}
            <div className="hidden md:block">
              {value?.state?.awayTeamBowlerPlayers?.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  className="flex items-center px-4 py-2 cursor-pointer"
                  onSelect={() => setSelectedOption(option)}
                >
                  <span
                    className={`${
                      selectedOption.value === option.value
                        ? 'bg-black-400'
                        : ''
                    } px-2 py-1 rounded w-full`}
                  >
                    {option.label}
                  </span>
                </DropdownMenuItem>
              ))}
            </div>

            {/* Mobile View */}
            <div className="pl-5 block md:hidden pb-5">
              <Select defaultValue="">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Tilak Verma" />
                </SelectTrigger>
                <SelectContent className="border-none">
                  <SelectGroup>
                    {value?.state.teamPlayerOptions
                      .filter((player) => player.role === 'bowler')
                      .map((option) => {
                        return (
                          <SelectItem value={option.value}>
                            {option.label}
                          </SelectItem>
                        );
                      })}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
