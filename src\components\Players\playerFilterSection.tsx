import { X } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import React from 'react';

import { Button } from '../UI/button';
import { Checkbox } from '../UI/checkbox';
import { Label } from '../UI/label';
import { generateUniqueId } from '@/lib/utils';

type FiltersState = {
  teams: Record<string, boolean>; // Using a Record for dynamic teams
  position: Record<string, boolean>;
};

type TeamKey = keyof FiltersState['teams'];
type FilterKey = keyof Omit<FiltersState, 'teams'>;

interface PlayedProfileDetailsModal {
  setFilters: Dispatch<SetStateAction<FiltersState>>;
  filters: FiltersState;
}

const positionsList = [
  'Wicket-keepers',
  'Batsmen',
  'Bowlers',
  'Dual position players',
];

const teamsList = [
  'Adelaide Strikers',
  'Melbourne Stars',
  'Brisbane Heat',
  'Perth Scrochers',
  'Hobart Hurricanes',
  'Sydney Sixers',
  'Melbourne Renegades',
  'Sydney Thunder',
]; // Dynamic team list

const PlayerFilterSection = ({
  setFilters,
  filters,
}: PlayedProfileDetailsModal) => {
  const handleCheckboxChange = (
    key: any,
    isTeam: boolean = false,
    isPosition: boolean = false,
  ) => {
    setFilters((prev) => {
      if (isTeam) {
        return {
          ...prev,
          teams: {
            ...prev.teams,
            [key]: !prev.teams[key as TeamKey],
          },
        };
      }
      if (isPosition) {
        return {
          ...prev,
          position: {
            ...prev.position,
            [key]: !prev.position[key],
          },
        };
      }
      return {
        ...prev,
        [key]: !prev[key as FilterKey],
      };
    });
  };

  const handleClearAll = () => {
    setFilters((prev) => ({
      ...prev,

      teams: Object.keys(prev.teams).reduce(
        (acc, team) => {
          acc[team] = false;
          return acc;
        },
        {} as Record<string, boolean>,
      ),
      position: Object.keys(prev.position).reduce(
        (acc, pos) => {
          acc[pos] = false;
          return acc;
        },
        {} as Record<string, boolean>,
      ),
    }));
  };

  return (
    <div className="px-[18px] max-799:px-0 py-3 max-h-[290px] h-full max-799:h-full overflow-y-auto no-scrollbar">
      <div className="flex max-799:hidden justify-end items-center mb-6">
        <button
          onClick={() => handleClearAll()}
          className="text-sm leading-[16px] text-secondary-100 flex items-center gap-1.5"
        >
          <X className="w-4 h-4 ml-1" />
          <span className="underline">Clear All</span>
        </button>
      </div>

      {/* By positions */}
      <div className="w-full mt-[19px] max-539:mt-[25px]">
        <h6 className="text-sm max-799:text-regular leading-[16px] max-799:leading-5 font-inter font-semibold text-black-100 mb-1.5 max-539:mb-[11px]">
          By position
        </h6>
        <div
          className={`${positionsList?.length > 4 ? 'grid-cols-2' : 'grid-cols-1'} grid  gap-2 gap-y-1.5`}
        >
          {positionsList?.map((position) => (
            <div className="flex items-center" key={generateUniqueId()}>
              <Checkbox
                id="adelaide-strikers"
                checked={filters.position[position]}
                onCheckedChange={() =>
                  handleCheckboxChange(position, false, true)
                }
                className="border-black-700"
              />
              <Label
                htmlFor={position}
                className="text-sm max-799:text-regular leading-[16px] max-799:leading-5 font-inter font-normal text-black-100  ml-3"
              >
                {position}
              </Label>
            </div>
          ))}
        </div>
      </div>
      {/* By team */}
      <div className="w-full mt-[19px] max-539:mt-[25px]">
        <h6 className="text-sm max-799:text-regular leading-[16px] max-799:leading-5 font-inter font-semibold text-black-100 mb-1.5 max-539:mb-[11px]">
          By team
        </h6>
        <div className="grid grid-cols-2 max-539:grid-cols-1 gap-2 gap-y-1.5">
          {teamsList?.map((team) => (
            <div className="flex items-center" key={generateUniqueId()}>
              <Checkbox
                id="adelaide-strikers"
                checked={filters.teams[team]}
                onCheckedChange={() => handleCheckboxChange(team, true, false)}
                className="border-black-700"
              />
              <Label
                htmlFor={team}
                className="text-sm max-799:text-regular leading-[16px] max-799:leading-5 font-inter font-normal text-black-100  ml-3"
              >
                {team}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <div className="relative hidden max-799:block">
        <div className="w-full max-w-screen-lg px-5 pt-[18px] mx-auto flex flex-col space-y-2 fixed bottom-0 left-1/2 transform -translate-x-1/2 mb-4 border-t border-primary-200/50">
          <Button>Apply</Button>
          <Button
            variant="link"
            className="underline text-white"
            onClick={() => handleClearAll()}
          >
            Clear All
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PlayerFilterSection;
