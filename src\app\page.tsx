'use client';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronDown,
  ChevronUp,
  SlidersHorizontal,
  X,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { format } from 'date-fns';

import AllCompetitionsFilter from '@/components/Competition/AllCompetitionsFilter';
import Loader from '@/components/Loader';
import PageTransitionEffect from '@/components/motions/PageTransitionEffect';
import Breadcrumbs from '@/components/UI/Breadcrumbs';
import CompCard from '@/components/UI/CompCard';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { useCompetition } from '@/helpers/context/competitionContext';
import useScreen from '@/hooks/useScreen';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { generateUniqueId, getSportsId } from '@/lib/utils';
import { SportsType } from '../../types';
import Select, { components } from 'react-select';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import CardBg from '@/assets/images/cardBG.png';
// Rename imports to Aussie Rules
import DarkSmart from '@/components/UI/Icons/DarkSmart';
import CoinsText from '@/components/UI/Icons/CoinsText';
import { Option } from '@/components/UI/CountryDropDown';
import { Input } from '@/components/UI/input';
import { useHeader } from '@/hooks/useHeader';

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const seasonOption = [
  {
    label: 10,
    value: 10,
  },
  {
    label: 20,
    value: 20,
  },
  {
    label: 50,
    value: 50,
  },
];

export default function Home() {
  const { width } = useScreen();
  const [activeTab, setActiveTab] = useState<number>(1);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [contestType, setContestType] = useState<string>('paid');
  const [entries, setEntries] = useState([0, 500]);
  const [prizepool, setPrizepool] = useState([0, 500000]);
  const [date, setDate] = useState<Date>();
  const [selectedSport, setSelectedSport] = useState<Option | null>(null);
  const [selectedLeague, setSelectedLeague] = useState<Option[] | null>(null);
  const [selectedTeam, setSelectedTeam] = useState<Option[] | null>(null);
  const [teamSearch, setTeamSearch] = useState('');
  const [leagueSearch, setLeagueSearch] = useState('');

  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const sports = searchParams.get('sports') as string;
  const sport_id = searchParams.get('sport_id') as string;
  const filter_sport_id = searchParams.get('filter_sport_id') as string;

  const sportOptions = [
    { id: '', value: 'all', label: 'All Sports' },
    { id: '4', value: 'cricket', label: 'Cricket' },
    { id: '9', value: 'afl', label: 'Aussie Rules' },
    { id: '12', value: 'nrl', label: 'Rugby League' },
    { id: '8', value: 'soccer', label: 'Football' },
  ];
  useEffect(() => {
    const urlContestType = searchParams.get('contestType');
    if (urlContestType) {
      setContestType(urlContestType);
    } else {
      const currentParams = new URLSearchParams(window.location.search);
      currentParams.set('contestType', 'paid');
      currentParams.set('status', '1');
      router.push(`${pathname}?${currentParams.toString()}`);
    }
  }, []);

  useEffect(() => {
    const sportId = searchParams.get('sport_id');
    const sports = searchParams.get('sports');
    if (sportId || sports === 'all') {
      setSelectedLeague(null);
      setSelectedTeam(null);
      setLeagueSearch('');
      setTeamSearch('');
      setDate(undefined);
      const currentParams = new URLSearchParams(window.location.search);
      const currentContestType = currentParams.get('contestType') || 'paid';
      currentParams.set('contestType', currentContestType);
      currentParams.delete('league');
      currentParams.delete('team');
      currentParams.delete('tournamentId');
      currentParams.delete('teamId');
      router.push(`${pathname}?${currentParams.toString()}`);
    }
  }, [searchParams.get('sport_id'), searchParams.get('sports')]);

  useEffect(() => {
    const status = searchParams.get('status');
    if (status) {
      setSelectedLeague(null);
      setSelectedTeam(null);
      setLeagueSearch('');
      setTeamSearch('');
      setDate(undefined);
      const currentParams = new URLSearchParams(window.location.search);
      const currentContestType = currentParams.get('contestType') || 'paid';
      currentParams.set('contestType', currentContestType);
      currentParams.delete('league');
      currentParams.delete('team');
      currentParams.delete('tournamentId');
      currentParams.delete('teamId');
      router.push(`${pathname}?${currentParams.toString()}`);
    }
  }, [searchParams.get('status')]);

  const breadcrumbsLinks = [
    { label: 'Home', href: '/' },
    { label: 'SmartPlay', href: '#' },
    { label: 'All Competitions', href: '#' },
  ];

  interface TabProps {
    id: string;
    label: string;
    content: string;
  }

  const [status, setStatus] = useState<string>('upcoming');

  const { tabs } = useHeader();

  const sportsType = searchParams.get('sports');

  const handleTabChanges = (id: number, label?: string) => {
    setActiveTab(id);
    setCurrentPage(1);
    setItemsPerPage(10);

    if (label) {
      // Special cases for URL mapping
      let urlLabel = label.toLowerCase();
      if (label === 'AUSSIE RULES') urlLabel = 'afl';
      if (label === 'RUGBY LEAGUE') urlLabel = 'nrl';
      if (label === 'FOOTBALL') urlLabel = 'soccer';
      const sportsId = getSportsId(urlLabel as SportsType);
      let query = sportsId
        ? `?sports=${urlLabel}&sport_id=${sportsId}`
        : `?sports=${urlLabel}`;
      const status = 1;
      query += `&status=${status}`;

      router.push(query);
    }
  };

  const handleResetFilter = () => {
    setContestType('paid');
    setEntries([0, 500]);
    setPrizepool([0, 500000]);
    setDate(undefined);
    setSelectedSport(null);
    setSelectedLeague(null);
    setSelectedTeam(null);
    setTeamSearch('');
    setLeagueSearch('');
    setIsOpen(false);

    const currentParams = new URLSearchParams(window.location.search);
    const status = currentParams.get('status');

    currentParams.delete('sports');
    currentParams.delete('sport_id');
    currentParams.delete('league');
    currentParams.delete('team');
    currentParams.delete('date');
    currentParams.delete('tournamentId');
    currentParams.delete('teamId');
    currentParams.delete('filter_sport_id');

    if (status) {
      currentParams.set('status', status);
    }
    currentParams.set('contestType', 'paid');

    router.push(`${pathname}?${currentParams.toString()}`);
  };

  const {
    allCompetions,
    isLoadingAllCompetionsData,
    currentPage,
    setCurrentPage,
    itemsPerPage,
    setItemsPerPage,
    totalItems,
  } = useCompetition();

  const compData = allCompetions?.map((competition) => {
    const { eventConfiguration } = competition;

    if (eventConfiguration) {
      return {
        ...competition,
        eventConfiguration: {
          ...eventConfiguration,
          prizePool:
            typeof eventConfiguration.prizePool === 'string'
              ? JSON.parse(eventConfiguration.prizePool)
              : eventConfiguration.prizePool,
          drawPool:
            typeof eventConfiguration.drawPool === 'string'
              ? JSON.parse(eventConfiguration.drawPool)
              : eventConfiguration.drawPool,
        },
      };
    }

    return competition;
  });

  useEffect(() => {
    if (sportsType) {
      // Find tab by matching either the label or special cases
      const tab = tabs.find(
        (tab) =>
          tab.label.toLowerCase() === sportsType ||
          (sportsType === 'afl' && tab.label === 'AUSSIE RULES') ||
          (sportsType === 'nrl' && tab.label === 'RUGBY LEAGUE') ||
          (sportsType === 'soccer' && tab.label === 'FOOTBALL'),
      );
      setActiveTab(tab?.labelId ?? 1);
    } else {
      setActiveTab(1);
    }
  }, [sportsType]);

  const handleContestTypeChange = (type: 'paid' | 'free') => {
    setContestType(type);
    const currentParams = new URLSearchParams(window.location.search);
    currentParams.set('contestType', type);
    router.push(`${pathname}?${currentParams.toString()}`);
    setCurrentPage(1);
    setItemsPerPage(10);
  };

  const handleStatusChange = (tab: string) => {
    const params = new URLSearchParams(searchParams.toString());

    if (tab.toLowerCase() === 'upcoming') {
      params.set('status', '1');
      params.delete('compType');
    } else if (tab.toLowerCase() === 'live') {
      params.set('status', '2');
      params.delete('compType');
    } else if (tab.toLowerCase() === 'completed') {
      params.set('status', '3');
    }

    router.push(`${pathname}?${params.toString()}`);
    setStatus(tab.toLowerCase());
    setCurrentPage(1);
    setItemsPerPage(10);
  };

  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());
    if (params.get('status') === '2') {
      setStatus('live');
    }
    if (params.get('status') === '1') {
      setStatus('upcoming');
    }
    if (params.get('status') === '3') {
      setStatus('completed');
    }
  }, [searchParams]);

  const handleFilterChange = (type: string, value: any) => {
    const currentParams = new URLSearchParams(window.location.search);

    switch (type) {
      case 'contestType':
        setContestType(value);
        currentParams.set('contestType', value);
        break;
      case 'league':
        setSelectedLeague(value);
        if (value?.value && value.value !== 'all') {
          currentParams.set('league', value.value);
        } else {
          currentParams.delete('league');
        }
        break;
      case 'team':
        setSelectedTeam(value);
        if (value?.value && value.value !== 'all') {
          currentParams.set('team', value.value);
        } else {
          currentParams.delete('team');
        }
        break;
      case 'date':
        setDate(value);
        if (value) {
          currentParams.set('date', format(value, 'yyyy-MM-dd'));
        } else {
          currentParams.delete('date');
        }
        break;
      case 'sport':
        setSelectedSport(value);
        if (value?.value && value.value !== 'all') {
          const sportId = sportOptions
            .find((sport) => sport.value === value.value)
            ?.id?.toString();
          if (sportId) {
            currentParams.set('filter_sport_id', sportId);
            currentParams.set('sports', String(value.value));
          }
        } else {
          currentParams.delete('filter_sport_id');
          currentParams.delete('sports');
          currentParams.delete('league');
          currentParams.delete('team');
          currentParams.delete('tournamentId');
          currentParams.delete('teamId');
          currentParams.delete('date');
        }
        if (!currentParams.get('contestType')) {
          currentParams.set('contestType', contestType);
        }
        break;
    }

    router.push(`${pathname}?${currentParams.toString()}`);
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Handle page changes
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Handle items per page change
  const changeItemsPerPage = (value: number) => {
    setItemsPerPage(value);
    // Reset to first page when changing items per page
    setCurrentPage(1);
  };

  const [inputPage, setInputPage] = useState<string>(currentPage.toString());

  useEffect(() => {
    setInputPage(currentPage.toString());
  }, [currentPage]);

  return (
    <PageTransitionEffect variant="quickFade">
      <div className="">
        <div className="pt-[33px] max-799:pt-[18px] pb-[12px]  text-black-100  max-799:pb-[9px] bg-off-white-200 px-8 max-1024:px-0 rounded-t-md mt-2">
          <Breadcrumbs links={breadcrumbsLinks} />
          <h1 className="text-[31.36px] max-799:text-[22.4px] max-799:leading-[28px] font-normal text-black-100 max-1024:text-black-100 font-veneerCleanSoft">
            All Competitions
          </h1>
        </div>
        <div className=" bg-off-white-200 max-1024:bg-white max-799:mx-[-12px]">
          <div className="w-full max-799:px-3">
            <div className="overflow-x-auto no-scrollbar no-scrollbar pb-4 bg-white border-b-black-400 border-b-[1px]">
              <div className="flex gap-[18px] border-t-black-400 border-t-[1px] max-1024:gap-3 font-normal border-b-[3px] bg-white shadow-md border-b-secondary-100 w-max min-w-full">
                {tabs?.map((tab) => (
                  <button
                    key={tab.labelId}
                    className={`w-auto font-veneerCleanSoft ${(tab?.labelId === activeTab ||
                        (sportsType === 'afl' &&
                          tab.label === 'AUSSIE RULES') ||
                        (sportsType === 'nrl' &&
                          tab.label === 'RUGBY LEAGUE')) &&
                        !tab?.comingSoon
                        ? 'border-b-[3px] bg-tab-active-gradient border-primary-200 text-secondary-100'
                        : 'border-b-[3px] border-transparent text-black-100 '
                      }`}
                    onClick={() =>
                      tab?.comingSoon === false &&
                      handleTabChanges(tab?.labelId, tab?.label)
                    }
                  >
                    <div className="px-[34px] max-799:px-3 pt-[18px] max-799:pt-3 pb-2 relative w-[130px] flex flex-col items-center">
                      <div className="max-799:mx-auto max-799:hidden flex justify-center items-center">
                        {tab?.labelId === activeTab
                          ? tab?.activeIcon
                          : tab?.icon}
                      </div>
                      <div className="tab-svg-icon max-799:mx-auto max-799:block hidden">
                        {tab?.labelId === activeTab
                          ? tab?.mobileActiveIcon
                          : tab?.mobileIcon}
                      </div>
                      <div className="whitespace-nowrap text-center">
                        {tab?.label}
                      </div>
                      {tab?.comingSoon && (
                        <div className="absolute left-1/2 bottom-[-20px] max-799:bottom-[-16px] transform -translate-x-1/2 -translate-y-1/2 w-max px-1.5 max-799:px-1 py-[2px] rounded-[6px] bg-negative-200 text-[11.42px] max-799:text-[7px] leading-[14px] max-799:leading-[9px] font-inter font-light text-white">
                          {tab?.title}
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
          <div className="px-[27px] max-799:px-3 pt-4 pb-0">
            <div className="flex justify-between items-center">
              {/* <div className="flex space-x-2">
                <span className="cursor-pointer" onClick={getPaidEvents}>
                  <SmartComp />
                </span>
                <span className="cursor-pointer" onClick={getFreeEvents}>
                  <FreeComp />
                </span>
              </div> */}

              <div className="w-full max-w-[100%]">
                {/* Competition Type Tabs */}
                <div className="flex flex-col gap-4">
                  {/* Status Tabs */}
                  <div className="flex border-secondary-100 border-b-4">
                    {['UPCOMING', 'LIVE', 'COMPLETED'].map((tab) => (
                      <button
                        key={tab}
                        onClick={() => handleStatusChange(tab)}
                        className={`px-4 py-2 text-[25px] max-799:text-[16px] leading-[24px] max-799:leading-[20px] font-veneerCleanSoft ${status === tab.toLowerCase()
                            ? 'bg-primary-200 rounded-t-md text-white'
                            : 'text-gray-500'
                          }`}
                      >
                        {tab}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* <div>
                <motion.button
                  className={`${isOpen && width > 799 ? 'rounded-t-lg' : 'rounded-lg'} w-full py-[9px] px-[15px] ${isOpen ? 'bg-secondary-100' : 'bg-black-300'} flex items-center justify-between max-799:justify-end max-799:flex-row-reverse max-799:gap-1.5 cursor-pointer`}
                  onClick={() => setIsOpen(!isOpen)}
                >
                  <div className="flex items-center gap-2">
                    <SlidersHorizontal
                      className={isOpen ? 'text-white fill-white' : ''}
                    />
                    {width > 799 && (
                      <motion.div
                        animate={{ rotate: isOpen ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <ChevronDown
                          className={`h-4 w-4 ${isOpen ? 'text-white' : ''}`}
                        />
                      </motion.div>
                    )}
                  </div>
                </motion.button>
              </div> */}
            </div>
            <div className="w-full">
              <div className="flex justify-between items-center max-799:flex-col max-799:items-start max-799:space-y-2 md:mt-4">
                <div className="flex space-x-1 bg-black-300 rounded-[6px] px-[3px] py-[3px] max-799:w-full">
                  <button
                    onClick={() => handleContestTypeChange('paid')}
                    className={`flex items-baseline gap-1 px-4 py-2 rounded-[6px] text-[18px] leading-[19px] font-veneerCleanSoft max-799:w-1/2 max-799:justify-center ${searchParams.get('contestType') === 'paid'
                        ? 'bg-primary-200 text-white'
                        : 'text-primary-500'
                      }`}
                  >
                    {searchParams.get('contestType') === 'paid' ? (
                      <>
                        {/* <DarkSmartCoins /> */}
                        <img
                          src="/fantasy/images/light-smart-b-coins.png"
                          alt="dark-smart-coins"
                        />
                      </>
                    ) : (
                      <div className="flex">
                        <DarkSmart />
                        <CoinsText />
                      </div>
                    )}
                    <span> COMPETITIONS</span>
                  </button>

                  <button
                    onClick={() => handleContestTypeChange('free')}
                    className={`px-4 py-2 rounded-[6px] text-[18px] leading-[19px] font-veneerCleanSoft max-799:w-1/2 max-799:text-center ${searchParams.get('contestType') === 'free'
                        ? 'bg-primary-200 text-white'
                        : 'text-primary-500'
                      }`}
                  >
                    FREE COMPETITIONS
                  </button>
                </div>

                <button
                  onClick={() => setIsOpen(!isOpen)}
                  className={`${isOpen ? 'bg-secondary-100 text-white' : 'bg-black-300 text-black-100'} flex items-center space-x-1  border-black-400  px-4 py-2 rounded-[8px] text-[16px] leading-[19px] max-799:w-full max-799:flex-row-reverse max-799:justify-end max-799:gap-1`}
                >
                  <span>Filters</span>
                  {width > 799 ? (
                    isOpen ? (
                      <ChevronUp className="h-5 w-5" />
                    ) : (
                      <ChevronDown className="h-5 w-5" />
                    )
                  ) : (
                    <SlidersHorizontal />
                  )}
                </button>
              </div>

              <hr className="border-black-400  mt-4" />
            </div>

            {width > 799 ? (
              <AnimatePresence>
                {isOpen && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{
                      duration: 0.3,
                      ease: 'easeInOut',
                    }}
                  >
                    <AllCompetitionsFilter
                      contestType={contestType}
                      setContestType={setContestType}
                      handleResetFilter={handleResetFilter}
                      entries={entries}
                      setEntries={setEntries}
                      prizepool={prizepool}
                      setPrizepool={setPrizepool}
                      screenWidth={width}
                      setIsOpen={setIsOpen}
                      date={date}
                      setDate={setDate}
                      selectedSport={selectedSport}
                      setSelectedSport={setSelectedSport}
                      selectedLeague={selectedLeague}
                      setSelectedLeague={setSelectedLeague}
                      selectedTeam={selectedTeam}
                      setSelectedTeam={setSelectedTeam}
                      teamSearch={teamSearch}
                      setTeamSearch={setTeamSearch}
                      leagueSearch={leagueSearch}
                      setLeagueSearch={setLeagueSearch}
                      handleFilterChange={handleFilterChange}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            ) : (
              <Drawer open={isOpen} onOpenChange={setIsOpen}>
                <DrawerContent className="bg-white h-full">
                  <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md flex justify-between items-center">
                    <span>FILTERS</span>
                    <button
                      onClick={() => setIsOpen(false)}
                      className="p-1 hover:bg-white/10 rounded-full transition-colors"
                    >
                      <X className="text-white" />
                    </button>
                  </div>
                  <div className="p-4 space-y-4 mt-10 h-screen">
                    <AllCompetitionsFilter
                      contestType={contestType}
                      setContestType={setContestType}
                      handleResetFilter={handleResetFilter}
                      entries={entries}
                      setEntries={setEntries}
                      prizepool={prizepool}
                      setPrizepool={setPrizepool}
                      screenWidth={width}
                      setIsOpen={setIsOpen}
                      date={date}
                      setDate={setDate}
                      selectedSport={selectedSport}
                      setSelectedSport={setSelectedSport}
                      selectedLeague={selectedLeague}
                      setSelectedLeague={setSelectedLeague}
                      selectedTeam={selectedTeam}
                      setSelectedTeam={setSelectedTeam}
                      teamSearch={teamSearch}
                      setTeamSearch={setTeamSearch}
                      leagueSearch={leagueSearch}
                      setLeagueSearch={setLeagueSearch}
                      handleFilterChange={handleFilterChange}
                    />
                  </div>
                </DrawerContent>
              </Drawer>
            )}
          </div>

          {/* New */}
        </div>
        <div>
          <div className="pt-[20px] max-1024:pt-[12px] px-[33px] max-1024:px-0 pb-[90px] max-1024:pb-[45px] bg-off-white-200">
            <div>
              {!compData?.length && !isLoadingAllCompetionsData && (
                <div className="mt-2 p-2 text-center space-y-2">
                  <p className="text-[18px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter text-black-100">
                    We're lining up the latest games.
                  </p>
                  <p className="text-sm">Stay sharp and check back in a bit!</p>
                </div>
              )}

              {isLoadingAllCompetionsData && (
                <div className="mt-2 p-2 text-center flex justify-center items-center">
                  <Loader />
                </div>
              )}

              {compData?.map((item: any, index: number) => {
                return (
                  <React.Fragment key={generateUniqueId()}>
                    <CompCard addMore={index === 1} compData={item} />
                  </React.Fragment>
                );
              })}

              {/* Pagination controls */}

              {compData?.length &&
                compData?.length > 0 &&
                !isLoadingAllCompetionsData ? (
                <div className="flex items-center gap-2 justify-end">
                  <div className="flex items-center space-x-2">
                    <span className="text-[16px] max-639:text-[14px] leading-[19px] max-639:leading-[16px] font-inter font-normal text-black-100">
                      Results per page
                    </span>

                    <Select
                      className="React season-select-comp"
                      value={seasonOption?.find((item: any) => {
                        return item?.value === itemsPerPage;
                      })}
                      onChange={(e: any) => changeItemsPerPage(e?.value)}
                      options={seasonOption}
                      classNamePrefix="select"
                      placeholder="season stats"
                      isSearchable={false}
                      components={{ DropdownIndicator }}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={goToPreviousPage}
                      disabled={currentPage === 1}
                      className="border border-black-400 w-9 h-9 rounded-md flex items-center justify-center"
                    >
                      <ChevronLeft
                        className={`${currentPage === 1 ? 'text-black-400' : ''} h-6 w-6`}
                      />
                    </button>

                    {/* <div className="flex items-center">
                        <div className="border border-black-400 w-9 h-9 flex items-center justify-center rounded-md">
                          {currentPage}
                        </div>
                      </div> */}

                    <Input
                      className="w-9 h-9 rounded-md flex items-center justify-center min-w-[50px] text-center"
                      value={inputPage}
                      type="number"
                      onChange={(e: any) => {
                        const value = e.target.value;
                        if (value === '0') return;
                        setInputPage(value);
                      }}
                      onKeyDown={(e: any) => {
                        if (e.key === 'Enter') {
                          const value = e.target.value;
                          if (value === '') {
                            setCurrentPage(1);
                            return;
                          }
                          const numValue = parseInt(value);
                          if (!isNaN(numValue)) {
                            if (numValue > totalPages) {
                              setCurrentPage(1);
                            } else if (numValue > 0) {
                              setCurrentPage(numValue);
                            }
                          }
                        }
                      }}
                      onBlur={() => {
                        const value = inputPage;
                        if (value === '' || parseInt(value) > totalPages) {
                          setCurrentPage(1);
                        } else {
                          const numValue = parseInt(value);
                          if (!isNaN(numValue) && numValue > 0) {
                            setCurrentPage(numValue);
                          }
                        }
                      }}
                    />

                    <button
                      onClick={goToNextPage}
                      disabled={currentPage === totalPages}
                      className="border border-black-400 w-9 h-9 rounded-md flex items-center justify-center"
                    >
                      <ChevronRight
                        className={`${currentPage === totalPages ? 'text-black-400' : ''} h-6 w-6`}
                      />
                    </button>

                    <span className="text-[16px] max-639:text-[14px] leading-[19px] max-639:leading-[16px] font-inter font-normal text-black-100">
                      of {totalPages}
                    </span>
                  </div>
                </div>
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </div>
    </PageTransitionEffect>
  );
}
