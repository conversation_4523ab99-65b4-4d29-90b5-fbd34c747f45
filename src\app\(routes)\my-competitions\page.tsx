'use client';
import moment from 'moment';
import { AnimatePresence, motion } from 'motion/react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import Select, { components } from 'react-select';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import Loader from '@/components/Loader';
import Breadcrumbs from '@/components/UI/Breadcrumbs';
import CompCard from '@/components/UI/CompCard';
import CustomTabs from '@/components/UI/CustomTab';
import { useCompetition } from '@/helpers/context/competitionContext';
import { generateUniqueId, getSportsId } from '@/lib/utils';
import { SportsType } from '../../../../types';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Input } from '@/components/UI/input';
import { useHeader } from '@/hooks/useHeader';

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const seasonOption = [
  {
    label: 10,
    value: 10,
  },
  {
    label: 20,
    value: 20,
  },
  {
    label: 50,
    value: 50,
  },
];

export default function MyCompetitionsPage() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState<number>(1);
  const [myCompetitionsTabsActive, setMyCompetitionsTabsActive] =
    useState<number>(1);

  const breadcrumbsLinks = [
    { label: 'Home', href: '/' },
    { label: 'SmartPlay', href: '#' },
    { label: 'My Competitions', href: '#' },
  ];

  const { tabs } = useHeader();

  const {
    allCompetionsData,
    isLoadingAllCompetionsData,
    eventDetailsResponse,
    currentPage,
    setCurrentPage,
    itemsPerPage,
    setItemsPerPage,
    totalItems,
  } = useCompetition();

  const eventStatus = searchParams.get('status');

  const allCompetionsDataDetails =
    allCompetionsData?.result?.sort((a, b) => {
      const today = moment(); // Current date and time
      const startTimeA = moment(a?.startTime);
      const startTimeB = moment(b?.startTime);

      // Check if the events are in the future
      const isFutureA = startTimeA.isAfter(today);
      const isFutureB = startTimeB.isAfter(today);

      // If both events are in the future, sort by closest to current date
      if (isFutureA && isFutureB) {
        return startTimeA.diff(today) - startTimeB.diff(today);
      }

      // If only one event is in the future, prioritize that event
      if (isFutureA) return -1;
      if (isFutureB) return 1;

      // Both events are in the past, sort by most recently completed
      return startTimeB.diff(today) - startTimeA.diff(today);
    }) || [];

  const [myCompetitionsTabs, setMyCompetitionsTabs] = useState([
    { label: 'Upcoming', labelId: 1, count: 0 },
    { label: 'Live', labelId: 2, count: 0 },
    { label: 'Completed', labelId: 3, count: 0 },
  ]);

  useEffect(() => {
    const upcomingCount =
      allCompetionsData?.eventCountData?.upcomingEventCount ?? 0;
    const liveCount = allCompetionsData?.eventCountData?.liveEventCount ?? 0;
    const completedCount =
      allCompetionsData?.eventCountData?.findCompletedEvents ?? 0;

    const allCount = myCompetitionsTabs?.reduce(
      (acc, tab) => acc + tab?.count,
      0,
    );

    // if (allCount === 0) {
    setMyCompetitionsTabs((prevTabs) => {
      // Only update if counts are different
      const hasChanged = prevTabs.some((tab, index) => {
        const newCounts = [upcomingCount, liveCount, completedCount];
        return tab.count !== newCounts[index];
      });
      return hasChanged
        ? [
          { label: 'Upcoming', labelId: 1, count: upcomingCount },
          { label: 'Live', labelId: 2, count: liveCount },
          { label: 'Completed', labelId: 3, count: completedCount },
        ]
        : prevTabs;
    });
    // }
  }, [allCompetionsData?.eventCountData]);

  const handleTabChanges = (id: number, label?: string) => {
    setActiveTab(id);
    setCurrentPage(1);
    setItemsPerPage(10);

    const searchParams = new URLSearchParams(window.location.search);

    if (label) {
      // Special cases for URL mapping
      let urlLabel = label.toLowerCase();
      if (label === 'AUSSIE RULES') urlLabel = 'afl';
      if (label === 'RUGBY LEAGUE') urlLabel = 'nrl';
      if (label === 'FOOTBALL') urlLabel = 'soccer';
      const sportsId = getSportsId(urlLabel as SportsType);
      searchParams.set('sports', urlLabel);

      if (sportsId) {
        searchParams.set('sport_id', sportsId.toString());
      } else {
        searchParams.delete('sport_id');
      }
    }

    router.push(`${pathname}?${searchParams.toString()}`);
  };

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);

    searchParams.set('status', JSON.stringify(myCompetitionsTabsActive));
    searchParams.set('compType', 'my');

    router.push(`${pathname}?${searchParams.toString()}`);
    setCurrentPage(1);
    setItemsPerPage(10);
  }, [myCompetitionsTabsActive]);

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const status = searchParams.get('status');
    if (status) {
      setMyCompetitionsTabsActive(Number(status));
    }
  }, [searchParams]);

  // Add new effect to handle initial tab selection
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const sports = searchParams.get('sports');

    if (sports) {
      // Find the tab that matches the sports parameter
      const matchingTab = tabs.find((tab) => {
        const tabLabel = tab.label.toLowerCase();
        if (sports === 'afl' && tabLabel === 'aussie rules') return true;
        if (sports === 'nrl' && tabLabel === 'rugby league') return true;
        if (sports === 'soccer' && tabLabel === 'football') return true;
        return tabLabel === sports;
      });

      if (matchingTab) {
        setActiveTab(matchingTab.labelId);
      }
    }
  }, []);

  // Calculate total pages
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Handle page changes
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Handle items per page change
  const changeItemsPerPage = (value: number) => {
    setItemsPerPage(value);
    // Reset to first page when changing items per page
    setCurrentPage(1);
  };

  const [inputPage, setInputPage] = useState<string>(currentPage.toString());

  useEffect(() => {
    setInputPage(currentPage.toString());
  }, [currentPage]);

  const renderNotDataText = (eventStatus: number) => {
    switch (eventStatus) {
      case 1:
        return (
          <div className="mt-2 p-2 text-center space-y-2">
            <p className="text-[18px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter text-black-100">
              No games, coach!
            </p>
            <p className="text-sm">Enter a game in 'All Comps'</p>
          </div>
        );

      case 2:
        return (
          <div className="mt-2 p-2 text-center space-y-2">
            <p className="text-[18px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter text-black-100">
              No games, coach!
            </p>
            <p className="text-sm">
              Enter a game in 'All Comps' and wait until play begins.
            </p>
          </div>
        );

      case 3:
        return (
          <div className="mt-2 p-2 text-center space-y-2">
            <p className="text-[18px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter text-black-100">
              No games, coach!
            </p>
            <p className="text-sm">Enter a game in 'All Comps'</p>
          </div>
        );
    }
  };
  return (
    <div className="">
      <div className="pt-[33px] max-799:pt-[18px] text-black-100  max-799:pb-[9px] bg-off-white-200 px-8 rounded-t-md mt-2">
        <Breadcrumbs links={breadcrumbsLinks} />
        <h1 className="text-[31.36px] max-799:text-[22.4px] max-799:leading-[28px] font-normal text-black-100 max-1024:text-black-100 font-veneerCleanSoft">
          My Competitions
        </h1>
      </div>
      <div className=" bg-off-white-200 max-1024:bg-white max-799:mx-[-12px]">
        <div className="w-full max-799:px-3">
          <div className="overflow-x-auto no-scrollbar no-scrollbar pb-2">
            <div className="flex gap-[18px] max-1024:gap-3 font-normal border-b-[3px] border-b-secondary-100 w-max min-w-full">
              {tabs?.map((tab) => (
                <button
                  key={tab.labelId}
                  className={`w-auto font-veneerCleanSoft ${(tab?.labelId === activeTab ||
                      (searchParams.get('sports') === 'afl' &&
                        tab.label === 'AUSSIE RULES') ||
                      (searchParams.get('sports') === 'nrl' &&
                        tab.label === 'RUGBY LEAGUE')) &&
                      !tab?.comingSoon
                      ? 'border-b-[3px] bg-tab-active-gradient border-primary-200 text-secondary-100'
                      : 'border-b-[3px] border-transparent text-black-100 '
                    }`}
                  onClick={() =>
                    tab?.comingSoon === false &&
                    handleTabChanges(tab?.labelId, tab?.label)
                  }
                >
                  <div className="px-[34px] max-799:px-3 pt-[18px] max-799:pt-3 pb-2 relative w-[130px] flex flex-col items-center">
                    <div className="max-799:mx-auto max-799:hidden block">
                      {tab?.labelId === activeTab ? tab?.activeIcon : tab?.icon}
                    </div>
                    <div className="tab-svg-icon max-799:mx-auto max-799:block hidden">
                      {tab?.labelId === activeTab
                        ? tab?.mobileActiveIcon
                        : tab?.mobileIcon}
                    </div>
                    <div className="whitespace-nowrap text-center">
                      {tab?.label}
                    </div>
                    {tab?.comingSoon && (
                      <div className="absolute left-1/2 bottom-[-20px] max-799:bottom-[-16px] transform -translate-x-1/2 -translate-y-1/2 w-max px-1.5 max-799:px-1 py-[2px] rounded-[6px] bg-negative-200 text-[11.42px] max-799:text-[7px] leading-[14px] max-799:leading-[9px] font-inter font-light text-white">
                        {tab?.title}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div>
        <div className="pt-[31px] max-1024:pt-[12px] px-[33px] max-1024:px-0 pb-[90px] max-1024:pb-[45px] bg-off-white-200">
          <div className="pt-[21px] max-1024:pt-1.5  max-1024:pb-[14px] bg-off-white-200 ">
            <CustomTabs<number>
              tabs={myCompetitionsTabs}
              setActiveTab={setMyCompetitionsTabsActive}
              activeTab={myCompetitionsTabsActive}
              className="text-[25px] max-799:text-[16px] !leading-[32px] max-799:!leading-[20px] font-veneerCleanSoft"
            />
          </div>

          <div className="mt-[18px]">
            {isLoadingAllCompetionsData && (
              <div className="mt-1.5">
                <Loader />
              </div>
            )}
            {!isLoadingAllCompetionsData &&
              allCompetionsDataDetails?.length > 0 &&
              allCompetionsDataDetails.map((item: any, index: number) => (
                <React.Fragment key={generateUniqueId()}>
                  <AnimatePresence mode="popLayout">
                    <motion.div
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      exit={{ y: -10, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <CompCard compData={item} />
                    </motion.div>
                  </AnimatePresence>
                </React.Fragment>
              ))}

            {allCompetionsDataDetails?.length &&
              allCompetionsDataDetails?.length > 0 &&
              !isLoadingAllCompetionsData ? (
              <div className="flex items-center gap-2 justify-end">
                <div className="flex items-center space-x-2">
                  <span className="text-[16px] max-639:text-[14px] leading-[19px] max-639:leading-[16px] font-inter font-normal text-black-100">
                    Results per page
                  </span>

                  <Select
                    className="React season-select-comp"
                    value={seasonOption?.find((item: any) => {
                      return item?.value === itemsPerPage;
                    })}
                    onChange={(e: any) => changeItemsPerPage(e?.value)}
                    options={seasonOption}
                    classNamePrefix="select"
                    placeholder="season stats"
                    isSearchable={false}
                    components={{ DropdownIndicator }}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={goToPreviousPage}
                    disabled={currentPage === 1}
                    className="border border-black-400 w-9 h-9 rounded-md flex items-center justify-center"
                  >
                    <ChevronLeft
                      className={`${currentPage === 1 ? 'text-black-400' : ''} h-6 w-6`}
                    />
                  </button>

                  {/* <div className="flex items-center">
                    <div className="border border-black-400 w-9 h-9 flex items-center justify-center rounded-md">
                      {currentPage}
                    </div>
                  </div> */}

                  <Input
                    className="w-9 h-9 rounded-md flex items-center justify-center min-w-[50px] text-center"
                    value={inputPage}
                    type="number"
                    onChange={(e: any) => {
                      const value = e.target.value;
                      if (value === '0') return;
                      setInputPage(value);
                    }}
                    onKeyDown={(e: any) => {
                      if (e.key === 'Enter') {
                        const value = e.target.value;
                        if (value === '') {
                          setCurrentPage(1);
                          return;
                        }
                        const numValue = parseInt(value);
                        if (!isNaN(numValue)) {
                          if (numValue > totalPages) {
                            setCurrentPage(1);
                          } else if (numValue > 0) {
                            setCurrentPage(numValue);
                          }
                        }
                      }
                    }}
                    onBlur={() => {
                      const value = inputPage;
                      if (value === '' || parseInt(value) > totalPages) {
                        setCurrentPage(1);
                      } else {
                        const numValue = parseInt(value);
                        if (!isNaN(numValue) && numValue > 0) {
                          setCurrentPage(numValue);
                        }
                      }
                    }}
                  />

                  <button
                    onClick={goToNextPage}
                    disabled={currentPage === totalPages}
                    className="border border-black-400 w-9 h-9 rounded-md flex items-center justify-center"
                  >
                    <ChevronRight
                      className={`${currentPage === totalPages ? 'text-black-400' : ''} h-6 w-6`}
                    />
                  </button>

                  <span className="text-[16px] max-639:text-[14px] leading-[19px] max-639:leading-[16px] font-inter font-normal text-black-100">
                    of {totalPages}
                  </span>
                </div>
              </div>
            ) : (
              <></>
            )}
            {!isLoadingAllCompetionsData &&
              allCompetionsDataDetails?.length === 0 &&
              renderNotDataText(Number(eventStatus))}
          </div>
        </div>
      </div>
    </div>
  );
}
