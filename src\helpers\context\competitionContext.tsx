'use client';
import type {
  QueryObserverResult,
  RefetchOptions,
} from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'next/navigation';
import type { Dispatch, ReactNode, SetStateAction } from 'react';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';

import { quyerKeys } from '@/lib/queryKeys';

import type { CompetitionStatusProps, Player } from '../../../types';
import type {
  CompetitionResponse,
  DreamTeam,
  Event,
  EventResponse,
  EventTimeStatus,
  FantasyTeamResponse,
  GetFavouriteTeamResponse,
  GetPlayersResponse,
  PlayersByRoleLimit,
  PlayersByRoleType,
} from '../../../types/competitions';
import { LIVE_POLLIN_TIME } from '../constants/index';
import {
  getAllCompetitions,
  getCompetition,
  getDreamTeam,
  getFavouriteTeam,
  getPlayers,
} from '../fetchers/competitions';
import type { PlayersByRole } from './createTeamContext';
import {
  RugbyLeaguePlayersByRole,
  RugbyPlayer,
} from '../../../types/rugby-league/index';
import { footballPlayersByRole } from '../../../types/football';
import { getAFLPlayersList } from '@/lib/utils';
import {
  SoccerPlayer,
  SoccerPlayerRole,
} from './soccer/createSoccerTeamContext';
import { useDebounce } from 'use-debounce';

type CompetitionContextType = {
  allCompetions: Event[] | undefined;
  eventDetailsResponse: CompetitionResponse | undefined;
  eventDetailsResponseLoading: boolean;
  playerListResponse: GetPlayersResponse | undefined;
  isPlayerListResponseLoading: boolean;
  playerByRole: PlayersByRoleType;
  playerByRoleLimit: PlayersByRoleLimit;
  isLoadingAllCompetionsData: boolean;
  allCompetionsData: EventResponse | undefined;
  favouriteTeamResponse: GetFavouriteTeamResponse | undefined;
  refetchAllComp: (
    options?: RefetchOptions,
  ) => Promise<QueryObserverResult<EventResponse, Error>>;
  dreamTeamResponse: FantasyTeamResponse | undefined;
  setActiveTeam: Dispatch<SetStateAction<DreamTeam | undefined>>;
  activeTeam: DreamTeam | undefined;
  isDreamTeamResponseLoading: boolean;
  eventTimeStatus: EventTimeStatus;
  setEventTimeStatus: Dispatch<SetStateAction<EventTimeStatus>>;
  refetchDreamTeam: (
    options?: RefetchOptions,
  ) => Promise<QueryObserverResult<FantasyTeamResponse, Error>>;
  rugbyLeaguePlayersByRole: RugbyLeaguePlayersByRole;
  AFLPlayersByRoles: footballPlayersByRole;
  allSoccerPlayers: Player[];
  totalItems: number;
  setTotalItems: Dispatch<SetStateAction<number>>;
  currentPage: number;
  setCurrentPage: Dispatch<SetStateAction<number>>;
  itemsPerPage: number;
  setItemsPerPage: Dispatch<SetStateAction<number>>;
  reservePlayersCount: number;
};

const competitionContext = createContext<CompetitionContextType | undefined>(
  undefined,
);

const initialTimeStatus: EventTimeStatus = {
  isLockout: false,
  isLive: false,
  isCompleted: false,
};
const CompetitionProvider = ({ children }: { children: ReactNode }) => {
  // Other context
  // Query Params
  const searchParams = useSearchParams();
  const event_id = searchParams.get('event_id');
  const sport_id = searchParams.get('sport_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const contestType = searchParams.get('contestType');
  const maxEntryStart = searchParams.get('maxEntryStart');
  const maxEntryEnd = searchParams.get('maxEntryEnd');
  const prizeStart = searchParams.get('prizeStart');
  const prizeEnd = searchParams.get('prizeEnd');
  const status = searchParams.get('status');
  const dreamTeamId = searchParams.get('dreamTeamId');
  const applyFilter = searchParams.get('applyFilter');
  const compType = searchParams.get('compType');
  const teamId = searchParams.get('teamId');
  const tournamentId = searchParams.get('tournamentId');
  const startDate = searchParams.get('date');
  const filter_sport_id = searchParams.get('filter_sport_id');

  const [activeTeam, setActiveTeam] = useState<DreamTeam | undefined>(
    undefined,
  );

  const [eventTimeStatus, setEventTimeStatus] =
    useState<EventTimeStatus>(initialTimeStatus);

  // Add pagination state
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [debouncedCurrentPage] = useDebounce(currentPage, 500);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Get All Comptitions
  const {
    data: allCompetionsData,
    isLoading: isLoadingAllCompetionsData,
    refetch: refetchAllComp,
  } = useQuery({
    queryFn: () =>
      filter_sport_id
        ? getAllCompetitions(
          contestType,
          maxEntryStart,
          maxEntryEnd,
          prizeStart,
          prizeEnd,
          status,
          compType,
          filter_sport_id,
          teamId,
          tournamentId,
          startDate,
          currentPage,
          itemsPerPage,
        )
        : getAllCompetitions(
          contestType,
          maxEntryStart,
          maxEntryEnd,
          prizeStart,
          prizeEnd,
          status,
          compType,
          sport_id,
          teamId,
          tournamentId,
          startDate,
          currentPage,
          itemsPerPage,
        ),
    queryKey: [
      quyerKeys.getAllCompetitons,
      status,
      applyFilter,
      sport_id,
      contestType,
      teamId,
      tournamentId,
      startDate,
      filter_sport_id,
      currentPage,
      itemsPerPage,
      compType,
    ],
  });
  const allCompetions: Event[] | undefined = allCompetionsData?.result;
  const [eventStatus, setEventStatus] = useState<CompetitionStatusProps>();

  // Update total items when data changes
  useEffect(() => {
    if (allCompetionsData) {
      setTotalItems(allCompetionsData?.count);
    }
  }, [allCompetionsData]);

  // GET single competions
  const {
    data: eventDetailsResponse,
    isFetching: eventDetailsResponseLoading,
  } = useQuery({
    queryKey: [quyerKeys.getCompetiton],
    queryFn: () => getCompetition(event_id, sport_id, competition_id),
    enabled: !!event_id,
    refetchInterval: () => {
      if (competition_id && eventStatus === 'inprogress') {
        return LIVE_POLLIN_TIME;
      } else {
        return false;
      }
    },
  });

  //GET Dream Team

  const seasonId =
    searchParams.get('seasonId') ??
    `${eventDetailsResponse?.result?.eventDetails?.CricketSeason?.id}`;

  const {
    data: dreamTeamResponse,
    isFetching: isDreamTeamResponseLoading,
    refetch: refetchDreamTeam,
  } = useQuery({
    queryKey: [quyerKeys.getDreamTeam, activeTeam?.id, dreamTeamId],
    queryFn: () =>
      getDreamTeam(activeTeam?.id! || +dreamTeamId!, tournament_id, seasonId),
    enabled: !!activeTeam?.id && !!tournament_id,
    staleTime: 0,

    refetchInterval: () =>
      eventDetailsResponse?.result?.eventDetails?.status === 'inprogress'
        ? LIVE_POLLIN_TIME
        : false,
  });

  // GET all player list
  const { data: playerListResponse, isLoading: isPlayerListResponseLoading } = useQuery({
    queryKey: [quyerKeys.getAllPlayers, seasonId],
    queryFn: () =>
      getPlayers(
        tournament_id,
        sport_id,
        event_id,
        competition_id,
        null,
        seasonId,
      ),
    enabled: !!tournament_id && !!event_id,
  });


  // GET favourite team
  const { data: favouriteTeamResponse } = useQuery({
    queryKey: [quyerKeys.getFavouriteTeam],
    queryFn: () => getFavouriteTeam(event_id, sport_id, competition_id),
    enabled: !!event_id,
  });

  // Cricket Players

  const BAT =
    playerListResponse?.result?.filter(
      (player) =>
        player.role === 'Batter' || player.role === 'Batting Allrounder' || player.role === 'WK-Batsman' || player.role === 'WK-Batter',
    ) || [];
  const WKP =
    playerListResponse?.result?.filter(
      (player) => player.role === 'WK-Batter' || player.role === 'WK-Batsman',
    ) || [];

  const BOW =
    playerListResponse?.result?.filter(
      (player) =>
        player.role === 'Bowler' || player.role === 'Bowling Allrounder',
    ) || [];

  const ALL =
    playerListResponse?.result?.filter(
      (player) =>
        player.role === 'Batting Allrounder' ||
        player.role === 'Bowling Allrounder',
    ) || [];

  // Rugby Players

  const rugbyLeaguePlayersByRole: RugbyLeaguePlayersByRole = {
    // @ts-ignore
    BAC: playerListResponse?.result
      ?.filter(
        (player) =>
          player.role === 'Wing' ||
          player.role === 'Fullback' ||
          player.role === 'Full Back' ||
          player.role === 'FB' ||
          player?.role === 'Centre' ||
          player?.role === 'Utility' ||
          player?.role === 'Outside Back' ||
          player.role === 'Back' ||
          player.role === 'Interchange'
      )

      .filter((player) => player.lineupStatus !== 'unannounced') ?? [],
    // @ts-ignore
    HAL: playerListResponse?.result
      ?.filter(
        (player) =>
          player.role === 'Halfback' ||
          player.role === 'half back' ||
          player?.role === 'Utility' ||
          player.role === 'Five Eighth' ||
          player.role === 'Five-Eighth' ||
          player.role === 'Interchange'
      )
      .filter((player) => player.lineupStatus !== 'unannounced') ?? [],
    // @ts-ignore
    BR: playerListResponse?.result
      ?.filter(
        (player) =>
          player.role === 'Back Row' ||
          player?.role === 'Utility' ||
          player.role === 'Lock' ||
          player?.role === 'Second Row' ||
          player.role === 'Interchange'
      )
      .filter((player) => player.lineupStatus !== 'unannounced') ?? [],
    // @ts-ignore
    FRF: playerListResponse?.result
      ?.filter(
        (player) =>
          player.role === 'Prop Forward' ||
          player.role === 'Prop' ||
          player?.role === 'Utility' ||
          player.role === 'Hooker' ||
          player.role === 'Interchange'
      )
      .filter((player) => player.lineupStatus !== 'unannounced') ?? [],
    // @ts-ignore
    IC: playerListResponse?.result
      .filter((player) => player.lineupStatus !== 'unannounced') ?? [],
  };

  // AFL player filters

  const playerByRole: PlayersByRole = {
    BAT,
    WKP,
    BOW,
    ALL,
  };

  // @ts-ignore
  // const AFLPlayersByRoles = getAFLPlayersList(playerListResponse?.result ?? []);

  const AFLPlayersByRoles: footballPlayersByRole = {
    // @ts-ignore
    BL:
      playerListResponse?.result.filter(
        (player) =>
          player.role === 'Defender' ||
          player.role === 'Key Defender' ||
          player.role === 'Back Pocket Left' ||
          player.role === 'Back Pocket Right' ||
          player.role === 'Full Back' ||
          player.role === 'Interchange' ||
          player.role === 'Back Pocket',
      ).filter((player) => player.lineupStatus !== 'unannounced') ?? [],
    // @ts-ignore
    FL:
      playerListResponse?.result.filter(
        (player) =>
          player.role === 'Forward' ||
          player.role === 'Key Forward' ||
          player.role === 'Forward Pocket Right' ||
          player.role === 'Forward Pocket Left' ||
          player.role === 'Full Forward' ||
          player.role === 'Forward Pocket' ||
          player.role === 'Interchange' ||
          player.role === 'Forward-Pocket' ||
          player.role === 'Full-Forward',
      ).filter((player) => player.lineupStatus !== 'unannounced') ?? [],
    // @ts-ignore
    HBL:
      playerListResponse?.result.filter(
        (player) =>
          player.role === 'Defender' ||
          player.role === 'Key Defender' ||
          player.role === 'Half Back Flank Right' ||
          player.role === 'Centre Half Back' ||
          player.role === 'Half Back Flank Left' ||
          player.role === 'Half Back Flank Righ' ||
          player.role === 'Interchange' ||
          player.role === 'Half-Back' ||
          player.role === 'Centre Half-Back',
      ).filter((player) => player.lineupStatus !== 'unannounced') ?? [],
    // @ts-ignore
    HFL:
      playerListResponse?.result.filter(
        (player) =>
          player.role === 'Half Forward Flank L' ||
          player.role === 'Half Forward Flank R' ||
          player.role === 'Midfielder' ||
          player.role === 'Centre Half Forward' ||
          player.role === 'Interchange' ||
          player.role === 'Half-Forward' ||
          player.role === 'Centre Half-Forward',
      ).filter((player) => player.lineupStatus !== 'unannounced') ?? [],
    // @ts-ignore
    MID:
      playerListResponse?.result.filter(
        (player) =>
          player.role === 'Midfielder' ||
          player.role === 'Wing Left' ||
          player.role === 'Wing Right' ||
          player.role === 'Interchange',
      ).filter((player) => player.lineupStatus !== 'unannounced') ?? [],
    // @ts-ignore
    FOL:
      playerListResponse?.result.filter(
        (player) =>
          player.role === 'Ruck' ||
          player.role === 'Ruck Rover' ||
          player.role === 'Rover' ||
          player.role === 'Forward' ||
          player.role === 'Interchange',
      ).filter((player) => player.lineupStatus !== 'unannounced') ?? [],
    // @ts-ignore
    IC: playerListResponse?.result.filter((player) => player.lineupStatus !== 'unannounced') ?? [],
  };

  const reservePlayersCount = eventDetailsResponse?.result?.reservePlayersCount || 0;


  // Tournament Teamlimit
  const playerByRoleLimit: PlayersByRoleLimit = {
    ALL:
      eventDetailsResponse?.result?.sportRule?.find(
        (rule) => rule.positionType === 'allRounder',
      )?.minPlayer ?? 0,

    BAT:
      eventDetailsResponse?.result?.sportRule?.find(
        (rule) => rule.positionType === 'batsman',
      )?.minPlayer ?? 0,
    BOW:
      eventDetailsResponse?.result?.sportRule?.find(
        (rule) => rule.positionType === 'bowler',
      )?.minPlayer ?? 0,
    WKP:
      eventDetailsResponse?.result?.sportRule?.find(
        (rule) => rule.positionType === 'wicketKeeper',
      )?.minPlayer ?? 0,
  };

  // Soccer Players

  const G =
    playerListResponse?.result
      ?.filter((player) => player.role === 'G')
      .map((player) => ({ ...player, role: 'GK' })) || [];

  const D =
    playerListResponse?.result
      ?.filter((player) => player.role === 'D')
      .map((player) => ({ ...player, role: 'DEF' })) || [];

  const M =
    playerListResponse?.result
      ?.filter((player) => player.role === 'M')
      .map((player) => ({ ...player, role: 'MID' })) || [];

  const F =
    playerListResponse?.result
      ?.filter((player) => player.role === 'F')
      .map((player) => ({ ...player, role: 'FWD' })) || [];

  // @ts-ignore
  const allSoccerPlayers: SoccerPlayer[] = [...G, ...D, ...M, ...F];

  // Rugby PlayerRole By Limits

  const [defaultDreamTeam] = eventDetailsResponse?.result?.dreamTeams || [];

  useEffect(() => {
    if (
      // activeTeam &&
      // defaultDreamTeam &&
      // activeTeam.id !== defaultDreamTeam.id
      defaultDreamTeam
    ) {
      setActiveTeam(defaultDreamTeam);
    }
  }, [defaultDreamTeam]); // Include activeTeam in dependencies

  // Set Event Status
  useEffect(() => {
    setEventStatus(eventDetailsResponse?.result?.eventDetails?.status);
  }, [eventDetailsResponse?.result?.eventDetails?.status]);

  // GET Dream Team

  const competitionState = useMemo(
    (): CompetitionContextType => ({
      allCompetions,
      eventDetailsResponse,
      eventDetailsResponseLoading,
      playerListResponse,
      isPlayerListResponseLoading,
      playerByRole,
      playerByRoleLimit,
      isLoadingAllCompetionsData,
      allCompetionsData,
      favouriteTeamResponse,
      eventTimeStatus,
      dreamTeamResponse,
      activeTeam,
      isDreamTeamResponseLoading,
      refetchAllComp,
      setActiveTeam,
      setEventTimeStatus,
      refetchDreamTeam,
      rugbyLeaguePlayersByRole,
      AFLPlayersByRoles,
      // @ts-ignore
      allSoccerPlayers,
      totalItems,
      setTotalItems,
      currentPage,
      setCurrentPage,
      itemsPerPage,
      setItemsPerPage,
      reservePlayersCount,
    }),
    [
      allCompetions,
      eventDetailsResponse,
      eventDetailsResponseLoading,
      playerListResponse,
      isPlayerListResponseLoading,
      playerByRole,
      playerByRoleLimit,
      isLoadingAllCompetionsData,
      allCompetionsData,
      favouriteTeamResponse,
      eventTimeStatus,
      dreamTeamResponse,
      activeTeam,
      isDreamTeamResponseLoading,
      refetchAllComp,
      setActiveTeam,
      setEventTimeStatus,
      refetchDreamTeam,
      rugbyLeaguePlayersByRole,
      AFLPlayersByRoles,
      allSoccerPlayers,
      totalItems,
      currentPage,
      itemsPerPage,
      reservePlayersCount,
    ],
  );

  return (
    <competitionContext.Provider value={competitionState}>
      {children}
    </competitionContext.Provider>
  );
};

export const useCompetition = () => {
  const context = useContext(competitionContext);
  if (!context) {
    throw new Error('useCompetition must be used within a CompetitionProvider');
  }
  return context;
};

export default CompetitionProvider;
