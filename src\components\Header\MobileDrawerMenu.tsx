import Link from 'next/link';
import React from 'react';

import { identifiers } from '@/helpers/constants/identifier';

const MobileDrawerMenu = () => {
  const menuItems = identifiers.mainMenu;

  return (
    <nav className="text-white pt-8">
      <ul className="">
        {menuItems?.map((item) => (
          <li key={item?.id}>
            <Link
              href={item?.url || ''}
              className="block py-2 px-4 text-base leading-5 font-semibold"
            >
              {item?.name}
            </Link>
            {item?.submenu && item?.submenu?.length > 0 && (
              <ul className="ml-3">
                {item?.submenu?.map((subitem) => (
                  <li key={subitem?.id}>
                    <Link
                      href={subitem?.url}
                      className="block py-2 px-4 text-base leading-5"
                    >
                      {subitem?.name}
                    </Link>
                  </li>
                ))}
              </ul>
            )}
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default MobileDrawerMenu;
