'use client';

import React from 'react';

const OddsArrow = ({ color }: { color: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="114.63"
      height="25.923"
      viewBox="0 0 114.63 25.923"
    >
      <g id="Group_104257" data-name="Group 104257" transform="translate(30)">
        <g id="Group_104258" data-name="Group 104258" transform="translate(0)">
          <path
            id="Path_179027"
            data-name="Path 179027"
            d="M-15.587,19.806a.752.752,0,0,1-.442-.143L-29.69,9.755A.753.753,0,0,1-30,9.117a.753.753,0,0,1,.358-.612L-15.981.112A.751.751,0,0,1-15.22.1a.754.754,0,0,1,.385.657V4.578H34.077a.753.753,0,1,1,0,1.506H-15.587a.752.752,0,0,1-.753-.753V2.1L-27.894,9.2l11.554,8.379V12.961a.752.752,0,0,1,.753-.753H34.077a.753.753,0,1,1,0,1.506H-14.834v5.34a.752.752,0,0,1-.411.671.75.75,0,0,1-.342.082"
            transform="translate(0.001 0)"
            fill={color}
          />
          <path
            id="Path_179028"
            data-name="Path 179028"
            d="M145.778,49.268a.752.752,0,0,1-.753-.753V44.691H96.113a.753.753,0,1,1,0-1.506h49.666a.752.752,0,0,1,.753.753v3.231l11.554-7.1-11.554-8.379v4.615a.752.752,0,0,1-.753.753H96.113a.753.753,0,0,1,0-1.506h48.913V30.215a.753.753,0,0,1,1.195-.61l13.661,9.907a.753.753,0,0,1-.048,1.251l-13.661,8.393a.752.752,0,0,1-.394.112"
            transform="translate(-75.561 -23.345)"
            fill={color}
          />
        </g>
      </g>
    </svg>
  );
};

export default OddsArrow;
