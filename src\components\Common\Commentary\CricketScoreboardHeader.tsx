import Image from 'next/image';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Config } from '../../../helpers/context/config';

export default function CricketScoreboardHeader() {
  const { eventDetailsResponse } = useCompetition();
  const homeTeam = eventDetailsResponse?.result?.eventDetails?.homeTeam;
  const awayTeam = eventDetailsResponse?.result?.eventDetails?.awayTeam;
  const scoreBoard = eventDetailsResponse?.result?.eventDetails?.ScoreBoard;
  const homeTeamScore = scoreBoard?.Tr1C1;
  const awayTeamScore = scoreBoard?.Tr2C1;
  const requiredRuns = scoreBoard?.Epr;
  const remainingBalls = scoreBoard?.Ebat;

  return (
    <div className="w-full bg-[url('/fantasy/images/cricketHeaderBg.png')] bg-cover p-6 font-veneerCleanSoft font-normal">
      <div className="mx-auto max-w-6xl">
        <div className="flex items-center justify-center md:gap-[60px] gap-0 px-10 md:px-0 text-white mt-[18px]">
          {/* Home Team */}
          <div className="flex flex-col items-center">
            <span className="md:text-[16px] text-[12px] font-medium tracking-wider">
              {homeTeam?.name?.split(' ')[0]?.toUpperCase()}
            </span>
            <div className="flex items-center gap-2 mt-[-10px]">
              <span className="md:text-[31.36px] text-[22px] tracking-wider">
                {homeTeam?.name?.split(' ')[1]?.toUpperCase()}
              </span>
              <Image
                src={`${Config.mediaURL}${homeTeam?.flag}`}
                alt={`${homeTeam?.name} Logo`}
                width={32}
                height={32}
                className="h-8 w-8 object-contain"
                unoptimized
              />
            </div>
          </div>

          {/* Score */}
          <div className="mx-2 md:mx-0 md:text-[31.36px] text-[22px] md:tracking-wider tracking-normal">
            {homeTeamScore}/{awayTeamScore}
          </div>

          {/* Away Team */}
          <div className="flex flex-col items-center">
            <span className="md:text-[16px] text-[12px] font-medium tracking-wider">
              {awayTeam?.name?.split(' ')[0]?.toUpperCase()}
            </span>
            <div className="flex items-center gap-2 mt-[-10px]">
              <Image
                src={`${Config.mediaURL}${awayTeam?.flag}`}
                alt={`${awayTeam?.name} Logo`}
                width={32}
                height={32}
                className="h-8 w-8 object-contain"
                unoptimized
              />
              <span className="md:text-[31.36px] text-[22px] tracking-wider">
                {awayTeam?.name?.split(' ')[1]?.toUpperCase()}
              </span>
            </div>
          </div>
        </div>

        {/* Required Runs */}
        <div className="mt-[7px] text-center text-base text-gray-400 font-inter mb-[18px]">
          {scoreBoard?.ECo}
        </div>
      </div>
    </div>
  );
}
