import {
  SoccerPlayer,
  SoccerPlayerRole,
} from '@/helpers/context/soccer/createSoccerTeamContext';

interface SoccerPlayerScoreProps {
  score: number;
  isPlayerLocked: boolean;
  player: SoccerPlayer;
  setPlayerRoleToCaptain: () => void;
  setPlayerRoleToViceCaptain: () => void;
  activeTab: SoccerPlayerRole;
}

const SoccerPlayerScore = ({
  score,
  isPlayerLocked,
  player,
  setPlayerRoleToCaptain,
  setPlayerRoleToViceCaptain,
  activeTab,
}: SoccerPlayerScoreProps) => {
  return (
    <div>
      <div className="flex items-center gap-1">
        <div className="flex flex-col items-end">
          <div className="flex items-center gap-1">
            {score}
            {player.isCaptain && (
              <button
                className="w-5 h-5 bg-[#FC4714] flex flex-col justify-center items-center text-white rounded-full"
                onClick={() => setPlayerRoleToCaptain()}
                disabled={isPlayerLocked}
              >
                C
              </button>
            )}
            {player.isViceCaptain && (
              <button
                className="w-5 h-5 bg-[#003764] flex flex-col justify-center items-center text-white rounded-full"
                onClick={() => setPlayerRoleToViceCaptain()}
                disabled={isPlayerLocked}
              >
                VC
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SoccerPlayerScore;
