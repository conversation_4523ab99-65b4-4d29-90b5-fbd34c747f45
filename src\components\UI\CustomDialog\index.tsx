import { cn } from '@/lib/utils';
import type { Variants } from 'framer-motion';
import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';

// Define animation variants
const dialogVariants: Record<string, Variants> = {
  bottomToTop: {
    initial: {
      opacity: 0,
      y: '100%',
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 300,
      },
    },
    exit: {
      opacity: 0,
      y: '100%',
      transition: {
        duration: 0.2,
      },
    },
  },
  fadeIn: {
    initial: {
      opacity: 0,
      scale: 0.9,
    },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        type: 'tween',
      },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.2,
      },
    },
  },
  slideFromRight: {
    initial: {
      opacity: 0,
      x: '100%',
    },
    animate: {
      opacity: 1,
      x: 0,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 300,
      },
    },
    exit: {
      opacity: 0,
      x: '100%',
      transition: {
        duration: 0.2,
      },
    },
  },
};

// Extended props to include animation configuration
interface CustomDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title?: React.ReactNode;
  children: React.ReactNode;
  backgroundClass?: string;
  maxWidth?: string | number;
  className?: string;
  outerClickClose?: boolean;
  animationType?: keyof typeof dialogVariants;
  animationProps?: Partial<Variants>;
  titleClassName?: string;
}

const CustomDialog: React.FC<CustomDialogProps> = ({
  isOpen,
  onClose,
  title,
  children,
  backgroundClass = 'bg-white',
  maxWidth = '600px',
  className = '',
  outerClickClose = false,
  animationType = 'bottomToTop',
  animationProps = {},
  titleClassName = '',
}) => {
  const inlineMaxWidth =
    typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (outerClickClose && e.target === e.currentTarget) {
      onClose();
    }
  };

  // Merge default variant with any custom animation props
  const mergedVariants = {
    ...dialogVariants[animationType],
    ...animationProps,
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 flex items-center justify-center z-[1200] bg-black-100 bg-opacity-50"
          onClick={handleOverlayClick}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className={`${backgroundClass} ${className} w-full m-2 rounded-lg shadow-lg max-h-[calc(100%-64px)]`}
            style={{ maxWidth: inlineMaxWidth }}
            onClick={(e) => e.stopPropagation()}
            // @ts-expect-error
            variants={mergedVariants}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <div
              className={`${title
                ? 'justify-between bg-gray-100 rounded-tl-lg rounded-tr-lg'
                : 'justify-end'
                } flex items-center p-2 header-wrap`}
            >
              {title && (
                <div className="w-full">
                  <h2 className={cn(
                    'uppercase text-[22.4px] leading-[28px] font-normal font-veneerCleanSoft header-text text-left',
                    titleClassName
                  )}>
                    {title}
                  </h2>
                </div>
              )}
              <button
                aria-label="Close"
                onClick={onClose}
                className="close-icon text-gray-500 hover:text-gray-700"
                style={{ zIndex: 1000 }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  className="h-6 w-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <div className="p-4 overflow-y-auto max-h-[calc(100vh-180px)] dialog-details">
              {children}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CustomDialog;
