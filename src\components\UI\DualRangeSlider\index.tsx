'use client';

import * as SliderPrimitive from '@radix-ui/react-slider';
import * as React from 'react';

import { cn, generateUniqueId } from '@/lib/utils';

interface DualRangeSliderProps
  extends React.ComponentProps<typeof SliderPrimitive.Root> {
  labelPosition?: 'top' | 'bottom';
  label?: (value: number | undefined) => React.ReactNode;
  rangeColor?: string; // Add rangeColor prop
}

const DualRangeSlider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  DualRangeSliderProps
>(
  (
    {
      className,
      label,
      labelPosition = 'top',
      rangeColor = 'bg-secondary-100',
      ...props
    },
    ref,
  ) => {
    const initialValue = Array.isArray(props.value)
      ? props.value
      : [props.min, props.max];

    return (
      <SliderPrimitive.Root
        ref={ref}
        className={cn(
          'relative flex w-full touch-none select-none items-center',
          className,
        )}
        {...props}
      >
        <SliderPrimitive.Track className="relative h-[9px] w-full grow overflow-hidden rounded-full bg-black-500">
          <SliderPrimitive.Range
            className={cn('absolute h-full', rangeColor)}
          />
        </SliderPrimitive.Track>
        {initialValue.map((value, index) => (
          <React.Fragment key={generateUniqueId()}>
            <SliderPrimitive.Thumb className="relative block bg-white h-5 w-5 rounded-full bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
              {label && (
                <span
                  className={cn(
                    'absolute flex w-full justify-center text-[14px] leading-[16px] font-inter font-normal text-black-700',
                    labelPosition === 'top' && '-top-7',
                    labelPosition === 'bottom' && 'top-6',
                  )}
                >
                  {label(value)}
                </span>
              )}
            </SliderPrimitive.Thumb>
          </React.Fragment>
        ))}
      </SliderPrimitive.Root>
    );
  },
);
DualRangeSlider.displayName = 'DualRangeSlider';

export { DualRangeSlider };
