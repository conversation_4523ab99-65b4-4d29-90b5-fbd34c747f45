import axiosInstance from '@/helpers/axios/axiosInstance';
import { Config } from '@/helpers/context/config';

interface SoccerEvent {
  Min: string;
  Txt: string;
  MinEx: number;
  IT: number;
  commentryType: string;
}

interface ApiEvent {
  team?: string;
  type?: string;
  period?: string;
  minute?: string;
  min?: string;
  player?: string;
  player_name?: string;
  // Soccer specific properties
  Min?: string;
  Txt?: string;
  MinEx?: number;
  IT?: number;
  commentryType?: string;
}

interface NrlEvent extends ApiEvent {
  min: string;
  player_name?: string;
}

interface AflEvent extends ApiEvent {
  period: string;
  minute: string;
  player?: string;
}

interface CommentaryResponse {
  status: boolean;
  result: Array<{
    time: string;
    type: string;
    description: string;
    teamName?: string;
    teamFlag?: string;
    playerName?: string;
  }>;
  matchInfo?: {
    localteam: {
      name: string;
      score: string | number;
      flag?: string;
    };
    visitorteam: {
      name: string;
      score: string | number;
      flag?: string;
    };
    status: {
      value: string;
    };
  };
}

export const fetchMatchCommentary = async (
  matchId?: string,
  sportId?: number,
): Promise<CommentaryResponse> => {
  if (!matchId || !sportId) {
    return { status: false, result: [] };
  }

  let endpoint = '';

  // Soccer API
  if (sportId === 8) {
    endpoint = `public/soccer/commentary/${matchId}`;
  }
  // Determine which API endpoint to call based on sport
  else if (sportId === 12) {
    // NRL
    endpoint = `public/rls/commentary/${matchId}`;
  } else if (sportId === 9) {
    // AFL
    endpoint = `public/ar/commentary/${matchId}`;
  } else {
    throw new Error('Unsupported sport type');
  }

  const response = await axiosInstance.get(Config.baseURL + endpoint);

  // Handle different response structures based on sport
  let events: ApiEvent[] = [];

  if (sportId === 8) {
    // Soccer - data is directly in result array
    if (!response.data?.result) {
      return { status: true, result: [] };
    }
    events = response.data.result as SoccerEvent[];
  } else {
    // NRL and AFL - data is in events.event
    if (!response.data?.result?.events?.event) {
      return { status: true, result: [] };
    }
    events = Array.isArray(response.data.result.events.event)
      ? response.data.result.events.event
      : [response.data.result.events.event];
  }

  // Sort events appropriately based on sport
  if (sportId === 8) {
    // Soccer
    // (events as SoccerEvent[]).sort((a, b) => {
    //   const minuteA = parseInt(a.Min || '0');
    //   const minuteB = parseInt(b.Min || '0');
    //   return minuteB - minuteA; // Latest minute first
    // });
  } else if (sportId === 12) {
    // NRL
    (events as NrlEvent[]).sort(
      (a, b) => parseInt(b.min || '0') - parseInt(a.min || '0'),
    );
  } else if (sportId === 9) {
    // AFL
    (events as AflEvent[]).sort((a, b) => {
      const periodA = parseInt(a.period || '0');
      const periodB = parseInt(b.period || '0');

      if (periodA !== periodB) {
        return periodB - periodA; // Latest period first
      }

      return parseInt(b.minute || '0') - parseInt(a.minute || '0'); // Latest minute first
    });
  }

  // Transform events into the format expected by the LiveUpdates component
  const formattedResults = events.map((event: ApiEvent) => {
    if (sportId === 8) {
      // Soccer
      const soccerEvent = event as SoccerEvent;
      const teamName = soccerEvent.Txt.split(':')[0].trim();
      const playerInfo = soccerEvent.Txt.split('-')[1]?.trim() || '';

      return {
        time: `${soccerEvent.Min}'`,
        type: soccerEvent.commentryType,
        description: soccerEvent.Txt,
        teamName,
        teamFlag: '', // You'll need to add team flag logic if available
        playerName: playerInfo,
      };
    }

    const team =
      event.team === 'localteam' || event.team === 'hometeam'
        ? response.data.result.localteam
        : response.data.result.visitorteam;

    let time, description;

    // Format time based on sport
    if (sportId === 9) {
      // AFL
      const aflEvent = event as AflEvent;
      time = `Q${aflEvent.period || '0'} ${aflEvent.minute || '0'}'`;

      // Generate description based on event type
      switch (event.type) {
        case 'goal':
          description = `${aflEvent.player || 'Player'} kicks a goal for ${team.name}.`;
          break;
        case 'behind':
          description = `${aflEvent.player || 'Player'} kicks a behind for ${team.name}.`;
          break;
        case 'rushed':
          description = `Rushed behind for ${team.name}.`;
          break;
        default:
          description = `${event.type}: ${aflEvent.player || ''}, ${team.name}.`;
      }
    } else {
      // NRL
      const nrlEvent = event as NrlEvent;
      time = `${nrlEvent.min || '0'}'`;

      // Generate description based on event type
      switch (event.type) {
        case 'Try':
          description = `${nrlEvent.player_name || 'Player'} goes over for ${team.name} to add four points to their tally.`;
          break;
        case 'Penalty Try':
          description = `${nrlEvent.player_name || 'Player'} awarded a penalty try for ${team.name}.`;
          break;
        case 'Conversion':
          description = `${nrlEvent.player_name || 'Player'} converts for ${team.name}.`;
          break;
        default:
          description = `${event.type}: ${nrlEvent.player_name || ''}, ${team.name}.`;
      }
    }

    return {
      time,
      type: event.type || '',
      description,
      teamName: team.name,
      teamFlag: team.flag,
      playerName:
        sportId === 9
          ? (event as AflEvent).player
          : (event as NrlEvent).player_name,
    };
  });

  // Extract match information for header display
  const matchInfo = {
    localteam: {
      name: response.data.result.localteam?.name || '',
      score: response.data.result.localteam?.score || 0,
      flag: response.data.result.localteam?.flag || '',
    },
    visitorteam: {
      name: response.data.result.visitorteam?.name || '',
      score: response.data.result.visitorteam?.score || 0,
      flag: response.data.result.visitorteam?.flag || '',
    },
    status: {
      value: response.data.result.status?.value || '',
    },
  };

  return {
    status: true,
    result: formattedResults,
    matchInfo,
  };
};
