import Image from 'next/image';
import React from 'react';

import { useCompetition } from '@/helpers/context/competitionContext';
import { generateUniqueId } from '@/lib/utils';

import Loader from '../Loader';

// Define interfaces for API response structure

interface DrawPool {
  coins: number;
  drawType: string;
  drawNumber: number;
  description: string;
}

interface EventConfiguration {
  prizePool?: number;
  drawPool?: DrawPool[];
}

// Define interfaces for mapped data
interface PrizeItem {
  title: string;
  description: string;
  amount: number;
}

interface DrawsSection {
  title: string;
  subtitle?: string;
  items: PrizeItem[];
}

const Section: React.FC<DrawsSection> = ({ title, subtitle, items }) => (
  <div className="bg-white rounded-lg shadow-[0px_1px_3px_0px_#0000002b] p-2 mb-6">
    {/* <div className="mb-4">
      <h2 className="text-xl font-semibold mb-1">{title}</h2>
      {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
    </div> */}
    <div className="grid grid-flow-col grid-rows-10 auto-rows-auto gap-x-3 max-799:grid-flow-row max-799:gap-y-3">
      {items?.map((item, index) => (
        <div
          key={generateUniqueId()}
          className="flex justify-between items-center border border-gray-100"
        >
          <div className="flex-1 bg-gray-50 p-4 border-r border-gray-100 w-[80%]">
            <h3 className="font-bold mb-1">
              {item.title.toLocaleLowerCase()} Prize
            </h3>
          </div>
          <div className="flex items-center justify-center space-x-1 px-4 w-[20%]">
            <div className="min-w-[20px]">
              <Image
                src={'/fantasy/images/smartbCoin.svg'}
                width={20}
                height={20}
                alt={'coins'}
                unoptimized={true}
              />
            </div>
            <span className="font-semibold">
              {item?.amount?.toLocaleString() || 0}
            </span>
          </div>
        </div>
      ))}
    </div>
  </div>
);

const PrizeDrawsDisplay = () => {
  const { eventDetailsResponse, eventDetailsResponseLoading } =
    useCompetition();

  // @ts-expect-error
  const eventConfiguration: EventConfiguration | undefined =
    eventDetailsResponse?.result?.eventConfiguration;

  // Extract and transform prizePool and drawPool data from the API response
  const prizes: PrizeItem[] =
    eventDetailsResponse?.result.prizePoolArray?.map((item) => ({
      title: `${item?.placeType}`, // Adjust formatting if needed
      description: item?.description,
      amount: item?.coins,
    })) || [];

  const renderContent = () => {
    if (eventDetailsResponseLoading) {
      return (
        <div className="mt-1.5">
          <Loader />
        </div>
      );
    }

    if (eventConfiguration && prizes?.length > 0) {
      return (
        <div className="w-full rounded-md space-y-6">
          {prizes?.length > 0 && <Section title="Prizes" items={prizes} />}
        </div>
      );
    }

    return (
      <div className="mt-2 p-2 text-center">
        <p className="text-[16px] max-799:text-[14px] leading-[19px] max-799:leading-[16px] font-semibold font-inter text-black-100">
          No Data Available
        </p>
      </div>
    );
  };

  return <>{renderContent()}</>;
};

export default PrizeDrawsDisplay;
