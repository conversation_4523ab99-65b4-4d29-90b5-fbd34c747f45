import React from 'react';

const Twitter = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="25"
      height="25"
      viewBox="0 0 25 25"
    >
      <defs>
        <clipPath id="clip-path">
          <rect
            id="Rectangle_11370"
            data-name="Rectangle 11370"
            width="14.089"
            height="13.162"
            fill="#fbfbfb"
          />
        </clipPath>
      </defs>
      <g
        id="Group_42997"
        data-name="Group 42997"
        transform="translate(-1061 -590)"
      >
        <circle
          id="Ellipse_2265"
          data-name="Ellipse 2265"
          cx="12.5"
          cy="12.5"
          r="12.5"
          transform="translate(1061 590)"
        />
        <g
          id="Group_36013"
          data-name="Group 36013"
          transform="translate(1066.455 595.918)"
        >
          <g
            id="Group_36013-2"
            data-name="Group 36013"
            clip-path="url(#clip-path)"
          >
            <path
              id="Path_21915"
              data-name="Path 21915"
              d="M18.942,11.559Q16.707,8.58,14.474,5.6l-.651.692q2.185,2.914,4.371,5.827l.08.107H16.393l-3.577-4.86-.652.695q1.289,1.748,2.575,3.5l1.181,1.6h4.225q-.6-.8-1.2-1.6M8.08.935H9.8l1.454,1.938,1.976,2.635.649-.692L10.273,0H6.229L9.644,4.641q.97,1.315,1.938,2.63l.651-.694Z"
              transform="translate(-6.055)"
              fill="#fbfbfb"
            />
            <path
              id="Path_21916"
              data-name="Path 21916"
              d="M6.76,236.635l-.652.695-3.283,3.5-1.5,1.6H0l1.5-1.6,2.09-2.229,1.932-2.059.651-.694Z"
              transform="translate(0 -229.268)"
              fill="#fbfbfb"
            />
            <path
              id="Path_21917"
              data-name="Path 21917"
              d="M263.95,0l-5.255,5.6-.651.692-.588-.784.649-.692L262.623,0Z"
              transform="translate(-250.276)"
              fill="#fbfbfb"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};

export default Twitter;
