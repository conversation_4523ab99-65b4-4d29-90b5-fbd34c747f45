'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import Image from 'next/image';
import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import type { Control } from 'react-hook-form';
import { Controller } from 'react-hook-form';
import { components } from 'react-select';
import AsyncSelect from 'react-select/async';
import { useDebounce } from 'use-debounce';

import { Config } from '@/helpers/context/config';

import { getDefaultCountryFlag } from '../../../../db/db';

interface Country {
  id: string | number;
  country: string;
  country_flag?: string;
  phoneCode?: number;
}

export interface Option {
  id: string | number;
  label: string;
  value?: string | number;
  country_flag?: string;
  phoneCode?: number | null | string;
}

export const fetchCountries = async (
  offset: number,
  search: string,
  countryIds: string | undefined, // Parameter to accept prioritized country IDs
): Promise<Option[]> => {
  // Initialize an array for prioritized countries
  let prioritizedCountries: Option[] = [];

  // Check if countryIds is provided
  if (countryIds) {
    // Make a single API call for prioritized country IDs
    const res = await fetch(
      `${Config.baseURL}public/country?countryId=${countryIds}`,
    );

    if (!res.ok) {
      throw new Error('Network response was not ok for prioritized countries');
    }

    const data = await res.json();

    // Map the prioritized countries
    prioritizedCountries =
      data?.result?.rows?.map((country: Country) => ({
        id: country.id,
        label: country.country,
        value: country.id,
        country_flag: country?.country_flag,
        phoneCode: country?.phoneCode,
      })) || [];
  }

  // Fetch paginated countries, excluding prioritized countries
  const excludedCountryIds = prioritizedCountries
    .map((country) => country.id)
    .join(',');
  const resPaginated = await fetch(
    Config.baseURL +
      `public/country?limit=20&offset=${offset}&search=${search}&excludeCountryIds=${excludedCountryIds}`,
  );

  if (!resPaginated.ok) {
    throw new Error('Network response was not ok for paginated countries');
  }

  const paginatedData = await resPaginated.json();

  // Map the paginated countries
  const paginatedCountries =
    paginatedData?.result?.rows?.map((country: Country) => ({
      id: country.id,
      label: country.country,
      value: country.id,
      country_flag: country?.country_flag,
      phoneCode: country?.phoneCode,
    })) || [];

  // Combine prioritized countries with paginated countries
  return [...prioritizedCountries, ...paginatedCountries];
};

const handleError = (e: React.SyntheticEvent<HTMLImageElement>) => {
  e.currentTarget.onerror = null; // Prevent looping
  e.currentTarget.src = getDefaultCountryFlag(); // Set the default image
};

const CustomOption = (props: any) => {
  const mediaURL = Config.mediaURL;
  let flagUrl = '';
  if (props?.data?.country_flag) {
    if (props.data.country_flag.includes('uploads')) {
      flagUrl = mediaURL + props.data.country_flag;
    } else {
      flagUrl = props.data.country_flag;
    }
  }
  const countryCode = props?.data?.phoneCode;

  return (
    <components.Option {...props}>
      <div className="flex space-x-2">
        <Image
          src={flagUrl}
          onError={handleError}
          width={0}
          height={0}
          sizes="100vw"
          style={{ width: '20px', height: 'auto' }}
          alt={props.label}
          unoptimized={true}
        />
        {countryCode && <div>+{countryCode}</div>}
        <span className="text-sm truncate ...">{props.label}</span>
      </div>
    </components.Option>
  );
};

const CustomSingleValue = (props: any) => {
  const mediaURL = Config.mediaURL;
  let flagUrl = '';
  if (props?.data?.country_flag) {
    if (props.data.country_flag.includes('uploads')) {
      flagUrl = mediaURL + props.data.country_flag;
    } else {
      flagUrl = props.data.country_flag;
    }
  }

  const { data } = props;
  const countryCode = data?.phoneCode;
  return (
    <div className="absolute">
      <div className="flex justify-center items-center">
        <Image
          src={flagUrl}
          width={0}
          height={0}
          onError={handleError}
          sizes="100vw"
          style={{ width: '27px', height: '20px', padding: '2px' }}
          alt={props.label}
          unoptimized={true}
        />
        {countryCode && <div>+{countryCode}</div>}
      </div>
    </div>
  );
};

const CountryDropDown = ({
  name,
  control,
  placeholder,
  isPhoneCodeInput,
  className,
  value,
  setValue,
  setUserState,
}: {
  name: string;
  control: Control<any>;
  placeholder?: string;
  isPhoneCodeInput?: boolean;
  className?: string;
  value: Option;
  setValue: Dispatch<SetStateAction<Option | undefined>>;
  setUserState: Dispatch<SetStateAction<Option | undefined>>;
}) => {
  const [inputValue, setInputValue] = useState('');
  const [debouncedSearch] = useDebounce(inputValue, 300);

  // Prioritized country IDs
  const prioritizedCountryIds = '13,230,231,101';

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteQuery({
      queryKey: ['async-countries', debouncedSearch, prioritizedCountryIds],
      queryFn: async ({ pageParam = 0 }) => {
        // Only fetch prioritized countries on the first page
        const priorityCountries =
          pageParam === 0
            ? await fetchCountries(0, '', prioritizedCountryIds)
            : [];

        // Fetch paginated countries
        const paginatedCountries = await fetchCountries(
          pageParam,
          debouncedSearch,
          pageParam === 0 ? prioritizedCountryIds : undefined,
        );

        // Combine, removing any potential duplicates
        const combinedCountries = [...priorityCountries, ...paginatedCountries];
        const uniqueCountries = Array.from(
          new Map(combinedCountries.map((item) => [item.id, item])).values(),
        );

        return uniqueCountries;
      },
      initialPageParam: 0,
      getNextPageParam: (lastPage, allPages) => {
        if (!lastPage || lastPage.length < 20) return undefined;
        return allPages.length * 20;
      },
    });

  // Function to load options for AsyncSelect
  const loadOptions = async (inputValue: string): Promise<Option[]> => {
    return await fetchCountries(0, inputValue, prioritizedCountryIds);
  };

  return (
    <div className="w-full">
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <AsyncSelect
            className={className}
            classNamePrefix="select"
            components={{
              Option: isPhoneCodeInput ? CustomOption : components.Option,
              SingleValue: isPhoneCodeInput
                ? CustomSingleValue
                : components.SingleValue,
            }}
            {...field}
            styles={{
              container: (provided) => ({
                ...provided,
              }),
              valueContainer: (provided) => ({
                ...provided,
                overflow: 'visible',
              }),
              // @ts-expect-error
              placeholder: (provided, state) => ({
                ...provided,
                position: 'absolute',
                top:
                  state.hasValue ||
                  state.selectProps.inputValue ||
                  state.selectProps.menuIsOpen
                    ? -14
                    : 'auto',
                backgroundColor:
                  state.hasValue ||
                  state.selectProps.inputValue ||
                  state.selectProps.menuIsOpen
                    ? 'white'
                    : 'transparent',
                transition: 'top 0.2s, font-size 0.1s !important',
                fontSize:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '12px !important',
                color: state.selectProps.menuIsOpen ? '#4455c7' : '#a4a4a4',
                padding:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '0px 3px',
                paddingLeft:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '1px !important',
                marginLeft:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '7px !important',
                lineHeight:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '8px !important',
              }),
            }}
            placeholder={placeholder}
            onInputChange={(value) => setInputValue(value)}
            loadOptions={loadOptions}
            defaultOptions={data?.pages?.flat() || []}
            onChange={(country) => {
              // @ts-expect-error
              field.onChange(country?.id);
              // @ts-expect-error
              setValue(country || undefined);

              if (!isPhoneCodeInput) {
                // @ts-expect-error
                setUserState('');
              }
            }}
            value={value}
            onMenuScrollToBottom={() => {
              if (hasNextPage && !isFetchingNextPage) {
                fetchNextPage();
              }
            }}
          />
        )}
      />
    </div>
  );
};

export default CountryDropDown;
