import { StoryObj, Meta } from '@storybook/react';
import ReservePlayerModal from './ReservePlayerModal';
import { TeamProvider } from '@/helpers/context/createTeamContext';

const meta: Meta<typeof ReservePlayerModal> = {
  title: 'Components/ReservePlayerModal',
  component: ReservePlayerModal,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
  },

  decorators: [
    (Story) => (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <TeamProvider>
          <Story />
        </TeamProvider>
      </div>
    ),
  ],

  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof ReservePlayerModal>;

export const Open: Story = {
  args: {
    children: <></>,
  },
};
