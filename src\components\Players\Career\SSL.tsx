import React from 'react';

import SortingDown from '@/assets/images/icons/sortingDown.svg';
import SortingUp from '@/assets/images/icons/sortingUp.svg';
import { generateUniqueId } from '@/lib/utils';

interface MatchData {
  year?: number;
  startingPrice?: number;
  endPrice?: number;
  priceChange?: number;
  matchesPlayed?: number;
  avgScore?: number;
}

interface CareerStatsSSLData {
  careerStatsSSLData: MatchData[];
}

const StickyHeaderCell = ({ label, left, width }: any) => (
  <th
    className={`px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left sticky bg-primary-200 z-[9]`}
    style={{ left, width }}
  >
    <div className="flex items-center gap-1">
      {label}
      <div className="flex items-center flex-col">
        <span>
          <SortingUp />
        </span>
        <span className="mt-[1.3px]">
          <SortingDown />
        </span>
      </div>
    </div>
  </th>
);

const TableCell = ({ content, isSticky, left, width, textCenter }: any) => (
  <td
    className={`px-[15px] py-[9px] text-[12px] leading-[15px] font-inter font-normal text-black-100 text-left ${isSticky ? 'sticky bg-white z-[9]' : ''}`}
    style={{ width, textAlign: textCenter, ...(isSticky ? { left } : {}) }}
  >
    {content}
  </td>
);

const HeaderCell = ({ label, textCenter }: any) => (
  <th
    className="px-[15px] py-1.5 text-[11.42px] leading-[14px] font-inter font-semibold text-white text-left"
    style={{ textAlign: textCenter }}
  >
    <div
      className="flex items-center gap-1"
      style={{ justifyContent: textCenter }}
    >
      {label}
      <div className="flex items-center flex-col">
        <span>
          <SortingUp />
        </span>
        <span className="mt-[1.3px]">
          <SortingDown />
        </span>
      </div>
    </div>
  </th>
);

const SSLpage = ({ careerStatsSSLData }: CareerStatsSSLData) => {
  return (
    <div>
      <div className="upcoming-matches-table overflow-x-auto no-scrollbar rounded-b-lg">
        <table className="w-full min-w-max table-auto">
          <thead>
            <tr className="bg-primary-200">
              <StickyHeaderCell label="Year" left="0px" />
              <HeaderCell label="Starting price" textCenter="center" />
              <HeaderCell label="End price" textCenter="center" />
              <HeaderCell label="Price change" textCenter="center" />
              <HeaderCell label="Matches  played" textCenter="center" />
              <HeaderCell label="Avg score" textCenter="center" />
            </tr>
          </thead>
          <tbody>
            {careerStatsSSLData?.map((match, index) => (
              <tr
                key={generateUniqueId()}
                className={`${index === careerStatsSSLData?.length - 1 ? '' : 'border-b-[1px] border-black-300'}`}
              >
                <TableCell
                  content={
                    <div className="flex items-center gap-2">
                      <div>
                        <p>{match?.year}</p>
                      </div>
                    </div>
                  }
                  isSticky={true}
                  left="0px"
                />
                <TableCell
                  content={`$${match?.startingPrice}`}
                  textCenter="center"
                />
                <TableCell
                  content={`$${match?.endPrice}`}
                  textCenter="center"
                />
                <TableCell content={match?.priceChange} textCenter="center" />
                <TableCell content={match?.matchesPlayed} textCenter="center" />
                <TableCell content={match?.avgScore} textCenter="center" />
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SSLpage;
