// components/MatchInfoCard.tsx
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/UI/card';
import { Table, TableBody, TableCell, TableRow } from '@/components/UI/table';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { CricketEvent } from '../../../../types/commentry';

interface MatchInfo {
  match: string;
  date: string;
  time: string;
  venue: string;
  toss: string;
  players: string;
}

interface MatchInfoScoreCardProps {
  matchInfo: MatchInfo;
  matchDetailsData: CricketEvent | undefined;
}

const MatchInfoScoreCard: React.FC<MatchInfoScoreCardProps> = ({
  matchInfo,
  matchDetailsData,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <Card className="mt-2 rounded-none border-0">
      <CardHeader
        className=" text-white cursor-pointer flex flex-row items-center justify-between py-2 px-4 rounded"
        onClick={() => setIsExpanded(!isExpanded)}
        style={{ background: 'linear-gradient(to right, #4455C7, #003764)' }}
      >
        <CardTitle className="text-[22.4px]  font-veneerCleanSoft font-normal">
          MATCH INFO
        </CardTitle>
        {isExpanded ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </CardHeader>
      {isExpanded && (
        <CardContent className="p-0">
          <Table>
            <TableBody className="bg-gray-100 hover:bg-gray-100">
              {Object.entries(matchInfo).map(([key, value], index) => (
                <TableRow
                  key={key}
                  className={index % 2 === 0 ? 'bg-white' : 'bg-gray-100'}
                >
                  <TableCell className="font-medium md:w-[150px] w-0 text-left p-2 capitalize">
                    {key}
                  </TableCell>
                  <TableCell className="text-center text-black-700 w-[60px]">
                    -
                  </TableCell>
                  <TableCell className="text-left">{value}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      )}
    </Card>
  );
};

export default MatchInfoScoreCard;
