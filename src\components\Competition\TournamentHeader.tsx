import { Tooltip } from '@material-tailwind/react';
import moment from 'moment';
import Image from 'next/image';
import Countdown from 'react-countdown';

import { useCompetition } from '@/helpers/context/competitionContext';
import { cn, type TimeDifference } from '@/lib/utils';

import type { CompetitionStatusProps } from '../../../types';
import Loader from '../Loader';
import CountDownTimer from '../UI/CountDownTimer';
import StatsCard from './StatsCard';
import { TriangleAlert } from 'lucide-react';
type TournamentHeaderProps = {
  status?: CompetitionStatusProps;
};

export default function TournamentHeader({
  status,
}: Readonly<TournamentHeaderProps>) {
  const { eventDetailsResponse, eventDetailsResponseLoading } =
    useCompetition();
  const prizePools =
    eventDetailsResponse?.result?.eventConfiguration?.prizePool ?? 0;

  const totalPrizePool = prizePools;
  const renderer = ({
    days,
    hours,
    minutes,
    seconds,
    completed,
  }: TimeDifference) => {
    return (
      <CountDownTimer
        completed={completed}
        days={days}
        minutes={minutes}
        hours={hours}
        seconds={seconds}
      />
    );
  };

  const eventStatus = status;
  const isEventCompleted = eventStatus === 'finished';

  let topTeamPayOut: number = 0;
  const eventType = eventDetailsResponse?.result?.eventConfiguration?.eventType;
  const userEntery = eventDetailsResponse?.result?.userEntry ?? 0;


  let firstPrize = 0;
  if (eventType === 'free') {
    firstPrize = totalPrizePool / 2;
  } else {
    firstPrize = eventDetailsResponse?.result?.eventConfiguration?.prizePoolFirst ?? 0;
  }


  switch (eventType) {
    case 'free':
    case 'paid':
      topTeamPayOut = userEntery > 20 ? 20 : userEntery;
      break;
  }

  return (
    <div>
      {/* Stats */}
      <div className="flex justify-between flex-col-reverse md:flex-row md:gap-16 gap-3">
        <div className="flex flex-col md:w-[calc(100%-270px)] w-full">
          <div
            className={cn(
              'flex flex-wrap gap-2 mb-4 items-center justify-center lg:flex-nowrap lg:justify-between',
              eventDetailsResponse?.result?.eventDetails?.status === 'upcoming'
                ? 'max-w-[661px]'
                : 'max-w-full',
            )}
          >
            {/* Prize Pool */}
            <StatsCard
              title="Prize Pool"
              value={totalPrizePool}
              icon="/fantasy/images/smartbCoin.svg"
              alt="coins"
            />
            {/* Entry Coins */}
            <StatsCard
              title="Entry Coins"
              value={
                eventDetailsResponse?.result?.eventConfiguration?.eventType ===
                  'paid'
                  ? eventDetailsResponse?.result?.eventConfiguration?.entryCoin
                  : 'Free'
              }
              icon="/fantasy/images/smartbCoin.svg"
              alt="coins"
            />
            {/* Max Entries */}
            <StatsCard
              title="Max Entries"
              value={
                eventDetailsResponse?.result?.eventConfiguration?.minUserEntry
                  ? eventDetailsResponse?.result?.eventConfiguration
                    ?.minUserEntry
                  : 'Unlimited'
              }
            />
            {/* Entries */}

            <StatsCard
              title="Entries"
              value={
                eventDetailsResponse?.result?.userEntry
                  ? eventDetailsResponse?.result?.userEntry + ' Teams'
                  : 0
              }
            />

            {isEventCompleted && eventDetailsResponse?.result?.currentRank && (
              <StatsCard
                title={`Rank`}
                value={`${eventDetailsResponse?.result?.currentRank!} (${eventDetailsResponse?.result?.name})`}
              />
            )}

            {isEventCompleted && eventDetailsResponse?.result?.winningPrice && (
              <StatsCard
                title="Winnings"
                icon="/fantasy/images/smartbCoin.svg"
                alt="coins"
                value={eventDetailsResponse?.result?.winningPrice}
              />
            )}
          </div>

          {/* Icons */}

          <div className="flex justify-between md:justify-start space-x-4">
            <Tooltip
              content={`1st prize: ${firstPrize} coins`}
              placement="bottom"
              className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
            >
              <div className="bg-[#D4D6D8] rounded px-2 py-1 flex items-center space-x-1 w-full lg:w-[88px] justify-center">
                <Image
                  src="/fantasy/images/icons/winIcon.svg"
                  width={15}
                  height={15}
                  alt="win"
                  unoptimized={true}
                />
                <span className="text-[#003764] text-sm">
                  {firstPrize}
                </span>
              </div>
            </Tooltip>

            <Tooltip
              content={`Payout: Top ${topTeamPayOut} teams`}
              placement="bottom"
              className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
            >
              <div className="bg-[#D4D6D8] rounded px-2 py-1 flex items-center w-full lg:w-[88px] justify-center">
                <Image
                  src="/fantasy/images/icons/topIcon.svg"
                  width={20}
                  height={20}
                  alt="win"
                  unoptimized={true}
                />
                <span className="text-[#003764] text-sm">
                  Top {topTeamPayOut}
                </span>
              </div>
            </Tooltip>

            {eventDetailsResponse?.result?.eventConfiguration?.drawPool && (
              <Tooltip
                content={`Draw:  For members`}
                placement="bottom"
                className="bg-primary-200 text-white text-[11.42px] leading-[14px] font-inter font-normal p-1.5 rounded-[4px]"
              >
                <div className="bg-[#D4D6D8] rounded px-2 py-1 flex items-center w-full lg:w-[88px] justify-center">
                  <Image
                    src="/fantasy/images/icons/drawIcon.svg"
                    width={20}
                    height={20}
                    alt="win"
                    unoptimized={true}
                  />
                  <span className="text-[#003764] text-sm">Draw</span>
                </div>
              </Tooltip>
            )}

            {eventType === 'free' && (
              <div className="bg-[#D4D6D8] text-[#003764] rounded p-1 mb-2 items-center max-w-[50rem]  space-x-2 md:flex mt-2  hidden">
                <TriangleAlert className="w-4 h-4 text-orange-400" />
                <span className="text-[16px] w-full text-center">
                  Free coins will be awarded to the top 20 players only if the
                  game has minimum 10 entries.
                </span>
              </div>
            )}
          </div>
          {eventType === 'free' && (
            <div className="bg-[#D4D6D8] text-[#003764] rounded p-1 items-center max-w-[50rem]  space-x-2 flex mt-2 md:hidden">
              <TriangleAlert className="w-4 h-4 text-orange-400" />
              <span className="md:text-[16px] text-sm w-full md:text-center text-left">
                Free coins will be awarded to the top 20 players only if the
                game has minimum 10 entries.
              </span>
            </div>
          )}
        </div>

        {/* <div className="w-[253px] flex justify-center items-center"></div> */}
        <div>
          <div className="w-full px-2">
            {eventDetailsResponseLoading ? (
              <Loader />
            ) : (
              <Countdown
                date={moment
                  .utc(eventDetailsResponse?.result?.eventDetails?.startTime)
                  .local()
                  .toDate()}
                renderer={renderer}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
