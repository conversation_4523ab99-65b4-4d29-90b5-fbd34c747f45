import './membership.scss';

import React from 'react';

import { useUserMemeberShip } from '@/helpers/context/userMembershipContext';

import ActiveMembership from './ActiveMembership';
import WeeklyMemberShip from './WeeklyMembership';

export default function MemberShip() {
  const { activeMembership } = useUserMemeberShip();

  return (
    <div>{activeMembership ? <ActiveMembership /> : <WeeklyMemberShip />}</div>
  );
}
