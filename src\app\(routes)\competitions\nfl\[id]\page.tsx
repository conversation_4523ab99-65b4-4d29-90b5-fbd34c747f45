'use client';
import ContentWrapper from '@/components/Layout/ContentWrapper';
import Breadcrumbs from '@/components/UI/Breadcrumbs';
import { useState } from 'react';
import CustomTabs from '@/components/UI/CustomTab';
import { useFootballContext } from '@/helpers/context/football/createFootballTeamContext';
import { cn } from '@/lib/utils';
import { Button } from '@/components/UI/button';
import Prizes from '@/components/Competition/Prizes';
import Standings from '@/components/Competition/Standings';
import CompetitionDetailsHeader from '@/components/Common/Competition/CompetitionDetailsHeader';
import FootballFantasyUI from '../../football/(components)/FootballFantasyUI';
import FootballPlayerSelectionUI from '../../football/(components)/FootballPlayerSelectionUI';
import FootballLeaderBoard from '../../football/(components)/FootballLeaderBoard';

const Page = () => {
  const breadcrumbsLinks = [
    { label: 'Home', href: '/' },
    { label: 'SmartPlay', href: '#' },
  ];

  const tabs = [
    {
      label: 'my team',
      labelId: 1,
      count: 0,
    },
    {
      label: 'leaderboard',
      labelId: 2,
    },
    {
      label: 'prizes',
      labelId: 3,
    },
    {
      label: 'standings',
      labelId: 4,
    },
  ];
  const [activeTab, setActiveTab] = useState<number | string>(1);

  const {
    activeTabPlayer,
    setActiveTabPlayer,
    state: {
      remainingBudget,
      playersByRole: { BL, FL, FOL, HBL, HFL, IC, MID },
      playerByRoleLimit: {
        BL: DEFLIMI,
        FL: FWDLIMIT,
        FOL: FOLLIMIT,
        HBL: MIDLIMIT,
        IC: RUCLIMIT,
      },
    },
    clearTeam,
  } = useFootballContext();

  const testStats = {
    selectedPlayer: '',
    remainingSalary: remainingBudget,
    totalScore: 200,
  };

  const testUserProfile = {
    nickName: 'FootballFanatic',
    profilePicUrl: '/path/to/profile-pic.png',
  };

  const testTeamData = {
    favouriteTeam: {
      players: ['Player 1', 'Player 2', 'Player 3'],
    },
    expertTeam: {
      players: ['Player 4', 'Player 5', 'Player 6'],
    },
  };

  const testEventDetails = {
    salaryCap: 5000000,
    eventName: 'Champions League Final',
  };

  const testCompetitionData = {
    currentRank: 10,
    totalRank: 100,
    totalScore: 300,
    totalLivePoints: 150,
  };

  return (
    <div className="min-h-screen">
      <ContentWrapper>
        {/* HEADER  */}
        <Breadcrumbs links={breadcrumbsLinks} />
        <span className="flex items-center max-799:flex-col max-799:items-start text-[31.36px] font-veneerCleanSoft lg:text-white text-black-100">
          {'Melbourne storm vs penrith panthers'.toUpperCase()}
        </span>
        <div className="bg-off-white-200 md:px-[32px] md:py-[16px] p-0">
          {/* <TournamentHeader {...footballTournamentMockData} /> */}

          <div className="mt-2">
            <CustomTabs<number | string>
              tabs={tabs}
              setActiveTab={setActiveTab}
              activeTab={activeTab}
            />
          </div>
        </div>
        {/* MAIN */}
        {activeTab === 1 && (
          <div className="bg-off-white-200 md:px-[32px] md:py-[16px] p-0 mt-2">
            <CompetitionDetailsHeader
              stats={testStats}
              status="team-creation"
              activeTab="Preview"
              userProfile={testUserProfile}
              teamData={testTeamData}
              eventDetails={testEventDetails}
              competitionData={testCompetitionData}
              playerLimits={{
                DEFLIMI,
                FWDLIMIT,
                MIDLIMIT,
                RUCLIMIT,
              }}
              playersByRole={{
                BL: [],
                FL: [],
                FOL: [],
                HBL: [],
                HFL: [],
                IC: [],
                MID: [],
              }}
              sportType="football"
            />

            <div
              className="grid grid-cols-1 lg:grid-cols-2 h-full
       gap-x-2 mt-2"
            >
              <div className="w-full h-full">
                <FootballFantasyUI type="NFL" />
              </div>
              <FootballPlayerSelectionUI
                activeTab={activeTabPlayer}
                setActiveTab={setActiveTabPlayer}
                playerByRole={{
                  BL: [],
                  FL: [],
                  FOL: [],
                  HBL: [],
                  HFL: [],
                  IC: [],
                  MID: [],
                }}
                stats={testStats}
              />
            </div>

            {/* Bottom Action Buttons */}
            <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center bg-white w-full">
              <div
                className={cn(
                  'flex w-full space-x-2 pb-4 md:space-x-0  md:mt-0 justify-around items-center',
                )}
              >
                <div className="space-x-2 mt-5 flex flex-wrap gap-2 justify-center">
                  <Button
                    size="sm"
                    variant="ghost"
                    className=" !bg-[#335F83] text-white w-40"
                  >
                    Clear last entry
                  </Button>
                  <Button size="sm" className="w-40 ">
                    Submit Team
                  </Button>
                  <Button
                    size="sm"
                    variant="link"
                    className=" text-secondary-100 w-40 border-secondary-100 border"
                    onClick={clearTeam}
                  >
                    Clear All
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 2 && (
          <div className="bg-off-white-100 mt-2 py-2">
            <CompetitionDetailsHeader
              stats={testStats}
              status="upcoming"
              activeTab="Preview"
              userProfile={testUserProfile}
              teamData={testTeamData}
              eventDetails={testEventDetails}
              competitionData={testCompetitionData}
              playerLimits={{
                DEFLIMI,
                FWDLIMIT,
                MIDLIMIT,
                RUCLIMIT,
              }}
              playersByRole={{
                BL: [],
                FL: [],
                FOL: [],
                HBL: [],
                HFL: [],
                IC: [],
                MID: [],
              }}
              sportType="football"
              readOnly
            />
            <FootballLeaderBoard />
          </div>
        )}

        {activeTab === 3 && (
          <div className="bg-off-white-100 mt-2 py-2">
            <Prizes />
          </div>
        )}
        {activeTab === 4 && (
          <div className="bg-off-white-100 mt-2 py-2">
            <Standings />
          </div>
        )}
      </ContentWrapper>
    </div>
  );
};

export default Page;
