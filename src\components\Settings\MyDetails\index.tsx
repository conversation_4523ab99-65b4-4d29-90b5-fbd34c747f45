'use client';
import './myDetails.scss';

import { yupResolver } from '@hookform/resolvers/yup';
import { Input, Spinner } from '@material-tailwind/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { differenceInYears } from 'date-fns';
import moment from 'moment';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as Yup from 'yup';
import { ChevronRight } from 'lucide-react';
import { Button } from '@/components/UI/button';
import ProfileCoins from '@/assets/images/icons/profileCoins.png';
import LeftCoins from '../../../../public/images/leftcoins.png';
import type { CommonOption } from '@/components/UI/CommonSelect';
import { CommonSelect } from '@/components/UI/CommonSelect';
import type { Option } from '@/components/UI/CountryDropDown';
import CountryDropDown from '@/components/UI/CountryDropDown';
import type { FileWithPreview } from '@/components/UI/ImageUploader';
import ImageUploader from '@/components/UI/ImageUploader';
import RHFDatePicker from '@/components/UI/RHFDatePicker';
import StateDropDown from '@/components/UI/StateDropDown';
import { setApiMessage } from '@/helpers/commonFunctions';
import { MIN_MAX_NUMBER } from '@/helpers/constants/index';
import {
  updateUserProfile,
  useUserProfileContext,
} from '@/helpers/context/userContext';
import { quyerKeys } from '@/lib/queryKeys';

import { GENDER_OPTIONS } from '../../../../db/db';
import Image from 'next/image';
import { useFantasyUser } from '@/helpers/context/fantasyUserContext';
import useScreen from '@/hooks/useScreen';

export type ProfileFormData = {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  dob: Date;
  gender: string;
  country: string | number | null;
  state: string | number | null;
  phone: string;
  phoneCode: string | number;
};

type ApiResponse = {
  success: boolean;
  image: {
    id: number;
    filePath: string;
    updatedAt: string; // or Date if you'd like to convert it to a Date object
    createdAt: string; // or Date if you'd like to convert it to a Date object
  };
};

const profileSchema = Yup.object().shape({
  firstName: Yup.string()
    .required('First name is required')
    .min(2, 'First name must be at least 2 characters long'),
  lastName: Yup.string()
    .required('Last name is required')
    .min(2, 'Last name must be at least 2 characters long'),
  username: Yup.string().required('Username is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  dob: Yup.date()
    .nullable()
    .required('Date of birth is required')
    .max(new Date(), 'Date of birth cannot be in the future')
    .test(
      'age-validation',
      'You must be at least 18 years old',
      function (value) {
        if (!value) return false;
        return differenceInYears(new Date(), value) >= 18;
      },
    ),
  gender: Yup.string().required('Gender is required'),
  country: Yup.string().optional().nullable(),
  state: Yup.string().optional().nullable(),
  phone: Yup.string()
    .optional()
    .test(
      'phone-validation',
      'Phone number must be between 5-10 digits',
      function (value) {
        if (!value) return true; // Skip validation if no value
        return value.length >= 5 && value.length <= 10;
      },
    ),
  phoneCode: Yup.string().optional().nullable(),
});
type MyDetailsProps = {
  setActiveTab: Dispatch<SetStateAction<number>>
}


const MyDetails = ({ setActiveTab }: MyDetailsProps) => {
  const {
    register,
    handleSubmit,
    setValue,
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm<ProfileFormData>({
    resolver: yupResolver(profileSchema) as any,
  });
  const { width } = useScreen();
  console.log(errors, 'errors');

  const queryClient = useQueryClient();

  const { mutate, isPending } = useMutation({
    mutationFn: updateUserProfile,
    mutationKey: [quyerKeys.updateUserProfileKey],
    onSuccess: () => {
      setApiMessage('success', 'Profile Updated Successfully');
      queryClient.invalidateQueries({ queryKey: [quyerKeys.userProfile] });
    },
  });

  const onSubmit = (data: ProfileFormData) => {
    mutate({
      firstName: data.firstName,
      address: {
        country: data.country || null,
        state: data.state || null,
      },
      dob: moment(data.dob).format(),
      gender: data.gender,
      lastName: data.lastName,
      nickName: data.username,
      phone: data.phone,
      phoneCountryId: data.phoneCode,
    });
  };

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileWithPreview | null>(
    null,
  );
  const [country, setCountry] = useState<Option | undefined>();
  const [countryCode, setCountryCode] = useState<Option | undefined>();
  const [gender, setGender] = useState<CommonOption>();
  const [userState, setUserState] = useState<Option | undefined>();
  const { user } = useUserProfileContext();
  const { user: fantasyUser } = useFantasyUser();

  const userProfile: ProfileFormData = {
    country: user?.address?.country,
    dob: new Date(user?.dob),
    email: user?.username,
    firstName: user?.firstName,
    lastName: user?.lastName,
    gender: user?.gender,
    phone: user?.phone,
    phoneCode: user?.phoneCountryId,
    state: user?.address?.state,
    username: user?.nickName,
  };

  const countryOption: Option = {
    id: user?.address?.Country?.countryCode,
    label: user?.address?.Country?.country,
    country_flag: user?.Country?.country_flag,
    phoneCode: user?.Country?.phoneCode,
  };

  const userStateOption: Option = {
    label: user?.address?.State?.state,
    value: user?.address?.state,
    id: user?.address?.id,
  };

  useEffect(() => {
    const selectedGender = GENDER_OPTIONS.find(
      (gender) => gender?.value === user?.gender,
    );
    reset(userProfile);
    setCountryCode(countryOption);
    setCountry(countryOption);
    setGender(selectedGender);
    setUserState(userStateOption);
  }, [user]);

  const handleUploadSuccess = (response: ApiResponse) => {
    // Handle successful upload response
    mutate({ MediaId: response?.image?.id });
    setApiMessage('success', 'Updated Successfully');
    // You can show your own success message here
  };

  const handleUploadError = (error: Error) => {
    // Handle upload error
    console.error('Upload error:', error);
    // You can show your own error message here
  };

  useEffect(() => {
    if (!userState) {
      setValue('state', '');
    } else if (userState.value) {
      setValue('state', userState.value);
    }
  }, [userState]);
  return (
    <>
      {/* <h4 className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[16px] font-inter font-semibold text-black-100 capitalize">
        My Details
      </h4> */}

      {/* This BT design 0005326: SmartPlay - Create Bonus Coins System */}

      <div className="flex items-center gap-x-[33px] max-799:gap-x-3">
        <div className="w-1/2 rounded-[18px] py-[18px] flex items-center justify-center max-799:justify-start max-799:px-1.5 gap-x-[15px] max-799:gap-x-1 bg-[linear-gradient(180deg,_rgba(68,85,199,1)_0%,_rgba(24,28,61,1)_100%)] ">
          <div>
            <Image
              src={ProfileCoins}
              alt="coins"
              width={width > 799 ? 50 : 36}
              height={width > 799 ? 50 : 36}
            />
          </div>
          <div>
            <p className="text-[31.36px] max-799:text-[22.4px] leading-[43.9px] max-799:leading-[27px] font-semibold font-inter text-white">
              {fantasyUser?.coins
                ? fantasyUser?.coins - (fantasyUser?.holdCoins ?? 0)
                : 0}
            </p>
            <p className="text-[16px] max-799:text-[12px] leading-[19px] max-799:leading-[15px] font-normal font-inter text-white">
              SmartCoins Balance
            </p>
          </div>
        </div>
        <div className="w-1/2 rounded-[18px] py-[18px] flex items-center justify-center max-799:justify-start max-799:px-1.5 gap-x-[15px] max-799:gap-x-1 bg-[linear-gradient(180deg,_rgba(0,55,100,1)_0%,_rgba(9,11,13,1)_100%)]">
          <div>
            <Image
              src={ProfileCoins}
              alt="coins"
              width={width > 799 ? 50 : 36}
              height={width > 799 ? 50 : 36}
            />
          </div>
          <div>
            <p className="text-[31.36px] max-799:text-[22.4px] leading-[43.9px] max-799:leading-[27px] font-semibold font-inter text-white">
              {fantasyUser?.bonusCoins ?? 0}
            </p>
            <p className="text-[16px] max-799:text-[12px] leading-[19px] max-799:leading-[15px] font-normal font-inter text-white">
              BonusCoins Balance
            </p>
          </div>
        </div>
      </div>
      <div className="mt-[27px] mb-[20px] rounded-[18px] bg-[linear-gradient(180deg,_rgba(182,182,182,0.4)_0%,_rgba(100,105,109,0.16)_50%,_rgba(8,19,28,0.05)_100%)] py-[18px] max-1120:py-[9px] px-[76px] max-1120:px-3 flex items-center justify-between max-1024:flex-col max-1024:gap-y-2 max-1024:items-start">
        <div className="flex items-center gap-x-[21.4px] max-1024:gap-x-[11.8px] w-full max-1024:w-auto">
          <Image
            src={LeftCoins}
            alt="Left Coins"
            className=""
            width={width > 1024 ? 78 : 46}
            height={width > 1024 ? 56 : 33}
          />
          <div>
            <p className="text-[31.36px] max-1024:text-[18px] leading-[43.9px] max-1024:leading-[21px] font-semibold font-inter text-black-100">
              Refer and earn BonusCoins
            </p>
            <p className="text-[16px] max-1024:text-[11.42px] leading-[19px] max-1024:leading-[16px] font-normal font-inter text-black-700 mt-2">
              Invite friends, earn BonusCoins! Share your link and unlock
              rewards.
            </p>
          </div>
        </div>
        <div className="w-full max-1024:ml-[57.8px] max-1024:w-auto">
          <p className="text-[43.9px] max-1024:text-[27px] leading-[53px] max-1024:leading-[32px] font-apotekCompRegular font-normal uppercase text-orange-500 flex items-center justify-center gap-x-2 cursor-pointer max-1024:items-start" onClick={() => setActiveTab(5)}>
            Refer Now <ChevronRight />
          </p>
        </div>
      </div>

      <div className="rounded-[18px] bg-white p-[33px] max-799:p-[18px] mt-[20px]">
        <div className="mt-2 flex space-x-2 items-center justify-center max-799:flex-col">
          <ImageUploader
            isDialogOpen={isDialogOpen}
            setDialogOpen={setIsDialogOpen}
            selectedFile={selectedFile}
            setSelectedFile={setSelectedFile}
            handleUploadError={handleUploadError}
            handleUploadSuccess={handleUploadSuccess}
          />
          <Button
            variant="link"
            onClick={() => {
              setIsDialogOpen(true);
            }}
            className="text-[16px] underline"
          >
            Profile Picture
          </Button>
        </div>
        <form onSubmit={handleSubmit(onSubmit)} className="!text-[16px]">
          <div className="grid grid-cols-2 max-1024:grid-cols-2 max-799:grid-cols-1 gap-2 gap-y-[26px] drop-down-selector mt-8">
            <div>
              <Input
                {...(register('firstName') as any)}
                label="First Name"
                placeholder="First Name"
                color="indigo"
                error={errors.firstName?.message}
                className="bg-white !text-[16px] !h-[44px]"

              />
              {errors.firstName?.message && (
                <p className="text-red-400 text-[16px] mt-[2px]">
                  {errors.firstName.message}
                </p>
              )}
            </div>
            <div>
              <Input
                {...(register('lastName') as any)}
                label="Last Name"
                color="indigo"
                placeholder="Last Name"
                error={errors.lastName?.message}
                className="bg-white !text-[16px] !h-[44px]"
              />
              {errors.lastName?.message && (
                <p className="text-red-400 text-[16px] mt-[2px]">
                  {errors.lastName.message}
                </p>
              )}
            </div>
            <div>
              <Input
                {...(register('email') as any)}
                label="Email"
                placeholder="Email"
                variant='outlined'
                color="indigo"
                type="email"
                error={errors.email?.message}
                className="bg-white !text-[16px] !h-[44px]"
                disabled

                style={{
                  border: '1px solid #D4D6D8',
                }}
              />
              {errors.email?.message && (
                <p className="text-red-400 text-[16px] mt-[2px]">
                  {errors.email.message}
                </p>
              )}
            </div>
            <div>
              <Input
                {...(register('username') as any)}
                label="Username"
                placeholder="Username"
                className="primary-input bg-white !text-[16px] !h-[44px]"
                color="indigo"
                error={errors.username?.message}
              />
              {errors.username?.message && (
                <p className="text-red-400 text-[16px] mt-[2px]">
                  {errors.username.message}
                </p>
              )}
            </div>
            <div className="w-full details-dob">
              <RHFDatePicker name="dob" control={control} label="Birth Date" />
              {errors.dob?.message && (
                <p className="text-red-400 text-[16px] mt-[2px]">
                  {errors.dob.message}
                </p>
              )}
            </div>
            <div>
              <div className="flex parent-container  space-x-1">
                <div className="w-[30%] phone-input">
                  <CountryDropDown
                    name="phoneCode"
                    control={control}
                    isPhoneCodeInput
                    placeholder="code"
                    value={
                      countryCode ?? {
                        label: '1',
                        value: 0,
                        country_flag: '',
                        id: 1,
                        phoneCode: 0,
                      }
                    }
                    setValue={setCountryCode}
                    setUserState={setUserState}
                  />
                  {errors.phoneCode?.message && (
                    <p className="text-red-400 text-[16px] mt-[2px]">
                      {errors.phoneCode.message}
                    </p>
                  )}
                </div>
                <div className="w-[calc(100%-30%)]">
                  <Input
                    {...(register('phone') as any)}
                    label="Phone"
                    placeholder="Phone"
                    className="primary-input bg-white rounded-l-none py-1 !text-[16px] !h-[44px] !rounded-lg"
                    color="indigo"
                    error={errors.phone?.message}
                    type="number"
                  />
                  {errors.phone?.message && (
                    <p className="text-red-400 text-[16px] mt-[2px]">
                      {errors.phone.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
            <div>
              <CountryDropDown
                name="country"
                control={control}
                placeholder="Country of residence"
                className="React details-select"
                value={
                  country ?? {
                    id: 1,
                    label: '',
                    country_flag: '',
                    phoneCode: 0,
                    value: '',
                  }
                }
                setValue={setCountry}
                setUserState={setUserState}
              />
              {errors.country?.message && (
                <p className="text-red-500 text-[16px]">
                  {errors.country.message}
                </p>
              )}
            </div>
            <div>
              <StateDropDown
                name="state"
                control={control}
                countryId={+watch('country')!}
                placeholder="State"
                className="React details-select"
                value={userState}
                setValue={setUserState}
              />
              {errors.state?.message && (
                <p className="text-red-500 text-[16px]">
                  {errors.state.message}
                </p>
              )}
            </div>
            <div>
              <CommonSelect
                placeholder="Select Gender"
                name="gender"
                control={control}
                options={GENDER_OPTIONS}
                disaplayValue={gender}
                setDisplayValue={setGender}
                onChange={(value) => {
                  setValue('gender', value);
                }}
              />
            </div>
          </div>
          <div className="mt-8">
            <Button className="!w-full" type="submit" disabled={isPending}>
              {isPending ? <Spinner {...({} as any)} /> : 'Save'}
            </Button>
          </div>
        </form>
      </div>
    </>
  );
};

export default MyDetails;
