.Confirmation-required-modal {
  .header-text {
    text-align: left;
  }

  .header-wrap {
    border-bottom: none;
  }
}

.sort-container {
  .select__control {
    width: 300px !important;
    min-width: 100% !important;
    border: none !important;
    overflow: hidden !important;
  }

  /* Media query for mobile screens */
  @media (max-width: 768px) {
    /* Adjust max-width as needed */
    .select__control {
      width: 100% !important;
    }

    .select__menu {
      min-width: 250px !important;
    }

    .css-1nmdiq5-menu {
      left: -110px;
    }
  }
}

.season-select-comp {
  .select__control {
    background-color: transparent !important;
    border: none !important;

    .select__single-value {
      color: #191919;
      font-weight: 600;
      text-transform: capitalize;
    }
  }

  .select__menu {
    border-radius: 8px;
    overflow: auto;
    text-transform: capitalize;
  }
}

.submited-success-modal {
  .dialog-details {
    padding: 0px 18px 30px;
  }
}
