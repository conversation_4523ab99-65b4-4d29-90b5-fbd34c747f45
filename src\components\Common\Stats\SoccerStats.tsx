'use client';

import SimpleTabs from '@/components/UI/SimpleTabs';
import { useState, useEffect, useMemo } from 'react';
import SoccerTeamStats from './SoccerTeamStats';
import SoccerSmartPlayStats from './SoccerSmartPlayStats';
import SoccerPlayerStats from './SoccerPlayerStats';
import { useStatsContext } from '@/helpers/context/stats';
import { useCompetition } from '@/helpers/context/competitionContext';
import Select, { components } from 'react-select';
import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import { cn } from '@/lib/utils';
import { SortingState, VisibilityState } from '@tanstack/react-table';
import { Button } from '@/components/UI/button';
import { Input } from '@/components/UI/input';
import { Search, SlidersHorizontal, X } from 'lucide-react';
import { IconButton } from '@material-tailwind/react';
import SettingsIcon from '@/components/UI/Icons/SettingsIcon';
import CustomisePanel from '../Commentary/CustomisePanel';
import CustomisePanelMobile from '../Commentary/CustomisePanelMobile';
import { StatsLegend } from '../Commentary/CricketLegend';
import { SOCCER_PLAYER_STATS_LEGEND } from '@/helpers/constants/index';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Card } from '@/components/UI/card';
import { SOCCER_SMARTPLAY_STATS_LEGEND } from '@/helpers/constants';

const positionOptions = [
  {
    value: null,
    label: 'All Positions',
  },
  {
    value: 'G',
    label: 'Goalkeeper',
  },
  {
    value: 'D',
    label: 'Defender',
  },
  {
    value: 'M',
    label: 'Midfielder',
  },
  {
    value: 'F',
    label: 'Forward',
  },
];

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

const SoccerStats = () => {
  const tabs = [
    { id: 'team-stats', name: 'Team Stats' },
    { id: 'player-stats', name: 'Player Stats' },
    { id: 'smartplay-stats', name: 'SmartPlay Stats' },
  ];

  const { eventDetailsResponse } = useCompetition();
  const [showFilter, setShowFilter] = useState(false);
  const [showTableCustomization, setShowTableCustomization] = useState(false);
  const [showTableCustomizationMobile, setShowTableCustomizationMobile] =
    useState(false);
  const [showMobileFilter, setShowMobileFilter] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showLegends, setShowLegends] = useState(false);
  const [selectedPositions, setSelectedPositions] = useState<string>();
  const [selectedTeams, setSelectedTeams] = useState<string>('all');

  const homeTeam =
    eventDetailsResponse?.result?.eventDetails?.homeTeam?.name ?? '';
  const awayTeam =
    eventDetailsResponse?.result?.eventDetails?.awayTeam?.name ?? '';

  const homeTeamLogo =
    eventDetailsResponse?.result?.eventDetails?.homeTeam?.flag ?? '';
  const awayTeamLogo =
    eventDetailsResponse?.result?.eventDetails?.awayTeam?.flag ?? '';

  const teamNames: [string, string] = [homeTeam, awayTeam];
  const teamColors: [string, string] = ['#003764', '#4455C7'];
  const [activeTab, setActiveTab] = useState('team-stats');
  const { soccerTeamStats } = useStatsContext();

  const soccerCategories = [
    {
      id: 'general',
      name: 'General',
      children: [
        { id: 'goals', name: 'Goals' },
        { id: 'assists', name: 'Assists' },
        {
          id: 'appearances',
          name: 'Appearances',
        },
        {
          id: 'minutesPlayed',
          name: 'Minutes Played',
        },
        {
          id: 'yellowCards',
          name: 'Yellow Cards',
        },
        {
          id: 'redCards',
          name: 'Red Cards',
        },
      ],
    },
    {
      id: 'attack',
      name: 'Attack',
      children: [
        { id: 'shots', name: 'Shots' },
        { id: 'shotsOnGoal', name: 'Shots on Target' },
        { id: 'shots_off_target', name: 'Shots Off Target' },
        {
          id: 'hit_woodwork',
          name: 'Hit Woodwork',
        },
        {
          id: 'offside',
          name: 'Offside',
        },
        {
          id: 'touches',
          name: 'Touches',
        },
        {
          id: 'passes',
          name: 'Passes',
        },
        {
          id: 'crosses',
          name: 'Crosses',
        },
      ],
    },
    {
      id: 'defence',
      name: 'Defence',
      children: [
        { id: 'interceptions', name: 'Interceptions' },
        { id: 'blocks', name: 'Blocks' },
        { id: 'tackles', name: 'Tackles' },
        { id: 'clearances', name: 'Clearances' },
        { id: 'own_goals', name: 'Own Goals' },
        { id: 'errors_leading_to_goal', name: 'Errors Leading to Goal' },
        { id: 'penalties_conceded', name: 'Penalties Conceded' },
      ],
    },
    {
      id: 'goalkeeper',
      name: 'Goalkeeper',
      children: [
        { id: 'clean_sheets', name: 'Clean Sheets' },
        { name: 'Goals Conceded', id: 'goals_conceded' },
        { name: 'Saves', id: 'saves' },
        { name: 'Penalties Saved', id: 'penalties_saved' },
        { name: 'Goal Kicks', id: 'goal_kicks' },
      ],
    },
  ];

  const smartPlayCategories = [
    {
      id: 'scoring',
      name: 'Scoring',
      children: [
        { id: 'goalScorGsf', name: 'Goal Scored (Forward)' },
        { id: 'goalScorGsm', name: 'Goal Scored (Midfielder)' },
        {
          id: 'goalScorGsd',
          name: 'Goal Scored (Defender)',
        },
        {
          id: 'scoreBreakdown.goalScore',
          name: 'Goal Scored (Goalkeeper)',
        },
        {
          id: 'scoreBreakdown.assistScore',
          name: 'Assist',
        },
        {
          id: 'scoreBreakdown.shotOnTargetScore',
          name: 'Shot on Target',
        },
      ],
    },

    {
      id: 'generalPlay',
      name: 'General Play',
      children: [
        { id: 'passCompletionRate', name: 'Pass Completion Rate' },
        { id: 'keyPass', name: 'Key Pass' },
        { id: 'dribble', name: 'Dribble Completed' },
        { id: 'minutes10', name: 'Minutes Played' },
        {
          id: 'minutes30',
          name: 'Minutes Played (subbed before the 30th min)',
        },
      ],
    },
    {
      id: 'bonus',
      name: 'Bonus',
      children: [
        { id: 'scoreBreakdown.hatTrickScore', name: 'Hat Trick' },
        { id: 'matchWinningGoal', name: 'Match-Winning Goal' },
        { id: 'tenTackles', name: '10+ Tackles in a game' },
        { id: 'fiveSaves', name: '5+ Saves in a game' },
      ],
    },

    {
      id: 'penalty',
      name: 'Penalty',
      children: [
        { id: 'ownGoal', name: 'Own Goal' },
        { id: 'yellowCard', name: 'Yellow Card' },
        { id: 'redCard', name: 'Red Card' },
        { id: 'penaltyConceded', name: 'Penalty Conceded' },
        { id: 'penaltyMissed', name: 'Penalty Missed' },
      ],
    },
    {
      id: 'defensive',
      name: 'Defensive',
      children: [
        { id: 'cleanSheetDefender', name: 'Clean Sheet (Defender)' },
        { id: 'cleanSheetGoalkeeper', name: 'Clean Sheet (Goalkeeper)' },
        { id: 'tackle', name: 'Tackle Won' },
        {
          id: 'interception',
          name: 'Interception',
        },
        {
          id: 'block',
          name: 'Blocked Shot',
        },
        {
          id: 'save',
          name: 'Save',
        },
        {
          id: 'penSave',
          name: 'Penalty Save',
        },
      ],
    },
  ];

  const initializeSelectedState = () => {
    const state: Record<string, boolean> = {};

    soccerCategories.forEach((category) => {
      state[category.id] = true;
      category.children.forEach((child) => {
        state[child.id] = true;
      });
    });

    return state;
  };

  const initializeSelectedSmartPlayState = () => {
    const state: Record<string, boolean> = {};
    smartPlayCategories.forEach((category) => {
      state[category.id] = true;
      category.children.forEach((child) => {
        state[child.id] = true;
      });
    });
    return state;
  };

  const [selected, setSelected] = useState<Record<string, boolean>>(
    initializeSelectedState(),
  );
  const [selectedSmartPlay, setSelectedSmartPlay] = useState<
    Record<string, boolean>
  >(initializeSelectedSmartPlayState());
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    ...selected,
  });
  const [columnVisibilitySmartPlay, setColumnVisibilitySmartPlay] =
    useState<VisibilityState>({
      ...selectedSmartPlay,
    });

  useEffect(() => {
    setColumnVisibility(selected);
  }, [selected]);

  useEffect(() => {
    setColumnVisibilitySmartPlay(selectedSmartPlay);
  }, [selectedSmartPlay]);

  return (
    <div className="bg-white">
      <div
        className={cn(
          'mt-2 md:mt-0 md:px-8 px-0 flex justify-between gap-2 items-center flex-col lg:flex-row',
        )}
      >
        <div className="w-full md:w-[600px]">
          <SimpleTabs
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            tabs={tabs}
          />
        </div>
        {activeTab === 'player-stats' && (
          <div className="flex items-center w-full space-x-2 my-2 lg:justify-end justify-around">
            <div className="flex items-center gap-2">
              <div>
                <Select
                  className="React desktop-odds-select md:!min-w-[160px] !min-w-[140px]"
                  options={positionOptions}
                  classNamePrefix="select"
                  placeholder="Select Position"
                  isSearchable={false}
                  components={{ DropdownIndicator }}
                  defaultValue={positionOptions[0]}
                  value={positionOptions.find(
                    (position) => position.value === selectedPositions,
                  )}
                  onChange={(position: any) => {
                    setSelectedPositions(position?.value ?? '');
                  }}
                />
              </div>
              <div>
                <Select
                  className="React desktop-odds-select md:!min-w-[160px] !min-w-[140px]"
                  options={[
                    { value: 'all', label: 'All Teams' },
                    { value: homeTeam, label: homeTeam },
                    { value: awayTeam, label: awayTeam },
                  ]}
                  classNamePrefix="select"
                  placeholder="Select Team"
                  isSearchable={false}
                  components={{ DropdownIndicator }}
                  value={
                    selectedTeams === 'all'
                      ? { value: 'all', label: 'All Teams' }
                      : { value: selectedTeams, label: selectedTeams }
                  }
                  onChange={(team: any) => {
                    setSelectedTeams(team?.value ?? '');
                  }}
                />
              </div>
              <div className="relative">
                <Button
                  variant="outline"
                  className="bg-secondary-100 hidden md:block"
                  onClick={() =>
                    setShowTableCustomization(!showTableCustomization)
                  }
                >
                  <div className="flex space-x-2 text-white">
                    <SettingsIcon /> <span>Customise</span>
                  </div>
                </Button>
                <IconButton
                  {...({} as any)}
                  className="bg-secondary-100 md:hidden block"
                  onClick={() =>
                    setShowTableCustomizationMobile(
                      !showTableCustomizationMobile,
                    )
                  }
                >
                  <SettingsIcon />
                </IconButton>
                {showTableCustomization && (
                  <div className="absolute top-full right-[-142px] lg:right-0 mt-2 z-50">
                    <CustomisePanel
                      isOpen={showTableCustomization}
                      setIsOpen={setShowTableCustomization}
                      selected={selected}
                      setSelected={setSelected}
                      categories={soccerCategories} // Add your soccer categories here
                    />
                  </div>
                )}
              </div>
              <div className="relative">
                <div
                  onMouseEnter={() => setShowLegends(true)}
                  onMouseLeave={() => setShowLegends(false)}
                >
                  <Button variant="outline" className="hidden md:block">
                    Legends
                  </Button>
                  {showLegends && (
                    <>
                      <div className="absolute top-full right-0 h-2 w-full bg-transparent" />
                      <div className="absolute top-[calc(100%+8px)] right-0 z-50 bg-white rounded-md shadow-lg p-4 min-w-[500px]">
                        <StatsLegend stats={SOCCER_PLAYER_STATS_LEGEND} />
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'smartplay-stats' && (
          <div className="relative w-full">
            <div className="flex items-center w-full space-x-2 my-2 justify-between relative">
              <div className="relative w-full">
                <Search
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400"
                  size={16}
                />
                <Input
                  placeholder="Search (by player name)"
                  className="border-gray-100 pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Button
                    variant="outline"
                    className="bg-secondary-100 hidden md:block"
                    onClick={() =>
                      setShowTableCustomization(!showTableCustomization)
                    }
                  >
                    <div className="flex space-x-2 text-white">
                      <SettingsIcon /> <span>Customise</span>
                    </div>
                  </Button>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 md:hidden block"
                    onClick={() =>
                      setShowTableCustomizationMobile(
                        !showTableCustomizationMobile,
                      )
                    }
                  >
                    <SettingsIcon />
                  </IconButton>
                  {showTableCustomization && (
                    <div className="absolute top-full right-[-142px] lg:right-0 mt-2 z-50">
                      <CustomisePanel
                        isOpen={showTableCustomization}
                        setIsOpen={setShowTableCustomization}
                        selected={selectedSmartPlay}
                        setSelected={setSelectedSmartPlay}
                        categories={smartPlayCategories}
                      />
                    </div>
                  )}
                </div>
                <div>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 hidden md:block"
                    onClick={() => setShowFilter(!showFilter)}
                  >
                    <SlidersHorizontal />
                  </IconButton>
                  <IconButton
                    {...({} as any)}
                    className="bg-secondary-100 md:hidden block"
                    onClick={() => setShowMobileFilter(!showMobileFilter)}
                  >
                    <SlidersHorizontal />
                  </IconButton>
                </div>
                <div className="relative">
                  <div
                    onMouseEnter={() => setShowLegends(true)}
                    onMouseLeave={() => setShowLegends(false)}
                  >
                    <Button variant="outline" className="hidden md:block">
                      Legends
                    </Button>
                    {showLegends && (
                      <>
                        <div className="absolute top-full right-0 h-2 w-full bg-transparent" />
                        <div className="absolute top-[calc(100%+8px)] right-0 z-50 bg-white rounded-md shadow-lg p-4 min-w-[500px]">
                          <StatsLegend stats={SOCCER_SMARTPLAY_STATS_LEGEND} />
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Filter */}
            {showFilter && activeTab === 'smartplay-stats' && (
              <div className="absolute top-14 right-0 w-80 z-40">
                <Card className="p-4">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Position
                      </label>
                      <div className="space-y-2">
                        {positionOptions.map((option) => (
                          <div key={option.value} className="flex items-center">
                            <input
                              type="checkbox"
                              id={`desktop-position-${option.value}`}
                              onChange={() =>
                                setSelectedPositions(option.value ?? '')
                              }
                              className="h-4 w-4 text-primary-200 focus:ring-primary-200 border-gray-300 rounded"
                            />
                            <label
                              htmlFor={`desktop-position-${option.value}`}
                              className="ml-2 text-sm text-gray-700"
                            >
                              {option.label}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Team
                      </label>
                      <div className="space-y-2">
                        {[
                          { value: 'all', label: 'All Teams' },
                          { value: homeTeam, label: homeTeam },
                          { value: awayTeam, label: awayTeam },
                        ].map((option) => (
                          <div key={option.value} className="flex items-center">
                            <input
                              type="checkbox"
                              id={`desktop-team-${option.value}`}
                              onChange={() =>
                                setSelectedTeams(option.value || '')
                              }
                              checked={selectedTeams === option.value}
                              className="h-4 w-4 text-primary-200 focus:ring-primary-200 border-gray-300 rounded"
                            />
                            <label
                              htmlFor={`desktop-team-${option.value}`}
                              className="ml-2 text-sm text-gray-700"
                            >
                              {option.label}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="flex justify-end mt-4">
                      <Button
                        variant="outline"
                        onClick={() => setShowFilter(false)}
                        className="bg-primary-200 text-white hover:bg-primary-300"
                      >
                        Apply Filters
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>
            )}
          </div>
        )}
      </div>
      <div className="md:px-8 px-0">
        {activeTab === 'team-stats' && (
          <SoccerTeamStats
            teamNames={teamNames}
            teamColors={teamColors}
            stats={soccerTeamStats?.result?.rows ?? []}
            homeTeamLogo={homeTeamLogo}
            awayTeamLogo={awayTeamLogo}
          />
        )}
        {activeTab === 'smartplay-stats' && (
          <SoccerSmartPlayStats
            columnVisibility={columnVisibilitySmartPlay}
            onColumnVisibilityChange={setColumnVisibilitySmartPlay}
            searchQuery={searchQuery}
          />
        )}
        {activeTab === 'player-stats' && (
          <SoccerPlayerStats
            columnVisibility={columnVisibility}
            onColumnVisibilityChange={setColumnVisibility}
            searchQuery={searchQuery}
            selectedPositions={selectedPositions ?? ''}
            selectedTeams={selectedTeams}
          />
        )}
      </div>

      {showTableCustomizationMobile && activeTab === 'smartplay-stats' && (
        <CustomisePanelMobile
          isOpen={showTableCustomizationMobile}
          setIsOpen={setShowTableCustomizationMobile}
          categories={smartPlayCategories} // Add your soccer categories here
          selected={selectedSmartPlay}
          setSelected={setSelectedSmartPlay}
        />
      )}

      {showTableCustomizationMobile && activeTab === 'player-stats' && (
        <CustomisePanelMobile
          isOpen={showTableCustomizationMobile}
          setIsOpen={setShowTableCustomizationMobile}
          categories={soccerCategories} // Add your soccer categories here
          selected={selected}
          setSelected={setSelected}
        />
      )}

      <Drawer open={showMobileFilter} onOpenChange={setShowMobileFilter}>
        <DrawerContent className="bg-white mb-[70px]">
          <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md flex justify-between items-center">
            <span>FILTERS</span>
            <button
              onClick={() => setShowMobileFilter(false)}
              className="p-1 hover:bg-white/10 rounded-full transition-colors"
            >
              <X className="text-white" />
            </button>
          </div>
          <div className="p-4 space-y-4 mt-10">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Position
              </label>
              <div className="space-y-2">
                {positionOptions.map((option) => (
                  <div key={option.value} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`mobile-position-${option.value}`}
                      checked={selectedPositions === option.value}
                      onChange={() => setSelectedPositions(option.value ?? '')}
                      className="h-4 w-4 text-primary-200 focus:ring-primary-200 border-gray-300 rounded"
                    />
                    <label
                      htmlFor={`mobile-position-${option.value}`}
                      className="ml-2 text-sm text-gray-700"
                    >
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Team
              </label>
              <div className="space-y-2">
                {[
                  { value: 'all', label: 'All Teams' },
                  { value: homeTeam, label: homeTeam },
                  { value: awayTeam, label: awayTeam },
                ].map((option) => (
                  <div key={option.value} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`mobile-team-${option.value}`}
                      checked={selectedTeams === option.value}
                      onChange={() => setSelectedTeams(option.value || '')}
                      className="h-4 w-4 text-primary-200 focus:ring-primary-200 border-gray-300 rounded"
                    />
                    <label
                      htmlFor={`mobile-team-${option.value}`}
                      className="ml-2 text-sm text-gray-700"
                    >
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex justify-end mt-4">
              <Button
                variant="outline"
                onClick={() => setShowMobileFilter(false)}
                className="bg-primary-200 text-white hover:bg-primary-300"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  );
};

export default SoccerStats;
