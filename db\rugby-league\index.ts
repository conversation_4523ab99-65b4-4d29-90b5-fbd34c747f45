import {
  RugbyLeagueTournamentHeaderProps,
  RugbyPlayerArray,
} from '../../types/rugby-league';
export const rugbyPlayers: RugbyPlayerArray = [
  {
    id: 1001,
    role: 'BAC',
    squadsId: 500,
    tournamentId: 30,
    playerId: 1001,
    name: 'Player 1001',
    image: null,
    teamName: 'Team A',
    teamId: 101,
    scoreData: {
      lastScore: 0,
      lastPoint: 0,
      totalScore: 100,
      totalPoint: 110,
      lastThreeMatch: 0.0,
      lastFiveMatch: 0.0,
      totalPlayed: 5,
      avg: 20,
      playerCurrentSalary: 180000,
      playerLastSalary: 160000,
    },
  },
  {
    id: 1002,
    role: 'BAC',
    squadsId: 501,
    tournamentId: 30,
    playerId: 1002,
    name: 'Player 1002',
    image: null,
    teamName: 'Team B',
    teamId: 102,
    scoreData: {
      lastScore: 2,
      lastPoint: 3,
      totalScore: 105,
      totalPoint: 114,
      lastThreeMatch: 2.5,
      lastFiveMatch: 3.1,
      totalPlayed: 5,
      avg: 21,
      playerCurrentSalary: 185000,
      playerLastSalary: 164000,
    },
  },
  {
    id: 1003,
    role: 'BAC',
    squadsId: 502,
    tournamentId: 30,
    playerId: 1003,
    name: 'Player 1003',
    image: null,
    teamName: 'Team C',
    teamId: 103,
    scoreData: {
      lastScore: 4,
      lastPoint: 6,
      totalScore: 110,
      totalPoint: 118,
      lastThreeMatch: 5.0,
      lastFiveMatch: 6.2,
      totalPlayed: 5,
      avg: 22,
      playerCurrentSalary: 190000,
      playerLastSalary: 168000,
    },
  },
  {
    id: 1004,
    role: 'BAC',
    squadsId: 503,
    tournamentId: 30,
    playerId: 1004,
    name: 'Player 1004',
    image: null,
    teamName: 'Team D',
    teamId: 104,
    scoreData: {
      lastScore: 6,
      lastPoint: 9,
      totalScore: 115,
      totalPoint: 122,
      lastThreeMatch: 7.5,
      lastFiveMatch: 9.3,
      totalPlayed: 5,
      avg: 23,
      playerCurrentSalary: 195000,
      playerLastSalary: 172000,
    },
  },
  {
    id: 1005,
    role: 'BAC',
    squadsId: 504,
    tournamentId: 30,
    playerId: 1005,
    name: 'Player 1005',
    image: null,
    teamName: 'Team E',
    teamId: 105,
    scoreData: {
      lastScore: 8,
      lastPoint: 12,
      totalScore: 120,
      totalPoint: 126,
      lastThreeMatch: 10.0,
      lastFiveMatch: 12.4,
      totalPlayed: 5,
      avg: 24,
      playerCurrentSalary: 200000,
      playerLastSalary: 176000,
    },
  },
  {
    id: 1006,
    role: 'BAC',
    squadsId: 505,
    tournamentId: 30,
    playerId: 1006,
    name: 'Player 1006',
    image: null,
    teamName: 'Team F',
    teamId: 106,
    scoreData: {
      lastScore: 0,
      lastPoint: 0,
      totalScore: 125,
      totalPoint: 130,
      lastThreeMatch: 12.5,
      lastFiveMatch: 15.5,
      totalPlayed: 5,
      avg: 25,
      playerCurrentSalary: 205000,
      playerLastSalary: 180000,
    },
  },
  {
    id: 1007,
    role: 'HAL',
    squadsId: 500,
    tournamentId: 30,
    playerId: 1007,
    name: 'Player 1007',
    image: null,
    teamName: 'Team A',
    teamId: 101,
    scoreData: {
      lastScore: 0,
      lastPoint: 0,
      totalScore: 100,
      totalPoint: 110,
      lastThreeMatch: 0.0,
      lastFiveMatch: 0.0,
      totalPlayed: 5,
      avg: 20,
      playerCurrentSalary: 180000,
      playerLastSalary: 160000,
    },
  },
  {
    id: 1008,
    role: 'HAL',
    squadsId: 501,
    tournamentId: 30,
    playerId: 1008,
    name: 'Player 1008',
    image: null,
    teamName: 'Team B',
    teamId: 102,
    scoreData: {
      lastScore: 2,
      lastPoint: 3,
      totalScore: 105,
      totalPoint: 114,
      lastThreeMatch: 2.5,
      lastFiveMatch: 3.1,
      totalPlayed: 5,
      avg: 21,
      playerCurrentSalary: 185000,
      playerLastSalary: 164000,
    },
  },
  {
    id: 1009,
    role: 'HAL',
    squadsId: 502,
    tournamentId: 30,
    playerId: 1009,
    name: 'Player 1009',
    image: null,
    teamName: 'Team C',
    teamId: 103,
    scoreData: {
      lastScore: 4,
      lastPoint: 6,
      totalScore: 110,
      totalPoint: 118,
      lastThreeMatch: 5.0,
      lastFiveMatch: 6.2,
      totalPlayed: 5,
      avg: 22,
      playerCurrentSalary: 190000,
      playerLastSalary: 168000,
    },
  },
  {
    id: 1010,
    role: 'HAL',
    squadsId: 503,
    tournamentId: 30,
    playerId: 1010,
    name: 'Player 1010',
    image: null,
    teamName: 'Team D',
    teamId: 104,
    scoreData: {
      lastScore: 6,
      lastPoint: 9,
      totalScore: 115,
      totalPoint: 122,
      lastThreeMatch: 7.5,
      lastFiveMatch: 9.3,
      totalPlayed: 5,
      avg: 23,
      playerCurrentSalary: 195000,
      playerLastSalary: 172000,
    },
  },
  {
    id: 1011,
    role: 'HAL',
    squadsId: 504,
    tournamentId: 30,
    playerId: 1011,
    name: 'Player 1011',
    image: null,
    teamName: 'Team E',
    teamId: 105,
    scoreData: {
      lastScore: 8,
      lastPoint: 12,
      totalScore: 120,
      totalPoint: 126,
      lastThreeMatch: 10.0,
      lastFiveMatch: 12.4,
      totalPlayed: 5,
      avg: 24,
      playerCurrentSalary: 200000,
      playerLastSalary: 176000,
    },
  },
  {
    id: 1012,
    role: 'HAL',
    squadsId: 505,
    tournamentId: 30,
    playerId: 1012,
    name: 'Player 1012',
    image: null,
    teamName: 'Team F',
    teamId: 106,
    scoreData: {
      lastScore: 0,
      lastPoint: 0,
      totalScore: 125,
      totalPoint: 130,
      lastThreeMatch: 12.5,
      lastFiveMatch: 15.5,
      totalPlayed: 5,
      avg: 25,
      playerCurrentSalary: 205000,
      playerLastSalary: 180000,
    },
  },
  {
    id: 1013,
    role: 'BR',
    squadsId: 500,
    tournamentId: 30,
    playerId: 1013,
    name: 'Player 1013',
    image: null,
    teamName: 'Team A',
    teamId: 101,
    scoreData: {
      lastScore: 0,
      lastPoint: 0,
      totalScore: 100,
      totalPoint: 110,
      lastThreeMatch: 0.0,
      lastFiveMatch: 0.0,
      totalPlayed: 5,
      avg: 20,
      playerCurrentSalary: 180000,
      playerLastSalary: 160000,
    },
  },
  {
    id: 1014,
    role: 'BR',
    squadsId: 501,
    tournamentId: 30,
    playerId: 1014,
    name: 'Player 1014',
    image: null,
    teamName: 'Team B',
    teamId: 102,
    scoreData: {
      lastScore: 2,
      lastPoint: 3,
      totalScore: 105,
      totalPoint: 114,
      lastThreeMatch: 2.5,
      lastFiveMatch: 3.1,
      totalPlayed: 5,
      avg: 21,
      playerCurrentSalary: 185000,
      playerLastSalary: 164000,
    },
  },
  {
    id: 1015,
    role: 'BR',
    squadsId: 502,
    tournamentId: 30,
    playerId: 1015,
    name: 'Player 1015',
    image: null,
    teamName: 'Team C',
    teamId: 103,
    scoreData: {
      lastScore: 4,
      lastPoint: 6,
      totalScore: 110,
      totalPoint: 118,
      lastThreeMatch: 5.0,
      lastFiveMatch: 6.2,
      totalPlayed: 5,
      avg: 22,
      playerCurrentSalary: 190000,
      playerLastSalary: 168000,
    },
  },
  {
    id: 1016,
    role: 'BR',
    squadsId: 503,
    tournamentId: 30,
    playerId: 1016,
    name: 'Player 1016',
    image: null,
    teamName: 'Team D',
    teamId: 104,
    scoreData: {
      lastScore: 6,
      lastPoint: 9,
      totalScore: 115,
      totalPoint: 122,
      lastThreeMatch: 7.5,
      lastFiveMatch: 9.3,
      totalPlayed: 5,
      avg: 23,
      playerCurrentSalary: 195000,
      playerLastSalary: 172000,
    },
  },
  {
    id: 1017,
    role: 'BR',
    squadsId: 504,
    tournamentId: 30,
    playerId: 1017,
    name: 'Player 1017',
    image: null,
    teamName: 'Team E',
    teamId: 105,
    scoreData: {
      lastScore: 8,
      lastPoint: 12,
      totalScore: 120,
      totalPoint: 126,
      lastThreeMatch: 10.0,
      lastFiveMatch: 12.4,
      totalPlayed: 5,
      avg: 24,
      playerCurrentSalary: 200000,
      playerLastSalary: 176000,
    },
  },
  {
    id: 1018,
    role: 'BR',
    squadsId: 505,
    tournamentId: 30,
    playerId: 1018,
    name: 'Player 1018',
    image: null,
    teamName: 'Team F',
    teamId: 106,
    scoreData: {
      lastScore: 0,
      lastPoint: 0,
      totalScore: 125,
      totalPoint: 130,
      lastThreeMatch: 12.5,
      lastFiveMatch: 15.5,
      totalPlayed: 5,
      avg: 25,
      playerCurrentSalary: 205000,
      playerLastSalary: 180000,
    },
  },
  {
    id: 1019,
    role: 'FRF',
    squadsId: 500,
    tournamentId: 30,
    playerId: 1019,
    name: 'Player 1019',
    image: null,
    teamName: 'Team A',
    teamId: 101,
    scoreData: {
      lastScore: 0,
      lastPoint: 0,
      totalScore: 100,
      totalPoint: 110,
      lastThreeMatch: 0.0,
      lastFiveMatch: 0.0,
      totalPlayed: 5,
      avg: 20,
      playerCurrentSalary: 180000,
      playerLastSalary: 160000,
    },
  },
  {
    id: 1020,
    role: 'FRF',
    squadsId: 501,
    tournamentId: 30,
    playerId: 1020,
    name: 'Player 1020',
    image: null,
    teamName: 'Team B',
    teamId: 102,
    scoreData: {
      lastScore: 2,
      lastPoint: 3,
      totalScore: 105,
      totalPoint: 114,
      lastThreeMatch: 2.5,
      lastFiveMatch: 3.1,
      totalPlayed: 5,
      avg: 21,
      playerCurrentSalary: 185000,
      playerLastSalary: 164000,
    },
  },
  {
    id: 1021,
    role: 'FRF',
    squadsId: 502,
    tournamentId: 30,
    playerId: 1021,
    name: 'Player 1021',
    image: null,
    teamName: 'Team C',
    teamId: 103,
    scoreData: {
      lastScore: 4,
      lastPoint: 6,
      totalScore: 110,
      totalPoint: 118,
      lastThreeMatch: 5.0,
      lastFiveMatch: 6.2,
      totalPlayed: 5,
      avg: 22,
      playerCurrentSalary: 190000,
      playerLastSalary: 168000,
    },
  },
  {
    id: 1022,
    role: 'FRF',
    squadsId: 503,
    tournamentId: 30,
    playerId: 1022,
    name: 'Player 1022',
    image: null,
    teamName: 'Team D',
    teamId: 104,
    scoreData: {
      lastScore: 6,
      lastPoint: 9,
      totalScore: 115,
      totalPoint: 122,
      lastThreeMatch: 7.5,
      lastFiveMatch: 9.3,
      totalPlayed: 5,
      avg: 23,
      playerCurrentSalary: 195000,
      playerLastSalary: 172000,
    },
  },
  {
    id: 1023,
    role: 'FRF',
    squadsId: 504,
    tournamentId: 30,
    playerId: 1023,
    name: 'Player 1023',
    image: null,
    teamName: 'Team E',
    teamId: 105,
    scoreData: {
      lastScore: 8,
      lastPoint: 12,
      totalScore: 120,
      totalPoint: 126,
      lastThreeMatch: 10.0,
      lastFiveMatch: 12.4,
      totalPlayed: 5,
      avg: 24,
      playerCurrentSalary: 200000,
      playerLastSalary: 176000,
    },
  },
  {
    id: 1024,
    role: 'FRF',
    squadsId: 505,
    tournamentId: 30,
    playerId: 1024,
    name: 'Player 1024',
    image: null,
    teamName: 'Team F',
    teamId: 106,
    scoreData: {
      lastScore: 0,
      lastPoint: 0,
      totalScore: 125,
      totalPoint: 130,
      lastThreeMatch: 12.5,
      lastFiveMatch: 15.5,
      totalPlayed: 5,
      avg: 25,
      playerCurrentSalary: 205000,
      playerLastSalary: 180000,
    },
  },
  {
    id: 1025,
    role: 'IC',
    squadsId: 500,
    tournamentId: 30,
    playerId: 1025,
    name: 'Player 1025',
    image: null,
    teamName: 'Team A',
    teamId: 101,
    scoreData: {
      lastScore: 0,
      lastPoint: 0,
      totalScore: 100,
      totalPoint: 110,
      lastThreeMatch: 0.0,
      lastFiveMatch: 0.0,
      totalPlayed: 5,
      avg: 20,
      playerCurrentSalary: 180000,
      playerLastSalary: 160000,
    },
  },
  {
    id: 1026,
    role: 'IC',
    squadsId: 501,
    tournamentId: 30,
    playerId: 1026,
    name: 'Player 1026',
    image: null,
    teamName: 'Team B',
    teamId: 102,
    scoreData: {
      lastScore: 2,
      lastPoint: 3,
      totalScore: 105,
      totalPoint: 114,
      lastThreeMatch: 2.5,
      lastFiveMatch: 3.1,
      totalPlayed: 5,
      avg: 21,
      playerCurrentSalary: 185000,
      playerLastSalary: 164000,
    },
  },
  {
    id: 1027,
    role: 'IC',
    squadsId: 502,
    tournamentId: 30,
    playerId: 1027,
    name: 'Player 1027',
    image: null,
    teamName: 'Team C',
    teamId: 103,
    scoreData: {
      lastScore: 4,
      lastPoint: 6,
      totalScore: 110,
      totalPoint: 118,
      lastThreeMatch: 5.0,
      lastFiveMatch: 6.2,
      totalPlayed: 5,
      avg: 22,
      playerCurrentSalary: 190000,
      playerLastSalary: 168000,
    },
  },
  {
    id: 1028,
    role: 'IC',
    squadsId: 503,
    tournamentId: 30,
    playerId: 1028,
    name: 'Player 1028',
    image: null,
    teamName: 'Team D',
    teamId: 104,
    scoreData: {
      lastScore: 6,
      lastPoint: 9,
      totalScore: 115,
      totalPoint: 122,
      lastThreeMatch: 7.5,
      lastFiveMatch: 9.3,
      totalPlayed: 5,
      avg: 23,
      playerCurrentSalary: 195000,
      playerLastSalary: 172000,
    },
  },
  {
    id: 1029,
    role: 'IC',
    squadsId: 504,
    tournamentId: 30,
    playerId: 1029,
    name: 'Player 1029',
    image: null,
    teamName: 'Team E',
    teamId: 105,
    scoreData: {
      lastScore: 8,
      lastPoint: 12,
      totalScore: 120,
      totalPoint: 126,
      lastThreeMatch: 10.0,
      lastFiveMatch: 12.4,
      totalPlayed: 5,
      avg: 24,
      playerCurrentSalary: 200000,
      playerLastSalary: 176000,
    },
  },
  {
    id: 1030,
    role: 'IC',
    squadsId: 505,
    tournamentId: 30,
    playerId: 1030,
    name: 'Player 1030',
    image: null,
    teamName: 'Team F',
    teamId: 106,
    scoreData: {
      lastScore: 0,
      lastPoint: 0,
      totalScore: 125,
      totalPoint: 130,
      lastThreeMatch: 12.5,
      lastFiveMatch: 15.5,
      totalPlayed: 5,
      avg: 25,
      playerCurrentSalary: 205000,
      playerLastSalary: 180000,
    },
  },
];
export const mockRugbyLeagueTournamentHeaderProps: RugbyLeagueTournamentHeaderProps =
  {
    competitionStatus: 'upcoming',
    tournamentDetails: {
      prizePoolAmount: 10000, // Example prize pool amount
      tournamentType: 'paid', // Example tournament type
      entryCoin: '50', // Example entry coin
      minUserEntry: '1', // Minimum user entry
      userEntryCount: 30, // Example user entry count
      currentRank: '5', // Example current rank
      tournamentName: 'Rugby World Cup', // Example tournament name
      winningPrizeAmount: '5000', // Example winning prize amount
      drawPoolAvailable: true, // Example draw pool availability
      startTime: '2023-11-01T15:00:00Z', // Example start time in ISO format
    },
    isTournamentDetailsLoading: false, // Example loading state
  };

export const rugbyTournamentMockData = {
  competitionStatus: 'finished', // or 'ongoing'
  tournamentDetails: {
    prizePoolAmount: 2000, // Total prize pool in coins
    tournamentType: 'free', // 'free' or 'paid'
    userEntryCount: 15, // Number of teams entered
    currentRank: 1, // Current rank of the user
    tournamentName: 'NRL', // Name of the tournament
    winningPrize: 1000, // Prize for the winner
    drawPoolAvailable: false, // Whether a draw pool is available
    startTime: '2023-09-10T16:00:00Z', // Start time in UTC
  },
};
