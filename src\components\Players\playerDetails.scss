.player-details-section {
  .league-select {
    .select__menu {
      border-radius: 8px;
      overflow: auto;
      margin-top: 2px;
    }
  }

  .season-select {
    .select__menu {
      border-radius: 8px;
      overflow: auto;
      margin-top: 2px;
    }
  }

  .sort-select {
    .select__menu {
      border-radius: 8px;
      overflow: auto;
      width: 280px;
      right: 0px;
      margin-top: 2px;
    }
  }
}

// modal dialog css

.player-modal {
  .header-wrap {
    padding: 0px;
    align-items: flex-start;
    column-gap: 15px;
    border-bottom: none;

    @media (max-width: 799px) {
      column-gap: 0px;
    }
  }

  .close-icon {
    padding: 6px;
  }

  .dialog-details {
    padding: 0px;

    // player details modal table design

    .season-select {
      .select__control {
        background-color: transparent !important;
        border: none !important;

        .select__single-value {
          color: black;
          font-weight: 600;
        }
      }

      .select__menu {
        border-radius: 8px;
        overflow: auto;
      }
    }
  }
}

// sorting arrow css

.asc-sort {
  path {
    fill: #4455c7;
  }
}

.desc-sort {
  path {
    fill: #4455c7;
  }
}