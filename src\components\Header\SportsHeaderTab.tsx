import React from 'react';

interface Tab {
  label: string; // The text label for the tab
  labelId: number; // Identifier for the tab
  icon: React.ReactNode; // Icon for desktop
  mobileIcon?: React.ReactNode; // Icon for mobile (optional)
  activeIcon: React.ReactNode; // Active state icon for desktop
  mobileActiveIcon?: React.ReactNode; // Active state icon for mobile (optional)
  comingSoon?: boolean; // Indicates if the tab is coming soon (optional)
  title?: string; // Tooltip or additional text when "coming soon" (optional)
}

interface SportsHeaderTabProps {
  tabs: Tab[];
  activeTab: number;
  handleTabChanges: (id: number, label?: string) => void;
  className?: string;
}

const SportsHeaderTab: React.FC<SportsHeaderTabProps> = ({
  tabs,
  activeTab,
  handleTabChanges,
  className = '',
}) => {
  return (
    <div
      className={`bg-off-white-200 max-1024:bg-white max-799:mx-[-12px] ${className}`}
    >
      <div className="w-full max-799:px-3">
        <div className="overflow-x-auto no-scrollbar no-scrollbar pb-2">
          <div className="flex gap-[18px] max-1024:gap-3 font-normal border-b-[3px] border-b-secondary-100 w-max min-w-full">
            {tabs?.map((tab) => (
              <button
                key={tab.labelId}
                className={`w-auto font-veneerCleanSoft ${tab?.labelId === activeTab && !tab?.comingSoon
                    ? 'border-b-[3px] bg-tab-active-gradient border-primary-200 text-secondary-100'
                    : 'border-b-[3px] border-transparent text-black-100'
                  }`}
                onClick={() =>
                  !tab?.comingSoon && handleTabChanges(tab?.labelId, tab?.label)
                }
              >
                <div className="px-[34px] max-799:px-3 pt-[18px] max-799:pt-3 pb-2 relative w-[130px] flex flex-col items-center">
                  {/* Desktop icon */}
                  <div className="max-799:mx-auto max-799:hidden flex justify-center items-center">
                    {tab?.labelId === activeTab ? tab?.activeIcon : tab?.icon}
                  </div>
                  {/* Mobile icon */}
                  <div className="tab-svg-icon max-799:mx-auto max-799:block hidden">
                    {tab?.labelId === activeTab
                      ? tab?.mobileActiveIcon
                      : tab?.mobileIcon}
                  </div>
                  {/* Tab Label */}
                  <div className="whitespace-nowrap text-center">
                    {tab?.label}
                  </div>
                  {/* Coming Soon Badge */}
                  {tab?.comingSoon && (
                    <div className="absolute left-1/2 bottom-[-20px] max-799:bottom-[-16px] transform -translate-x-1/2 -translate-y-1/2 w-max px-1.5 max-799:px-1 py-[2px] rounded-[6px] bg-negative-200 text-[11.42px] max-799:text-[7px] leading-[14px] max-799:leading-[9px] font-inter font-light text-white">
                      {tab?.title}
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SportsHeaderTab;
