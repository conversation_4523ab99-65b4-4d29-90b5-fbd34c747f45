'use client';
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/UI/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/UI/table';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { cn, generateUniqueId } from '@/lib/utils';

interface SoccerPlayer {
  name: string;
  position: string;
  number: number;
  playerId: number;
}

interface SoccerLineupCardProps {
  title: string;
  players: SoccerPlayer[];
  formation: string;
  togglePlayerDetails?: (playerId: number) => void;
}

const SoccerLineupCard: React.FC<SoccerLineupCardProps> = ({
  title,
  players,
  formation,
  togglePlayerDetails,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  // Group players by position
  const groupedPlayers = players.reduce(
    (acc, player) => {
      const position = player.position;
      if (!acc[position]) {
        acc[position] = [];
      }
      acc[position].push(player);
      return acc;
    },
    {} as Record<string, SoccerPlayer[]>,
  );

  return (
    <Card className="rounded-none border-0 w-full">
      <CardHeader
        className="text-white cursor-pointer flex flex-row items-center justify-between py-2 px-4 rounded"
        style={{ background: 'linear-gradient(to right, #4455C7, #003764)' }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-4">
          <CardTitle className="text-[22.4px] font-veneerCleanSoft font-normal">
            {title}
          </CardTitle>
          <span className="text-sm bg-white/20 px-2 py-1 rounded">
            {formation}
          </span>
        </div>
        {isExpanded ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </CardHeader>
      {isExpanded && (
        <CardContent className="p-0">
          {Object.entries(groupedPlayers).map(([position, positionPlayers]) => (
            <div key={position} className="mb-4">
              <h3 className="text-lg font-semibold px-4 py-2 bg-gray-100">
                {position}
              </h3>
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-100 hover:bg-gray-100">
                    <TableHead className="text-left p-2 w-[50px]">#</TableHead>
                    <TableHead className="text-left p-2">Player</TableHead>
                    <TableHead className="text-left p-2">Position</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {positionPlayers.map((player, index) => (
                    <TableRow
                      key={generateUniqueId()}
                      className={cn(
                        index % 2 === 0 ? 'bg-white' : 'bg-gray-100',
                        'cursor-pointer hover:bg-gray-200',
                      )}
                      onClick={() => togglePlayerDetails?.(player.playerId)}
                    >
                      <TableCell className="text-left p-2">
                        {player.number}
                      </TableCell>
                      <TableCell className="text-left p-2">
                        {player.name}
                      </TableCell>
                      <TableCell className="text-left p-2">
                        {player.position}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ))}
        </CardContent>
      )}
    </Card>
  );
};

export default SoccerLineupCard;
