import { useState } from 'react';

import { useCompetition } from '@/helpers/context/competitionContext';
import useScreen from '@/hooks/useScreen';

import { ModalHeaderDownIcon, PlayerCaptainViewIcon } from '../images';
import { Drawer, DrawerContent } from '../UI/drawer';
import CompetitionDetailsHeader from './CompetitionDetailsHeader';
import PrizeDrawsDisplay from './PrizeDrawsDisplay';
import RightSideDetails from './RightSideDetails';

const Prizes = () => {
  const { eventDetailsResponse, dreamTeamResponse } = useCompetition();
  const { width } = useScreen();
  const [openDrawer, setOpenDrawer] = useState<boolean>(false);
  return (
    <>
      {/* <CompetitionDetailsHeader
        stats={{
          remainingSalary:
            eventDetailsResponse?.result?.eventDetails?.CricketSeason
              ?.fantasy_sport_salary_cap!,
        }}
        status={eventDetailsResponse?.result?.eventDetails?.status}
        readOnly
        dreamTeamResponse={dreamTeamResponse}
      /> */}

      <div className="px-[33px] max-799:px-0 mt-5">
        <div className="flex flex-col md:flex-row justify-between gap-[33px]">
          <div className="w-full pb-2 h-full">
            <PrizeDrawsDisplay />
          </div>
          {/* <div className="flex flex-col space-y-2 w-full md:w-[40%]">
            <div className="hidden md:block">
              <RightSideDetails />
            </div>
          </div> */}
        </div>

        {/* {width <= 800 && (
          <Drawer open={openDrawer} onOpenChange={setOpenDrawer}>
            <DrawerContent className="overflow-hidden bg-gradient-to-b from-[#E5EAEF] to-[#93A0AD]">
              <div className="absolute bg-primary-200 p-2 max-799:p-0 font-bold text-xl text-white w-full z-50 flex items-center justify-center h-[47px]">
                <button
                  className="flex items-center gap-1.5"
                  onClick={() => setOpenDrawer(false)}
                >
                  <ModalHeaderDownIcon />
                </button>
              </div>
              <div className="mt-[44px] overflow-y-auto mb-10">
                <RightSideDetails />
              </div>
            </DrawerContent>
          </Drawer>
        )} */}

        {/* <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center ">
          <PlayerCaptainViewIcon
            className="md:hidden block"
            onClick={() => setOpenDrawer(true)}
          />
        </div> */}
      </div>
    </>
  );
};

export default Prizes;
