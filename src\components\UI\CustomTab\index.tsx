'use client';

import React from 'react';
import * as TabsPrimitive from '@radix-ui/react-tabs';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface Tab {
  label: string;
  labelId: number;
  count?: string | number;
  disabled?: boolean;
}

interface TabsProps<T> {
  tabs: Tab[];
  setActiveTab: (value: T) => void;
  activeTab: T;
  className?: string;
}

const CustomTabs = <T extends number | string>({
  tabs,
  setActiveTab,
  activeTab,
  className,
}: TabsProps<T>) => {
  const showTabs = tabs?.filter((tab) => !tab.disabled);

  return (
    <TabsPrimitive.Root
      value={activeTab.toString()}
      onValueChange={(value) => setActiveTab(Number(value) as T)}
      className="w-full"
    >
      <div className="overflow-x-auto no-scrollbar">
        <TabsPrimitive.List className="flex gap-[18px] max-799:gap-[6px] font-normal border-b-[3px] border-b-secondary-100 w-max min-w-full">
          {showTabs?.map((tab) => (
            <TabsPrimitive.Trigger
              key={tab.labelId}
              value={tab.labelId.toString()}
              disabled={tab.disabled}
              className={cn(
                'relative w-auto px-[9px] pt-1.5 text-[18px] max-799:text-[16px] leading-[23px] max-799:leading-[20px] font-veneerCleanSoft',
                'data-[state=active]:text-white',
                'data-[state=inactive]:text-primary-500 hover:text-primary-400',
                tab.disabled && 'opacity-50 cursor-not-allowed',
                !tab.disabled && 'cursor-pointer',
                className,
              )}
            >
              {tab.labelId.toString() === activeTab.toString() && (
                <motion.span
                  layoutId={`custom-tab-bubble`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="absolute inset-0 z-10 bg-primary-200 rounded-t-[6px]"
                  transition={{ type: 'spring', bounce: 0.2, duration: 0.6 }}
                />
              )}
              <span className="relative z-20 whitespace-nowrap">
                {tab.label}
                {tab.count ? ` (${tab.count})` : ' '}
              </span>
            </TabsPrimitive.Trigger>
          ))}
        </TabsPrimitive.List>
      </div>
    </TabsPrimitive.Root>
  );
};

export default CustomTabs;
