import Image from 'next/image';
import React from 'react';

import { Label } from '../label';
import { RadioGroupItem } from '../radio-group';

// Types for the props
interface CardListingProps {
  value: number | string;
  id: number | string;
  cardNumber: string;
  expiry: string;
}

const CardListing: React.FC<CardListingProps> = ({
  value,
  id,
  cardNumber,
  expiry,
}) => {
  return (
    <div className="flex justify-center items-center space-x-2 py-2">
      <RadioGroupItem value={`${value}`} id={`${id}`} />
      <div className="rounded shadow-md p-2 bg-[#F2F3F5] w-full flex flex-col">
        <div className="flex items-center space-x-2">
          <Label htmlFor={`${id}`}>
            <div className="flex items-center space-x-2">
              <Image
                src="/fantasy/images/visa.svg"
                height={0}
                width={50}
                style={{ width: 'auto', height: 'auto' }}
                alt="master card"
                unoptimized={true}
              />
              <span>**** {cardNumber}</span>
            </div>
          </Label>
        </div>
        <div className="text-sm text-muted-foreground mr-5 ml-2">
          Expiry: {expiry}
        </div>
      </div>
    </div>
  );
};

export default CardListing;
