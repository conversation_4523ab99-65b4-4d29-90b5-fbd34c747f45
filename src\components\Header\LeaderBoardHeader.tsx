import Image from 'next/image';
import React from 'react';

import DefaultTeam from '@/assets/images/icons/defaultTeam.png';
import { Card } from '@/components/UI/card';
import { Config } from '@/helpers/context/config';
import { formatNumberWithCommas } from '@/lib/utils';

export interface ProfileStats {
  remainingSalary: number;
}

interface ProfileHeaderProps {
  stats: ProfileStats;
  leaderBordList: any;
}

const LeaderBoradHeader: React.FC<ProfileHeaderProps> = ({
  stats,
  leaderBordList,
}) => {
  const playerProfile = (playerImage: any) => {
    let flagUrl: any;

    if (playerImage) {
      if (playerImage.includes('uploads')) {
        flagUrl = Config?.mediaURL + playerImage;
      } else {
        flagUrl = playerImage;
      }
    } else {
      flagUrl = DefaultTeam;
    }
    return (
      <Image
        src={flagUrl}
        alt="icon"
        width={65}
        height={65}
        className="object-cover rounded-full"
        unoptimized={true}
      />
    );
  };

  const firstLeaderList = leaderBordList?.[0];
  return (
    <div className="flex items-center justify-between gap-4 p-4 bg-white">
      {/* User Info and Stats */}
      <div className="flex space-x-2 items-center">
        {playerProfile(firstLeaderList?.profileImage)}
        <h2 className="text-[22.4px] font-semibold">
          {firstLeaderList?.firstName} {firstLeaderList?.lastName}
        </h2>
      </div>
      {/* Salary */}
      <Card className="p-3 shadow-sm w-80 bg-dark-card-gradient text-white">
        <div className="text-center">
          <div className="text-sm text-slate-500 mb-1">Remaining Salary:</div>
          <div className="font-semibold text-slate-900">
            ${formatNumberWithCommas(stats?.remainingSalary)}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default LeaderBoradHeader;
