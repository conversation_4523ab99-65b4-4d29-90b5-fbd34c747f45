'use client';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import Select, { components } from 'react-select';

import SelectIndicator from '@/assets/images/icons/selectdropdownindicator.svg';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { Config } from '@/helpers/context/config';
import { quyerKeys } from '@/lib/queryKeys';

import type {
  FetchRLTournamentsSeasonResponse,
  FetchStandingsTeamDetailsResponse,
  FetchTournamentsSeasonResponse,
  SoccerStandingsResponse,
  StandingsTeamDetails,
} from '../../../types';
import Loader from '../Loader';
import DataTable from '../UI/DataTabel';
import {
  ARTeamStandingsColumns,
  RLTeamStandingsColumns,
  SoccerTeamStandingsColumns,
  teamStandingsColumns,
} from '../UI/DataTabel/standingsColumns';
import { generateUniqueId } from '@/lib/utils';

const fetchStandingDetails = async (
  tournament_id: string | null,
  season_id: string | null,
): Promise<FetchStandingsTeamDetailsResponse> => {
  const res = await axiosInstance.get<FetchStandingsTeamDetailsResponse>(
    `${Config.baseURL}sports/statistics/cricket/table/tournament/${tournament_id}?seasonId=${season_id ?? season_id}`,
  );
  return res?.data;
};

const fetchRLStandingDetails = async (
  tournament_id: string | null,
  season_id: string | null,
  sport_id: string | null,
) => {
  const res = await axiosInstance.get(
    `${Config.baseURL}sports/statistics/tournament/ladder/${tournament_id}?SportId=${sport_id}&seasonId=${season_id ?? season_id}`,
  );
  return res?.data;
};

const fetchSoccerStandingDetails = async (
  tournament_id: string | null,
  season_id: string | null,
): Promise<SoccerStandingsResponse> => {
  const res = await axiosInstance.get<SoccerStandingsResponse>(
    `${Config.baseURL}public/statistics/soccer/tournament/ladder/${tournament_id}?seasonId=${season_id}`,
  );
  return res?.data;
};

const fetchSeasonDetails = async (
  tournament_id: string | null,
  sport_id: string | null,
): Promise<FetchTournamentsSeasonResponse> => {
  const res = await axiosInstance.get<FetchTournamentsSeasonResponse>(
    `${Config.baseURL}/allsport/season?SportId=${sport_id}&tournamentId=${tournament_id}&leaderboard=true`,
  );
  return res?.data;
};

const DropdownIndicator = (props: any) => {
  return (
    <components.DropdownIndicator {...props}>
      <SelectIndicator />
    </components.DropdownIndicator>
  );
};

interface SeasonItem {
  year: string | number; // Assuming year can be either string or number
  id: string | number; // Assuming id can be either string or number
}

const Standings = () => {
  const searchParams = useSearchParams();
  const tournament_id = searchParams.get('tournament_id');
  const sport_id = searchParams.get('sport_id');
  const seasonId = searchParams.get('seasonId');
  const [selectedSeason, setSelectedSeason] = useState<any>(null);
  const [seasonOption, setSeasonOption] = useState<SeasonItem[] | undefined>(
    [],
  );

  const { data: StandingsList, isLoading } = useQuery({
    queryFn: () => fetchStandingDetails(tournament_id, selectedSeason),
    queryKey: [quyerKeys.getStandingsList, selectedSeason],
    enabled: sport_id == '4',
  });

  const { data: RLStandingsList, isLoading: isRLStandingsLoading } = useQuery({
    queryFn: () =>
      fetchRLStandingDetails(tournament_id, selectedSeason, sport_id),
    queryKey: [quyerKeys.getStandingsList, selectedSeason],
    enabled: sport_id == '12' || sport_id == '9',
  });

  const { data: SoccerStandingsList, isLoading: isSoccerStandingsLoading } =
    useQuery({
      queryFn: () => fetchSoccerStandingDetails(tournament_id, selectedSeason),
      queryKey: [quyerKeys.getSoccerStandingsList, selectedSeason],
      enabled: sport_id == '8',
    });

  const { data: seasonList, isLoading: isSeasonLoading } = useQuery({
    queryFn: () => fetchSeasonDetails(tournament_id, sport_id),
    queryKey: [quyerKeys.getSeasonList],
  });

  const StandingsDetailsList = StandingsList?.result?.result;
  const RLStandingsDetailsList = RLStandingsList?.result;

  const seasonListData = seasonList?.result?.rows?.sort((a: any, b: any) => {
    return b?.year - a?.year;
  });

  useEffect(() => {
    const seasonData = seasonListData?.map((item: SeasonItem) => {
      return {
        label: `Season Stats - ${item?.year}`,
        value: item?.id,
      };
    });
    // const sortedData = seasonData?.sort((a, b) => {
    //   return a?.label.localeCompare(b?.label);
    // });
    // let alldatas = sortedData?.unshift({
    //   label: 'All Season',
    //   value: 0,
    // });
    // @ts-expect-error
    setSeasonOption(seasonData);

    const currentYear = new Date().getFullYear().toString();
    const filteredItems = seasonListData?.map((item: SeasonItem) => {
      return {
        label: `Season Stats - ${item?.year}`,
        value: item?.id,
      };
    });

    setSelectedSeason(filteredItems?.[0]?.value);
  }, [seasonListData]);

  // Group by 'name' (assuming 'name' refers to ground)
  const GroupedByGround = StandingsDetailsList?.reduce<
    Record<string, StandingsTeamDetails[]>
  >((acc, item) => {
    const ground = item?.name; // Change to the desired property if needed
    if (!acc[ground]) {
      acc[ground] = [];
    }
    acc[ground].push({ ...item, group: ground });
    return acc;
  }, {});

  return (
    <>
      <div className="px-0 mx-0"></div>

      <div className="mx-[33px] max-799:mx-0 pt-[18px]">
        {isLoading ? (
          <div className="mt-1.5 ">
            <Loader />
          </div>
        ) : (
          <>
            <div className="">
              <h2 className="text-[16px] leading-[19px] font-semibold font-inter text-black-100">
                League Standings
              </h2>
              <div className="min-w-[230px] max-w-[230px] mt-[15px]">
                <Select
                  styles={{
                    control: (base) => ({
                      ...base,
                      border: 'none',
                      boxShadow: 'none',
                    }),
                  }}
                  value={seasonOption?.find((item: any) => {
                    return item?.value === selectedSeason;
                  })}
                  onChange={(e: any) => setSelectedSeason(e?.value)}
                  options={seasonOption}
                  classNamePrefix="select"
                  placeholder="season stats"
                  isSearchable={false}
                  isLoading={isSeasonLoading}
                  components={{ DropdownIndicator }}
                />
              </div>
            </div>
            <div className="rounded-lg grid grid-cols-1 w-full">
              {(() => {
                switch (sport_id) {
                  case '4':
                    return Object.entries(GroupedByGround || {})
                      .sort(([groupEndA], [groupEndB]) =>
                        groupEndA.localeCompare(groupEndB),
                      )
                      .map(([groupEnd, item]) => (
                        <div
                          className="h-full overflow-y-hidden pb-12 shadow- mt-2"
                          key={generateUniqueId()}
                        >
                          <DataTable
                            key={groupEnd}
                            columns={teamStandingsColumns}
                            data={item}
                            stickyColumns={[0]}
                          />
                        </div>
                      ));
                  case '12':
                    return (
                      <div className="h-full overflow-y-hidden pb-12 shadow- mt-2">
                        <DataTable
                          columns={RLTeamStandingsColumns}
                          data={RLStandingsDetailsList ?? []}
                          isLoading={isRLStandingsLoading}
                          stickyColumns={[0]}
                        />
                      </div>
                    );
                  case '8':
                    return (
                      <div className="h-full overflow-y-hidden pb-12 shadow- mt-2">
                        <DataTable
                          columns={SoccerTeamStandingsColumns}
                          data={SoccerStandingsList?.result ?? []}
                          isLoading={isSoccerStandingsLoading}
                          stickyColumns={[0]}
                        />
                      </div>
                    );
                  default:
                    return (
                      <div className="h-full overflow-y-hidden pb-12 shadow- mt-2">
                        <DataTable
                          columns={ARTeamStandingsColumns}
                          data={RLStandingsDetailsList ?? []}
                          isLoading={isRLStandingsLoading}
                          stickyColumns={[0]}
                        />
                      </div>
                    );
                }
              })()}
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default Standings;
