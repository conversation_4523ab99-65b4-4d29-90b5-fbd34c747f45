# Modules Documentation

## Competition Module

### Overview

The competition module handles all aspects of fantasy sports competitions across different sports (AFL, Soccer, Rugby League, NFL).

### Components

1. **Competition Management**

   - Location: `src/app/(routes)/competitions/`
   - Features:
     - Competition creation
     - Team management
     - Live updates
     - Scoring system

2. **Sport-Specific Implementations**
   - AFL Football: `src/app/(routes)/competitions/football/`
   - Soccer: `src/app/(routes)/competitions/soccer/`
   - Rugby League: `src/app/(routes)/competitions/rugby-league/`
   - NFL: `src/app/(routes)/competitions/nfl/`

## Player Management Module

### Overview

Handles player selection, statistics, and team composition across different sports.

### Components

1. **Player Selection**

   - Location: `src/components/Players/`
   - Features:
     - Player search and filtering
     - Role-based selection
     - Statistics display
     - Captain/Vice-Captain selection

2. **Player Statistics**
   - Location: `src/components/Common/Stats/`
   - Features:
     - Performance metrics
     - Historical data
     - Live updates

## Team Creation Module

### Overview

Manages the creation and modification of fantasy teams across different sports.

### Features

1. **Team Builder**

   - Sport-specific team composition rules
   - Salary cap management
   - Player role restrictions
   - Team validation

2. **Team Types**
   - Manual selection
   - Auto-generation ("Lucky Team")
   - Expert suggestions

## Scoring System Module

### Overview

Handles point calculations and scoring across different sports.

### Components

1. **Sport-Specific Scoring**

   - AFL Scoring Rules
   - Soccer Scoring System
   - Rugby League Points
   - NFL Scoring

2. **Score Tracking**
   - Live score updates
   - Historical performance
   - Bonus points system

## User Management Module

### Overview

Handles user authentication, profiles, and settings.

### Components

1. **Authentication**

   - Location: `src/app/api/auth/`
   - Features:
     - User registration
     - Login/Logout
     - Session management

2. **User Profile**
   - Location: `src/components/Settings/`
   - Features:
     - Profile management
     - Preferences
     - Transaction history
     - Notifications

## Data Management Module

### Overview

Handles data storage, retrieval, and real-time updates.

### Components

1. **API Integration**

   - Location: `src/helpers/api.tsx`
   - Features:
     - Data fetching
     - Real-time updates
     - Error handling

2. **State Management**
   - Location: `src/store/`
   - Features:
     - Global state
     - Sport-specific states
     - User preferences
