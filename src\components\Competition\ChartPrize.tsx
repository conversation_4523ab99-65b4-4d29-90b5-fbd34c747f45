import React from 'react';

interface ChartIconProps extends React.SVGProps<SVGSVGElement> {
  fillColor?: string;
  unFilledColor?: string;
  numberOfFilled?: number;
}

export const MyChartUpIcon: React.FC<ChartIconProps> = ({
  fillColor = '#989898',
  unFilledColor = '#989898',
  numberOfFilled = 0,
  ...props
}) => {
  const paths = [
    {
      id: 'Path_179065',
      d: 'M32.541,235.621h2.094a.931.931,0,0,1,.929.929v6.338a.931.931,0,0,1-.929.929H32.541a.931.931,0,0,1-.929-.929V236.55a.931.931,0,0,1,.929-.929',
      transform: 'translate(-29.761 -221.828)',
    },
    {
      id: 'Path_179070',
      d: 'M154.265,174.709h2.094a.931.931,0,0,1,.929.929v9.9a.931.931,0,0,1-.929.929h-2.094a.931.931,0,0,1-.929-.929v-9.9a.931.931,0,0,1,.929-.929',
      transform: 'translate(-144.36 -164.482)',
    },
    {
      id: 'Path_179067',
      d: 'M279.007,114.723v13.47a.93.93,0,0,1-.928.929h-2.018a1.374,1.374,0,0,1-.622-.179l-.009-.006a.93.93,0,0,1-.335-.479,1.275,1.275,0,0,1-.039-.253s0-.007,0-.011v-13.47a.931.931,0,0,1,.929-.929h2.093a.93.93,0,0,1,.929.929',
      transform: 'translate(-258.955 -107.133)',
    },
    {
      id: 'Path_179066',
      d: 'M400.737,53.813V70.848a.931.931,0,0,1-.929.929h-2.093a.931.931,0,0,1-.929-.929V53.813a.931.931,0,0,1,.929-.929h2.093a.931.931,0,0,1,.929.929',
      transform: 'translate(-373.559 -49.788)',
    },
  ];

  return (
    <svg width={27.179} height={22} viewBox="0 0 27.179 22" {...props}>
      <g id="Group_110641" transform="translate(0 0)">
        <g id="Group_104583" transform="translate(0 0)">
          {paths.map((path, index) => (
            <path
              key={path.id}
              id={path.id}
              d={path.d}
              transform={path.transform}
              fill={index < numberOfFilled ? fillColor : unFilledColor}
              fillRule="evenodd"
            />
          ))}
          <path
            id="Path_179073"
            d="M8.319,1.45C3.64-2.953-2.952,3.643,1.449,8.32c4.678,4.4,11.272-2.194,6.87-6.87M5.328,8.061c.028.334.048.833-.412.845s-.461-.5-.452-.837A2.265,2.265,0,0,1,3.3,7.394a.433.433,0,0,1,.593-.63,1.281,1.281,0,0,0,1,.475A1.041,1.041,0,0,0,6,6.291a1.041,1.041,0,0,0-1.114-.948,1.908,1.908,0,0,1-1.98-1.815h0a1.861,1.861,0,0,1,1.54-1.77c-.02-.34-.073-.883.411-.9s.453.563.454.907c2.135.532,1.3,1.962.587,1.252a1.685,1.685,0,0,0-1.012-.443h0a1.044,1.044,0,0,0-1.115.948h0c.132,1.372,1.745.625,2.5,1.464a1.779,1.779,0,0,1-.941,3.068"
            transform="translate(0 0)"
            fill={fillColor}
            fillRule="evenodd"
          />
        </g>
      </g>
    </svg>
  );
};

interface ChartIconProps extends React.SVGProps<SVGSVGElement> {
  fillColor?: string;
  unFilledColor?: string;
  numberOfFilled?: number;
}

export const MyChartDownIcon: React.FC<ChartIconProps> = ({
  fillColor = '#989898',
  unFilledColor = '#989898',
  numberOfFilled = 0,
  ...props
}) => {
  const paths = [
    {
      id: 'Path_179065',
      d: 'M32.541,235.621h2.094a.931.931,0,0,1,.929.929v6.338a.931.931,0,0,1-.929.929H32.541a.931.931,0,0,1-.929-.929V236.55a.931.931,0,0,1,.929-.929',
      transform: 'translate(-5538.768 -6556.828)',
    },
    {
      id: 'Path_179070',
      d: 'M154.265,174.709h2.094a.931.931,0,0,1,.929.929v9.9a.931.931,0,0,1-.929.929h-2.094a.931.931,0,0,1-.929-.929v-9.9a.931.931,0,0,1,.929-.929',
      transform: 'translate(-5667.618 -6499.482)',
    },
    {
      id: 'Path_179067',
      d: 'M279.007,114.723v13.47a.93.93,0,0,1-.928.929h-2.018a1.374,1.374,0,0,1-.622-.179l-.009-.006a.93.93,0,0,1-.335-.479,1.275,1.275,0,0,1-.039-.253s0-.007,0-.011v-13.47a.931.931,0,0,1,.929-.929h2.093a.93.93,0,0,1,.929.929',
      transform: 'translate(-5796.463 -6442.133)',
    },
    {
      id: 'Path_179074',
      d: 'M400.737,53.813V70.848a.931.931,0,0,1-.929.929h-2.093a.931.931,0,0,1-.929-.929V53.813a.931.931,0,0,1,.929-.929h2.093a.931.931,0,0,1,.929.929',
      transform: 'translate(-5925.319 -6384.788)',
    },
  ];

  // Reverse the fill order to match decreasing values
  const reversedNumberOfFilled = Math.min(numberOfFilled, paths.length);

  return (
    <svg width={27.533} height={22} viewBox="0 0 27.533 22" {...props}>
      <g id="Group_110634" transform="translate(5528.533 6335)">
        {paths.map((path, index) => (
          <path
            key={path.id}
            id={path.id}
            d={path.d}
            transform={path.transform}
            fill={
              index >= paths.length - reversedNumberOfFilled
                ? fillColor
                : unFilledColor
            }
            fillRule="evenodd"
          />
        ))}
        <path
          id="Path_179073"
          d="M8.319,1.45C3.64-2.953-2.952,3.643,1.449,8.32c4.678,4.4,11.272-2.194,6.87-6.87M5.328,8.061c.028.334.048.833-.412.845s-.461-.5-.452-.837A2.265,2.265,0,0,1,3.3,7.394a.433.433,0,0,1,.593-.63,1.281,1.281,0,0,0,1,.475A1.041,1.041,0,0,0,6,6.291a1.041,1.041,0,0,0-1.114-.948,1.908,1.908,0,0,1-1.98-1.815h0a1.861,1.861,0,0,1,1.54-1.77c-.02-.34-.073-.883.411-.9s.453.563.454.907c2.135.532,1.3,1.962.587,1.252a1.685,1.685,0,0,0-1.012-.443h0a1.044,1.044,0,0,0-1.115.948h0c.132,1.372,1.745.625,2.5,1.464a1.779,1.779,0,0,1-.941,3.068"
          transform="translate(-5510.768 -6335)"
          fill={fillColor}
          fillRule="evenodd"
        />
      </g>
    </svg>
  );
};
