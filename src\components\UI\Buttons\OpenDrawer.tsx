'use client';

import React from 'react';

const OpenDrawerButton = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="46"
      height="26"
      viewBox="0 0 46 26"
    >
      <defs>
        <linearGradient
          id="linear-gradient"
          x1="0.5"
          x2="0.5"
          y2="1"
          gradientUnits="objectBoundingBox"
        >
          <stop offset="0" stopColor="#003764" stopOpacity="0.902" />
          <stop offset="1" stopColor="#090b0d" />
        </linearGradient>
      </defs>
      <g
        id="Group_104087"
        data-name="Group 104087"
        transform="translate(-139.04 26.215) rotate(-180)"
      >
        <g id="Group_114482" data-name="Group 114482">
          <path
            id="Rectangle_2837"
            data-name="Rectangle 2837"
            d="M0,0H46a0,0,0,0,1,0,0V6A20,20,0,0,1,26,26H20A20,20,0,0,1,0,6V0A0,0,0,0,1,0,0Z"
            transform="translate(-185.04 0.215)"
            opacity="0.74"
            fill="url(#linear-gradient)"
          />
          <path
            id="Path_12085"
            data-name="Path 12085"
            d="M0,0,6.682,6.832,13.363,0"
            transform="translate(-168.903 8.883)"
            fill="none"
            stroke="#fff"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
        </g>
      </g>
    </svg>
  );
};

export default OpenDrawerButton;
