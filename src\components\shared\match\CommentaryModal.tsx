import React, { useState, useEffect, SetStateAction, Dispatch } from 'react';
import { Dialog } from '@/components/UI/dialog';
import {
  Award,
  RotateCw,
  Flag,
  ArrowRightLeft,
  MessagesSquare,
  Clock,
  X,
  Loader,
} from 'lucide-react';
import Image from 'next/image';
import type { NrlMatch, MatchEvent, CommentaryFilters } from '@/lib/types/nrl';
import type {
  AflMatch,
  AflMatchEvent,
  AflCommentaryFilters,
} from '@/lib/types/afl';
import { Config } from '@/helpers/context/config';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { useQuery } from '@tanstack/react-query';
import { quyerKeys } from '@/lib/queryKeys';
import {
  CricketCommentaryResponse,
  CommentaryEvent as CricketCommentaryEvent,
} from '../../../../types/commentry';
import { useCompetition } from '@/helpers/context/competitionContext';
import { extractSoccerPlayerName } from '@/lib/utils';

interface CricketEvent {
  id: number;
  Ov: string;
  Aid: number | null;
  Oid: number | null;
  T: string;
  S: string;
  Sv: string;
  event: string | null;
  ballNbr: string;
  Run: string;
  over_ended: boolean;
  legbyes: string | null;
  createdAt: string;
  updatedAt: string;
  inningId: number | null;
  eventId: number;
  bowler: string | null;
  betting: string | null;
  batTeamName: string | null;
}

interface CommentaryEvent {
  id?: string;
  type: string;
  team: string;
  min?: string;
  period?: string;
  minute?: string;
  player_name?: string;
  player?: string;
  T?: string;
  Run?: string;
  event?: string | null;
  ballNbr?: string;
  over_ended?: boolean;
  legbyes?: string | null;
  batTeamName?: string | null;
  [key: string]: any;
}

interface CommentaryTeam {
  id: number;
  name: string;
  nameCode?: string;
  flag?: string | null;
  score: string | number;
  shortName?: string;
  [key: string]: any;
}

interface CommentaryMatchData {
  localteam: CommentaryTeam;
  visitorteam: CommentaryTeam;
  status: {
    value: string;
    [key: string]: any;
  };
  events: {
    event: CommentaryEvent[] | CommentaryEvent;
  };
  [key: string]: any;
}

interface CommentaryResponse {
  status: boolean;
  result: CommentaryMatchData;
  matchInfo?: {
    localteam: CommentaryTeam;
    visitorteam: CommentaryTeam;
    status: {
      value: string;
    };
  };
}

interface CommentaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  matchId?: string;
  sportId?: number;
  isLiveCommentaryLoading?: boolean;
  liveCommentary?: CricketCommentaryResponse;
  setEventFilter: Dispatch<SetStateAction<string | undefined>>;
  compCard?: {
    localteam: {
      name: string;
      score: string | number;
      flag?: string;
    };
    visitorteam: {
      name: string;
      score: string | number;
      flag?: string;
    };
    status: {
      value: string;
    };
  };
}

const fetchMatchCommentary = async (
  matchId: string | undefined,
  sportId: number,
  compCard?: any,
): Promise<CommentaryResponse> => {
  if (!matchId) {
    throw new Error('Match ID is required');
  }

  let endpoint = '';

  // Determine which API endpoint to call based on sport
  if (sportId === 12) {
    // NRL
    endpoint = `public/rls/commentary/${matchId}`;
  } else if (sportId === 9) {
    // AFL
    endpoint = `public/ar/commentary/${matchId}`;
  } else if (sportId === 8) {
    // Soccer
    endpoint = `public/soccer/commentary/${matchId}`;
  } else {
    throw new Error('Unsupported sport type');
  }

  const res = await axiosInstance.get<CommentaryResponse>(
    Config.baseURL + endpoint,
  );

  // Transform the response for soccer matches
  if (sportId === 8) {
    const soccerEvents = Array.isArray(res.data.result)
      ? res.data.result.map((event: any) => ({
          id: event.id?.toString() || '',
          type: event.commentryType || 'NONE',
          team: event.team || '',
          min: event.Min || '0',
          player_name: extractSoccerPlayerName(event.Txt) || '',
          description: event.Txt || '',
          teamName: event.teamName || '',
          teamFlag: event.teamFlag || '',
          time: event.Min || '0',
        }))
      : [];

    if (!compCard) {
      throw new Error('Match details not available');
    }

    const transformedResponse = {
      status: res.data.status,
      result: {
        localteam: {
          id: 0,
          name: compCard.localteam.name,
          score: compCard.localteam.score,
          flag: compCard.localteam.flag,
        },
        visitorteam: {
          id: 0,
          name: compCard.visitorteam.name,
          score: compCard.visitorteam.score,
          flag: compCard.visitorteam.flag,
        },
        status: {
          value: compCard.status.value,
        },
        events: {
          event: soccerEvents,
        },
      },
    };

    return transformedResponse;
  }

  return res?.data;
};

const CommentaryModal: React.FC<CommentaryModalProps> = ({
  matchId,
  isOpen,
  onClose,
  sportId = 0,
  isLiveCommentaryLoading,
  liveCommentary,
  setEventFilter,
  compCard,
}) => {
  // Ensure eventDetailsResponse is in scope for all usages
  const { eventDetailsResponse } = useCompetition();

  const [filter, setFilter] = useState<string>('Full Commentary');
  const [filteredEvents, setFilteredEvents] = useState<CommentaryEvent[]>([]);
  const isAfl = sportId === 9;
  const isNrl = sportId === 12;
  const isCricket = sportId === 4;
  const isSoccer = sportId === 8;

  // Add styles for fallback to initial
  useEffect(() => {
    const styleTag = document.createElement('style');
    styleTag.innerHTML = `
      .fallback-to-initial {
        position: relative;
      }
      .fallback-to-initial::after {
        content: attr(data-initial);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-weight: bold;
        font-size: 1rem;
        color: #1e40af; /* text-blue-900 */
      }
    `;
    document.head.appendChild(styleTag);

    return () => {
      document.head.removeChild(styleTag);
    };
  }, []);

  // Use React Query for data fetching
  const {
    data: commentaryData,
    isLoading,
    error: queryError,
    refetch,
  } = useQuery({
    queryFn: () => fetchMatchCommentary(matchId, sportId, compCard),
    queryKey: [quyerKeys.getMatchCommentary, matchId, sportId],
    enabled: isOpen && !!matchId && !isCricket,
    staleTime: 30000,
    refetchInterval: isOpen ? 60000 : false,
  });

  const match = commentaryData?.result || null;
  const error = queryError ? 'Failed to load match data' : null;

  // Effect to set filtered events when match changes
  useEffect(() => {
    if (isCricket && liveCommentary?.result?.result) {
      // Ensure result is always an array
      const eventsData = Array.isArray(liveCommentary.result.result)
        ? liveCommentary.result.result
        : [liveCommentary.result.result];

      const events = eventsData.map((event: any) => ({
        id: event.id ? event.id.toString() : '',
        type: event.event || 'NONE',
        team: event.T || '',
        min: event.Ov,
        player_name: event.T?.split(' to ')[1] || '',
        T: event.T,
        Run: event.Run,
        event: event.event || undefined,
        ballNbr: event.ballNbr,
        over_ended: event.over_ended,
        legbyes: event.legbyes || undefined,
        batTeamName: event.batTeamName,
      }));

      // Sort events
      events.sort((a, b) => {
        const [overA, ballA] = (a.min || '0.0').split('.').map(Number);
        const [overB, ballB] = (b.min || '0.0').split('.').map(Number);

        if (overA !== overB) return overB - overA;
        return ballB - ballA;
      });

      setFilteredEvents(events);
      return;
    }

    if (!match?.events?.event) {
      setFilteredEvents([]);
      return;
    }

    const events = Array.isArray(match.events.event)
      ? [...match.events.event]
      : [match.events.event];

    if (isNrl) {
      events.sort((a, b) => parseInt(b.min || '0') - parseInt(a.min || '0'));
    } else if (isAfl) {
      events.sort((a, b) => {
        const periodA = parseInt(a.period || '0');
        const periodB = parseInt(b.period || '0');

        if (periodA !== periodB) {
          return periodB - periodA;
        }

        return parseInt(b.minute || '0') - parseInt(a.minute || '0');
      });
    }

    setFilteredEvents(events);
  }, [match, isNrl, isAfl, isCricket, liveCommentary]);

  // Effect to filter events when the filter type changes
  useEffect(() => {
    if (isCricket && liveCommentary?.result?.result) {
      // Ensure result is always an array
      const eventsData = Array.isArray(liveCommentary.result.result)
        ? liveCommentary.result.result
        : [liveCommentary.result.result];

      let events = eventsData.map((event: any) => ({
        id: event.id ? event.id.toString() : '',
        type: event.event || 'NONE',
        team: event.T || '',
        min: event.Ov,
        player_name: event.T?.split(' to ')[1] || '',
        T: event.T,
        Run: event.Run,
        event: event.event || undefined,
        ballNbr: event.ballNbr,
        over_ended: event.over_ended,
        legbyes: event.legbyes || undefined,
        batTeamName: event.batTeamName,
      }));

      // Filter events if not 'Full Commentary'
      if (filter !== 'Full Commentary') {
        events = events.filter((event) => event.type === filter);
      }

      // Sort events
      events.sort((a, b) => {
        const [overA, ballA] = (a.min || '0.0').split('.').map(Number);
        const [overB, ballB] = (b.min || '0.0').split('.').map(Number);

        if (overA !== overB) return overB - overA;
        return ballB - ballA;
      });

      setFilteredEvents(events);
      return;
    }

    if (!match?.events?.event) return;

    let events = Array.isArray(match.events.event)
      ? [...match.events.event]
      : [match.events.event];

    if (filter !== 'Full Commentary') {
      if (isNrl) {
        events = events.filter((event) => event.type === filter);
      } else if (isAfl) {
        events = events.filter((event) => event.type === filter);
      }
    }

    if (isNrl) {
      events.sort((a, b) => parseInt(b.min || '0') - parseInt(a.min || '0'));
    } else if (isAfl) {
      events.sort((a, b) => {
        const periodA = parseInt(a.period || '0');
        const periodB = parseInt(b.period || '0');

        if (periodA !== periodB) {
          return periodB - periodA;
        }

        return parseInt(b.minute || '0') - parseInt(a.minute || '0');
      });
    }
    if (filter !== 'Full Commentary') {
      if (isSoccer) {
        events = events.filter((event) => event.type === filter);
      }
    }

    setFilteredEvents(events);
  }, [
    match?.events?.event,
    filter,
    isNrl,
    isAfl,
    isCricket,
    liveCommentary,
    isSoccer,
  ]);

  // Helper function to determine event style and icon
  const getEventStyle = (eventType: string) => {
    if (isCricket) {
      switch (eventType) {
        case 'WICKET':
        case 'OUT':
          return {
            icon: <Award className="h-4 w-4" />,
            color: 'text-red-500',
            bgColor: 'bg-red-50',
            textColor: 'text-red-700',
          };
        case 'FOUR':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-blue-500',
            bgColor: 'bg-blue-50',
            textColor: 'text-blue-700',
          };
        case 'SIX':
          return {
            icon: <Award className="h-4 w-4" />,
            color: 'text-purple-500',
            bgColor: 'bg-purple-50',
            textColor: 'text-purple-700',
          };
        default:
          return {
            icon: <MessagesSquare className="h-4 w-4" />,
            color: 'text-gray-500',
            bgColor: 'bg-gray-50',
            textColor: 'text-gray-700',
          };
      }
    } else if (isNrl) {
      switch (eventType) {
        case 'Try':
        case 'Penalty Try':
          return {
            icon: <Award className="h-4 w-4" />,
            color: 'text-emerald-500',
            bgColor: 'bg-emerald-50',
            textColor: 'text-emerald-700',
          };
        case 'Conversion':
        case 'CONVERSION':
          return {
            icon: <RotateCw className="h-4 w-4" />,
            color: 'text-blue-500',
            bgColor: 'bg-blue-50',
            textColor: 'text-blue-700',
          };
        case 'Penalty':
        case 'PENALTY':
        case 'Penalty Goal':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-purple-500',
            bgColor: 'bg-purple-50',
            textColor: 'text-purple-700',
          };
        case 'INTERCHANGE':
        case 'Interchange':
          return {
            icon: <ArrowRightLeft className="h-4 w-4" />,
            color: 'text-gray-500',
            bgColor: 'bg-gray-50',
            textColor: 'text-gray-700',
          };
        case 'Conv Miss':
        case '2Pts FG Miss':
          return {
            icon: <RotateCw className="h-4 w-4" />,
            color: 'text-orange-500',
            bgColor: 'bg-orange-50',
            textColor: 'text-orange-700',
          };
        case 'Field Goal':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-indigo-500',
            bgColor: 'bg-indigo-50',
            textColor: 'text-indigo-700',
          };
        default:
          return {
            icon: <MessagesSquare className="h-4 w-4" />,
            color: 'text-gray-500',
            bgColor: 'bg-gray-50',
            textColor: 'text-gray-700',
          };
      }
    } else if (isAfl) {
      switch (eventType) {
        case 'goal':
          return {
            icon: <Award className="h-4 w-4" />,
            color: 'text-emerald-500',
            bgColor: 'bg-emerald-50',
            textColor: 'text-emerald-700',
          };
        case 'behind':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-blue-500',
            bgColor: 'bg-blue-50',
            textColor: 'text-blue-700',
          };
        case 'rushed':
          return {
            icon: <ArrowRightLeft className="h-4 w-4" />,
            color: 'text-purple-500',
            bgColor: 'bg-purple-50',
            textColor: 'text-purple-700',
          };
        default:
          return {
            icon: <MessagesSquare className="h-4 w-4" />,
            color: 'text-gray-500',
            bgColor: 'bg-gray-50',
            textColor: 'text-gray-700',
          };
      }
    } else if (isSoccer) {
      switch (eventType) {
        case 'goal':
          return {
            icon: <Award className="h-4 w-4" />,
            color: 'text-green-500',
            bgColor: 'bg-green-50',
            textColor: 'text-green-700',
          };
        case 'yellowCard':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-yellow-700',
            bgColor: 'bg-yellow-50',
            textColor: 'text-yellow-700',
          };
        case 'redCard':
          return {
            icon: <Flag className="h-4 w-4" />,
            color: 'text-red-500',
            bgColor: 'bg-red-50',
            textColor: 'text-red-700',
          };
        case 'substitution':
          return {
            icon: <ArrowRightLeft className="h-4 w-4" />,
            color: 'text-blue-500',
            bgColor: 'bg-blue-50',
            textColor: 'text-blue-700',
          };
        default:
          return {
            icon: <MessagesSquare className="h-4 w-4" />,
            color: 'text-gray-500',
            bgColor: 'bg-gray-50',
            textColor: 'text-gray-700',
          };
      }
    }

    return {
      icon: <MessagesSquare className="h-4 w-4" />,
      color: 'text-gray-500',
      bgColor: 'bg-gray-50',
      textColor: 'text-gray-700',
    };
  };

  const getTeamIcon = (teamFlag: string | undefined): string | undefined => {
    if (!teamFlag || teamFlag.trim() === '') return undefined;

    if (teamFlag.includes('uploads')) {
      return `${Config.mediaURL}${teamFlag}`;
    }

    if (teamFlag.startsWith('http')) {
      return teamFlag;
    }

    return teamFlag;
  };

  const getTeamInitial = (team: CommentaryTeam) => {
    if (team.nameCode && team.nameCode.trim() !== '') {
      return team.nameCode.substring(0, 1).toUpperCase();
    }

    if (team.shortName && team.shortName.trim() !== '') {
      return team.shortName.substring(0, 1).toUpperCase();
    }

    if (team.name && team.name.trim() !== '') {
      return team.name.substring(0, 1).toUpperCase();
    }

    return 'T';
  };

  // Get match info based on sport type
  const getMatchInfo = () => {
    if (isSoccer && eventDetailsResponse?.result?.eventDetails) {
      const eventDetails = eventDetailsResponse.result.eventDetails;
      return {
        localteam: {
          name: eventDetails.homeTeam?.name || '',
          score: eventDetails.ScoreBoard?.Tr1 || 0,
          flag: eventDetails.homeTeam?.flag
            ? `${Config.mediaURL}${eventDetails.homeTeam.flag}`
            : '',
        },
        visitorteam: {
          name: eventDetails.awayTeam?.name || '',
          score: eventDetails.ScoreBoard?.Tr2 || 0,
          flag: eventDetails.awayTeam?.flag
            ? `${Config.mediaURL}${eventDetails.awayTeam.flag}`
            : '',
        },
        status: {
          value: eventDetails.status || 'In Progress',
        },
      };
    }
    return commentaryData?.matchInfo;
  };

  const matchInfo = getMatchInfo();

  // Show loading state
  if (isLoading || isLiveCommentaryLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="bg-white p-8 rounded-lg shadow-xl flex flex-col items-center">
          <Loader className="animate-spin h-8 w-8 text-blue-600 mb-4" />
          <p className="text-gray-600">Loading match data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || (!match && !isCricket)) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="bg-white p-8 rounded-lg shadow-xl">
          <h3 className="text-lg font-bold text-red-600 mb-2">Error</h3>
          <p className="text-gray-700">{error || 'Match data not available'}</p>
          <button
            onClick={onClose}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  // Determine filter options based on sport
  const renderFilterOptions = () => {
    if (isCricket) {
      return (
        <select
          value={filter}
          onChange={(e) => {
            if (e.target.value !== 'Full Commentary') {
              setEventFilter(e.target.value);
              setFilter(e.target.value);
            } else {
              setEventFilter(undefined);
              setFilter(e.target.value);
            }
          }}
          className="w-48 p-2 rounded border bg-blue-600 text-white border-blue-700"
        >
          <option value="Full Commentary">Full Commentary</option>
          <option value="Wicket">Wickets</option>
          <option value="Four">Fours</option>
          <option value="Six">Sixes</option>
        </select>
      );
    } else if (isNrl) {
      return (
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="w-48 p-2 rounded border bg-blue-600 text-white border-blue-700"
        >
          <option value="Full Commentary">Full Commentary</option>
          <option value="Try">Try</option>
          <option value="Conversion">Conversion</option>
          <option value="Conv Miss">Conv Miss</option>
          <option value="Penalty">Penalty</option>
          <option value="Penalty Try">Penalty Try</option>
          <option value="Penalty Goal">Penalty Goal</option>
          <option value="Field Goal">Field Goal</option>
          <option value="2Pts FG Miss">2 Point Field Goal Miss</option>
        </select>
      );
    } else if (isAfl) {
      return (
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="w-48 p-2 rounded border bg-blue-600 text-white border-blue-700"
        >
          <option value="Full Commentary">Full Commentary</option>
          <option value="goal">Goal</option>
          <option value="behind">Behind</option>
        </select>
      );
    } else if (isSoccer) {
      return (
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="w-48 p-2 rounded border bg-blue-600 text-white border-blue-700"
        >
          <option value="Full Commentary">Full Commentary</option>
          <option value="goal">Goal</option>
          <option value="yellowCard">Yellow Card</option>
          <option value="redCard">Red Card</option>
          <option value="substitution">Substitution</option>
        </select>
      );
    }

    return null;
  };

  // Render event description based on sport and event type
  const renderEventDescription = (event: CommentaryEvent) => {
    if (isSoccer) {
      switch (event.type) {
        case 'goal':
          return `${event.description} scored a goal`;
        case 'yellowCard':
          return `${event.description} received a yellow card`;
        case 'redCard':
          return `${event.description} received a red card`;
        case 'substitution':
          return `${event.description} was substituted`;
        default:
          return event.description || event.type;
      }
    } else if (isCricket) {
      switch (event.type) {
        case 'WICKET':
        case 'OUT':
          return `${event.player_name} takes a wicket for ${event.team}.`;
        case 'FOUR':
          return `${event.T} - FOUR runs!`;
        case 'SIX':
          return `${event.T} - SIX runs!`;
        default:
          return event.T;
      }
    } else if (isNrl) {
      switch (event.type) {
        case 'Try':
          return `${event.player_name} goes over for ${event.team} to add four points to their tally.`;
        case 'Penalty Try':
          return `${event.player_name} awarded a penalty try for ${event.team}.`;
        case 'Conversion':
          return `${event.player_name} converts for ${event.team}.`;
        case 'Penalty':
          return `${event.player_name} adds the two points for ${event.team}.`;
        case 'Penalty Goal':
          return `${event.player_name} kicks a penalty goal for ${event.team}.`;
        case 'Conv Miss':
          return `${event.player_name} misses the conversion for ${event.team}.`;
        case 'Field Goal':
          return `${event.player_name} kicks a field goal for ${event.team}.`;
        case '2Pts FG Miss':
          return `${event.player_name} misses a 2-point field goal for ${event.team}.`;
        case 'INTERCHANGE':
          return `${event.player_name} enters the field for ${event.team}.`;
        default:
          return `${event.type}: ${event.player_name || ''}, ${event.team}.`;
      }
    } else if (isAfl) {
      switch (event.type) {
        case 'goal':
          return `${event.player} kicks a goal for ${event.team}.`;
        case 'behind':
          return `${event.player} kicks a behind for ${event.team}.`;
        case 'rushed':
          return `Rushed behind for ${event.team}.`;
        default:
          return `${event.type}: ${event.player || ''}, ${event.team}.`;
      }
    }

    return '';
  };

  // Determine player name field based on sport
  const getPlayerName = (event: CommentaryEvent) => {
    if (isCricket) {
      return event.player_name || 'Unknown Player';
    } else if (isNrl) {
      return event.player_name || 'Unknown Player';
    } else if (isAfl) {
      return event.player || 'Unknown Player';
    } else if (isSoccer) {
      return event.player_name || 'Unknown Player';
    }

    return 'Unknown Player';
  };

  // Determine time/minute field based on sport
  const getEventTime = (event: CommentaryEvent) => {
    if (isCricket) {
      return event.min || '0.0';
    } else if (isNrl) {
      return `${event.min || '0'}'`;
    } else if (isAfl) {
      return `Q${event.period || '0'} ${event.minute || '0'}'`;
    } else if (isSoccer) {
      return event.min || '0.0';
    }

    return "0'";
  };

  // Determine the correct team for the event
  const getTeamForEvent = (event: CommentaryEvent): CommentaryTeam => {
    const defaultTeam: CommentaryTeam = {
      id: 0,
      name: typeof event.team === 'string' ? event.team : 'Unknown Team',
      score: 0,
    };

    if (!match) return defaultTeam;

    // Check for team references
    if (
      event.team === 'localteam' ||
      event.team === 'hometeam' ||
      event.team === match.localteam?.name
    ) {
      return match.localteam || defaultTeam;
    } else {
      return match.visitorteam || defaultTeam;
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] flex flex-col">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-bold">Match Commentary</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => refetch()}
              className="p-1 rounded-full hover:bg-gray-100 text-blue-600"
              title="Refresh commentary"
            >
              <RotateCw size={18} />
            </button>
            <button
              onClick={onClose}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Match Info */}
        {(match || (isCricket && compCard)) && (
          <div className="bg-gradient-to-r from-[#0A2D5A] to-[#1A4889] text-white p-4 flex justify-between items-center">
            <div className="flex items-center gap-2">
              {isCricket ? (
                <>
                  <div className="relative w-10 h-10 bg-white rounded-full flex items-center justify-center">
                    <img
                      src={getTeamIcon(compCard?.localteam?.flag)}
                      alt={compCard?.localteam?.name}
                      className="object-contain"
                      width={24}
                      height={24}
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.parentElement?.parentElement?.classList.add(
                          'fallback-to-initial',
                        );
                      }}
                    />
                  </div>
                  <span className="font-semibold">
                    {compCard?.localteam?.name || 'Team 1'}
                  </span>
                </>
              ) : match && match.localteam ? (
                <>
                  {match.localteam.flag && getTeamIcon(match.localteam.flag) ? (
                    <div
                      className="w-10 h-10 bg-white rounded-full flex items-center justify-center overflow-hidden"
                      data-initial={getTeamInitial(match.localteam)}
                    >
                      <div className="relative w-6 h-6">
                        <img
                          src={getTeamIcon(match.localteam.flag)}
                          alt={match.localteam.name}
                          className="object-contain"
                          width={24}
                          height={24}
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                            e.currentTarget.parentElement?.parentElement?.classList.add(
                              'fallback-to-initial',
                            );
                          }}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                      <span className="font-bold text-blue-900">
                        {getTeamInitial(match.localteam)}
                      </span>
                    </div>
                  )}
                  <span className="font-semibold">{match.localteam.name}</span>
                </>
              ) : null}
            </div>

            <div className="flex flex-col items-center">
              <div className="text-2xl font-bold">
                {isCricket ? (
                  <>
                    {compCard?.localteam?.score || '0'} -{' '}
                    {compCard?.visitorteam?.score || '0'}
                  </>
                ) : match && match.localteam && match.visitorteam ? (
                  <>
                    {match.localteam.score} - {match.visitorteam.score}
                  </>
                ) : null}
              </div>
              <div className="text-sm opacity-80">
                {isCricket
                  ? compCard?.status?.value || 'In Progress'
                  : match?.status?.value || 'In Progress'}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {isCricket ? (
                <div className="flex items-center gap-2">
                  <div className="relative w-10 h-10 bg-white rounded-full flex items-center justify-center">
                    <img
                      src={getTeamIcon(compCard?.visitorteam?.flag)}
                      alt={compCard?.visitorteam?.name}
                      className="object-contain"
                      width={24}
                      height={24}
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.parentElement?.parentElement?.classList.add(
                          'fallback-to-initial',
                        );
                      }}
                    />
                  </div>
                  <span className="font-semibold">
                    {compCard?.visitorteam?.name || 'Team 1'}
                  </span>
                </div>
              ) : match && match.visitorteam ? (
                <>
                  <span className="font-semibold">
                    {match.visitorteam.name}
                  </span>
                  {match.visitorteam.flag &&
                  getTeamIcon(match.visitorteam.flag) ? (
                    <div
                      className="w-10 h-10 bg-white rounded-full flex items-center justify-center overflow-hidden"
                      data-initial={getTeamInitial(match.visitorteam)}
                    >
                      <div className="relative w-6 h-6">
                        <img
                          src={getTeamIcon(match.visitorteam.flag)}
                          alt={match.visitorteam.name}
                          className="object-contain"
                          width={24}
                          height={24}
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                            e.currentTarget.parentElement?.parentElement?.classList.add(
                              'fallback-to-initial',
                            );
                          }}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                      <span className="font-bold text-blue-900">
                        {getTeamInitial(match.visitorteam)}
                      </span>
                    </div>
                  )}
                </>
              ) : null}
            </div>
          </div>
        )}

        {/* Filter Options */}
        <div className="p-3 bg-gray-50 border-b">{renderFilterOptions()}</div>

        {/* Commentary List */}
        <div className="flex-1 overflow-y-auto">
          {filteredEvents.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              No events to display
            </div>
          ) : (
            filteredEvents.map((event, index) => {
              const team = getTeamForEvent(event);
              const eventStyle = getEventStyle(event.type);
              const playerName = getPlayerName(event);
              const timeDisplay = getEventTime(event);

              return (
                <div
                  key={event.id || index}
                  className={`flex items-start gap-3 p-3 ${
                    index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                  }`}
                >
                  <div
                    className={`flex-shrink-0 w-8 h-8 rounded-full ${eventStyle.bgColor} flex items-center justify-center ${eventStyle.color}`}
                  >
                    {eventStyle.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium text-gray-900">
                        {isSoccer ? `${event.min}'` : timeDisplay}
                      </span>
                      {playerName && (
                        <span className="text-sm text-gray-600">
                          {playerName}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">
                      {renderEventDescription(event)}
                    </p>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default CommentaryModal;
