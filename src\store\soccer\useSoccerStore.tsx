import { CreateSoccerTeamPayload } from '../../../types/competitions';
import { create } from 'zustand';
import { combine, devtools } from 'zustand/middleware';
import {
  addPlayerToRole,
  calculateRemainingBudget,
  createExpertTeam,
  createFavoriteTeam,
  createLuckyTeam,
  getDreamTeam,
  initialSoccerTeamState,
  removePlayerFromRole,
  setPlayerToCaptain,
  setPlayerToViceCaptain,
} from '@/lib/soccerUtils';
import {
  SoccerExpertTeam,
  SoccerFavoriteTeam,
  SoccerPlayer,
  SoccerPlayerRole,
  SoccerPlayersByRole,
  SoccerTeamActions,
  SoccerTeamState,
} from '@/lib/types/soccer';
import { LocalStorage } from '@/lib/utils';

const createSoccerTeamActions = (
  set: (fn: (state: SoccerTeamState) => Partial<SoccerTeamState>) => void,
  get: () => SoccerTeamState,
): SoccerTeamActions => ({
  addPlayer: (player: SoccerPlayer, role: SoccerPlayerRole) =>
    set((state) => addPlayerToRole(state, player, role)),
  removePlayer: (player: SoccerPlayer, role: SoccerPlayerRole) =>
    set((state) => removePlayerFromRole(state, player, role)),
  setCreateDreamTeamPayload: (payload: CreateSoccerTeamPayload) =>
    set((state) => ({ createDreamTeamPayload: payload })),
  setRemainingBudget: (budget: number) =>
    set((state) => ({ remainingBudget: budget })),
  setActiveTabPlayer: (role: SoccerPlayerRole) =>
    set((state) => ({ activeTabPlayer: role })),
  setTotalBudget: (budget: number) => set((state) => ({ totalBudget: budget })),
  setPlayerByRoleLimit: (limits: SoccerTeamState['playerByRoleLimit']) =>
    set((state) => ({ playerByRoleLimit: limits })),
  setLastEntry: (entry: SoccerTeamState['lastEntry']) =>
    set((state) => ({ lastEntry: entry })),
  resetTeam: (totalBudget: number, remainingBudget: number) => {
    set((state) => {
      const soccerPlayersByRole = LocalStorage.getItem<SoccerPlayersByRole>(
        'soccer_dream_team',
      ) || {
        GKP: [],
        DEF: [],
        MID: [],
        FWD: [],
      };

      return {
        ...state,
        activeTabPlayer: 'GKP',
        soccerPlayersByRole: soccerPlayersByRole,
        totalBudget,
        remainingBudget: calculateRemainingBudget(
          totalBudget,
          Object.values(soccerPlayersByRole).flat(),
        ),
        lastEntry: {
          mode: 'MANUAL',
          players: [],
        },
      };
    });
  },
  setPlayerToCaptain: (player: SoccerPlayer, role: SoccerPlayerRole) =>
    set((state) => setPlayerToCaptain(state, player, role)),
  setPlayerToViceCaptain: (player: SoccerPlayer, role: SoccerPlayerRole) =>
    set((state) => setPlayerToViceCaptain(state, player, role)),

  createLuckyTeam: (sourcePlayersByRole: SoccerPlayersByRole) =>
    set((state) => createLuckyTeam(state, sourcePlayersByRole)),
  createFavoriteTeam: (
    favoriteTeam: SoccerFavoriteTeam,
    sourcePlayers: SoccerPlayer[],
    eventId: string,
    eventName: string,
    sportId: string,
    tournamentId: string,
    competitionId: string,
    name: string,
  ) =>
    set((state) =>
      createFavoriteTeam(
        state,
        favoriteTeam,
        sourcePlayers,
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
      ),
    ),
  createExpertTeam: (expertTeam: SoccerExpertTeam[], players: SoccerPlayer[]) =>
    set((state) => createExpertTeam(state, expertTeam, players)),

  getDreamTeam: (
    fantasyTeamResponse: any,
    playerId?: number,
    role?: SoccerPlayerRole,
  ) => set((state) => getDreamTeam(state, fantasyTeamResponse, playerId, role)),
  setShowPlayerTable: (show: boolean) =>
    set((state) => ({ showPlayerTable: show })),
  setShowFilter: (show: boolean) => set((state) => ({ showFilter: show })),
  setShowMobileFilter: (show: boolean) =>
    set((state) => ({ showMobileFilter: show })),
  addReservePlayer: (player, position) =>
    set((state) => {
      const newReserves = [...(state.reservePlayers || [null, null, null])];
      newReserves[position] = player;
      // Remove from main team if present
      const updatedPlayersByRole = { ...state.soccerPlayersByRole };
      (Object.keys(updatedPlayersByRole) as SoccerPlayerRole[]).forEach((role) => {
        updatedPlayersByRole[role] = updatedPlayersByRole[role].filter(
          (p: SoccerPlayer) => p.playerId !== player.playerId
        );
      });
      // Budget management: recalc remainingBudget
      const allMainPlayers = Object.values(updatedPlayersByRole).flat();
      const allReservePlayers = newReserves.filter(Boolean) as SoccerPlayer[];
      const totalCost = [...allMainPlayers, ...allReservePlayers].reduce(
        (sum, p) => sum + (p.scoreData?.playerCurrentSalary || 0),
        0
      );
      return {
        reservePlayers: newReserves,
        soccerPlayersByRole: updatedPlayersByRole,
        remainingBudget: state.totalBudget - totalCost,
      };
    }),
  removeReservePlayer: (position) =>
    set((state) => {
      const newReserves = [...(state.reservePlayers || [null, null, null])];
      newReserves[position] = null;
      // Budget management: recalc remainingBudget
      const allMainPlayers = Object.values(state.soccerPlayersByRole).flat();
      const allReservePlayers = newReserves.filter(Boolean) as SoccerPlayer[];
      const totalCost = [...allMainPlayers, ...allReservePlayers].reduce(
        (sum, p) => sum + (p.scoreData?.playerCurrentSalary || 0),
        0
      );
      return {
        reservePlayers: newReserves,
        remainingBudget: state.totalBudget - totalCost,
      };
    }),
  setActiveReservePosition: (position) =>
    set(() => ({ activeReservePosition: position })),
  setReservePlayers: (players) =>
    set(() => ({ reservePlayers: players })),
});

export const useSoccerStore = create(
  devtools(
    combine<SoccerTeamState, SoccerTeamActions>(
      initialSoccerTeamState,
      createSoccerTeamActions,
    ),
    {
      name: 'soccer-team',
    },
  ),
);
