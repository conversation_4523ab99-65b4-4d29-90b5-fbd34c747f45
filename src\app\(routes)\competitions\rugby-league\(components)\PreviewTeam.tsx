'use client';;
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { useCompetition } from '@/helpers/context/competitionContext';
import CompetitionDetailsHeader from '@/components/Competition/CompetitionDetailsHeader';
import { Button } from '@/components/UI/button';
import { PlayerCaptainViewIcon } from '@/components/images';
import RugbyLeagueFantsyUI from './RugbyLeagueFantsyUI';
import RugbyLeaguePlayerSelectionUI from './RugbyLeaguePlayerSelectionUI';
import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';
import { RugbyLeaguePlayersByRole } from '../../../../../../types/rugby-league';
import { getAllRugbyPlayers } from '@/lib/utils';
import DataTable from '@/components/UI/DataTabel';
import useScreen from '@/hooks/useScreen';
import {
  completedCricketPlayerListColumn,
  liveCricketPlayerListColumn,
} from '@/components/UI/DataTabel/columns/createTeamColumn';
import { upcomingPlayerListColumn } from '@/components/UI/DataTabel/upcomingPlayerlistColumns';
import { ColumnDef } from '@tanstack/react-table';
import { Player } from '../../../../../../types/competitions';
import SharePopup from '@/components/PopUp/Share/SharePopup';
import ShareIcon from '@/components/Icons/Share/ShareIcon';
import DreamTeamLoader from '@/components/Loading/DreamTeamLoader';

const PreviewTeam = () => {
  const {
    eventDetailsResponse,
    dreamTeamResponse,
    activeTeam,
    setActiveTeam,
    eventTimeStatus: { isLockout, isLive, isCompleted },
    rugbyLeaguePlayersByRole,
    isDreamTeamResponseLoading
  } = useCompetition();

  const {
    activeTabPlayer,
    setActiveTabPlayer,
    getDreamTeam: dispatchDreamTeam,
    clearTeam,
    state: {
      playersByRole: { BAC, BR, FRF, HAL, IC },
      playerByRoleLimit: {
        BAC: BACLIMIT,
        BR: BRLIMIT,
        FRF: FRFLIMIT,
        HAL: HALLIMIT,
        IC: ICLIMIT,
      },
      remainingBudget,
      reserveState: { reservePlayers, reservePlayersLimit },
    },
    openReserveModal,
  } = useRugbyLeagueContext();

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const event_id = searchParams.get('event_id');
  const sport_id = searchParams.get('sport_id');
  const tournament_id = searchParams.get('tournament_id');
  const seasonId = searchParams.get('seasonId');
  const competition_id = searchParams.get('competition_id');
  const comp_Type = searchParams.get('comp_Type');
  const dreamTeamId = searchParams.get('dreamTeamId');
  const role = searchParams.get('role') as keyof RugbyLeaguePlayersByRole;
  const playerId = searchParams.get('playerId');
  const [activeTab, setActiveTab] = useState<string | number>(1);
  const allLiveScore = dreamTeamResponse?.result?.totalLivePoints;
  const playersByRole =
    dreamTeamResponse && getAllRugbyPlayers(dreamTeamResponse);
  const [captain] = dreamTeamResponse?.result?.captain! || [];
  const [viceCaptain] = dreamTeamResponse?.result?.viceCaptain! || [];
  const flattenedPlayerByRole = Object.values(playersByRole || {})
    ?.map((player) => {
      if (player?.playerId === captain?.playerId) {
        return { ...player, positionType: 'captain' };
      }

      if (player?.playerId === viceCaptain?.playerId) {
        return { ...player, positionType: 'viceCaptain' };
      }

      return player;
    })
    ?.sort((a, b) => a.name.localeCompare(b.name));
  const router = useRouter();
  const { width } = useScreen();
  const query = {
    event_id,
    sport_id,
    tournament_id,
    seasonId,
    competition_id,
    dreamTeamId: `${activeTeam?.id}`,
    comp_Type,
  };
  const [showSharePopup, setShowSharePopup] = useState(false);
  let playerColumn: ColumnDef<Player>[] = [];
  const eventStatus = eventDetailsResponse?.result?.eventDetails?.status;
  switch (eventStatus) {
    case 'finished':
      playerColumn = completedCricketPlayerListColumn;
      break;
    case 'innings break':
    case 'inprogress':
    case 'drink':
      playerColumn = liveCricketPlayerListColumn;
      break;
    case 'upcoming':
      playerColumn = upcomingPlayerListColumn;
      break;
  }

  useEffect(() => {
    if (dreamTeamResponse && dreamTeamId) {
      dispatchDreamTeam(dreamTeamResponse, +playerId!, role);
    }
    return () => {
      clearTeam();
    };
  }, [dreamTeamResponse]);

  useEffect(() => {
    if (activeTeam?.id && comp_Type === 'my') {
      // @ts-expect-error
      router.push(`${pathname}?${new URLSearchParams(query).toString()}`);
    }
  }, [activeTeam, dreamTeamId]);

  const dreamTeams = eventDetailsResponse?.result?.dreamTeams;

  useEffect(() => {
    if (dreamTeams && !activeTeam?.id && dreamTeamId) {
      setActiveTeam(() => dreamTeams.find((team) => team.id === +dreamTeamId));
    }

    if (dreamTeams && !activeTeam?.id && !dreamTeamId) {
      setActiveTeam(() => dreamTeams[0]);
    }
  }, [dreamTeamId, dreamTeams]);

  // Prepare reserve slots: fill with current reserves, pad with nulls if needed
  const reserveSlots = Array.from({ length: reservePlayersLimit }, (_, i) => reservePlayers?.[i] ?? null);


  const headerStats = {
    selectedPlayer: `Player ${BAC?.length + BR?.length + HAL?.length + FRF?.length + IC?.length}/${BACLIMIT + BRLIMIT + HALLIMIT + FRFLIMIT + ICLIMIT
      }`,
    remainingSalary: remainingBudget,
    totalScore: 200,
  };

  return (
    <div className="bg-white px-0">
      <div>
        <div className="px-[33px] max-799:px-0 bg-off-white-200 pb-[40px]">
          {/* Competition Header */}
          <div>
            <CompetitionDetailsHeader
              stats={{
                selectedPlayer: '17',
                remainingSalary:
                  eventStatus === 'upcoming'
                    ? remainingBudget
                    : dreamTeamResponse?.result?.totalSpendValue!,

                allLiveScore: allLiveScore,
              }}
              status={eventDetailsResponse?.result?.eventDetails?.status}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              dreamTeamResponse={dreamTeamResponse}
            />
          </div>

          <div className="flex space-x-2 mt-2 flex-col lg:flex-row w-full">
            <div className="w-full">
              {activeTab === 1 ? (
                isDreamTeamResponseLoading ? <DreamTeamLoader /> : <RugbyLeagueFantsyUI />
              ) : (
                <>
                  <DataTable
                    columns={playerColumn}
                    // @ts-expect-error
                    data={flattenedPlayerByRole || []}
                    stickyColumns={width <= 700 ? [0] : []}
                  />
                  {/* <div className="w-full text-center bg-secondary-100 text-white px-3 py-1 text-sm text-black">
                      RESERVES
                    </div> */}
                  {/* <DataTable
                    // @ts-expect-error
                    columns={playerColumn || []}
                    // @ts-expect-error
                    data={reserveSlots}
                    stickyColumns={[]}
                    hideHeader={true}
                    hideCellVisibility={true}
                  /> */}
                </>
              )}
            </div>
            <div className="w-[95%]">
              <RugbyLeaguePlayerSelectionUI
                activeTab={activeTabPlayer}
                setActiveTab={setActiveTabPlayer}
                playerByRole={rugbyLeaguePlayersByRole}
                stats={headerStats}
              />
            </div>
          </div>
        </div>
      </div>

      {eventDetailsResponse?.result?.eventDetails?.status === 'upcoming' &&
        !isLockout &&
        !isLive &&
        !isCompleted &&
        !openReserveModal && (
          <div className="bg-gray-50 z-[1200] fixed bottom-0 left-0 right-0">
            <div className="absolute -top-[25px] left-1/2 transform -translate-x-1/2 hidden"></div>
            <div className="flex w-full space-x-2 pb-4 md:space-x-0 md:mt-0 justify-around items-center">
              <div className="space-x-2 mt-5 flex flex-wrap gap-2 justify-center">
                <Button
                  size="sm"
                  disabled={
                    eventDetailsResponse?.result?.eventConfiguration
                      ?.eventType === 'free' &&
                    eventDetailsResponse?.result?.dreamTeams?.length > 0
                  }
                  variant="ghost"
                  className="!bg-[#335F83] text-white w-40 !disabled:cursor-not-allowed"
                  onClick={() => {
                    clearTeam();
                    router.push(
                      `/competitions/rugby-league/${eventDetailsResponse?.result?.eventConfiguration?.eventId}?event_id=${eventDetailsResponse?.result?.eventConfiguration?.eventId}&sport_id=12&tournament_id=${eventDetailsResponse?.result?.eventDetails?.RLTournamentId}&seasonId=${eventDetailsResponse?.result?.eventDetails?.RLSeasonId}&competition_id=${eventDetailsResponse?.result?.eventConfiguration?.id}&add_more=true`,
                    );
                  }}
                >
                  {eventDetailsResponse?.result?.eventConfiguration
                    ?.eventType === 'free' &&
                    eventDetailsResponse?.result?.dreamTeams?.length > 0
                    ? 'Team Entered'
                    : 'Add one more team'}
                </Button>
                <SharePopup
                  isOpen={showSharePopup}
                  onClose={() => setShowSharePopup(false)}
                >
                  <button
                    type="button"
                    className="cursor-pointer border-none bg-transparent p-0 flex items-center justify-center"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setShowSharePopup(true);
                    }}
                  >
                    <ShareIcon width={35} height={35} />
                  </button>
                </SharePopup>
              </div>

              <div className="fixed bottom-[71px] left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center ">
                <PlayerCaptainViewIcon className="md:hidden block" />
              </div>
            </div>
          </div>
        )}

      {eventDetailsResponse?.result?.eventDetails?.status !== 'upcoming' && (
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 z-[100] flex justify-center items-center ">
          <PlayerCaptainViewIcon className="md:hidden block" />
        </div>
      )}
    </div>
  );
};

export default PreviewTeam;
