'use client';
import { <PERSON><PERSON>he<PERSON> } from 'lucide-react';
import moment from 'moment';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import Cricket from '@/assets/images/sportIcon/graySportIcon/Cricket.svg';
import { Button } from '@/components/UI/button';
import { LOCK_OUT_TIME } from '@/helpers/constants/index';
import { Config } from '@/helpers/context/config';
import { useTeam } from '@/helpers/context/createTeamContext';
import useScreen from '@/hooks/useScreen';
import { cn, getTournamentName, LocalStorage } from '@/lib/utils';
import cardTopCurve from '@/assets/images/cardTopCurve.png';
import DefaultTeam from '@/assets/images/icons/defaultTeam.png';
import NewDefaultTeam from '@/assets/images/icons/icon_default_team.png';
import WinArrow from '@/assets/images/icons/winArrow.svg';
import LeaderboardIcon from '../Icons/LeaderboardIcon';
import CommentaryIcon from '../Icons/CommentaryIcon';
import ScoreCardIcon from '../Icons/ScoreCardIcon';
import type { CompetitionStatusProps, ScoreBoard } from '../../../../types';
import Competition from './Competition';
import CompetitionMobileView from './CompetitionMobileView';
import SportIcon, { WhiteSportIcon } from '../Icons/SportIcon';
import { useRugbyLeagueContext } from '@/helpers/context/rugby-league/createRugbyLeageContext';
import CommentaryButton from '@/components/shared/match/CommentaryButton';
import CommentaryModal from '@/components/shared/match/CommentaryModal';
import type {
  AflMatch,
  AflMatchEvent,
  AflCommentaryFilters,
} from '@/lib/types/afl';
import { useCricketCommentary } from '@/helpers/context/commentry/cricket';
import { CricketCommentaryResponse } from '../../../../types/commentry';
import Image from 'next/image';
import Countdown from 'react-countdown';
import CardBottom from './cardBottom';

export interface EventObject {
  id: number;
  eventName: string;
  awayTeamId: number;
  homeTeamId: number;
  startTime: string;
  CricketSeasonId: number;
  Eid: string;
  RLScores: any;
  ARScores: any;
  status: CompetitionStatusProps;
  SportId: number;
  CricketTournamentId: number;
  awayTeam: Team;
  homeTeam: Team;
  CricketTournament: CricketTournament;
  eventConfiguration: EventConfiguration;
  isDreamTeamExist: number;
  SoccerSeasonId?: number;
  userEntry: number;
  winnerCode: string | number | null;
  ScoreBoard: ScoreBoard;
  currentRank: number;
  name: string;
  winningPrice: number | null;
  RLTournamentId?: number;
  RLSeasonId?: number;
  ARTournamentId?: number;
  ARSeasonId?: number;
  liveScore?: number;
  SoccerTournamentId?: number;
  lineups?: boolean
}

interface Team {
  id: number;
  name: string;
  gender: string;
  flag: string | null;
}

interface CricketTournament {
  id: number;
  name: string;
  gender: string;
}

interface EventConfiguration {
  id: number;
  sportId: number;
  eventId: number;
  startTime: string;
  round: string | null;
  status: string;
  eventType: 'free' | 'paid';
  prizePool: number;
  entryCoin: number;
  drawPool: DrawPool[];
  minUserEntry: number | null;
  createdAt: string;
  updatedAt: string;
  competitionId: number;
  prizePoolFirst: number;
}

interface DrawPool {
  drawNumber: number;
  drawType: string;
  coins: number;
  description: string;
}

type CompCardProps = Readonly<{
  compData: EventObject;
  addMore?: boolean;
}>;

const getTeamIcon = (teamFlag: string | undefined): string | undefined => {
  if (!teamFlag) return undefined;
  return teamFlag.includes('uploads')
    ? `${Config.mediaURL}${teamFlag}`
    : teamFlag;
};

interface RenderActionButtonProps {
  compType: string | null;
  compData: EventObject;
  isDisabled: boolean;
  userFullEntery: boolean;
  handleEnterViewComp: () => void;
  handleEnterMoreComp: () => void;
  handleEnterComp: () => Promise<void>;
  status: string | null;
}

function renderActionButton({
  compType,
  compData,
  isDisabled,
  userFullEntery,
  handleEnterViewComp,
  handleEnterMoreComp,
  handleEnterComp,
  status,
}: RenderActionButtonProps): JSX.Element {
  const isFreeEnteryUserFull =
    compData?.eventConfiguration?.eventType === 'free' &&
    (compData?.isDreamTeamExist ?? 0) > 0;

  if (
    (compData?.isDreamTeamExist ||
      compData?.status === 'inprogress' ||
      compData?.status === 'Strategic Timeout' ||
      compData?.status === 'Stumps' ||
      compData?.status === "innings break" ||
      compData?.status === 'finished') &&
    (status !== '1' || compType === 'my')
  ) {
    return (
      <div>
        <Button
          onClick={handleEnterViewComp}
          className="w-full h-full px-[18px] py-0.5 rounded-[8px] text-[43.9px] max-1024:text-[25px] leading-[53px] max-1024:leading-[31px] font-apotekCompRegular uppercase font-normal text-white"
          style={{
            background:
              'linear-gradient(180deg, rgba(252, 121, 20, 1) 0%, rgba(252, 71, 20, 1) 100%)',
          }}
        >
          View Comp
        </Button>
      </div>
    );
  }

  if ((compData?.isDreamTeamExist ?? 0) > 0 && compType !== 'my') {
    return (
      <button
        className="w-full bg-[#1C9A6C] disabled:bg-gray-200 rounded-[8px] text-[43.9px] max-1024:text-[25px] leading-[53px] max-1024:leading-[31px] font-apotekCompRegular uppercase font-normal text-white flex justify-center items-center gap-x-1.5 cursor-pointer px-[18px] py-0.5"
        onClick={handleEnterViewComp}
      // disabled={isDisabled || userFullEntery || isFreeEnteryUserFull}
      >
        <CircleCheck />
        <span className="text-center text-wrap">
          {compData?.eventConfiguration?.eventType === 'free' &&
            (compData?.isDreamTeamExist ?? 0) > 0
            ? 'Team Entered'
            : 'Enter Again'}
        </span>
      </button>
    );
  }
  return (
    <div>
      <Button
        disabled={
          isDisabled ||
          ((compData?.eventConfiguration?.minUserEntry ?? 0) > 0 &&
            (compData?.userEntry ?? 0) >=
            (compData?.eventConfiguration?.minUserEntry ?? 0))
        }
        onClick={handleEnterComp}
        className="w-full h-full px-[18px] py-0.5 rounded-[8px] text-[43.9px] max-1024:text-[25px] leading-[53px] max-1024:leading-[31px] font-apotekCompRegular uppercase font-normal text-white"
        style={{
          background:
            'linear-gradient(180deg, rgba(252, 121, 20, 1) 0%, rgba(252, 71, 20, 1) 100%)',
        }}
      >
        Enter Comp
      </Button>
    </div>
  );
}

export default function CompCard({ compData }: CompCardProps) {
  const searchParams = useSearchParams();
  const compType = searchParams.get('compType');
  const afl = searchParams.get('sports') === 'afl';
  const nrl = searchParams.get('sports') === 'nrl';
  const soccer = searchParams.get('sports') === 'soccer';
  const nfl = searchParams.get('sports') === 'nfl';
  const router = useRouter();
  const homeTeamIcon = getTeamIcon(compData?.homeTeam?.flag ?? '');
  const awayTeamIcon = getTeamIcon(compData?.awayTeam?.flag ?? '');
  const status = searchParams.get('status');


  const { width } = useScreen();
  const { resetTeam } = useTeam();
  const { clearTeam } = useRugbyLeagueContext();
  const {
    setMatchId,
    state: { liveCommentary, isLiveCommentaryLoading },
    setEventFilter,
  } = useCricketCommentary();

  const fetchDayName = (date: any) => {
    const days = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    const d = new Date(date);
    const dayName = days[d.getDay()];
    return dayName;
  };
  const [isDisabled, setIsDisabled] = useState<boolean>(false);

  useEffect(() => {
    if (!compData?.startTime) return;

    const checkTime = () => {
      const eventStartTime = moment.utc(compData.startTime).local();
      const currentTime = moment();
      const diffInMinutes = eventStartTime.diff(currentTime, 'minutes');
      const startDate = moment
        .utc(compData?.startTime)
        .local()
        .format('DD/MM/YYYY');
      const currentDate = moment().format('DD/MM/YYYY');

      // Check if the event date is in the past
      const isDateExpired = moment(currentDate, 'DD/MM/YYYY').isAfter(
        moment(startDate, 'DD/MM/YYYY'),
      );

      // Disable button if:
      // 1. The event date has already passed, OR
      // 2. 5 minutes or less are remaining before the event
      setIsDisabled(isDateExpired || diffInMinutes <= LOCK_OUT_TIME);
    };

    // Check immediately and set an interval to update every minute
    checkTime();
    const interval = setInterval(checkTime, 60000); // Every minute

    return () => clearInterval(interval); // Cleanup on component unmount
  }, [compData?.startTime]);

  let userFullEntery: boolean = false;

  const minUserEntry = compData?.eventConfiguration?.minUserEntry;
  const maxUserEntery = compData?.userEntry;
  if (minUserEntry && minUserEntry === maxUserEntery) {
    userFullEntery = true;
  }
  if (!minUserEntry) {
    userFullEntery = false;
  }

  const renderer = ({ days, hours, minutes, seconds, completed }: any) => {
    if (completed) {
      // Render a completed state
      return <span>Closed</span>;
    }

    let timeString = '';

    if (days > 0) {
      timeString = `${days}d : ${hours}h : ${minutes}m`;
    } else if (hours > 0) {
      timeString = `${hours}h : ${minutes}m : ${seconds}s`;
    } else if (minutes > 0) {
      timeString = `${minutes}m : ${seconds}s`;
    } else if (seconds > 0) {
      timeString = `${seconds}s`;
    }

    return <span>{timeString}</span>;
  };

  const handleEnterComp = async () => {
    LocalStorage.removeItem('dream_team');
    LocalStorage.removeItem('rugby_league_dream_team');
    LocalStorage.removeItem('soccer_dream_team');
    const mockAPIResponse = {
      compId: 4932,
      eventId: 4932,
      sportId: 4,
      tournamentId: 27,
      seasonId: 33,
      competitionId: 176,
      dreamTeamId: 2207,
    };
    const crikectUrl = `/competitions/${compData?.id}?event_id=${compData?.id}&sport_id=4&tournament_id=${compData.CricketTournamentId}&competition_id=${compData?.eventConfiguration?.competitionId}&seasonId=${compData?.CricketSeasonId}&add_more=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const aflUrl = `/competitions/football/${compData?.id}?event_id=${compData?.id}&sport_id=9&tournament_id=${compData.ARTournamentId}&competition_id=${compData?.eventConfiguration?.competitionId}&seasonId=${compData.ARSeasonId}&add_more=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const nrlUrl = `/competitions/rugby-league/${compData?.id}?event_id=${compData?.id}&sport_id=12&tournament_id=${compData?.RLTournamentId}&competition_id=${compData?.eventConfiguration?.competitionId}&seasonId=${compData?.RLSeasonId}&add_more=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const soccerUrl = `/competitions/soccer/${compData?.id}?event_id=${compData?.id}&sport_id=8&tournament_id=${compData?.SoccerTournamentId}&competition_id=${compData?.eventConfiguration?.competitionId}&seasonId=${compData?.SoccerSeasonId}&add_more=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const nflUrl = `/competitions/nfl/${mockAPIResponse.compId}?event_id=${mockAPIResponse.eventId}&sport_id=4&tournament_id=${mockAPIResponse.tournamentId}&competition_id=${mockAPIResponse.competitionId}&seasonId=${mockAPIResponse.seasonId}&add_more=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    resetTeam();
    clearTeam();
    if (compData?.SportId === 9) {
      return router.push(aflUrl);
    }
    if (compData?.SportId === 12) {
      return router.push(nrlUrl);
    }
    if (compData?.SportId === 15) {
      return router.push(nflUrl);
    }
    if (compData?.SportId === 8) {
      return router.push(soccerUrl);
    } else {
      router.push(crikectUrl);
    }
  };

  const handleEnterMoreComp = () => {
    LocalStorage.removeItem('dream_team');
    LocalStorage.removeItem('rugby_league_dream_team');
    LocalStorage.removeItem('soccer_dream_team');
    resetTeam();
    clearTeam();

    const crikectUrl = `/competitions/${compData?.id}?event_id=${compData?.id}&sport_id=4&tournament_id=${compData.CricketTournamentId}&competition_id=${compData?.eventConfiguration?.competitionId}&add_more=true&seasonId=${compData?.CricketSeasonId}&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const aflUrl = `/competitions/football/${compData?.id}?event_id=${compData?.id}&sport_id=9&tournament_id=${compData.ARTournamentId}&competition_id=${compData?.eventConfiguration?.competitionId}&seasonId=${compData?.ARSeasonId}&add_more=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const nrlUrl = `/competitions/rugby-league/${compData?.id}?event_id=${compData?.id}&sport_id=12&tournament_id=${compData?.RLTournamentId}&competition_id=${compData?.eventConfiguration?.competitionId}&seasonId=${compData?.RLSeasonId}&add_more=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const soccerUrl = `/competitions/soccer/${compData?.id}?event_id=${compData?.id}&sport_id=8&tournament_id=${compData?.SoccerTournamentId}&competition_id=${compData?.eventConfiguration?.competitionId}&seasonId=${compData?.SoccerSeasonId}&add_more=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    if (compData?.SportId === 9) {
      return router.push(aflUrl);
    }
    if (compData?.SportId === 12) {
      return router.push(nrlUrl);
    }
    if (compData?.SportId === 8) {
      return router.push(soccerUrl);
    } else {
      router.push(crikectUrl);
    }
  };

  const handleEnterViewComp = () => {
    LocalStorage.removeItem('dream_team');
    LocalStorage.removeItem('rugby_league_dream_team');
    resetTeam();
    clearTeam();

    const crikectUrl = `/competitions/${compData?.id}?event_id=${compData?.id}&sport_id=4&tournament_id=${compData.CricketTournamentId}&seasonId=${compData?.CricketSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&comp_Type=${compType === 'my' ? 'my' : 'my'}${!compData?.isDreamTeamExist ? '&leaderboard=true' : ''}`;
    const aflUrl = `/competitions/football/${compData?.id}?event_id=${compData?.id}&sport_id=9&tournament_id=${compData.ARTournamentId}&seasonId=${compData?.ARSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&comp_Type=${compType === 'my' ? 'my' : 'my'}${!compData?.isDreamTeamExist ? '&leaderboard=true' : ''}`;
    const nrlUrl = `/competitions/rugby-league/${compData?.id}?event_id=${compData?.id}&sport_id=12&tournament_id=${compData?.RLTournamentId}&seasonId=${compData?.RLSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&comp_Type=${compType === 'my' ? 'my' : 'my'}${!compData?.isDreamTeamExist ? '&leaderboard=true' : ''}`;
    const soccerUrl = `/competitions/soccer/${compData?.id}?event_id=${compData?.id}&sport_id=8&tournament_id=${compData?.SoccerTournamentId}&seasonId=${compData?.SoccerSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    if (compData?.SportId === 9) {
      return router.push(aflUrl);
    }
    if (compData?.SportId === 12) {
      return router.push(nrlUrl);
    }
    if (compData?.SportId === 8) {
      return router.push(soccerUrl);
    } else {
      router.push(crikectUrl);
    }
  };

  const handleLeaderboard = () => {
    LocalStorage.removeItem('dream_team');
    LocalStorage.removeItem('rugby_league_dream_team');

    resetTeam();
    clearTeam();

    const crikectUrl = `/competitions/${compData?.id}?event_id=${compData?.id}&sport_id=4&tournament_id=${compData.CricketTournamentId}&seasonId=${compData?.CricketSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&leaderboard=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const aflUrl = `/competitions/football/${compData?.id}?event_id=${compData?.id}&sport_id=9&tournament_id=${compData.ARTournamentId}&seasonId=${compData?.ARSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&leaderboard=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const nrlUrl = `/competitions/rugby-league/${compData?.id}?event_id=${compData?.id}&sport_id=12&tournament_id=${compData?.RLTournamentId}&seasonId=${compData?.RLSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&leaderboard=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const soccerUrl = `/competitions/soccer/${compData?.id}?event_id=${compData?.id}&sport_id=8&tournament_id=${compData?.SoccerTournamentId}&seasonId=${compData?.SoccerSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&leaderboard=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    if (compData?.SportId === 9) {
      return router.push(aflUrl);
    }
    if (compData?.SportId === 12) {
      return router.push(nrlUrl);
    }
    if (compData?.SportId === 8) {
      return router.push(soccerUrl);
    } else {
      router.push(crikectUrl);
    }
  };

  const handleScoreCard = () => {
    LocalStorage.removeItem('dream_team');
    LocalStorage.removeItem('rugby_league_dream_team');
    resetTeam();
    clearTeam();

    const crikectUrl = `/competitions/${compData?.id}?event_id=${compData?.id}&sport_id=4&tournament_id=${compData.CricketTournamentId}&seasonId=${compData?.CricketSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&score_card=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const aflUrl = `/competitions/football/${compData?.id}?event_id=${compData?.id}&sport_id=9&tournament_id=${compData.ARTournamentId}&seasonId=${compData?.ARSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&score_card=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    const nrlUrl = `/competitions/rugby-league/${compData?.id}?event_id=${compData?.id}&sport_id=12&tournament_id=${compData?.RLTournamentId}&seasonId=${compData?.RLSeasonId}&competition_id=${compData?.eventConfiguration?.competitionId}&score_card=true&comp_Type=${compType === 'my' ? 'my' : 'all'}`;
    if (compData?.SportId === 9) {
      return router.push(aflUrl);
    }
    if (compData?.SportId === 12) {
      return router.push(nrlUrl);
    } else {
      router.push(crikectUrl);
    }
  };

  const fetchCricketScore = (teamScore: any, teamtype: any) => {
    // Helper function to format score with CW and CD conditions
    const formatScore = (
      C1: any,
      CW1: any,
      CW2: any,
      C2: any,
      CD1: any,
      CD2: any,
    ) => {
      let result = `${C1 ?? '-'}`;

      // Handling first CW score
      if (CW1 && CW1 !== 10) {
        result += `/${CW1}${CD1 && CD1 === 1 ? 'd' : ''}`;
      }

      // Handling second C2 score
      if (C2 || C2 === 0) {
        result += `& ${C2}`;
      }

      // Handling second CW score
      if (CW2 && CW2 !== 10) {
        result += `/${CW2}${CD2 && CD2 === 1 ? 'd' : ''}`;
      }

      return result;
    };

    const getTeamScore = (scorePrefix: string, teamScore: any) => {
      // Check if Exd is greater than '1'
      if (teamScore?.Exd > '1') {
        return formatScore(
          teamScore[`${scorePrefix}C1`],
          teamScore[`${scorePrefix}CW1`],
          teamScore[`${scorePrefix}CW2`],
          teamScore[`${scorePrefix}C2`],
          teamScore[`${scorePrefix}CD1`],
          teamScore[`${scorePrefix}CD2`],
        );
      }

      // Return default score format (C1 and CW1)
      const scoreC1 = teamScore?.[`${scorePrefix}C1`] ?? '-';
      const scoreCW1 = teamScore?.[`${scorePrefix}CW1`];
      const cw1Part = scoreCW1 && scoreCW1 !== 10 ? `/ ${scoreCW1}` : '';

      return `${scoreC1}${cw1Part}`;
    };

    let scoreDisplay: JSX.Element;

    // Determine team-specific prefix based on teamtype
    const scorePrefix = teamtype === 'hometeam' ? 'Tr1' : 'Tr2';
    const over1 = teamScore?.[`${scorePrefix}CO1`];
    const over2 = teamScore?.[`${scorePrefix}CO2`];

    // Format overs string
    let oversDisplay = '';
    if (over1) oversDisplay = `${over1}`;
    if (over2) oversDisplay += ` & ${over2}`;

    scoreDisplay = (
      <>
        <div
          className={`flex flex-col ${teamtype === 'hometeam' ? 'items-end' : 'items-start'}`}
        >
          <span className="text-[41.36px] max-1024:text-[30px] max-799:text-[22px] leading-[41.36px] max-1024:leading-[30px] max-799:leading-[22px] font-apotekCompRegular font-normal ">
            {getTeamScore(scorePrefix, teamScore)}
          </span>
          <span className="text-[31.36px] max-1024:text-[22.4px] max-799:text-[18px] leading-[31.36px] max-1024:leading-[22.4px] max-799:leading-[18px] font-normal font-apotekCompRegular ">
            {oversDisplay || '-'}
          </span>
        </div>
      </>
    );

    return scoreDisplay;
  };

  const otherSportScore = (scoreboard: any, team: string): JSX.Element => {
    if (!scoreboard) return <span>-</span>;

    // Extract scores from the data
    const homeScore = scoreboard.Tr1;
    const awayScore = scoreboard.Tr2;

    // Return appropriate score based on team type
    return (
      <span className="text-[41.36px] max-1024:text-[30px] leading-[41.36px] max-1024:leading-[30px] font-apotekCompRegular font-normal">
        {team === 'hometeam' ? homeScore : awayScore}
      </span>
    );
  };

  const otherSportCurrentScore = (
    scoreboard: any,
    team: any,
    data: any,
  ): JSX.Element => {
    if (!scoreboard) return <span>-</span>;

    // Extract scores from the data
    const homeScore = scoreboard?.filter(
      (obj: any) => obj?.teamId === data?.homeTeamId,
    )?.[0]?.score;
    const awayScore = scoreboard?.filter(
      (obj: any) => obj?.teamId === data?.awayTeamId,
    )?.[0]?.score;
    // Return appropriate score based on team type
    return (
      <span className="text-[41.36px] max-1024:text-[30px] leading-[41.36px] max-1024:leading-[30px] font-apotekCompRegular font-normal">
        {team === 'hometeam' ? homeScore?.current : awayScore?.current}
      </span>
    );
  };

  // Prize Pool

  let finalEventPrice: number = 0;
  let topTeamPayOut: number = 0;
  const prizePool = compData?.eventConfiguration?.prizePool || 0;
  const eventType = compData?.eventConfiguration?.eventType;

  switch (eventType) {
    case 'free':
      finalEventPrice = prizePool / 2;
      topTeamPayOut = compData.userEntry > 20 ? 20 : compData.userEntry;
      break;
    case 'paid':
      finalEventPrice = compData.eventConfiguration.prizePoolFirst;
      topTeamPayOut = compData.userEntry > 20 ? 20 : compData.userEntry;
      break;
  }

  let buttonContent = renderActionButton({
    compData,
    compType,
    handleEnterComp,
    handleEnterMoreComp,
    handleEnterViewComp,
    isDisabled,
    userFullEntery,
    status,
  });

  const [isCommentaryModalOpen, setIsCommentaryModalOpen] = useState(false);

  // Function to handle opening the commentary modal
  const handleOpenCommentary = () => {
    setIsCommentaryModalOpen(true);
    if (compData?.SportId === 4) {
      setMatchId(compData?.id);
    }
    if (compData?.SportId === 8) {
      setMatchId(compData?.id);
    }
  };

  // Function to handle closing the commentary modal
  const handleCloseCommentary = () => {
    setIsCommentaryModalOpen(false);
    setEventFilter(undefined);
  };

  // Check if commentary is available for this sport and match status
  const isCommentarySupported =
    compData?.SportId === 9 ||
    compData?.SportId === 12 ||
    compData?.SportId === 4 ||
    compData?.SportId === 8;
  const isCommentaryAvailable = () => {
    return (
      isCommentarySupported &&
      (compData?.status === 'finished' || compData?.status === 'inprogress')
    );
  };

  const renderTeamInfo = (team: 'home' | 'away') => {
    const teamData = team === 'home' ? compData?.homeTeam : compData?.awayTeam;

    let teamFlag;
    if (Config?.mediaURL && teamData?.flag) {
      teamFlag = Config?.mediaURL + teamData?.flag;
    }

    return (
      <div
        className={`w-[calc(50%-165px)] max-799:w-[calc(50%-66px)] flex items-center max-799:items-start gap-x-[51px] max-1024:gap-x-[25px] max-799:flex-col max-799:gap-y-2 mb-3 ${team === 'away' && 'flex-row-reverse  max-799:items-end'}`}
      >
        <div className="relative">
          <div>
            <Image
              src={teamFlag ?? NewDefaultTeam}
              alt={`${team} Team Icon`}
              width={width > 1024 ? 146 : 95}
              height={width > 1024 ? 126 : 105}
              unoptimized={true}
              className="opacity-[0.1] max-w-[146px] max-1024:max-w-[95px]"
            />
          </div>
          <div
            className={`absolute bottom-[14px] max-1024:bottom-0 ${team === 'home' ? 'right-[-33px] max-1024:right-[-15px]' : 'left-[-33px] max-1024:left-[-15px]'} `}
          >
            <Image
              src={teamFlag ?? NewDefaultTeam}
              alt={`${team} Team Icon`}
              width={width > 1024 ? 90 : 60}
              height={width > 1024 ? 90 : 60}
              unoptimized={true}
              className="max-w-[90px] max-1024:max-w-[60px]"
            />
          </div>
        </div>
        <div className="max-799:w-full max-799:text-center">
          <p className="text-[31.36px] max-1024:text-[22px] max-539:text-[16px] leading-[37px] max-1024:leading-[26px] max-539:leading-[19px] font-apotekCompRegular font-normal text-black-100 uppercase text-ellipsis line-clamp-2 overflow-hidden">
            {teamData?.name}
          </p>
        </div>
      </div>
    );
  };

  const renderTeamScore = (team: 'home' | 'away') => {
    let otherSportScoreData;
    if (compData?.SportId === 12) {
      otherSportScoreData = compData?.RLScores;
    } else if (compData?.SportId === 9) {
      otherSportScoreData = compData?.ARScores;
    }
    const teamScore =
      compData?.SportId === 4
        ? fetchCricketScore(compData?.ScoreBoard, `${team}team`)
        : compData?.SportId === 8
          ? otherSportScore(compData?.ScoreBoard, `${team}team`)
          : otherSportCurrentScore(
            otherSportScoreData,
            `${team}team`,
            compData,
          );
    const isWinner = compData?.winnerCode === (team === 'home' ? 1 : 2);
    return { teamScore, isWinner };
  };

  const renderTeamScoreSection = () => {
    const { isWinner: isHomeWinner, teamScore: homeTeamScore } =
      renderTeamScore('home');
    const { isWinner: isAwayWinner, teamScore: awayTeamScore } =
      renderTeamScore('away');
    return (
      <div className="flex items-center justify-center gap-x-[27px] max-799:gap-x-1">
        <div className="flex items-center gap-x-1.5">
          <p className={isHomeWinner ? 'text-black-100' : 'text-black-700'}>
            {homeTeamScore}
          </p>
          {isHomeWinner && <WinArrow />}
        </div>
        <div>-</div>
        <div className="flex items-center gap-x-1.5">
          <p className={isAwayWinner ? 'text-black-100' : 'text-black-700'}>
            {awayTeamScore}
          </p>
          {isAwayWinner && <WinArrow />}
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="mb-[18px]">
        <div className="h-[43px] w-full relative max-1024:bg-primary-200 max-1024:rounded-[15px_15px_0px_0px]">
          {width > 1024 && (
            <img src={cardTopCurve?.src} alt="logo" className="h-full" />
          )}
          <div className="absolute  flex top-0 max-1024:top-3 items-center justify-center w-full gap-1.5">
            <WhiteSportIcon sport_id={compData?.SportId} />
            <span className="font-inter text-[22.4px] max-1024:text-[14px] leading-[43px] max-1024:leading-[16px] font-semibold text-white">
              {getTournamentName(compData?.SportId, compData)}
            </span>
          </div>
        </div>
        <div className="bg-white flex justify-between items-start px-1">
          {renderTeamInfo('home')}
          <div className="w-[330px] max-799:w-[132px] mt-[14px] mb-3 text-center">
            {compData?.status === 'inprogress' ? (
              <p className="bg-[#D84727] px-[9px] py-1 rounded-[3px] text-[14px] leading-[16px] font-semibold font-inter text-white uppercase w-fit mx-auto">
                Live
              </p>
            ) : (
              <p className="text-[14px] max-1024:text-[11.42px] leading-[16px] max-1024:leading-[14px] font-inter font-normal text-black-700 uppercase">
                {moment.utc(compData?.startTime).local().format('D MMM')}
              </p>
            )}
            {compData?.status === 'upcoming' && (
              <>
                <p className="text-[61.47px] max-1024:text-[43.9px] leading-[73px] max-1024:leading-[53px] font-apotekCompRegular font-normal text-black-100 uppercase">
                  {moment.utc(compData?.startTime).local().format('hh:mm A')}
                </p>
                <p className="text-[#FC4714] text-[18px] max-1024:text-[14px] leading-[22px] max-1024:leading-[16px] font-bold font-inter uppercase">
                  <Countdown
                    date={moment.utc(compData?.startTime).local().toDate()}
                    renderer={renderer}
                  />
                </p>
                {
                  compData.status === "upcoming" && compData.lineups && (
                    <p
                      className="mt-1.5 px-1.5 py-1 text-[11.42px] max-1024:text-[10px] leading-[14px] max-1024:leading-3 font-inter font-light text-white w-max rounded-[6px] mx-auto"
                      style={{
                        background:
                          'linear-gradient(180deg, rgba(28, 154, 108, 1) 0%, rgba(25, 118, 84, 1) 100%)',
                      }}
                    >
                      Lineups Announced
                    </p>
                  )
                }
              </>
            )}
            {compData?.status !== 'upcoming' && renderTeamScoreSection()}
            <div className="flex justify-center mt-2.5 max-799:mt-1">
              {(compData?.status === 'finished' ||
                compData?.status === 'inprogress') && (
                  <div className={cn('h-full flex gap-x-[27px] max-799:gap-x-1')}>
                    <div
                      className="flex items-center gap-2 justify-center cursor-pointer"
                      onClick={handleLeaderboard}
                    >
                      <LeaderboardIcon />
                    </div>
                    {isCommentaryAvailable() && (
                      <div
                        className="flex items-center gap-2 justify-center pl-2 cursor-pointer"
                        onClick={handleOpenCommentary}
                      >
                        <CommentaryIcon />
                      </div>
                    )}
                    {compData.SportId === 4 && (
                      <div
                        className="flex items-center gap-2 justify-center pl-2 cursor-pointer"
                        onClick={handleScoreCard}
                      >
                        <ScoreCardIcon />
                      </div>
                    )}
                  </div>
                )}
            </div>
          </div>
          {renderTeamInfo('away')}
        </div>
        <CardBottom
          compData={compData}
          buttonContent={buttonContent}
          finalEventPrice={finalEventPrice}
        />
      </div>

      {/* <div className="bg-white rounded-lg shadow-[0px_1px_9px_0px_#0000002b] pt-[27px]  pb-1 max-799:pb-0 px-[27px] max-799:px-0 mb-[18px] max-799:mb-3">
        <div className="flex justify-between items-center max-799:px-3 mb-2">
          <div className="flex items-center gap-1">
            <SportIcon sport_id={compData?.SportId} />
            <span className="font-normal font-inter text-[14px] max-799:text-[11.42px] leading-[16px] max-799:leading-[14px] text-black-700">
              {getTournamentName(compData?.SportId, compData)}
            </span>
          </div>
          <div className="text-black-100 font-normal font-inter text-[14px] max-799:text-[11.42px] leading-[16px] max-799:leading-[14px]">{`${
            compData?.startTime
              ? fetchDayName(compData?.startTime) +
                ' ' +
                moment.utc(compData?.startTime).local().format('DD/MM/YYYY')
              : ''
          } | ${
            compData?.startTime
              ? moment.utc(compData?.startTime).local().format('hh:mm A')
              : ''
          }`}</div>
        </div>
        {width > 799 ? (
          <Competition
            buttonContent={buttonContent}
            compData={compData}
            fetchCricketScore={fetchCricketScore}
            otherSportScore={otherSportScore}
            finalEventPrice={finalEventPrice}
            renderer={renderer}
            topTeamPayOut={topTeamPayOut}
            handleOpenCommentary={handleOpenCommentary}
            isCommentaryAvailable={isCommentaryAvailable}
            handleLeaderboard={handleLeaderboard}
            handleScoreCard={handleScoreCard}
            extraActions={
              <CommentaryButton
                onClick={handleOpenCommentary}
                disabled={!isCommentaryAvailable()}
              />
            }
          />
        ) : (
          <CompetitionMobileView
            actionButton={buttonContent}
            awayTeamIcon={awayTeamIcon ?? ''}
            compData={compData}
            fetchCricketScore={fetchCricketScore}
            otherSportScore={otherSportScore}
            finalEventPrice={finalEventPrice}
            homeTeamIcon={homeTeamIcon ?? ''}
            renderer={renderer}
            topTeamPayOut={topTeamPayOut}
            extraActions={
              <CommentaryButton
                onClick={handleOpenCommentary}
                className="text-xs py-1.5"
                disabled={!isCommentaryAvailable()}
              />
            }
          />
        )}
      </div> */}
      {isCommentaryModalOpen && (
        <CommentaryModal
          isOpen={isCommentaryModalOpen}
          onClose={handleCloseCommentary}
          matchId={compData?.id?.toString()}
          sportId={compData?.SportId}
          isLiveCommentaryLoading={isLiveCommentaryLoading}
          liveCommentary={liveCommentary}
          setEventFilter={setEventFilter}
          compCard={{
            localteam: {
              name: compData?.homeTeam?.name || '',
              score:
                compData?.ScoreBoard?.Tr1C1 || compData?.ScoreBoard?.Tr1 || '0',
              flag: compData?.homeTeam?.flag || undefined,
            },
            visitorteam: {
              name: compData?.awayTeam?.name || '',
              score:
                compData?.ScoreBoard?.Tr2C1 || compData?.ScoreBoard?.Tr2 || '0',
              flag: compData?.awayTeam?.flag || undefined,
            },
            status: {
              value: compData?.status || 'In Progress',
            },
          }}
        />
      )}
    </>
  );
}
