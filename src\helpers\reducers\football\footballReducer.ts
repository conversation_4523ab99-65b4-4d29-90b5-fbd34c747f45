import {
  addAFLPlayersWithinBudget,
  findAFLCaptainViceCaptain,
  generateAFLExpertTeamPayload,
  generateAFLFavouriteTeamPayload,
  generateAFLTeamPayload,
  generateRandomAFLTeam,
  LocalStorage,
} from '@/lib/utils';
import {
  AFLReservePlayers,
  AFLTeamComposition,
  FootballAction,
  footballPlayersByRole,
  FootballTeamState,
  LastEnterPlayerData,
} from '../../../../types/football';
import {
  CreateAFLTeamPayload,
  FootballPlayer,
} from '../../../../types/competitions';
import { setApiMessage } from '@/helpers/commonFunctions';
import { ALL_AFL_ROLES } from '@/helpers/constants/index';

const calculateRemainingBudget = (
  totalBudget: number,
  playersByRole: footballPlayersByRole,
): number => {
  const totalCost = Object?.values(playersByRole || {})
    ?.flat()
    ?.reduce((sum, player) => sum + player?.scoreData?.playerCurrentSalary, 0);
  return totalBudget - totalCost;
};

const resetCaptainStatus = (
  playersByRole: footballPlayersByRole,
): footballPlayersByRole => {
  const updatedPlayersByRole = { ...playersByRole };
  Object.keys(updatedPlayersByRole).forEach((roleKey) => {
    updatedPlayersByRole[roleKey as keyof footballPlayersByRole] =
      updatedPlayersByRole[roleKey as keyof footballPlayersByRole].map(
        (player) => {
          if (player.isCaiptain) {
            return { ...player, isCaiptain: false };
          }
          return player;
        },
      );
  });
  return updatedPlayersByRole;
};

const resetViceCaptainStatus = (
  playersByRole: footballPlayersByRole,
): footballPlayersByRole => {
  const updatedPlayersByRole = { ...playersByRole };
  Object.keys(updatedPlayersByRole).forEach((roleKey) => {
    updatedPlayersByRole[roleKey as keyof footballPlayersByRole] =
      updatedPlayersByRole[roleKey as keyof footballPlayersByRole].map(
        (player) => {
          if (player.isViceCaiptain) {
            return { ...player, isViceCaiptain: false };
          }
          return player;
        },
      );
  });
  return updatedPlayersByRole;
};

export const FootballTeamReducer = (
  state: FootballTeamState,
  action: FootballAction,
): FootballTeamState => {
  const localAFLLeaguePlayersByRole =
    LocalStorage.getItem<footballPlayersByRole>('afl_dream_team');
  const localAFLLeagueReservePlayers = LocalStorage.getItem<AFLReservePlayers>(
    'afl_reserve_players',
  );
  switch (action.type) {
    case 'SET_TOTAL_BALANCE': {
      const { amount } = action.payload;
      return { ...state, totalBudget: amount, remainingBudget: amount };
    }
    case 'ADD_PLAYER': {
      const { player, role } = action.payload;
      const newPlayersByRole = {
        ...state.playersByRole,
        [role]: [...state.playersByRole[role], player],
      };

      const LastEnterPlayer: LastEnterPlayerData = {
        player,
        tabSection: role,
      };

      return {
        ...state,
        playersByRole: newPlayersByRole,
        lastEntry: {
          ...state.lastEntry,
          players: [...(state.lastEntry?.players ?? []), LastEnterPlayer],
          mode: 'MANUAL',
        },
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          newPlayersByRole,
        ),
      };
    }
    case 'REMOVE_PLAYER': {
      const { playerId } = action.payload;
      type RoleKey = keyof footballPlayersByRole;
      const allRoles: RoleKey[] = [
        'BL',
        'HBL',
        'MID',
        'HFL',
        'FL',
        'FOL',
        'IC',
      ];
      const newPlayersByRole: footballPlayersByRole = {
        BL: [],
        HBL: [],
        MID: [],
        HFL: [],
        FL: [],
        FOL: [],
        IC: [],
      };
      allRoles.forEach((role) => {
        const allPlayers = (state.playersByRole[role] = state.playersByRole[
          role
        ].filter((player) => player.playerId !== playerId));
        newPlayersByRole[role] = allPlayers;
      });

      const updatedLastEntryPlayers = state.lastEntry.players.filter(
        (entry) => entry?.player?.playerId !== playerId,
      );

      return {
        ...state,
        playersByRole: newPlayersByRole,
        lastEntry: {
          ...state.lastEntry,
          players: updatedLastEntryPlayers,
        },
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          newPlayersByRole,
        ),
      };
    }

    case 'SET_CAPTAIN': {
      const { role, playerId } = action.payload;
      let updatedPlayersByRole = resetCaptainStatus(state.playersByRole);

      // Set the new captain
      updatedPlayersByRole[role] = updatedPlayersByRole[role].map((player) => {
        // If player is already captain
        if (player.playerId === playerId && player.isCaiptain) {
          return { ...player, isCaiptain: false };
        }
        // if players is fresh
        if (
          player.playerId === playerId &&
          !player.isCaiptain &&
          !player.isViceCaiptain
        ) {
          return { ...player, isCaiptain: true };
        }
        // If player is already vice captain and assing them captain
        if (player.isViceCaiptain && player.playerId === playerId) {
          return { ...player, isCaiptain: true, isViceCaiptain: false };
        }

        return player;
      });

      return {
        ...state,
        playersByRole: updatedPlayersByRole,
      };
    }

    case 'SET_VICE_CAPTAIN': {
      const { role, playerId } = action.payload;
      let updatedPlayersByRole = resetViceCaptainStatus(state.playersByRole);

      // Set the new vice captain
      updatedPlayersByRole[role] = updatedPlayersByRole[role].map((player) => {
        if (player.isCaiptain && player.playerId === playerId) {
          return { ...player, isCaiptain: false, isViceCaiptain: true };
        }
        if (!player.isViceCaiptain && player.playerId === playerId) {
          return { ...player, isViceCaiptain: !player.isViceCaiptain };
        }
        return player;
      });

      return {
        ...state,
        playersByRole: updatedPlayersByRole,
      };
    }

    case 'SET_PLAYER_LIMITS': {
      const playerLimits = action.payload;
      return {
        ...state,
        playerByRoleLimit: playerLimits,
      };
    }

    case 'CREATE_LUCKY_TEAM': {
      try {
        const { playersByRole } = action.payload;
        const teamComposion: AFLTeamComposition = {
          BL: {
            min: state.playerByRoleLimit.BL,
            max: state.playerByRoleLimit.BL,
          },
          HBL: {
            min: state.playerByRoleLimit.HBL,
            max: state.playerByRoleLimit.HBL,
          },
          HFL: {
            min: state.playerByRoleLimit.HFL,
            max: state.playerByRoleLimit.HFL,
          },
          MID: {
            min: state.playerByRoleLimit.MID,
            max: state.playerByRoleLimit.MID,
          },
          FL: {
            min: state.playerByRoleLimit.FL,
            max: state.playerByRoleLimit.FL,
          },
          FOL: {
            min: state.playerByRoleLimit.FOL,
            max: state.playerByRoleLimit.FOL,
          },
          IC: {
            min: state.playerByRoleLimit.IC,
            max: state.playerByRoleLimit.IC,
          },

          TOTAL_PLAYERS: 22,
        };

        console.log('teamComposion', teamComposion);

        const luckyPlayersByRole = generateRandomAFLTeam(
          playersByRole,
          teamComposion,
        );

        console.log('luckyPlayersByRole', luckyPlayersByRole);

        const { captain, viceCaptain } =
          findAFLCaptainViceCaptain(luckyPlayersByRole);

        const luckyPlayerPayloadData = generateAFLTeamPayload(
          luckyPlayersByRole,
          captain,
          viceCaptain,
          state.reserveState.reservePlayerPayload || [],
        );

        const validLuckyPlayersByRole = addAFLPlayersWithinBudget(
          luckyPlayersByRole,
          state.remainingBudget,
        );

        const {
          eventId: luckyEventId,
          eventName: luckyEventName,
          sportId: luckySportId,
          tournamentId: luckyTournamentId,
          competitionId: luckyCompetitionId,
          name: luckyTeamName,
        } = action.payload;

        const createLuckyDreamTeamPayload: CreateAFLTeamPayload = {
          playerData: luckyPlayerPayloadData,
          eventId: luckyEventId,
          eventName: luckyEventName,
          sportId: luckySportId,
          tournamentId: luckyTournamentId,
          competitionId: luckyCompetitionId,
          name: luckyTeamName,
        };

        return {
          ...state,
          playersByRole: validLuckyPlayersByRole,
          createDreamTeamPayload: createLuckyDreamTeamPayload,
          remainingBudget: calculateRemainingBudget(
            state.totalBudget,
            validLuckyPlayersByRole,
          ),
          lastEntry: {
            ...state.lastEntry,
            mode: 'FEELING_LUCKY',
          },
        };
      } catch (error) {
        if (error instanceof Error) {
          setApiMessage('error', error?.message);
        }

        return state;
      }
    }

    case 'CREATE_DREAM_TEAM': {
      const {
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
        coins,
        bonusCoins,
      } = action.payload;

      const { viceCaptain, captain } = findAFLCaptainViceCaptain(
        state.playersByRole,
      );

      const playerPayloadData = generateAFLTeamPayload(
        state.playersByRole,
        captain,
        viceCaptain,
        state.reserveState.reservePlayerPayload || [],
      );
      const createDreamTeamPayload: CreateAFLTeamPayload = {
        playerData: playerPayloadData,
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
        coins,
        bonusCoins,
      };
      return { ...state, createDreamTeamPayload };
    }

    case 'GET_DREAM_TEAM': {
      const { fantasyTeamResponse, playerId, role } = action.payload;
      const dreamPlayersByRole = fantasyTeamResponse.result;
      const newDreamPlayerByRole: footballPlayersByRole = {
        BL: [],
        HBL: [],
        MID: [],
        HFL: [],
        FL: [],
        FOL: [],
        IC: [],
      };

      const [playerCaptain] = dreamPlayersByRole?.captain || [];
      const [playerViceCaptain] = dreamPlayersByRole?.viceCaptain || [];

      ALL_AFL_ROLES.forEach((role) => {
        switch (role) {
          case 'BL': // Back Line
            newDreamPlayerByRole['BL'] = dreamPlayersByRole?.backLine?.map(
              (player) => ({
                ...player,
                isCaiptain: player?.playerId === playerCaptain?.playerId,
                isViceCaiptain:
                  player?.playerId === playerViceCaptain?.playerId,
              }),
            );
            break;
          case 'HBL': // Half Back Line
            newDreamPlayerByRole['HBL'] = dreamPlayersByRole?.halfBackLine?.map(
              (player) => ({
                ...player,
                isCaiptain: player?.playerId === playerCaptain?.playerId,
                isViceCaiptain:
                  player?.playerId === playerViceCaptain?.playerId,
              }),
            );
            break;
          case 'MID': // Midfield
            newDreamPlayerByRole['MID'] = dreamPlayersByRole?.midfield?.map(
              (player) => ({
                ...player,
                isCaiptain: player?.playerId === playerCaptain?.playerId,
                isViceCaiptain:
                  player?.playerId === playerViceCaptain?.playerId,
              }),
            );
            break;
          case 'HFL': // Half Forward Line
            newDreamPlayerByRole['HFL'] =
              dreamPlayersByRole?.halfForwardLine?.map((player) => ({
                ...player,
                isCaiptain: player?.playerId === playerCaptain?.playerId,
                isViceCaiptain:
                  player?.playerId === playerViceCaptain?.playerId,
              }));
            break;
          case 'FL': // Forward Line
            newDreamPlayerByRole['FL'] = dreamPlayersByRole?.forwardLine?.map(
              (player) => ({
                ...player,
                isCaiptain: player?.playerId === playerCaptain?.playerId,
                isViceCaiptain:
                  player?.playerId === playerViceCaptain?.playerId,
              }),
            );
            break;
          case 'FOL': // Followers (Ruck, Rover, Ruck Rover)
            newDreamPlayerByRole['FOL'] = dreamPlayersByRole?.followers?.map(
              (player) => ({
                ...player,
                isCaiptain: player?.playerId === playerCaptain?.playerId,
                isViceCaiptain:
                  player?.playerId === playerViceCaptain?.playerId,
              }),
            );
            break;
          case 'IC': // Interchange
            // @ts-ignore
            newDreamPlayerByRole['IC'] = dreamPlayersByRole.interchange?.map(
              (player) => ({
                ...player,
                isCaiptain: player?.playerId === playerCaptain?.playerId,
                isViceCaiptain:
                  player?.playerId === playerViceCaptain?.playerId,
              }),
            );
            break;
        }
      });
      const roleToRemove = role;
      const playerIdToRemove = playerId;
      const updatedPlayersByRole = { ...newDreamPlayerByRole };
      if (roleToRemove && playerIdToRemove) {
        updatedPlayersByRole[roleToRemove] = updatedPlayersByRole[
          roleToRemove
        ]?.filter((player) => player.playerId !== playerIdToRemove);
      }

      // reserve players with correct position
      // create array with reserve limit with null values
      const reservePlayers = Array(state.reserveState.reservePlayersLimit).fill(
        null,
      );

      // add reserve players to correct position, ensuring we don't exceed the limit
      dreamPlayersByRole?.reserve?.forEach((player) => {
        const rank = (player.reserveRank ?? 0) - 1;
        if (rank < state.reserveState.reservePlayersLimit) {
          reservePlayers[rank] = player;
        }
      });

      return {
        ...state,
        playersByRole: updatedPlayersByRole,
        reserveState: {
          ...state.reserveState,
          reservePlayers: reservePlayers,
        },
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          updatedPlayersByRole,
        ),
      };
    }

    case 'CREATE_AFL_EXPERT_TEAM': {
      const { dreamPlayers, playerByRole } = action.payload;

      const resetExpertPlayersByRole: footballPlayersByRole = {
        BL: [],
        HBL: [],
        MID: [],
        HFL: [],
        FL: [],
        FOL: [],
        IC: [],
      };

      ALL_AFL_ROLES.forEach((role) => {
        const rolesPlayer = playerByRole[role].map((player) => {
          if (player.isCaiptain) {
            return { ...player, isCaiptain: false };
          }
          if (player.isViceCaiptain) {
            return { ...player, isViceCaiptain: false };
          }
          return player;
        });
        resetExpertPlayersByRole[role] = rolesPlayer;
      });

      const newPlayerByRole = generateAFLExpertTeamPayload(
        dreamPlayers,
        resetExpertPlayersByRole,
      );

      const validExpertPlayersByRole = addAFLPlayersWithinBudget(
        newPlayerByRole,
        state.remainingBudget,
      );

      return {
        ...state,
        playersByRole: validExpertPlayersByRole,
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          validExpertPlayersByRole,
        ),

        lastEntry: {
          ...state.lastEntry,
          mode: 'EXPERT_FAVORITE',
        },
      };
    }
    case 'CREATE_FAVOURITE_TEAM': {
      const { favoriteTeam, playersByRole: favouritePlayerByRole } =
        action.payload;
      const resetPlayersByRole: footballPlayersByRole = {
        BL: [],
        HBL: [],
        MID: [],
        HFL: [],
        FL: [],
        FOL: [],
        IC: [],
      };

      ALL_AFL_ROLES.forEach((role) => {
        const rolesPlayer = favouritePlayerByRole[role].map((player) => {
          if (player.isCaiptain) {
            return { ...player, isCaiptain: false };
          }
          if (player.isViceCaiptain) {
            return { ...player, isViceCaiptain: false };
          }
          return player;
        });
        resetPlayersByRole[role] = rolesPlayer;
      });

      const newPlayersByRole = generateAFLFavouriteTeamPayload(
        favoriteTeam,
        state.playerByRoleLimit,
        resetPlayersByRole,
      );

      const validPlayersByRole = addAFLPlayersWithinBudget(
        newPlayersByRole,
        state.remainingBudget,
      );

      return {
        ...state,
        playersByRole: validPlayersByRole,
        remainingBudget: calculateRemainingBudget(
          state.totalBudget,
          validPlayersByRole,
        ),

        lastEntry: {
          ...state.lastEntry,
          mode: 'FAVORITE',
        },
      };
    }

    case 'CLEAR_TEAM': {
      return {
        ...state,
        playersByRole: localAFLLeaguePlayersByRole || {
          BL: [],
          HBL: [],
          MID: [],
          HFL: [],
          FL: [],
          FOL: [],
          IC: [],
        },
        remainingBudget: state.totalBudget,
        reserveState: {
          reservePlayers: localAFLLeagueReservePlayers || [null, null, null],
          reservePlayersLimit: state.reserveState.reservePlayersLimit,
        },
      };
    }

    case 'ADD_RESERVE_PLAYER': {
      const { player, position } = action.payload;
      const remainingBudget =
        state.remainingBudget - player.scoreData?.playerCurrentSalary;
      // adding player to correct position
      const newReservePlayers = [...state.reserveState.reservePlayers];
      newReservePlayers[position] = player;

      return {
        ...state,
        reserveState: {
          ...state.reserveState,
          reservePlayers: newReservePlayers,
        },
        remainingBudget,
      };
    }

    case 'REMOVE_RESERVE_PLAYER': {
      const { playerId, position } = action.payload;
      const player = state.reserveState.reservePlayers.find(
        (player) => player?.playerId === playerId,
      );
      if (!player) {
        return state;
      }
      const remainingBudget =
        state.remainingBudget + player.scoreData?.playerCurrentSalary;
      return {
        ...state,
        reserveState: {
          ...state.reserveState,
          reservePlayers: state.reserveState.reservePlayers.map((player) =>
            player?.playerId === playerId ? null : player,
          ),
        },
        remainingBudget,
      };
    }

    case 'CREATE_RESERVE_PLAYER_PAYLOAD': {
      const { reservePlayerPayload } = action.payload;
      return {
        ...state,
        reserveState: { ...state.reserveState, reservePlayerPayload },
      };
    }

    default: {
      return state;
    }
  }
};
