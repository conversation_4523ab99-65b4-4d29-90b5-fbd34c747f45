'use client';

import 'react-image-crop/dist/ReactCrop.css';

import { Loader2, Trash2Icon, UploadIcon } from 'lucide-react';
import React, { type SyntheticEvent, useEffect } from 'react';
import type { FileWithPath } from 'react-dropzone';
import React<PERSON><PERSON>, {
  centerCrop,
  type Crop,
  makeAspectCrop,
  type PixelCrop,
} from 'react-image-crop';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/UI/avatar';
import { Button } from '@/components/UI/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogTrigger,
} from '@/components/UI/dialog';

export type FileWithPreview = FileWithPath & {
  preview: string;
};

interface ImageCropperProps {
  dialogOpen: boolean;
  setDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedFile: FileWithPreview | null;
  setSelectedFile: React.Dispatch<React.SetStateAction<FileWithPreview | null>>;
  onCroppedImage: (croppedImageUrl: string) => void;
  uploadEndpoint: string; // API endpoint for image upload
  onUploadSuccess?: (response: any) => void; // Callback for successful upload
  onUploadError?: (error: Error) => void; // Callback for upload error
}

export default function ImageCropper({
  dialogOpen,
  setDialogOpen,
  selectedFile,
  setSelectedFile,
  onCroppedImage,
  uploadEndpoint,
  onUploadSuccess,
  onUploadError,
}: Readonly<ImageCropperProps>) {
  const aspect = 1;
  const imgRef = React.useRef<HTMLImageElement | null>(null);
  const [crop, setCrop] = React.useState<Crop>();
  const [completedCrop, setCompletedCrop] = React.useState<PixelCrop>();
  const [zoom, setZoom] = React.useState<number>(1);
  const [isUploading, setIsUploading] = React.useState(false);

  // Convert base64 to blob
  const base64ToBlob = async (base64Data: string): Promise<Blob> => {
    const response = await fetch(base64Data);
    const blob = await response.blob();
    return blob;
  };

  // Function to upload image to server
  const uploadImage = async (croppedImageUrl: string) => {
    try {
      setIsUploading(true);

      // Convert base64 image to blob
      const imageBlob = await base64ToBlob(croppedImageUrl);

      // Create FormData and append the image
      const formData = new FormData();
      formData.append('image', imageBlob, 'cropped-image.png');

      // Upload to server
      const response = await fetch(uploadEndpoint, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const data = await response.json();

      // Call success callback if provided
      onUploadSuccess?.(data);

      // Close the dialog
      setDialogOpen(false);

      return data;
    } catch (error) {
      console.error('Upload error:', error);

      // Call error callback if provided
      if (error instanceof Error) {
        onUploadError?.(error);
      }

      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  // Function to generate the cropped image with zoom
  function getCroppedImg(
    image: HTMLImageElement,
    crop: PixelCrop,
    zoom: number,
  ): string {
    const canvas = document.createElement('canvas');
    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;

    const cropWidth = crop.width * scaleX;
    const cropHeight = crop.height * scaleY;

    canvas.width = cropWidth;
    canvas.height = cropHeight;

    const ctx = canvas.getContext('2d');

    if (ctx) {
      ctx.imageSmoothingQuality = 'high';
      ctx.imageSmoothingEnabled = true;

      const sourceX = (crop.x * scaleX) / zoom;
      const sourceY = (crop.y * scaleY) / zoom;
      const sourceWidth = cropWidth / zoom;
      const sourceHeight = cropHeight / zoom;

      ctx.drawImage(
        image,
        sourceX,
        sourceY,
        sourceWidth,
        sourceHeight,
        0,
        0,
        cropWidth,
        cropHeight,
      );
    }

    return canvas.toDataURL('image/png', 1.0);
  }

  // Handler when the image is loaded
  function onImageLoad(e: SyntheticEvent<HTMLImageElement>) {
    if (aspect) {
      const { width, height } = e.currentTarget;
      setCrop(centerAspectCrop(width, height, aspect));
    }
  }

  // Handler when the crop is complete
  function onCropComplete(crop: PixelCrop) {
    setCompletedCrop(crop);
  }

  // Handle the final crop and upload
  async function onCropAndUpload() {
    if (imgRef.current && completedCrop?.width && completedCrop?.height) {
      try {
        const croppedImageUrl = getCroppedImg(
          imgRef.current,
          completedCrop,
          zoom,
        );
        onCroppedImage(croppedImageUrl);
        await uploadImage(croppedImageUrl);
      } catch (error) {
        console.error('Error processing image:', error);
        if (error instanceof Error) {
          onUploadError?.(error);
        }
      }
    }
  }

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    overflow: 'hidden',
    width: '100%',
    height: '460px',
  };

  const imageStyle: React.CSSProperties = {
    transform: `scale(${zoom})`,
    transformOrigin: 'center',
    transition: 'transform 0.3s ease',
    width: '100%',
    height: '100%',
    objectFit: 'contain',
  };

  useEffect(() => {
    if (!dialogOpen) {
      setSelectedFile(null);
    }
  }, [dialogOpen]);

  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogTrigger />
      <DialogContent className="p-0 gap-0">
        <div className="p-6 size-full">
          <div style={containerStyle}>
            <ReactCrop
              crop={crop}
              onChange={(_, percentCrop) => setCrop(percentCrop)}
              onComplete={onCropComplete}
              aspect={aspect}
              className="w-full h-full"
            >
              <Avatar className="size-full rounded-none">
                <AvatarImage
                  ref={imgRef}
                  className="size-full rounded-none"
                  alt="Image Cropper Shell"
                  src={selectedFile?.preview}
                  onLoad={onImageLoad}
                  style={imageStyle}
                />
                <AvatarFallback className="size-full min-h-[460px] rounded-none">
                  Loading...
                </AvatarFallback>
              </Avatar>
            </ReactCrop>
          </div>
        </div>

        <div className="p-6 pt-0 justify-center">
          <div className="block mb-2 text-sm">Zoom</div>
          <input
            type="range"
            min="1"
            max="3"
            step="0.1"
            value={zoom}
            onChange={(e) => setZoom(Number(e.target.value))}
            className="w-full"
          />
        </div>

        <DialogFooter className="p-6 pt-0 justify-center">
          <DialogClose asChild>
            <Button
              size={'sm'}
              type="reset"
              className="w-fit"
              variant={'outline'}
              onClick={() => setSelectedFile(null)}
              disabled={isUploading}
            >
              <Trash2Icon className="mr-1.5 size-4" />
              Cancel
            </Button>
          </DialogClose>
          <Button
            type="submit"
            size={'sm'}
            className="w-fit"
            onClick={onCropAndUpload}
            disabled={isUploading}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-1.5 size-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <UploadIcon className="mr-1.5 size-4" />
                Crop & Upload
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Helper function to center the crop (unchanged)
export function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number,
): Crop {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 50,
        height: 50,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}
