import { Dispatch, SetStateAction } from 'react';
import {
  CreateRugbyTeamPayload,
  FantasyTeamResponse,
  PlayerStats,
} from '../competitions';
import { CompetitionStatusProps, TeamFilterState } from '..';

export type RugbyLeaguePlayerRoleType = keyof RugbyLeaguePlayersByRole;

export type RugbyPlayer = {
  id: number;
  role: RugbyLeaguePlayerRoleType;
  squadsId: number;
  tournamentId: number;
  playerId: number;
  name: string;
  scoreData: PlayerStats;
  teamName: string;
  image?: string | null;
  playerValue?: number | null;
  isAdded?: boolean;
  positionType?: RugbyLeaguePlayerRoleType;
  isCaiptain?: boolean;
  isViceCaiptain?: boolean;
  teamId?: number;
  reserveRank?: number;
  playedLastMatch?: boolean;
  lineupStatus?: 'unannounced' | 'announced' | 'substitute';
};

export type RugbyPlayerArray = RugbyPlayer[];

export type RugbyLeaguePlayersByRole = {
  BAC: RugbyPlayer[];
  HAL: RugbyPlayer[];
  BR: RugbyPlayer[];
  FRF: RugbyPlayer[];
  IC: RugbyPlayer[];
};

export type RugbyLeaguePlayersByRoleLimit = {
  BAC: number;
  HAL: number;
  BR: number;
  FRF: number;
  IC: number;
};

export type RugbyDreamTeamPlayer = {
  id: number;
  dreamTeamId: number;
  playerId: number;
  positionType:
    | 'backRow'
    | 'backs'
    | 'frontRowForwards'
    | 'halves'
    | 'interchange'
    | 'viceCaptain'
    | 'captain';
};

export type CreateExpertTeam = {
  type: 'CREATE_EXPERT_TEAM';
  payload: {
    dreamPlayers: RugbyDreamTeamPlayer[];
    playerByRole: RugbyLeaguePlayersByRole;
  };
};

export type LastEnterPlayerData = {
  player: RugbyPlayer;
  tabSection: keyof RugbyLeaguePlayersByRole;
};

export type LastEntryType = {
  players: LastEnterPlayerData[];
  mode: 'FEELING_LUCKY' | 'FAVORITE' | 'EXPERT_FAVORITE' | 'MANUAL';
};

// Reserve player support
export type ReservePlayerPayload = {
  playerId: number;
  playerValue: number;
  reserve: boolean;
  reserveRank: number;
};

export interface ReserveState {
  reservePlayers: (RugbyPlayer | null)[];
  reservePlayersLimit: number;
  reservePlayerPayload?: ReservePlayerPayload[];
}

export type RugbyTeamState = {
  playersByRole: RugbyLeaguePlayersByRole;
  playerByRoleLimit: RugbyLeaguePlayersByRoleLimit;
  remainingBudget: number;
  totalBudget: number;
  lastEntry: LastEntryType;
  createDreamTeamPayload?: CreateRugbyTeamPayload;
  reserveState: ReserveState;
};

type AddPlayer = {
  type: 'ADD_PLAYER';
  payload: {
    player: RugbyPlayer;
    role: keyof RugbyLeaguePlayersByRole;
  };
};
type RemovePlayer = {
  type: 'REMOVE_PLAYER';
  payload: {
    playerId: number;
    role: keyof RugbyLeaguePlayersByRole;
  };
};

type SetCaptain = {
  type: 'SET_CAPTAIN';
  payload: {
    playerId: number;
    role: keyof RugbyLeaguePlayersByRole;
  };
};

type SetViceCaptain = {
  type: 'SET_VICE_CAPTAIN';
  payload: {
    playerId: number;
    role: keyof RugbyLeaguePlayersByRole;
  };
};

type ClearTeam = {
  type: 'CLEAR_TEAM';
};

type CreateDreamTeam = {
  type: 'CREATE_DREAM_TEAM';
  payload: {
    sportId: string | null;
    eventId: string | null;
    tournamentId: string | null;
    eventName: string | null;
    competitionId: string | null;
    name: string;
    coins?: number | null;
    bonusCoins?: number | null;
  };
};

type GetDreamTeam = {
  type: 'GET_DREAM_TEAM';
  payload: {
    fantasyTeamResponse: FantasyTeamResponse;
    playerId?: number;
    role?: keyof RugbyLeaguePlayersByRole;
  };
};

export type LuckyCreateTeamPayload = {
  playersByRole: RugbyLeaguePlayersByRole;
  sportId: string | null;
  eventId: string | null;
  tournamentId: string | null;
  eventName: string | null;
  competitionId: string | null;
  name: string;
};

export interface RugbyPlayerFavouriteType {
  maxSelected: number;
  playerId: number;
  positionType: string;
}

export interface RugbyFavoriteTeam {
  backRow: RugbyPlayerFavouriteType[];
  backs: RugbyPlayerFavouriteType[];
  frontRowForwards: RugbyPlayerFavouriteType[];
  halves: RugbyPlayerFavouriteType[];
  interchange: RugbyPlayerFavouriteType[];
  viceCaptain: RugbyPlayerFavouriteType[];
  captain: RugbyPlayerFavouriteType[];
}

export interface ValidRugbyFavoriteTeamSelection {
  backRow: RugbyPlayerFavouriteType[];
  backs: RugbyPlayerFavouriteType[];
  frontRowForwards: RugbyPlayerFavouriteType[];
  halves: RugbyPlayerFavouriteType[];
  interchange: RugbyPlayerFavouriteType[];
}

export interface RugbyTeamSelectionData {
  possibleCaptains: RugbyPlayerFavouriteType[];
  possibleViceCaptains: RugbyPlayerFavouriteType[];
  validTeamSelection: ValidRugbyFavoriteTeamSelection;
}

type CreateLuckyTeam = {
  type: 'CREATE_LUCKY_TEAM';
  payload: LuckyCreateTeamPayload;
};
type PlayerRole = 'BAC' | 'BR' | 'FRF' | 'HAL' | 'IC';
export type RugbyTeamComposition = {
  [key in PlayerRole]: {
    min: number;
    max: number;
  };
} & {
  TOTAL_PLAYERS: number;
};

export type ApplySportsRules = {
  type: 'APPLY_SPORT_RULES';
  payload: { playerByRoleLimit: RugbyLeaguePlayersByRoleLimit };
};

export type SetTotalBalance = {
  type: 'SET_TOTAL_BALANCE';
  payload: {
    amount: number;
  };
};

export type CreateFavoriteTeam = {
  type: 'CREATE_FAVOURITE_TEAM';
  payload: {
    favoriteTeam: RugbyFavoriteTeam;
    playersByRole: RugbyLeaguePlayersByRole;
  };
};

type AddReservePlayer = {
  type: 'ADD_RESERVE_PLAYER';
  payload: { player: RugbyPlayer; position: number };
};
type RemoveReservePlayer = {
  type: 'REMOVE_RESERVE_PLAYER';
  payload: { playerId: number; position: number };
};
type CreateReservePlayerPayload = {
  type: 'CREATE_RESERVE_PLAYER_PAYLOAD';
  payload: { reservePlayerPayload: ReservePlayerPayload[] };
};
type ClearReservePlayers = {
  type: 'CLEAR_RESERVE_PLAYERS';
};

export type RugbyLeagueAction =
  | AddPlayer
  | RemovePlayer
  | SetCaptain
  | SetViceCaptain
  | ClearTeam
  | CreateDreamTeam
  | GetDreamTeam
  | CreateLuckyTeam
  | SetTotalBalance
  | CreateFavoriteTeam
  | CreateExpertTeam
  | AddReservePlayer
  | RemoveReservePlayer
  | CreateReservePlayerPayload
  | ClearReservePlayers;

export type RugbyLeagueContextType = {
  state: RugbyTeamState;
  activeTabPlayer: keyof RugbyLeaguePlayersByRole;
  setActiveTabPlayer: Dispatch<SetStateAction<keyof RugbyLeaguePlayersByRole>>;
  showPlayerTabel: boolean;
  setShowPlayerTabel: Dispatch<SetStateAction<boolean>>;
  addPlayer: (
    player: RugbyPlayer,
    role: keyof RugbyLeaguePlayersByRole,
  ) => void;
  removePlayer: (
    playerId: number,
    role: keyof RugbyLeaguePlayersByRole,
  ) => void;
  setPlayerRoleToCaptain: (
    playerId: number,
    role: keyof RugbyLeaguePlayersByRole,
  ) => void;
  setPlayerRoleToViceCaiptain: (
    playerId: number,
    role: keyof RugbyLeaguePlayersByRole,
  ) => void;
  clearTeam: () => void;
  createDreamTeam: (payload: {
    sportId: string | null;
    eventId: string | null;
    tournamentId: string | null;
    eventName: string | null;
    competitionId: string | null;
    name: string;
    coins?: number | null;
    bonusCoins?: number | null;
  }) => void;
  getDreamTeam: (
    fantasyTeamResponse: FantasyTeamResponse,
    playerId?: number,
    role?: keyof RugbyLeaguePlayersByRole,
  ) => void;
  createLuckyTeam: (payload: LuckyCreateTeamPayload) => void;
  setBudget: (amount: number) => void;
  createFavouriteTeam: (payload: {
    favoriteTeam: RugbyFavoriteTeam;
    playersByRole: RugbyLeaguePlayersByRole;
  }) => void;
  createExpertTeam: (
    payload: RugbyDreamTeamPlayer[],
    playerByRole: RugbyLeaguePlayersByRole,
  ) => void;
  team: TeamFilterState;
  setTeam: Dispatch<SetStateAction<TeamFilterState>>;
  // Reserve player API
  openReserveModal: boolean;
  setOpenReserveModal: Dispatch<SetStateAction<boolean>>;
  activePlayerPosition: number;
  setActivePlayerPosition: Dispatch<SetStateAction<number>>;
  addReservePlayer: (player: RugbyPlayer, position: number) => void;
  removeReservePlayer: (playerId: number, position: number) => void;
  createReservePlayerPayload: (
    reservePlayerPayload: ReservePlayerPayload[],
  ) => void;
  clearReservePlayers: () => void;
};

export type RugbyLeagueTournamentHeaderProps = {
  competitionStatus?: CompetitionStatusProps;
  tournamentDetails: {
    prizePoolAmount: number;
    tournamentType: string;
    entryCoin?: string | number;
    minUserEntry?: string | number;
    userEntryCount: number;
    currentRank?: string | number;
    tournamentName?: string;
    winningPrizeAmount?: string | number;
    drawPoolAvailable?: boolean;
    startTime: string;
  };
  isTournamentDetailsLoading: boolean;
};
