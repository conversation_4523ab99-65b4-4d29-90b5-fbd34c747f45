import type {
  UseMutationOptions,
  UseQueryOptions,
} from '@tanstack/react-query';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type { AxiosError, AxiosRequestConfig } from 'axios';
import axios from 'axios';

// Types
export type ApiResponse<T> = {
  data: T;
  status: number;
  message?: string;
};

export type ApiError = {
  message: string;
  status: number;
  errors?: Record<string, string[]>;
};

// HTTP Methods Enum
export enum HttpMethod {
  GET = 'get',
  POST = 'post',
  PUT = 'put',
  PATCH = 'patch',
  DELETE = 'delete',
}

// API Endpoints Enum
export enum ApiEndpoint {
  Players = '/api/players',
  Teams = '/api/teams',
  GetAllPlan = `/subscription/plan-list?category=fantasy`,
}

// Base API Configuration
const DEFAULT_CONFIG: AxiosRequestConfig = {
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    'Content-Type': 'application/json',
    Authorization:
      'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************.7AWjHRFKYvkK_mszdbiCAyXUmXSeyQn-j4fkjOqevK8',
  },
};

class CustomApiError extends Error {
  status: number;
  errors?: any;

  constructor(message: string, status: number, errors?: any) {
    super(message);
    this.name = 'CustomApiError';
    this.status = status;
    this.errors = errors;

    // Maintain proper stack trace (only available in V8 environments like Node.js)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CustomApiError);
    }
  }
}

// API Client with proper error handling and types
async function apiClient<T>(
  method: HttpMethod.GET | HttpMethod.DELETE,
  endpoint: ApiEndpoint,
  config?: AxiosRequestConfig,
): Promise<ApiResponse<T>>;
async function apiClient<T, D>(
  method: HttpMethod.POST | HttpMethod.PUT | HttpMethod.PATCH,
  endpoint: ApiEndpoint,
  data: D,
  config?: AxiosRequestConfig,
): Promise<ApiResponse<T>>;
async function apiClient<T, D = unknown>(
  method: HttpMethod,
  endpoint: ApiEndpoint,
  dataOrConfig?: D | AxiosRequestConfig,
  config?: AxiosRequestConfig,
): Promise<ApiResponse<T>> {
  try {
    const isDataMethod = [
      HttpMethod.POST,
      HttpMethod.PUT,
      HttpMethod.PATCH,
    ].includes(method);
    const axiosConfig: AxiosRequestConfig = {
      ...DEFAULT_CONFIG,
      ...(isDataMethod ? config : (dataOrConfig as AxiosRequestConfig)),
      method,
      url: endpoint,
    };

    if (isDataMethod) {
      axiosConfig.data = dataOrConfig;
    }

    const response = await axios(axiosConfig);

    return {
      data: response.data,
      status: response.status,
      message: response.statusText,
    };
  } catch (error) {
    const axiosError = error as AxiosError<ApiError>;
    throw new CustomApiError(
      axiosError.response?.data?.message ?? 'An unexpected error occurred',
      axiosError.response?.status ?? 500,
      axiosError.response?.data?.errors,
    );
  }
}

// Enhanced Custom Hooks
export function useFetchData<T>(
  key: string | readonly unknown[],
  endpoint: ApiEndpoint,
  options?: Omit<
    UseQueryOptions<ApiResponse<T>, ApiError>,
    'queryKey' | 'queryFn'
  >,
) {
  return useQuery<ApiResponse<T>, ApiError>({
    queryKey: Array.isArray(key) ? key : [key],
    queryFn: () => apiClient<T>(HttpMethod.GET, endpoint),
    ...options,
  });
}

export function useCreateData<T, D>(
  key: string | readonly unknown[],
  endpoint: ApiEndpoint,
  options?: Omit<UseMutationOptions<ApiResponse<T>, ApiError, D>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();

  return useMutation<ApiResponse<T>, ApiError, D>({
    mutationFn: (data: D) => apiClient<T, D>(HttpMethod.POST, endpoint, data),
    onSuccess: () => {
      const queryKey = Array.isArray(key) ? key : [key];
      queryClient.invalidateQueries({ queryKey });
    },
    ...options,
  });
}

export function useUpdateData<T, D>(
  key: string | readonly unknown[],
  endpoint: ApiEndpoint,
  options?: Omit<UseMutationOptions<ApiResponse<T>, ApiError, D>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();

  return useMutation<ApiResponse<T>, ApiError, D>({
    mutationFn: (data: D) => apiClient<T, D>(HttpMethod.PUT, endpoint, data),
    onSuccess: () => {
      const queryKey = Array.isArray(key) ? key : [key];
      queryClient.invalidateQueries({ queryKey });
    },
    ...options,
  });
}

export function useDeleteData<T>(
  key: string | readonly unknown[],
  endpoint: ApiEndpoint,
  options?: Omit<
    UseMutationOptions<ApiResponse<T>, ApiError, void>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();

  return useMutation<ApiResponse<T>, ApiError>({
    mutationFn: () => apiClient<T>(HttpMethod.DELETE, endpoint),
    onSuccess: () => {
      const queryKey = Array.isArray(key) ? key : [key];
      queryClient.invalidateQueries({ queryKey });
    },
    ...options,
  });
}
