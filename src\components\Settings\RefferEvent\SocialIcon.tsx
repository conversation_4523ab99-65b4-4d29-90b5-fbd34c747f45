import React from 'react';

type SocialType = 'whatsapp' | 'facebook' | 'messenger' | 'twitter' | 'snapchat' | 'linkedin' | 'imessage';

const SocialIcon = ({ type }: { type: SocialType }) => {
  const getColor = () => {
    switch (type) {
      case 'whatsapp':
        return 'bg-green-500';
      case 'facebook':
        return 'bg-blue-600';
      case 'messenger':
        return 'bg-blue-400';
      case 'twitter':
        return 'bg-black';
      case 'snapchat':
        return 'bg-yellow-400';
      case 'linkedin':
        return 'bg-blue-700';
      case 'imessage':
        return 'bg-green-400';
      default:
        return 'bg-gray-500';
    }
  };
  const getIcon = () => {
    switch (type) {
      case 'whatsapp':
        return '👋';
      case 'facebook':
        return 'f';
      case 'messenger':
        return '✉️';
      case 'twitter':
        return '✕';
      case 'snapchat':
        return '👻';
      case 'linkedin':
        return 'in';
      case 'imessage':
        return '💬';
      default:
        return '?';
    }
  };
  return (
    <div
      className={`${getColor()} w-10 h-10 rounded-full flex items-center justify-center text-white font-bold`}
    >
      {getIcon()}
    </div>
  );
};

export default SocialIcon; 