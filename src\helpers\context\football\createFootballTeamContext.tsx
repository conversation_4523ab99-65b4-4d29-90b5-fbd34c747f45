import {
  createContext,
  ReactNode,
  useContext,
  useMemo,
  useReducer,
  useState,
} from 'react';
import {
  AFLDreamTeamPlayer,
  AFLFavoriteTeam,
  footballPlayersByRole,
  footballTeamContextType,
  FootballTeamState,
  LuckyCreateTeamPayload,
  TabItem,
  ReservePlayerPayload,
} from '../../../../types/football';
import { FootballTeamReducer } from '@/helpers/reducers/football/footballReducer';
import {
  FantasyTeamResponse,
  FootballPlayer,
} from '../../../../types/competitions';
import { setApiMessage } from '@/helpers/commonFunctions';

const footballTeamContext = createContext<footballTeamContextType | undefined>(
  undefined,
);

const initialState: FootballTeamState = {
  playersByRole: {
    BL: [],
    HBL: [],
    MID: [],
    HFL: [],
    FL: [],
    FOL: [],
    IC: [],
  },
  playerByRoleLimit: {
    BL: 3,
    HBL: 3,
    MID: 3,
    HFL: 3,
    FL: 3,
    FOL: 3,
    IC: 4,
  },
  lastEntry: {
    mode: 'MANUAL',
    players: [],
  },
  remainingBudget: 0,
  totalBudget: 0,
  reserveState: {
    reservePlayers: [null, null, null],
    reservePlayersLimit: 3,
  },
};

const FootballProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(FootballTeamReducer, initialState);
  const [activeTabPlayer, setActiveTabPlayer] =
    useState<keyof footballPlayersByRole>('BL');
  const [showPlayerTabel, setShowPlayerTabel] = useState<boolean>(false);
  const [openReserveModal, setOpenReserveModal] = useState<boolean>(false);
  const [activePlayerPosition, setActivePlayerPosition] = useState<number>(0);

  const {
    playersByRole: { BL, HBL, MID, HFL, FL, FOL, IC },
    playerByRoleLimit: {
      BL: BLLIMIT,
      HBL: HBLLIMIT,
      MID: MIDLIMIT,
      HFL: HFLLIMIT,
      FL: FLLIMIT,
      FOL: FOLLIMIT,
      IC: ICLIMIT,
    },
  } = state;

  // Helper functions
  const getTotalPlayers = (playersByRole: footballPlayersByRole): number => {
    return Object.values(playersByRole).reduce(
      (total, players) => total + players.length,
      0,
    );
  };



  const canAddReservePlayer = (
    player: FootballPlayer,
  ) => {
    const isPlayerSelected = Object.values(state.playersByRole)
      .flat()
      .some((p) => p.playerId === player.playerId);

    if (isPlayerSelected) {
      return { canAdd: false, reason: 'Player already selected in team' };
    }

    // withing budget
    const price = player.scoreData?.playerCurrentSalary;
    if (price > state.remainingBudget) {
      return { canAdd: false, reason: 'Insufficient budget' };
    }

    return { canAdd: true };
  };

  const canAddPlayer = (
    player: FootballPlayer,
    role: keyof footballPlayersByRole,
  ) => {
    // Check if player is already selected in any role
    const isPlayerSelected = Object.values(state.playersByRole)
      .flat()
      .some((p) => p.playerId === player.playerId);

    if (isPlayerSelected) {
      return { canAdd: false, reason: 'Player already selected in team' };
    }

    // Check total players limit
    if (getTotalPlayers(state.playersByRole) >= 22) {
      return { canAdd: false, reason: 'Team is full with 22 players' };
    }

    // // Check role-specific limit
    if (state.playersByRole[role].length >= state.playerByRoleLimit[role]) {
      return {
        canAdd: false,
        reason: `Maximum ${state.playerByRoleLimit[role]} ${role} player${state.playerByRoleLimit[role] > 1 ? 's' : ''
          } allowed`,
      };
    }

    // // Check budget
    const price = player.scoreData?.playerCurrentSalary;
    if (price > state.remainingBudget) {
      setApiMessage('error', 'Insufficient budget');
      return { canAdd: false, reason: 'Insufficient budget' };
    }

    return { canAdd: true };
  };

  const addPlayer = (
    player: FootballPlayer,
    role: keyof footballPlayersByRole,
  ) => {
    const { canAdd, reason } = canAddPlayer(player, role);
    if (!canAdd) {
      setApiMessage('error', reason ?? '');
      return;
    }
    dispatch({ type: 'ADD_PLAYER', payload: { player, role } });
  };

  const removePlayer = (
    playerId: number,
    role: keyof footballPlayersByRole,
  ) => {
    dispatch({ type: 'REMOVE_PLAYER', payload: { playerId, role } });
  };

  const setPlayerRoleToCaptain = (
    playerId: number,
    role: keyof footballPlayersByRole,
  ) => {
    dispatch({ type: 'SET_CAPTAIN', payload: { playerId, role } });
  };

  const setPlayerRoleToViceCaiptain = (
    playerId: number,
    role: keyof footballPlayersByRole,
  ) => {
    dispatch({ type: 'SET_VICE_CAPTAIN', payload: { playerId, role } });
  };

  const setBudget = (amount: number) => {
    dispatch({ type: 'SET_TOTAL_BALANCE', payload: { amount } });
  };

  const clearTeam = () => {
    dispatch({ type: 'CLEAR_TEAM' });
  };

  const createLuckyTeam = (payload: LuckyCreateTeamPayload) => {
    dispatch({ type: 'CREATE_LUCKY_TEAM', payload });
  };

  const createDreamTeam = (payload: {
    sportId: string | null;
    eventId: string | null;
    tournamentId: string | null;
    eventName: string | null;
    competitionId: string | null;
    name: string;
    coins?: number | null;
    bonusCoins?: number | null;
  }) => {
    dispatch({ type: 'CREATE_DREAM_TEAM', payload });
  };

  const getDreamTeam = (
    fantasyTeamResponse: FantasyTeamResponse,
    playerId?: number,
    role?: keyof footballPlayersByRole,
  ) => {
    dispatch({
      type: 'GET_DREAM_TEAM',
      payload: { fantasyTeamResponse, playerId, role },
    });
  };

  const createFavouriteTeam = (payload: {
    favoriteTeam: AFLFavoriteTeam;
    playersByRole: footballPlayersByRole;
  }) => {
    dispatch({
      type: 'CREATE_FAVOURITE_TEAM',
      payload,
    });
  };

  const createAFLExpertTeam = (
    payload: AFLDreamTeamPlayer[],
    playerByRole: footballPlayersByRole,
  ) => {
    dispatch({
      type: 'CREATE_AFL_EXPERT_TEAM',
      payload: { dreamPlayers: payload, playerByRole },
    });
  };

  // Reserve player functions
  const addReservePlayer = (player: FootballPlayer, position: number) => {
    const { canAdd, reason } = canAddReservePlayer(player);
    if (!canAdd) {
      setApiMessage('error', reason ?? '');
      return;
    }

    dispatch({ type: 'ADD_RESERVE_PLAYER', payload: { player, position } });
  };

  const removeReservePlayer = (playerId: number, position: number) => {
    dispatch({ type: 'REMOVE_RESERVE_PLAYER', payload: { playerId, position } });
    setActivePlayerPosition(position);
  };

  const createReservePlayerPayload = (reservePlayerPayload: ReservePlayerPayload[]) => {
    dispatch({ type: 'CREATE_RESERVE_PLAYER_PAYLOAD', payload: { reservePlayerPayload } });
  };

  const footballTeamContextValue: footballTeamContextType = useMemo(
    (): footballTeamContextType => ({
      state,
      activeTabPlayer,
      setActiveTabPlayer,
      addPlayer,
      removePlayer,
      setPlayerRoleToCaptain,
      setPlayerRoleToViceCaiptain,
      clearTeam,
      setShowPlayerTabel,
      showPlayerTabel,
      setBudget,
      createLuckyTeam,
      createDreamTeam,
      getDreamTeam,
      createFavouriteTeam,
      createAFLExpertTeam,
      // Reserve player functionality
      openReserveModal,
      setOpenReserveModal,
      activePlayerPosition,
      setActivePlayerPosition,
      addReservePlayer,
      removeReservePlayer,
      createReservePlayerPayload,
      dispatch,
    }),
    [
      state,
      activeTabPlayer,
      setActiveTabPlayer,
      addPlayer,
      removePlayer,
      setPlayerRoleToCaptain,
      setPlayerRoleToViceCaiptain,
      clearTeam,
      setShowPlayerTabel,
      showPlayerTabel,
      setBudget,
      createLuckyTeam,
      createDreamTeam,
      getDreamTeam,
      createFavouriteTeam,
      createAFLExpertTeam,
      // Reserve player functionality
      openReserveModal,
      setOpenReserveModal,
      activePlayerPosition,
      setActivePlayerPosition,
      addReservePlayer,
      removeReservePlayer,
      createReservePlayerPayload,
      dispatch,
    ],
  );

  return (
    <footballTeamContext.Provider value={footballTeamContextValue}>
      {children}
    </footballTeamContext.Provider>
  );
};

export const useFootballContext = () => {
  const context = useContext(footballTeamContext);
  if (context === undefined) {
    throw new Error(
      'useFootballContext must be used within a FootballProvider',
    );
  }
  return context;
};

export default FootballProvider;
