import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@material-tailwind/react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { Config } from '@/helpers/context/config';
import { useUserProfileContext } from '@/helpers/context/userContext';
import useScreen from '@/hooks/useScreen';
import { LocalStorage } from '@/lib/utils';

const ProfileIconMenu = () => {
  const router = useRouter();
  const { width } = useScreen();
  const [openMenu, setOpenMenu] = useState(false);
  const user = useUserProfileContext();
  const [imgSrc, setImgSrc] = useState(
    user?.user?.Media?.filePath
      ? `${Config?.mediaURL}${user?.user?.Media?.filePath}`
      : '/fantasy/images/user/fallBackUser.svg', // Default to placeholder
  );

  useEffect(() => {
    if (user?.user?.Media?.filePath) {
      setImgSrc(`${Config?.mediaURL}${user?.user?.Media?.filePath}`);
    }
  }, [user]);

  const handelLogout = async () => {
    try {
      const res = await fetch('/fantasy/api/auth/logout', { method: 'POST' });
      const data = await res.json();
      if (data.success) {
        LocalStorage.removeItem('auth_token');
        LocalStorage.removeItem('event_id');
        LocalStorage.removeItem('event_type');
        LocalStorage.removeItem('cricket_reserve_players');
        window.location.href = '/fantasy';
      }
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  return (
    <div>
      <Menu
        open={openMenu}
        handler={setOpenMenu}
        allowHover
        placement="bottom-end"
      >
        <MenuHandler>
          {/* @ts-expect-error */}
          <Button
            variant="text"
            className="flex items-center flex-col p-0 text-base font-normal capitalize tracking-normal hover:bg-transparent"
          >
            {width > 799 ? (
              <Image
                src={imgSrc}
                height={40}
                width={40}
                style={{
                  width: '40px',
                  height: '40px',
                  objectFit: 'cover',
                }}
                alt="profile"
                className="rounded-full border-[#FC4714] border-2"
                unoptimized={true}
              />
            ) : (
              <Image
                src={imgSrc}
                height={30}
                width={30}
                style={{
                  width: '30px',
                  height: '30px',
                  objectFit: 'cover',
                }}
                alt="profile"
                className="rounded-full border-[#FC4714] border-2"
                unoptimized={true}
              />
            )}
            <p className="text-[11.42px] leading-[14px] text-secondary-100 font-semibold">
              {user?.user?.firstName}
            </p>
          </Button>
        </MenuHandler>
        {/* @ts-expect-error */}
        <MenuList className="rounded-lg bg-white shadow-[0px_1px_9px_0px_#0000002e] px-0 py-[9px]">
          <MenuItem
            {...({} as any)}
            className="py-[9px] px-[18px] text-[16px] leading-[19px] font-inter font-normal text-primary-200"
            onClick={() => {
              router.push(Config.siteBaseURL + 'profile');
            }}
          >
            My Account
          </MenuItem>
          <MenuItem
            {...({} as any)}
            className="py-[9px] px-[18px] text-[16px] leading-[19px] font-inter font-normal text-primary-200"
            onClick={() => {
              router.push(Config.siteBaseURL + 'blackbook');
            }}
          >
            My BlackBook
          </MenuItem>
          <MenuItem
            {...({} as any)}
            className="py-[9px] px-[18px] text-[16px] leading-[19px] font-inter font-normal text-primary-200"
            onClick={() => {
              router.push(Config.siteBaseURL + 'smartbook');
            }}
          >
            My SmartBook
          </MenuItem>
          <hr />
          <MenuItem
            {...({} as any)}
            className="py-[9px] px-[18px] text-[16px] leading-[19px] font-inter font-normal text-primary-200"
            onClick={handelLogout}
          >
            Log out
          </MenuItem>
        </MenuList>
      </Menu>
    </div>
  );
};

export default ProfileIconMenu;
