import React, {
  createContext,
  useContext,
  useReducer,
  useMemo,
  useState,
  Dispatch,
  SetStateAction,
  useEffect,
} from 'react';
import {
  CreateSoccerTeamPayload,
  SoccerPlayerData,
} from '../../../../types/competitions';
import { LocalStorage } from '@/lib/utils';

import { useCompetition } from '../competitionContext';
import { setApiMessage } from '@/helpers/commonFunctions';

// Types
export type SoccerPlayerRole = 'GK' | 'DEF' | 'MID' | 'FWD';
export type SoccerAPIPlayerRole = 'G' | 'D' | 'M' | 'F';
export interface SoccerAPIPlayer {
  id: string;
  name: string;
  team: string;
  teamName: string;
  role: SoccerAPIPlayerRole;
  price: number;
  points: number;
  selected: boolean;
  number?: number; // Player's jersey number (optional)
  image?: string;
  isCaptain?: boolean;
  isViceCaptain?: boolean;
  playerValue?: number;
  squadsId: number;
  tournamentId: number;
  playerId: number;
  scoreData: {
    lastScore: number;
    totalScore: number;
    lastThreeMatch: number;
    lastFiveMatch: number;
    playerCurrentSalary: number;
    playerLastSalary: number;
    avg: number;
    livePoint?: number;
    totalPlayed: number;
    sel?: number;
  };
}

export interface SoccerPlayer {
  id: string;
  name: string;
  team: string;
  teamName: string;
  role: SoccerPlayerRole;
  price: number;
  points: number;
  selected: boolean;
  number?: number; // Player's jersey number (optional)
  image?: string;
  isCaptain?: boolean;
  isViceCaptain?: boolean;
  playerValue?: number;
  squadsId: number;
  tournamentId: number;
  playerId: number;
  scoreData: {
    lastScore: number;
    totalScore: number;
    lastThreeMatch: number;
    lastFiveMatch: number;
    playerCurrentSalary: number;
    playerLastSalary: number;
    avg: number;
    livePoint?: number;
    totalPlayed: number;
    sel?: number;
  };
}

export interface SoccerPlayersByRole {
  GK: SoccerPlayer[];
  DEF: SoccerPlayer[];
  MID: SoccerPlayer[];
  FWD: SoccerPlayer[];
}

interface SoccerTeamState {
  selectedPlayers: SoccerPlayer[];
  availablePlayers: SoccerPlayer[];
  createDreamTeamPayload?: CreateSoccerTeamPayload;
  remainingBudget: number;
  totalBudget: number;
  playerByRoleLimit: {
    GK: { min: number; max: number };
    DEF: { min: number; max: number };
    MID: { min: number; max: number };
    FWD: { min: number; max: number };
  };
  lastEntry: {
    mode: 'MANUAL' | 'LUCKY' | 'DREAM' | 'FAVORITE' | 'EXPERT';
    players: SoccerPlayer[];
  };
}

type SoccerTeamAction =
  | {
    type: 'SELECT_PLAYER';
    payload: { player: SoccerPlayer; role: SoccerPlayerRole };
  }
  | {
    type: 'REMOVE_PLAYER';
    payload: { playerId: number; role: SoccerPlayerRole };
  }
  | {
    type: 'CLEAR_TEAM';
    payload: {
      eventId: string;
      eventName: string;
      sportId: string;
      tournamentId: string;
      competitionId: string;
      teamName: string;
    };
  }
  | { type: 'SET_AVAILABLE_PLAYERS'; payload: SoccerPlayer[] }
  | {
    type: 'SET_CAPTAIN';
    payload: { playerId: number; role: SoccerPlayerRole };
  }
  | {
    type: 'SET_VICE_CAPTAIN';
    payload: { playerId: number; role: SoccerPlayerRole };
  }
  | { type: 'SET_TOTAL_BALANCE'; payload: { amount: number } }
  | {
    type: 'CREATE_LUCKY_TEAM';
    payload: {
      playersByRole: Record<SoccerPlayerRole, SoccerPlayer[]>;
      eventId: string;
      eventName: string;
      sportId: string;
      tournamentId: string;
      competitionId: string;
      name: string;
    };
  }
  | {
    type: 'CREATE_FAVORITE_TEAM';
    payload: {
      favoriteTeam: SoccerFavoriteTeam;
      players: SoccerPlayer[];
      eventId: string;
      eventName: string;
      sportId: string;
      tournamentId: string;
      competitionId: string;
      name: string;
    };
  }
  | {
    type: 'CREATE_DREAM_TEAM';
    payload: {
      eventId: string;
      eventName: string;
      sportId: string;
      tournamentId: string;
      competitionId: string;
      name: string;
    };
  }
  | {
    type: 'GET_DREAM_TEAM';
    payload: {
      fantasyTeamResponse: any;
      playerId?: number;
      role?: SoccerPlayerRole;
    };
  }
  | {
    type: 'SET_PLAYER_LIMITS';
    payload: {
      playerByRoleLimit: {
        GK: { min: number; max: number };
        DEF: { min: number; max: number };
        MID: { min: number; max: number };
        FWD: { min: number; max: number };
      };
    };
  }
  | {
    type: 'CREATE_EXPERT_TEAM';
    payload: {
      expertTeam: SoccerExpertTeam[];
      players: SoccerPlayer[];
      eventId: string;
      eventName: string;
      sportId: string;
      tournamentId: string;
      competitionId: string;
      name: string;
    };
  };

const initialState: SoccerTeamState = {
  selectedPlayers: [],
  availablePlayers: [],
  remainingBudget: 0,
  totalBudget: 0,
  playerByRoleLimit: {
    GK: { min: 0, max: 0 },
    DEF: { min: 0, max: 0 },
    MID: { min: 0, max: 0 },
    FWD: { min: 0, max: 0 },
  },
  lastEntry: {
    mode: 'MANUAL',
    players: [],
  },
};

// Add validation function
const canAddPlayer = (
  state: SoccerTeamState,
  player: SoccerPlayer,
  role: SoccerPlayerRole,
): { canAdd: boolean; reason?: string } => {
  const MAX_PLAYERS_ALLOWED = 11;
  const { min, max } = state.playerByRoleLimit[role];

  // Get current counts for each role
  const currentCount = state.selectedPlayers.filter(
    (p) => p.role === role,
  ).length;
  const totalSelected = state.selectedPlayers.length;

  const selectedGK = state.selectedPlayers.filter(
    (p) => p.role === 'GK',
  ).length;
  const selectedDEF = state.selectedPlayers.filter(
    (p) => p.role === 'DEF',
  ).length;
  const selectedMID = state.selectedPlayers.filter(
    (p) => p.role === 'MID',
  ).length;
  const selectedFWD = state.selectedPlayers.filter(
    (p) => p.role === 'FWD',
  ).length;

  // Check if player is already selected
  const isPlayerSelected = state.selectedPlayers.some(
    (p) => p.playerId === player.playerId,
  );
  if (isPlayerSelected) {
    return { canAdd: false, reason: 'Player already selected in team' };
  }

  // Check role limits
  if (currentCount >= max) {
    return {
      canAdd: false,
      reason: `Maximum ${max} ${role} player${max > 1 ? 's' : ''} allowed`,
    };
  }

  // Check total players limit
  if (totalSelected >= MAX_PLAYERS_ALLOWED) {
    return { canAdd: false, reason: 'Team is full with 11 players' };
  }

  // Check budget
  const price = player.scoreData?.playerCurrentSalary || player.price;
  if (price > state.remainingBudget) {
    setApiMessage('error', 'Insufficient budget');
    return { canAdd: false, reason: 'Insufficient budget' };
  }

  // Special case for goalkeeper when team is almost full
  if (selectedGK < 1 && totalSelected === 10 && role === 'GK') {
    return { canAdd: true };
  }

  // Validate team composition rules
  if (
    (selectedDEF === 5 && role === 'MID' && currentCount >= 4) ||
    (selectedFWD === 0 &&
      role === 'MID' &&
      currentCount >= 4 &&
      selectedDEF === 5) ||
    (selectedMID === 5 && selectedFWD === 0 && role !== 'FWD') ||
    ((selectedMID < 3 || selectedDEF < 3) &&
      role === 'FWD' &&
      selectedFWD >= 1) ||
    (selectedGK < 1 && totalSelected === 10)
  ) {
    return { canAdd: false, reason: 'Invalid team composition' };
  }

  return { canAdd: true };
};

// Add reset utility functions
const resetCaptainStatus = (players: SoccerPlayer[]): SoccerPlayer[] => {
  return players.map((player) => {
    if (player.isCaptain) {
      return { ...player, isCaptain: false };
    }
    return player;
  });
};

const resetViceCaptainStatus = (players: SoccerPlayer[]): SoccerPlayer[] => {
  return players.map((player) => {
    if (player.isViceCaptain) {
      return { ...player, isViceCaptain: false };
    }
    return player;
  });
};

// Add these utility functions before the reducer
const resetCaptainAndViceCaptain = (
  players: SoccerPlayer[],
): SoccerPlayer[] => {
  return players.map((player) => ({
    ...player,
    isCaptain: false,
    isViceCaptain: false,
  }));
};

const findSoccerCaptainViceCaptain = (
  players: SoccerPlayer[],
): { captain?: SoccerPlayer; viceCaptain?: SoccerPlayer } => {
  const captain = players.find((player) => player.isCaptain);
  const viceCaptain = players.find((player) => player.isViceCaptain);
  return { captain, viceCaptain };
};

const generateSoccerTeamPayload = (
  players: SoccerPlayer[],
  captain?: SoccerPlayer,
  viceCaptain?: SoccerPlayer,
): SoccerPlayerData => {
  const playerData: SoccerPlayerData = {
    GKP: [],
    DEF: [],
    MID: [],
    FWD: [],
    captain: captain
      ? [
        {
          playerId: captain.playerId,
          playerValue: captain.scoreData.playerCurrentSalary,
        },
      ]
      : [],
    viceCaptain: viceCaptain
      ? [
        {
          playerId: viceCaptain.playerId,
          playerValue: viceCaptain.scoreData.playerCurrentSalary,
        },
      ]
      : [],
  };

  players.forEach((player) => {
    const playerPayload = {
      playerId: player.playerId,
      playerValue: player.scoreData.playerCurrentSalary,
    };

    switch (player.role) {
      case 'GK':
        playerData.GKP.push(playerPayload);
        break;
      case 'DEF':
        playerData.DEF.push(playerPayload);
        break;
      case 'MID':
        playerData.MID.push(playerPayload);
        break;
      case 'FWD':
        playerData.FWD.push(playerPayload);
        break;
    }
  });

  return playerData;
};

// Add utility functions for lucky team generation
const generateRandomSoccerTeam = (
  playersByRole: Record<SoccerPlayerRole, SoccerPlayer[]>,
  teamComposition: Record<SoccerPlayerRole, { min: number; max: number }>,
): Record<SoccerPlayerRole, SoccerPlayer[]> => {
  const selectedPlayers: Record<SoccerPlayerRole, SoccerPlayer[]> = {
    GK: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  // Helper function to get random players from a role
  const getRandomPlayers = (
    players: SoccerPlayer[],
    count: number,
  ): SoccerPlayer[] => {
    const shuffled = [...players].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  };

  // First, ensure we have exactly 1 goalkeeper
  if (playersByRole.GK.length > 0) {
    selectedPlayers.GK = getRandomPlayers(playersByRole.GK, 1);
  }

  // Then select defenders (3-5)
  const defCount = Math.floor(Math.random() * 3) + 3; // Random between 3-5
  if (playersByRole.DEF.length >= defCount) {
    selectedPlayers.DEF = getRandomPlayers(playersByRole.DEF, defCount);
  }

  // Then select midfielders (3-5)
  const midCount = Math.floor(Math.random() * 3) + 3; // Random between 3-5
  if (playersByRole.MID.length >= midCount) {
    selectedPlayers.MID = getRandomPlayers(playersByRole.MID, midCount);
  }

  // Calculate remaining spots for forwards
  const remainingSpots = 11 - (1 + defCount + midCount);

  // Ensure at least one forward is selected but never more than 3
  const fwdCount = Math.min(3, Math.max(1, remainingSpots));
  if (playersByRole.FWD.length >= fwdCount) {
    selectedPlayers.FWD = getRandomPlayers(playersByRole.FWD, fwdCount);
  }

  // If we have more than 11 players, adjust the team composition
  const totalPlayers = Object.values(selectedPlayers).flat().length;
  if (totalPlayers > 11) {
    // Remove excess players while maintaining at least one forward
    const excessPlayers = totalPlayers - 11;
    let removedCount = 0;

    // Remove players in this order: MID, DEF (never remove GK or FWD)
    const rolesToRemove: SoccerPlayerRole[] = ['MID', 'DEF'];
    for (const role of rolesToRemove) {
      while (
        playersByRole[role].length > teamComposition[role].min &&
        removedCount < excessPlayers
      ) {
        selectedPlayers[role].pop();
        removedCount++;
      }
      if (removedCount >= excessPlayers) break;
    }
  }

  // Assign captain and vice-captain
  const allPlayers = Object.values(selectedPlayers).flat();
  if (allPlayers.length > 0) {
    // Assign captain (preferably from MID or FWD)
    const potentialCaptains = allPlayers.filter(
      (p) => p.role === 'MID' || p.role === 'FWD',
    );
    const captain =
      potentialCaptains.length > 0
        ? potentialCaptains[
        Math.floor(Math.random() * potentialCaptains.length)
        ]
        : allPlayers[Math.floor(Math.random() * allPlayers.length)];

    // Assign vice-captain (different from captain)
    const potentialViceCaptains = allPlayers.filter((p) => p !== captain);
    const viceCaptain =
      potentialViceCaptains[
      Math.floor(Math.random() * potentialViceCaptains.length)
      ];

    // Update the players with captain/vice-captain status
    allPlayers.forEach((player) => {
      if (player === captain) {
        player.isCaptain = true;
        player.isViceCaptain = false;
      } else if (player === viceCaptain) {
        player.isCaptain = false;
        player.isViceCaptain = true;
      } else {
        player.isCaptain = false;
        player.isViceCaptain = false;
      }
    });
  }

  return selectedPlayers;
};

const addSoccerPlayersWithinBudget = (
  playersByRole: Record<SoccerPlayerRole, SoccerPlayer[]>,
  remainingBudget: number,
): Record<SoccerPlayerRole, SoccerPlayer[]> => {
  let totalCost = 0;
  const validPlayers: Record<SoccerPlayerRole, SoccerPlayer[]> = {
    GK: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  // Helper function to calculate total cost of players
  const calculateTotalCost = (players: SoccerPlayer[]): number => {
    return players.reduce(
      (sum, player) =>
        sum + (player.scoreData?.playerCurrentSalary || player.price),
      0,
    );
  };

  // Calculate total cost of all players first
  const allPlayersCost = Object.values(playersByRole).reduce(
    (sum, players) => sum + calculateTotalCost(players),
    0,
  );

  // Check if total cost exceeds budget before processing
  if (allPlayersCost > remainingBudget) {
    setApiMessage('error', 'Insufficient budget');
    // Still process players to include as many as possible within budget
  }

  // Process each role
  for (const role of Object.keys(playersByRole) as SoccerPlayerRole[]) {
    const players = playersByRole[role];
    const roleCost = calculateTotalCost(players);

    if (totalCost + roleCost <= remainingBudget) {
      totalCost += roleCost;
      validPlayers[role] = players;
    } else {
      // Try to add players individually within budget
      const affordablePlayers = [];
      for (const player of players) {
        const playerCost =
          player.scoreData?.playerCurrentSalary || player.price;
        if (totalCost + playerCost <= remainingBudget) {
          affordablePlayers.push(player);
          totalCost += playerCost;
        }
      }
      validPlayers[role] = affordablePlayers;
    }
  }

  return validPlayers;
};

// Add these utility functions before the reducer
const generateSoccerFavoriteTeamPayload = (
  favoriteTeam: SoccerFavoriteTeam,
  playerByRoleLimit: SoccerTeamState['playerByRoleLimit'],
  availablePlayers: SoccerPlayer[],
): Record<SoccerPlayerRole, SoccerPlayer[]> => {
  const playersByRole: Record<SoccerPlayerRole, SoccerPlayer[]> = {
    GK: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  // Helper function to find player by ID
  const findPlayerById = (playerId: number): SoccerPlayer | undefined => {
    return availablePlayers.find((p) => p.playerId === playerId);
  };

  // Helper function to check if we've reached the total player limit
  const hasReachedTotalLimit = () => {
    return Object.values(playersByRole).flat().length >= 11;
  };

  // First, handle captain and vice-captain
  let captainPlayer: SoccerPlayer | undefined;
  let viceCaptainPlayer: SoccerPlayer | undefined;

  // Find and validate captain
  if (favoriteTeam.captain) {
    captainPlayer = findPlayerById(favoriteTeam.captain[0].playerId);
    if (captainPlayer) {
      const role = captainPlayer.role;
      if (playersByRole[role].length < playerByRoleLimit[role].max) {
        playersByRole[role].push({ ...captainPlayer, isCaptain: true });
      }
    }
  }

  // Find and validate vice-captain
  if (favoriteTeam.viceCaptain) {
    viceCaptainPlayer = findPlayerById(favoriteTeam.viceCaptain[0].playerId);
    if (viceCaptainPlayer) {
      const role = viceCaptainPlayer.role;
      if (playersByRole[role].length < playerByRoleLimit[role].max) {
        playersByRole[role].push({ ...viceCaptainPlayer, isViceCaptain: true });
      }
    }
  }

  // Now process the rest of the team
  if (favoriteTeam.GKP) {
    for (const player of favoriteTeam.GKP) {
      if (hasReachedTotalLimit()) break;
      const foundPlayer = findPlayerById(player.playerId);
      if (foundPlayer && playersByRole.GK.length < playerByRoleLimit.GK.max) {
        // Skip if this player is already added as captain or vice-captain
        if (
          foundPlayer.playerId !== captainPlayer?.playerId &&
          foundPlayer.playerId !== viceCaptainPlayer?.playerId
        ) {
          playersByRole.GK.push(foundPlayer);
        }
      }
    }
  }

  if (favoriteTeam.DEF) {
    for (const player of favoriteTeam.DEF) {
      if (hasReachedTotalLimit()) break;
      const foundPlayer = findPlayerById(player.playerId);
      if (foundPlayer && playersByRole.DEF.length < playerByRoleLimit.DEF.max) {
        // Skip if this player is already added as captain or vice-captain
        if (
          foundPlayer.playerId !== captainPlayer?.playerId &&
          foundPlayer.playerId !== viceCaptainPlayer?.playerId
        ) {
          playersByRole.DEF.push(foundPlayer);
        }
      }
    }
  }

  if (favoriteTeam.MID) {
    for (const player of favoriteTeam.MID) {
      if (hasReachedTotalLimit()) break;
      const foundPlayer = findPlayerById(player.playerId);
      if (foundPlayer && playersByRole.MID.length < playerByRoleLimit.MID.max) {
        // Skip if this player is already added as captain or vice-captain
        if (
          foundPlayer.playerId !== captainPlayer?.playerId &&
          foundPlayer.playerId !== viceCaptainPlayer?.playerId
        ) {
          playersByRole.MID.push(foundPlayer);
        }
      }
    }
  }

  if (favoriteTeam.FWD) {
    for (const player of favoriteTeam.FWD) {
      if (hasReachedTotalLimit()) break;
      const foundPlayer = findPlayerById(player.playerId);
      if (foundPlayer && playersByRole.FWD.length < playerByRoleLimit.FWD.max) {
        // Skip if this player is already added as captain or vice-captain
        if (
          foundPlayer.playerId !== captainPlayer?.playerId &&
          foundPlayer.playerId !== viceCaptainPlayer?.playerId
        ) {
          playersByRole.FWD.push(foundPlayer);
        }
      }
    }
  }

  // Ensure minimum players in each position
  const ensureMinimumPlayers = () => {
    // Ensure goalkeeper
    if (playersByRole.GK.length === 0) {
      const availableGK = availablePlayers.filter((p) => p.role === 'GK');
      if (availableGK.length > 0) {
        playersByRole.GK.push(availableGK[0]);
      }
    }

    // Ensure forward
    if (playersByRole.FWD.length === 0) {
      const availableFWD = availablePlayers
        .filter((p) => p.role === 'FWD')
        .filter(
          (p) =>
            !Object.values(playersByRole)
              .flat()
              .some((selected) => selected.playerId === p.playerId),
        );
      if (availableFWD.length > 0) {
        playersByRole.FWD.push(availableFWD[0]);
      }
    }

    // Ensure minimum defenders
    while (playersByRole.DEF.length < playerByRoleLimit.DEF.min) {
      const availableDEF = availablePlayers
        .filter((p) => p.role === 'DEF')
        .filter(
          (p) =>
            !Object.values(playersByRole)
              .flat()
              .some((selected) => selected.playerId === p.playerId),
        );
      if (availableDEF.length > 0) {
        playersByRole.DEF.push(availableDEF[0]);
      } else {
        break;
      }
    }

    // Ensure minimum midfielders
    while (playersByRole.MID.length < playerByRoleLimit.MID.min) {
      const availableMID = availablePlayers
        .filter((p) => p.role === 'MID')
        .filter(
          (p) =>
            !Object.values(playersByRole)
              .flat()
              .some((selected) => selected.playerId === p.playerId),
        );
      if (availableMID.length > 0) {
        playersByRole.MID.push(availableMID[0]);
      } else {
        break;
      }
    }
  };

  // Ensure minimum players in each position
  ensureMinimumPlayers();

  // Validate team composition
  const totalPlayers = Object.values(playersByRole).flat().length;
  if (totalPlayers > 11) {
    // If we have more than 11 players, remove excess players while maintaining role balance
    const excessPlayers = totalPlayers - 11;
    let removedCount = 0;

    // Remove players in this order: MID, DEF (never remove GK, FWD, captain, or vice-captain)
    const rolesToRemove: SoccerPlayerRole[] = ['MID', 'DEF'];
    for (const role of rolesToRemove) {
      while (
        playersByRole[role].length > playerByRoleLimit[role].min &&
        removedCount < excessPlayers
      ) {
        const playerToRemove = playersByRole[role].pop();
        // If we're about to remove captain or vice-captain, skip
        if (
          playerToRemove?.playerId === captainPlayer?.playerId ||
          playerToRemove?.playerId === viceCaptainPlayer?.playerId
        ) {
          continue;
        }
        removedCount++;
      }
      if (removedCount >= excessPlayers) break;
    }
  }

  return playersByRole;
};

// Add this function before the reducer
const calculateRemainingBudget = (
  totalBudget: number,
  players: SoccerPlayer[],
): number => {
  const totalCost = players.reduce(
    (sum, player) => sum + (player.scoreData?.playerCurrentSalary || 0),
    0,
  );
  return totalBudget - totalCost;
};

const soccerTeamReducer = (
  state: SoccerTeamState,
  action: SoccerTeamAction,
): SoccerTeamState => {
  const localSoccerPlayersByRole =
    LocalStorage.getItem<SoccerPlayer[]>('soccer_dream_team');
  switch (action.type) {
    case 'SET_PLAYER_LIMITS': {
      return {
        ...state,
        playerByRoleLimit: action.payload.playerByRoleLimit,
      };
    }
    case 'SELECT_PLAYER': {
      const { player, role } = action.payload;
      const price = player.scoreData?.playerCurrentSalary || 0;

      // Check if player can be added
      const { canAdd, reason } = canAddPlayer(state, player, role);

      if (!canAdd) {
        return state;
      }

      if (state.remainingBudget >= price) {
        const updatedPlayers = [...state.selectedPlayers, player];

        const { captain, viceCaptain } =
          findSoccerCaptainViceCaptain(updatedPlayers);

        // Generate payload for manual team creation
        const playerData = generateSoccerTeamPayload(
          updatedPlayers,
          captain,
          viceCaptain,
        );

        const remainingBudget = calculateRemainingBudget(
          state.totalBudget,
          updatedPlayers,
        );

        return {
          ...state,
          selectedPlayers: updatedPlayers,
          remainingBudget,
          createDreamTeamPayload: {
            ...state.createDreamTeamPayload,
            playerData,
          },
          lastEntry: {
            mode: 'MANUAL',
            players: updatedPlayers,
          },
        };
      }
      return state;
    }

    case 'REMOVE_PLAYER': {
      const { playerId, role } = action.payload;

      const playerToRemove = state.selectedPlayers.find(
        (p) => p.playerId === playerId && p.role === role,
      );

      if (playerToRemove) {
        let updatedPlayers = state.selectedPlayers.filter(
          (p) => p.playerId !== playerId,
        );

        const isPlayerCaptain = playerToRemove?.isCaptain;
        const isPlayerViceCaptain = playerToRemove?.isViceCaptain;

        if (isPlayerCaptain) {
          updatedPlayers = updatedPlayers.map((player) => ({
            ...player,
            isCaptain: false,
          }));
        }

        if (isPlayerViceCaptain) {
          updatedPlayers = updatedPlayers.map((player) => ({
            ...player,
            isViceCaptain: false,
          }));
        }

        const remainingBudget = calculateRemainingBudget(
          state.totalBudget,
          updatedPlayers,
        );
        const { captain, viceCaptain } =
          findSoccerCaptainViceCaptain(updatedPlayers);

        const createDreamTeamPayload = generateSoccerTeamPayload(
          updatedPlayers,
          captain,
          viceCaptain,
        );

        return {
          ...state,
          selectedPlayers: updatedPlayers,
          remainingBudget,
          createDreamTeamPayload: {
            ...createDreamTeamPayload,
            playerData: createDreamTeamPayload,
          },
          lastEntry: {
            mode: 'MANUAL',
            players: updatedPlayers,
          },
        };
      }
      return state;
    }

    case 'CLEAR_TEAM': {
      return state;
    }

    case 'SET_AVAILABLE_PLAYERS':
      return {
        ...state,
        availablePlayers: action.payload,
      };

    case 'SET_CAPTAIN': {
      const { playerId, role } = action.payload;
      let updatedPlayers = resetCaptainStatus(state.selectedPlayers);

      // Set the new captain
      updatedPlayers = updatedPlayers.map((player) => {
        // If player is already captain
        if (player.playerId === playerId && player.isCaptain) {
          return { ...player, isCaptain: false };
        }
        // if player is fresh
        if (
          player.playerId === playerId &&
          !player.isCaptain &&
          !player.isViceCaptain
        ) {
          return { ...player, isCaptain: true };
        }
        // If player is already vice captain and assigning them captain
        if (player.isViceCaptain && player.playerId === playerId) {
          return { ...player, isCaptain: true, isViceCaptain: false };
        }
        return player;
      });

      const { captain, viceCaptain } =
        findSoccerCaptainViceCaptain(updatedPlayers);

      const createDreamTeamPayload = generateSoccerTeamPayload(
        updatedPlayers,
        captain,
        viceCaptain,
      );

      const remainingBudget = calculateRemainingBudget(
        state.totalBudget,
        updatedPlayers,
      );

      return {
        ...state,
        selectedPlayers: updatedPlayers,
        createDreamTeamPayload: {
          ...createDreamTeamPayload,
          playerData: createDreamTeamPayload,
        },
        remainingBudget,
      };
    }

    case 'SET_VICE_CAPTAIN': {
      const { playerId, role } = action.payload;
      let updatedPlayers = resetViceCaptainStatus(state.selectedPlayers);

      // Set the new vice captain
      updatedPlayers = updatedPlayers.map((player) => {
        if (player.isCaptain && player.playerId === playerId) {
          return { ...player, isCaptain: false, isViceCaptain: true };
        }
        if (!player.isViceCaptain && player.playerId === playerId) {
          return { ...player, isViceCaptain: !player.isViceCaptain };
        }
        return player;
      });

      const { captain, viceCaptain } =
        findSoccerCaptainViceCaptain(updatedPlayers);

      const createDreamTeamPayload = generateSoccerTeamPayload(
        updatedPlayers,
        captain,
        viceCaptain,
      );

      const remainingBudget = calculateRemainingBudget(
        state.totalBudget,
        updatedPlayers,
      );

      return {
        ...state,
        selectedPlayers: updatedPlayers,
        createDreamTeamPayload: {
          ...createDreamTeamPayload,
          playerData: createDreamTeamPayload,
        },
        remainingBudget,
      };
    }

    case 'SET_TOTAL_BALANCE':
      return {
        ...state,
        totalBudget: action.payload.amount,
        remainingBudget: action.payload.amount,
      };

    case 'CREATE_LUCKY_TEAM': {
      try {
        const { playersByRole } = action.payload;
        const teamComposition: Record<
          SoccerPlayerRole,
          { min: number; max: number }
        > = {
          GK: state.playerByRoleLimit.GK,
          DEF: state.playerByRoleLimit.DEF,
          MID: state.playerByRoleLimit.MID,
          FWD: state.playerByRoleLimit.FWD,
        };

        // First reset all players' captain/vice-captain status
        const resetPlayersByRole = Object.entries(playersByRole).reduce(
          (acc, [role, players]) => ({
            ...acc,
            [role]: resetCaptainAndViceCaptain(players),
          }),
          {} as Record<SoccerPlayerRole, SoccerPlayer[]>,
        );

        const luckyPlayersByRole = generateRandomSoccerTeam(
          resetPlayersByRole,
          teamComposition,
        );

        const allSelectedPlayers = Object.values(luckyPlayersByRole).flat();
        const { captain, viceCaptain } =
          findSoccerCaptainViceCaptain(allSelectedPlayers);

        const luckyPlayerPayloadData = generateSoccerTeamPayload(
          allSelectedPlayers,
          captain,
          viceCaptain,
        );

        const validLuckyPlayersByRole = addSoccerPlayersWithinBudget(
          luckyPlayersByRole,
          state.totalBudget,
        );

        const {
          eventId: luckyEventId,
          eventName: luckyEventName,
          sportId: luckySportId,
          tournamentId: luckyTournamentId,
          competitionId: luckyCompetitionId,
          name: luckyTeamName,
        } = action.payload;

        const createLuckyDreamTeamPayload: CreateSoccerTeamPayload = {
          playerData: luckyPlayerPayloadData,
          eventId: luckyEventId,
          eventName: luckyEventName,
          sportId: luckySportId,
          tournamentId: luckyTournamentId,
          competitionId: luckyCompetitionId,
          name: luckyTeamName,
        };

        const allValidPlayers = Object.values(validLuckyPlayersByRole).flat();
        const remainingBudget = calculateRemainingBudget(
          state.totalBudget,
          allValidPlayers,
        );

        return {
          ...state,
          selectedPlayers: allValidPlayers,
          createDreamTeamPayload: createLuckyDreamTeamPayload,
          remainingBudget,
          lastEntry: {
            ...state.lastEntry,
            mode: 'LUCKY',
          },
        };
      } catch (error) {
        console.error('Error creating lucky team:', error);
        return state;
      }
    }

    case 'CREATE_FAVORITE_TEAM': {
      const {
        favoriteTeam,
        players,
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
      } = action.payload;

      // Reset all players' captain/vice-captain status before creating favorite team
      const resetPlayers = resetCaptainAndViceCaptain(players);

      const newPlayersByRole = generateSoccerFavoriteTeamPayload(
        favoriteTeam,
        state.playerByRoleLimit,
        resetPlayers,
      );

      const validPlayersByRole = addSoccerPlayersWithinBudget(
        newPlayersByRole,
        state.remainingBudget,
      );

      // Flatten the players into a single array
      const allPlayers = Object.values(validPlayersByRole).flat();

      // Calculate total cost
      const totalCost = allPlayers.reduce(
        (sum, player) =>
          sum + (player.scoreData?.playerCurrentSalary || player.price),
        0,
      );

      // Find captain and vice-captain
      const { captain, viceCaptain } = findSoccerCaptainViceCaptain(allPlayers);

      // Generate payload for favorite team
      const playerData = generateSoccerTeamPayload(
        allPlayers,
        captain,
        viceCaptain,
      );

      return {
        ...state,
        selectedPlayers: allPlayers,
        remainingBudget: state.totalBudget - totalCost,
        createDreamTeamPayload: {
          ...state.createDreamTeamPayload,
          playerData,
          eventId,
          eventName,
          sportId,
          tournamentId,
          competitionId,
          name,
        },
        lastEntry: {
          mode: 'FAVORITE',
          players: allPlayers,
        },
      };
    }

    case 'CREATE_DREAM_TEAM': {
      const { eventId, eventName, sportId, tournamentId, competitionId, name } =
        action.payload;

      const { captain, viceCaptain } = findSoccerCaptainViceCaptain(
        state.selectedPlayers,
      );
      const flattenedValidPlayers = state.selectedPlayers;
      return {
        ...state,
        selectedPlayers: flattenedValidPlayers,
        createDreamTeamPayload: {
          eventId,
          eventName,
          sportId,
          tournamentId,
          competitionId,
          name,
          playerData: generateSoccerTeamPayload(
            flattenedValidPlayers,
            captain,
            viceCaptain,
          ),
        },
        remainingBudget:
          state.totalBudget -
          flattenedValidPlayers.reduce(
            (sum: number, player: SoccerPlayer) =>
              sum + player.scoreData.playerCurrentSalary,
            0,
          ),
        lastEntry: {
          ...state.lastEntry,
          mode: 'MANUAL',
        },
      };
    }

    case 'GET_DREAM_TEAM': {
      const { fantasyTeamResponse, playerId, role } = action.payload;
      const dreamPlayersByRole = fantasyTeamResponse.result;

      const newDreamPlayers: SoccerPlayer[] = [];

      // Process each role from the dream team response
      if (dreamPlayersByRole?.GKP) {
        newDreamPlayers.push(
          ...dreamPlayersByRole.GKP.map((player: any) => ({
            id: player.id.toString(),
            name: player.name,
            team: player.teamName,
            teamName: player.teamName,
            role: 'GK' as SoccerPlayerRole,
            price: player.playerValue,
            points: player.scoreData?.totalPoint || 0,
            selected: true,
            number: undefined,
            image: player.image,
            isCaptain:
              player.playerId === dreamPlayersByRole.captain?.[0]?.playerId,
            isViceCaptain:
              player.playerId === dreamPlayersByRole.viceCaptain?.[0]?.playerId,
            playerValue: player.playerValue,
            squadsId: player.squadsId,
            tournamentId: player.tournamentId,
            playerId: player.playerId,
            scoreData: {
              lastScore: player.scoreData?.lastScore || 0,
              totalScore: player.scoreData?.totalScore || 0,
              lastThreeMatch: player.scoreData?.lastThreeMatch || 0,
              lastFiveMatch: 0,
              playerCurrentSalary: player.scoreData?.playerCurrentSalary || 0,
              playerLastSalary: player.scoreData?.playerLastSalary || 0,
              avg: player.scoreData?.avg || 0,
              livePoint: player.scoreData?.livePoint || 0,
              totalPlayed: player.scoreData?.totalPlayed || 0,
              totalPoint: player.scoreData?.totalPoint || 0,
            },
          })),
        );
      }

      if (dreamPlayersByRole?.DEF) {
        newDreamPlayers.push(
          ...dreamPlayersByRole.DEF.map((player: any) => ({
            id: player.id.toString(),
            name: player.name,
            team: player.teamName,
            teamName: player.teamName,
            role: 'DEF' as SoccerPlayerRole,
            price: player.playerValue,
            points: player.scoreData?.totalPoint || 0,
            selected: true,
            number: undefined,
            image: player.image,
            isCaptain:
              player.playerId === dreamPlayersByRole.captain?.[0]?.playerId,
            isViceCaptain:
              player.playerId === dreamPlayersByRole.viceCaptain?.[0]?.playerId,
            playerValue: player.playerValue,
            squadsId: player.squadsId,
            tournamentId: player.tournamentId,
            playerId: player.playerId,
            scoreData: {
              lastScore: player.scoreData?.lastScore || 0,
              totalScore: player.scoreData?.totalScore || 0,
              lastThreeMatch: player.scoreData?.lastThreeMatch || 0,
              lastFiveMatch: 0,
              playerCurrentSalary: player.scoreData?.playerCurrentSalary || 0,
              playerLastSalary: player.scoreData?.playerLastSalary || 0,
              avg: player.scoreData?.avg || 0,
              livePoint: player.scoreData?.livePoint || 0,
              totalPlayed: player.scoreData?.totalPlayed || 0,
              totalPoint: player.scoreData?.totalPoint || 0,
            },
          })),
        );
      }

      if (dreamPlayersByRole?.MID) {
        newDreamPlayers.push(
          ...dreamPlayersByRole.MID.map((player: any) => ({
            id: player.id.toString(),
            name: player.name,
            team: player.teamName,
            teamName: player.teamName,
            role: 'MID' as SoccerPlayerRole,
            price: player.playerValue,
            points: player.scoreData?.totalPoint || 0,
            selected: true,
            number: undefined,
            image: player.image,
            isCaptain:
              player.playerId === dreamPlayersByRole.captain?.[0]?.playerId,
            isViceCaptain:
              player.playerId === dreamPlayersByRole.viceCaptain?.[0]?.playerId,
            playerValue: player.playerValue,
            squadsId: player.squadsId,
            tournamentId: player.tournamentId,
            playerId: player.playerId,
            scoreData: {
              lastScore: player.scoreData?.lastScore || 0,
              totalScore: player.scoreData?.totalScore || 0,
              lastThreeMatch: player.scoreData?.lastThreeMatch || 0,
              lastFiveMatch: 0,
              playerCurrentSalary: player.scoreData?.playerCurrentSalary || 0,
              playerLastSalary: player.scoreData?.playerLastSalary || 0,
              avg: player.scoreData?.avg || 0,
              livePoint: player.scoreData?.livePoint || 0,
              totalPlayed: player.scoreData?.totalPlayed || 0,
              totalPoint: player.scoreData?.totalPoint || 0,
            },
          })),
        );
      }

      if (dreamPlayersByRole?.FWD) {
        newDreamPlayers.push(
          ...dreamPlayersByRole.FWD.map((player: any) => ({
            id: player.id.toString(),
            name: player.name,
            team: player.teamName,
            teamName: player.teamName,
            role: 'FWD' as SoccerPlayerRole,
            price: player.playerValue,
            points: player.scoreData?.totalPoint || 0,
            selected: true,
            number: undefined,
            image: player.image,
            isCaptain:
              player.playerId === dreamPlayersByRole.captain?.[0]?.playerId,
            isViceCaptain:
              player.playerId === dreamPlayersByRole.viceCaptain?.[0]?.playerId,
            playerValue: player.playerValue,
            squadsId: player.squadsId,
            tournamentId: player.tournamentId,
            playerId: player.playerId,
            scoreData: {
              lastScore: player.scoreData?.lastScore || 0,
              totalScore: player.scoreData?.totalScore || 0,
              lastThreeMatch: player.scoreData?.lastThreeMatch || 0,
              lastFiveMatch: 0,
              playerCurrentSalary: player.scoreData?.playerCurrentSalary || 0,
              playerLastSalary: player.scoreData?.playerLastSalary || 0,
              avg: player.scoreData?.avg || 0,
              livePoint: player.scoreData?.livePoint || 0,
              totalPlayed: player.scoreData?.totalPlayed || 0,
              totalPoint: player.scoreData?.totalPoint || 0,
            },
          })),
        );
      }

      // If a specific player needs to be removed
      let updatedPlayers = newDreamPlayers;
      if (role && playerId) {
        updatedPlayers = newDreamPlayers.filter(
          (player) => !(player.role === role && player.playerId === playerId),
        );
      }

      // Calculate total cost of selected players
      const totalCost = updatedPlayers.reduce(
        (sum, player) =>
          sum + (player.scoreData?.playerCurrentSalary || player.price),
        0,
      );

      return {
        ...state,
        selectedPlayers: updatedPlayers,
        remainingBudget: state.totalBudget - totalCost,
        lastEntry: {
          mode: 'DREAM',
          players: updatedPlayers,
        },
      };
    }

    case 'CREATE_EXPERT_TEAM': {
      const {
        expertTeam,
        players,
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
      } = action.payload;

      // Reset all players' captain/vice-captain status before creating expert team
      const resetPlayers = resetCaptainAndViceCaptain(players);

      const newPlayersByRole = generateSoccerExpertTeamPayload(
        expertTeam,
        resetPlayers,
      );

      const validPlayersByRole = addSoccerPlayersWithinBudget(
        newPlayersByRole,
        state.remainingBudget,
      );

      // Flatten the players into a single array
      const allPlayers = Object.values(validPlayersByRole).flat();

      // Calculate total cost
      const totalCost = allPlayers.reduce(
        (sum, player) =>
          sum + (player.scoreData?.playerCurrentSalary || player.price),
        0,
      );

      // Find captain and vice-captain
      const { captain, viceCaptain } = findSoccerCaptainViceCaptain(allPlayers);

      // Generate payload for expert team
      const playerData = generateSoccerTeamPayload(
        allPlayers,
        captain,
        viceCaptain,
      );

      return {
        ...state,
        selectedPlayers: allPlayers,
        remainingBudget: state.totalBudget - totalCost,
        createDreamTeamPayload: {
          ...state.createDreamTeamPayload,
          playerData,
          eventId,
          eventName,
          sportId,
          tournamentId,
          competitionId,
          name,
        },
        lastEntry: {
          mode: 'EXPERT',
          players: allPlayers,
        },
      };
    }

    default:
      return state;
  }
};

export interface SoccerPlayerFavouriteType {
  maxSelected: number;
  playerId: number;
  positionType: string;
}

export interface SoccerFavoriteTeam {
  DEF: SoccerPlayerFavouriteType[];
  FWD: SoccerPlayerFavouriteType[];
  GKP: SoccerPlayerFavouriteType[];
  MID: SoccerPlayerFavouriteType[];
  captain: SoccerPlayerFavouriteType[];
  viceCaptain: SoccerPlayerFavouriteType[];
}

export interface SoccerExpertTeam {
  id: number;
  dreamTeamId: number;
  playerId: number;
  positionType: 'GKP' | 'DEF' | 'MID' | 'FWD' | 'captain' | 'viceCaptain';
  playerValue: number;
}

interface SoccerTeamContextType {
  state: SoccerTeamState;
  activeTabPlayer: SoccerPlayerRole;
  setActiveTabPlayer: Dispatch<SetStateAction<SoccerPlayerRole>>;
  showPlayerTable: boolean;
  setShowPlayerTable: Dispatch<SetStateAction<boolean>>;
  addPlayer: (player: SoccerPlayer, role: SoccerPlayerRole) => void;
  removePlayer: (playerId: number, role: SoccerPlayerRole) => void;
  setPlayerRoleToCaptain: (playerId: number, role: SoccerPlayerRole) => void;
  setPlayerRoleToViceCaptain: (
    playerId: number,
    role: SoccerPlayerRole,
  ) => void;
  clearTeam: ({
    eventId,
    eventName,
    sportId,
    tournamentId,
    competitionId,
    teamName,
  }: {
    eventId: string;
    eventName: string;
    sportId: string;
    tournamentId: string;
    competitionId: string;
    teamName: string;
  }) => void;
  setBudget: (amount: number) => void;
  createLuckyTeam: ({
    players,
    eventId,
    eventName,
    sportId,
    tournamentId,
    competitionId,
    name,
  }: {
    players: SoccerPlayer[];
    eventId: string;
    eventName: string;
    sportId: string;
    tournamentId: string;
    competitionId: string;
    name: string;
  }) => void;
  createFavoriteTeam: (
    favoriteTeam: SoccerFavoriteTeam,
    players: SoccerPlayer[],
    eventId: string,
    eventName: string,
    sportId: string,
    tournamentId: string,
    competitionId: string,
    name: string,
  ) => void;
  setAvailablePlayers: (players: SoccerPlayer[]) => void;
  createDreamTeam: (payload: {
    eventId: string;
    eventName: string;
    sportId: string;
    tournamentId: string;
    competitionId: string;
    name: string;
  }) => void;
  getDreamTeam: (
    fantasyTeamResponse: any,
    playerId?: number,
    role?: SoccerPlayerRole,
  ) => void;
  dispatch: React.Dispatch<SoccerTeamAction>;
  showFilter: boolean;
  setShowFilter: Dispatch<SetStateAction<boolean>>;
  showMobileFilter: boolean;
  setShowMobileFilter: Dispatch<SetStateAction<boolean>>;
  setPlayerLimits: (playerByRoleLimit: {
    GK: { min: number; max: number };
    DEF: { min: number; max: number };
    MID: { min: number; max: number };
    FWD: { min: number; max: number };
  }) => void;
  createExpertTeam: (
    expertTeam: SoccerExpertTeam[],
    players: SoccerPlayer[],
    eventId: string,
    eventName: string,
    sportId: string,
    tournamentId: string,
    competitionId: string,
    name: string,
  ) => void;
}

const SoccerTeamContext = createContext<SoccerTeamContextType | undefined>(
  undefined,
);

export const SoccerTeamProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(soccerTeamReducer, initialState);
  const [activeTabPlayer, setActiveTabPlayer] =
    useState<SoccerPlayerRole>('GK');
  const [showPlayerTable, setShowPlayerTable] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [showMobileFilter, setShowMobileFilter] = useState(false);
  const { eventDetailsResponse } = useCompetition();

  const addPlayer = (player: SoccerPlayer, role: SoccerPlayerRole) => {
    dispatch({ type: 'SELECT_PLAYER', payload: { player, role } });
  };

  const removePlayer = (playerId: number, role: SoccerPlayerRole) => {
    dispatch({ type: 'REMOVE_PLAYER', payload: { playerId, role } });
  };

  const setPlayerRoleToCaptain = (playerId: number, role: SoccerPlayerRole) => {
    dispatch({ type: 'SET_CAPTAIN', payload: { playerId, role } });
  };

  const setPlayerRoleToViceCaptain = (
    playerId: number,
    role: SoccerPlayerRole,
  ) => {
    dispatch({ type: 'SET_VICE_CAPTAIN', payload: { playerId, role } });
  };

  const clearTeam = ({
    eventId,
    eventName,
    sportId,
    tournamentId,
    competitionId,
    teamName,
  }: {
    eventId: string;
    eventName: string;
    sportId: string;
    tournamentId: string;
    competitionId: string;
    teamName: string;
  }) => {
    dispatch({
      type: 'CLEAR_TEAM',
      payload: {
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        teamName,
      },
    });
  };

  const setBudget = (amount: number) => {
    dispatch({ type: 'SET_TOTAL_BALANCE', payload: { amount } });
  };

  const setPlayerLimits = (playerByRoleLimit: {
    GK: { min: number; max: number };
    DEF: { min: number; max: number };
    MID: { min: number; max: number };
    FWD: { min: number; max: number };
  }) => {
    dispatch({ type: 'SET_PLAYER_LIMITS', payload: { playerByRoleLimit } });
  };

  // SET PLAYER LIMITS

  useEffect(() => {
    if (eventDetailsResponse?.result?.sportRule) {
      const soccerRules = eventDetailsResponse.result.sportRule;
      dispatch({
        type: 'SET_PLAYER_LIMITS',
        payload: {
          playerByRoleLimit: {
            GK: {
              max:
                soccerRules.find((rule: any) => rule.positionType === 'GKP')
                  ?.maxPlayer || 1,
              min:
                soccerRules.find((rule: any) => rule.positionType === 'GKP')
                  ?.minPlayer || 1,
            },
            DEF: {
              max:
                soccerRules.find((rule: any) => rule.positionType === 'DEF')
                  ?.maxPlayer || 5,
              min:
                soccerRules.find((rule: any) => rule.positionType === 'DEF')
                  ?.minPlayer || 3,
            },
            MID: {
              max:
                soccerRules.find((rule: any) => rule.positionType === 'MID')
                  ?.maxPlayer || 5,
              min:
                soccerRules.find((rule: any) => rule.positionType === 'MID')
                  ?.minPlayer || 3,
            },
            FWD: {
              max:
                soccerRules.find((rule: any) => rule.positionType === 'FWD')
                  ?.maxPlayer || 3,
              min:
                soccerRules.find((rule: any) => rule.positionType === 'FWD')
                  ?.minPlayer || 1,
            },
          },
        },
      });
    }
  }, [eventDetailsResponse]);

  const createLuckyTeam = ({
    players,
    eventId,
    eventName,
    sportId,
    tournamentId,
    competitionId,
    name,
  }: {
    players: SoccerPlayer[];
    eventId: string;
    eventName: string;
    sportId: string;
    tournamentId: string;
    competitionId: string;
    name: string;
  }) => {
    // Group players by role
    const playersByRole: Record<SoccerPlayerRole, SoccerPlayer[]> = {
      GK: players.filter((p) => p.role === 'GK'),
      DEF: players.filter((p) => p.role === 'DEF'),
      MID: players.filter((p) => p.role === 'MID'),
      FWD: players.filter((p) => p.role === 'FWD'),
    };

    dispatch({
      type: 'CREATE_LUCKY_TEAM',
      payload: {
        playersByRole,
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
      },
    });
  };

  const createFavoriteTeam = (
    favoriteTeam: SoccerFavoriteTeam,
    players: SoccerPlayer[],
    eventId: string,
    eventName: string,
    sportId: string,
    tournamentId: string,
    competitionId: string,
    name: string,
  ) => {
    dispatch({
      type: 'CREATE_FAVORITE_TEAM',
      payload: {
        favoriteTeam,
        players,
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
      },
    });
  };

  const setAvailablePlayers = (players: SoccerPlayer[]) => {
    dispatch({ type: 'SET_AVAILABLE_PLAYERS', payload: players });
  };

  const createDreamTeam = (payload: {
    eventId: string;
    eventName: string;
    sportId: string;
    tournamentId: string;
    competitionId: string;
    name: string;
  }) => {
    dispatch({ type: 'CREATE_DREAM_TEAM', payload });
  };

  const getDreamTeam = (
    fantasyTeamResponse: any,
    playerId?: number,
    role?: SoccerPlayerRole,
  ) => {
    dispatch({
      type: 'GET_DREAM_TEAM',
      payload: { fantasyTeamResponse, playerId, role },
    });
  };

  const createExpertTeam = (
    expertTeam: SoccerExpertTeam[],
    players: SoccerPlayer[],
    eventId: string,
    eventName: string,
    sportId: string,
    tournamentId: string,
    competitionId: string,
    name: string,
  ) => {
    dispatch({
      type: 'CREATE_EXPERT_TEAM',
      payload: {
        expertTeam,
        players,
        eventId,
        eventName,
        sportId,
        tournamentId,
        competitionId,
        name,
      },
    });
  };

  const contextValue = useMemo(
    (): SoccerTeamContextType => ({
      state,
      activeTabPlayer,
      setActiveTabPlayer,
      showPlayerTable,
      setShowPlayerTable,
      addPlayer,
      removePlayer,
      setPlayerRoleToCaptain,
      setPlayerRoleToViceCaptain,
      clearTeam,
      setBudget,
      createLuckyTeam,
      createDreamTeam,
      createFavoriteTeam,
      setAvailablePlayers,
      getDreamTeam,
      dispatch,
      setShowFilter,
      showFilter,
      showMobileFilter,
      setShowMobileFilter,
      setPlayerLimits,
      createExpertTeam,
    }),
    [
      state,
      activeTabPlayer,
      setActiveTabPlayer,
      showPlayerTable,
      setShowPlayerTable,
      addPlayer,
      removePlayer,
      setPlayerRoleToCaptain,
      setPlayerRoleToViceCaptain,
      clearTeam,
      setBudget,
      createLuckyTeam,
      createDreamTeam,
      createFavoriteTeam,
      setAvailablePlayers,
      getDreamTeam,
      dispatch,
      setShowFilter,
      showFilter,
      showMobileFilter,
      setShowMobileFilter,
      setPlayerLimits,
      createExpertTeam,
    ],
  );

  return (
    <SoccerTeamContext.Provider value={contextValue}>
      {children}
    </SoccerTeamContext.Provider>
  );
};

export const useSoccerTeamContext = () => {
  const context = useContext(SoccerTeamContext);
  if (!context) {
    throw new Error(
      'useSoccerTeamContext must be used within a SoccerTeamProvider',
    );
  }
  return context;
};

// Add utility function to generate expert team payload
const generateSoccerExpertTeamPayload = (
  expertTeam: SoccerExpertTeam[],
  availablePlayers: SoccerPlayer[],
): Record<SoccerPlayerRole, SoccerPlayer[]> => {
  const playersByRole: Record<SoccerPlayerRole, SoccerPlayer[]> = {
    GK: [],
    DEF: [],
    MID: [],
    FWD: [],
  };

  // Helper function to find player by ID
  const findPlayerById = (playerId: number): SoccerPlayer | undefined => {
    return availablePlayers.find((p) => p.playerId === playerId);
  };

  // Process each player from the expert team
  expertTeam.forEach((player) => {
    const foundPlayer = findPlayerById(player.playerId);
    if (!foundPlayer) return;

    switch (player.positionType) {
      case 'GKP':
        if (playersByRole.GK.length < 1) {
          playersByRole.GK.push(foundPlayer);
        }
        break;
      case 'DEF':
        if (playersByRole.DEF.length < 5) {
          playersByRole.DEF.push(foundPlayer);
        }
        break;
      case 'MID':
        if (playersByRole.MID.length < 5) {
          playersByRole.MID.push(foundPlayer);
        }
        break;
      case 'FWD':
        if (playersByRole.FWD.length < 3) {
          playersByRole.FWD.push(foundPlayer);
        }
        break;
      case 'captain':
        // Find the player in their respective role and set as captain
        Object.values(playersByRole).forEach((players) => {
          const playerIndex = players.findIndex(
            (p) => p.playerId === player.playerId,
          );
          if (playerIndex !== -1) {
            players[playerIndex] = { ...players[playerIndex], isCaptain: true };
          }
        });
        break;
      case 'viceCaptain':
        // Find the player in their respective role and set as vice captain
        Object.values(playersByRole).forEach((players) => {
          const playerIndex = players.findIndex(
            (p) => p.playerId === player.playerId,
          );
          if (playerIndex !== -1) {
            players[playerIndex] = {
              ...players[playerIndex],
              isViceCaptain: true,
            };
          }
        });
        break;
    }
  });

  return playersByRole;
};
