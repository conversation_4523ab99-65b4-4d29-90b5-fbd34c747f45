'use client';

import { useState, useEffect, Dispatch, SetStateAction } from 'react';
import { Settings, X } from 'lucide-react';
import { Button } from '@/components/UI/button';
import { Dialog, DialogContent, DialogTrigger } from '@/components/UI/dialog';
import CategoryCheckbox from './CategoryCheckbox';
import { Collapse } from '@material-tailwind/react';
import { Drawer, DrawerContent } from '@/components/UI/drawer';
import { Checkbox } from '@/components/UI/checkbox';
import { Label } from '@/components/UI/label';

// Define the data structure
interface Category {
  id: string;
  name: string;
  children: SubCategory[];
}

interface SubCategory {
  id: string;
  name: string;
}

interface CustomisePanelMobileProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  categories: Category[];
  selected: Record<string, boolean>;
  setSelected: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
}

const CustomisePanelMobile = ({
  isOpen,
  setIsOpen,
  categories,
  selected,
  setSelected,
}: CustomisePanelMobileProps) => {
  const [indeterminate, setIndeterminate] = useState<Record<string, boolean>>(
    {},
  );

  const updateIndeterminateState = () => {
    const newIndeterminate: Record<string, boolean> = {};
    categories.forEach((category) => {
      const childStates = category.children.map((child) => selected[child.id]);
      const allChecked = childStates.every((state) => state);
      const someChecked = childStates.some((state) => state);
      newIndeterminate[category.id] = someChecked && !allChecked;
    });
    setIndeterminate(newIndeterminate);
  };

  useEffect(() => {
    updateIndeterminateState();
  }, [selected]);

  const handleParentChange = (categoryId: string) => {
    const category = categories.find((c) => c.id === categoryId);
    if (!category) return;

    const allChildrenChecked = category.children.every(
      (child) => selected[child.id],
    );

    setSelected((prev) => {
      const newState = { ...prev };
      // Toggle parent
      newState[categoryId] = !allChildrenChecked;
      // Toggle all children
      category.children.forEach((child) => {
        newState[child.id] = !allChildrenChecked;
      });
      return newState;
    });
  };

  const handleChildChange = (categoryId: string, childId: string) => {
    setSelected((prev) => {
      const newState = { ...prev };
      newState[childId] = !prev[childId];

      // Update parent state based on children
      const category = categories.find((c) => c.id === categoryId);
      if (category) {
        const allChildrenChecked = category.children.every(
          (child) => newState[child.id],
        );
        newState[categoryId] = allChildrenChecked;
      }

      return newState;
    });
  };

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerContent className="bg-white">
        <div className="absolute bg-primary-200 p-2 font-bold text-xl text-white w-full z-50 rounded-t-md flex justify-between items-center">
          <span className="uppercase">Customise</span>
          <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
            <X className="w-4 h-4 text-white" />
          </Button>
        </div>
        <div className="p-4 mt-10">
          <div className="flex justify-between items-center mb-4"></div>
          <div className="grid grid-cols-2 gap-4">
            {categories.map((category) => (
              <div key={category.id} className="space-y-2">
                <CategoryCheckbox
                  id={category.id}
                  label={category.name}
                  checked={category.children.every(
                    (child) => selected[child.id],
                  )}
                  indeterminate={indeterminate[category.id]}
                  onChange={() => handleParentChange(category.id)}
                />
                <Collapse open={true}>
                  <div className="ml-6 space-y-2">
                    {category.children.map((child) => (
                      <CategoryCheckbox
                        key={child.id}
                        id={child.id}
                        label={child.name}
                        checked={selected[child.id]}
                        onChange={() =>
                          handleChildChange(category.id, child.id)
                        }
                      />
                    ))}
                  </div>
                </Collapse>
              </div>
            ))}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default CustomisePanelMobile;
