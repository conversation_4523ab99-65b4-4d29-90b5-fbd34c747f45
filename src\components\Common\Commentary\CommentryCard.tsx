'use client';
import OddsArrow from '@/components/UI/Icons/OddsArrow';
import OddsLogo from '@/components/UI/Icons/OddsLogo';
import {
  cn,
  formatCommentary,
  generateUniqueId,
  renderCricketCommentaryEventText,
} from '@/lib/utils';
import { CommentaryItem, CommentaryItemAPI } from '../../../../types/commentry';
import OverDetails, { OverData } from './OverDetails';

interface BallCommentary {
  ball: number;
  runs: number;
  isWicket?: boolean;
  isBoundary?: boolean;
  commentary: string;
}

type BallDetail = {
  ball: number;
  result: string;
  batsman: string;
  bowler: string;
};

type OverDetails = {
  over: number;
  ballDetails: BallDetail[];
};

type TeamStats = {
  teamName: string;
  runs: number;
  overs: number;
  crr: number;
};

interface OverProps {
  overNumber: number;
  balls: BallCommentary[];
  matchStats: {
    runsScored: number;
    wickets: number;
    overs: number;
    runRate: string;
    batsmen: {
      name: string;
      runs: string;
      balls: string;
    }[];

    bowlers: {
      name: string;
      overs: string;
      runs: string;
      wickets: string;
      balls: BallCommentary[];
    }[];
  };
  odds: {
    team1: { name: string; odds: string };
    team2: { name: string; odds: string };
  };
  overs: OverDetails;
  teamStats: TeamStats;
}

const overDataDetaisl: OverDetails = {
  over: 1,
  ballDetails: [
    { ball: 1, result: '1', batsman: 'Glenn Maxwell', bowler: 'Mark Steketee' },
    { ball: 2, result: 'W', batsman: 'Mark Steketee', bowler: 'Mark Steketee' },
    { ball: 3, result: '1', batsman: 'Glenn Maxwell', bowler: 'Mark Steketee' },
    { ball: 4, result: '6', batsman: 'Glenn Maxwell', bowler: 'Mark Steketee' },
    { ball: 5, result: '4', batsman: 'Glenn Maxwell', bowler: 'Mark Steketee' },
    { ball: 6, result: '1', batsman: 'Glenn Maxwell', bowler: 'Mark Steketee' },
  ],
};

const formatBowlerStats = (overs: string, runs: string, wickets: string) => {
  const oversParts = overs.split('.'); // Split overs into overs and balls
  const totalBalls =
    parseInt(oversParts[0]) * 6 + (oversParts[1] ? parseInt(oversParts[1]) : 0);
  return `${overs} (${totalBalls}-${runs}-${wickets})`;
};

export default function CommentryCard({
  commentary,
}: {
  commentary: CommentaryItemAPI[];
}) {
  const overData: OverProps = {
    overNumber: 20,
    balls: [
      {
        ball: 19.6,
        runs: 1,
        commentary:
          'One run. Jahangir Khan to Richard Ngarava. Off cutter length ball, outside off stump on the front foot Slog, mis-timed to mid off for 1 run, fielded by Akram.',
      },
      {
        ball: 19.5,
        runs: 1,
        commentary:
          'One run. Jahangir Khan to Richard Ngarava. Off cutter length ball, outside off stump on the front foot Slog, mis-timed to mid off for 1 run, fielded by Akram.',
      },
      {
        ball: 19.4,
        runs: 0,
        isWicket: true,
        commentary:
          'Wicket. OUT! Caught. Jahangir Khan to Tashinga Musekiwa. Off cutter short, wide outside off stump on the back foot pulling, mis-timed in the air uncontrolled to mid wicket, caught by Tahir.',
      },
    ],
    matchStats: {
      runsScored: 13,
      wickets: 4,
      overs: 20,
      runRate: '8.16',
      batsmen: [
        { name: 'Glenn Maxwell', runs: '90', balls: '51' },
        { name: 'Mark Steketee', runs: '1', balls: '1' },
      ],
      bowlers: [
        {
          name: 'Mark Steketee',
          overs: '4',
          runs: '22',
          wickets: '2',
          balls: [
            {
              ball: 1,
              runs: 1,
              isWicket: false,
              commentary: 'Single to mid-off.',
            },
            { ball: 2, runs: 0, isWicket: false, commentary: 'Dot ball.' },
            {
              ball: 3,
              runs: 1,
              isWicket: false,
              commentary: 'Single to cover.',
            },
            { ball: 4, runs: 0, isWicket: true, commentary: 'Caught at slip.' },
            {
              ball: 5,
              runs: 4,
              isWicket: false,
              commentary: 'Four runs to square leg.',
            },
            { ball: 6, runs: 0, isWicket: false, commentary: 'Dot ball.' },
          ],
        },
        {
          name: 'Glenn Maxwell',
          overs: '2',
          runs: '15',
          wickets: '0',
          balls: [
            {
              ball: 1,
              runs: 1,
              isWicket: false,
              commentary: 'Single to point.',
            },
            {
              ball: 2,
              runs: 4,
              isWicket: false,
              commentary: 'Four runs to cover.',
            },
            { ball: 3, runs: 0, isWicket: false, commentary: 'Dot ball.' },
            { ball: 4, runs: 0, isWicket: false, commentary: 'Dot ball.' },
            {
              ball: 5,
              runs: 4,
              isWicket: false,
              commentary: 'Four runs to mid-wicket.',
            },
            {
              ball: 6,
              runs: 6,
              isWicket: false,
              commentary: 'Six runs over long-on.',
            },
          ],
        },
      ],
    },
    odds: {
      team1: { name: 'Adelaide Strikers', odds: '1.73' },
      team2: { name: 'Sydney Sixers', odds: '2.48' },
    },
    overs: overDataDetaisl,

    teamStats: {
      teamName: 'Adelaide Strikers',
      crr: 8.16,
      overs: 7,
      runs: 168,
    },
  };

  return (
    <div className="max-w-full mx-auto ">
      <div>
        {/* Ball by ball commentary */}
        <div className="space-y-3">
          {commentary.map((ball, index) => (
            <>
              {/* Over header */}
              {ball.overSeparator && (
                <div className="grid lg:grid-cols-4 grid-cols-2 lg:gap-4 gap-0 mb-4 bg-black-400 h-full">
                  <div
                    className="text-sm   border-r border-white p-2 md:p-0 flex flex-col items-start md:items-center justify-center"
                    style={{ paddingLeft: 0 }}
                  >
                    <div className="flex w-full h-full">
                      <div className="bg-primary-200 text-xs mt-[-8px] md:mt-0 mt:ml-0 lg:text-sm lg:hidden text-white p-2 md:p-0 rounded-md w-[60px] font-bold h-full  flex items-center justify-center mr-2">
                        {overData.overNumber}
                      </div>
                      <div className="bg-primary-200 mr-2 text-xs border hidden  lg:text-sm  text-white p-2 md:p-0 rounded-md w-[60px] font-bold lg:flex items-center justify-center">
                        {overData.overNumber}
                      </div>
                      <div className="text-xs lg:text-sm flex flex-col justify-center items-center w-full">
                        <div>
                          <span>Runs Scored:</span>{' '}
                          <span className="font-bold">
                            {overData.matchStats.runsScored}
                          </span>
                        </div>
                        <div className="text-gray-600 flex space-x-2">
                          {ball.overSeparator.o_summary}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="text-xs lg:text-sm md:border-r p-2 lg:p-0 border-white  flex flex-col items-center justify-center">
                    <div className="flex w-full space-x-1">
                      <div>{overData.teamStats.teamName}:</div>
                      <div className="font-bold text-black-100">
                        {overData.teamStats.runs}/{overData.teamStats.overs}
                      </div>
                    </div>
                    <div className="text-gray-600 text-left w-full">
                      CRR: {overData.teamStats.crr}
                    </div>
                  </div>

                  <div className="text-xs lg:text-sm border-r border-t p-2 border-white  flex flex-col items-start justify-center">
                    {overData.matchStats.batsmen.map((batsman, index) => (
                      <div
                        key={generateUniqueId()}
                        className="grid grid-cols-2 place-content-start gap-x-2 text-gray-700 lg:text-sm text-xs"
                      >
                        <div>{batsman.name}</div>
                        <div className="font-medium text-black-100 text-right">
                          {batsman.runs} ({batsman.balls})
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="text-sm  border-t border-white lg:border-none flex flex-col items-start p-2 lg:p-0 px-2 justify-center">
                    {overData.matchStats.bowlers.map((bowler, index) => (
                      <div
                        key={generateUniqueId()}
                        className="grid grid-cols-2 place-content-start gap-x-2 text-gray-700 text-xs lg:text-sm"
                      >
                        <div className="font-medium">{bowler.name}</div>
                        <div className="text-black-100 font-medium text-right">
                          {formatBowlerStats(
                            bowler.overs,
                            bowler.runs,
                            bowler.wickets,
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              <div
                key={generateUniqueId()}
                className={cn(
                  'px-4',
                  ball.ballNbr &&
                    'divide-y divide-lightborder flex gap-4  gap-y-6  justify-center items-center',
                )}
              >
                <div className="w-12 text-right font-mono">{ball?.Ov}</div>
                {ball.ballNbr && (
                  <div
                    className={cn(
                      'flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full border border-lightborder',
                      ball.event === 'WICKET' && 'bg-red-600',
                      ball.event === 'SIX' && 'bg-green-400',
                    )}
                  >
                    <span className="w-6 h-6 flex items-center justify-center  text-white rounded-full">
                      {renderCricketCommentaryEventText(ball.event)}
                    </span>
                  </div>
                )}

                <div className="flex-1 text-sm pt-2">
                  <div
                    key={generateUniqueId()}
                    className="mt-5"
                    dangerouslySetInnerHTML={{
                      __html: ball && formatCommentary(ball),
                    }}
                  />
                </div>
              </div>
            </>
          ))}

          <div className="max-w-full flex pb-4 px-2 space-x-4  items-start lg:items-center space-y-1 flex-col lg:flex-row w-full">
            <div className="text-left p-2 md:p-0 w-full max-w-[200px]">
              Odds Fluctuation:
            </div>
            <div className="flex  items-center flex-col md:flex-row space-y-2 md:space-y-0 md:justify-between justify-center  w-full md:divide-y-0 md:divide-x divide-y divide-lightborder">
              <div className="flex space-x-2 gap-4 justify-center items-center w-full flex-1">
                <div className="flex space-x-2 justify-between md:px-10 px-0 items-center w-[80%] md:w-full">
                  <div className="flex justify-center items-center flex-col">
                    <OddsLogo value={1.73} />
                    <p className="md:text-[16px] text-[12px] mt-2 text-center">
                      Sydney Sixers
                    </p>
                  </div>
                  <OddsArrow color="#E2662C" />
                  <div className="flex justify-center items-center flex-col">
                    <OddsLogo value={2.48} />
                    <p className="md:text-[16px] text-[12px] mt-2 text-center">
                      Sydney Sixers
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex space-x-2 gap-4 justify-center md:px-10 px-0 flex-1 w-full pt-2 md:pt-0">
                <div className="flex space-x-2 justify-between items-center w-[80%] md:w-full">
                  <div className="flex justify-center flex-col items-center">
                    <OddsLogo value={1.73} />
                    <p className="md:text-[16px] text-[12px] mt-2 text-center">
                      Sydney Sixers
                    </p>
                  </div>
                  <OddsArrow color="#1C9A6C" />
                  <div className="flex justify-center items-center flex-col">
                    <OddsLogo value={2.48} />
                    <p className="md:text-[16px] text-[12px] mt-2 text-center">
                      Sydney Sixers
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
