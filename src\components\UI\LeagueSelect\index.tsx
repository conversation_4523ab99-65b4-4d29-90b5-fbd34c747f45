'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import type { Control } from 'react-hook-form';
import { Controller } from 'react-hook-form';
import AsyncSelect from 'react-select/async';
import { useDebounce } from 'use-debounce';
import { components } from 'react-select';
import { Check } from 'lucide-react';

import { Config } from '@/helpers/context/config';

interface League {
  id: string | number;
  name: string;
}

interface Option {
  id: string | number;
  label: string;
  value?: string | number;
}

interface LeagueDropDownProps {
  name: string;
  control: Control<any>;
  sportId: number;
  placeholder: string;
  className?: string;
  value: Option[] | null;
  setValue: Dispatch<SetStateAction<Option[] | null>>;
  styles?: any;
  status: string | null;
}

const fetchLeagues = async (
  offset: number,
  search: string,
  sportId: number | null,
  status: string | null,
): Promise<{ options: Option[]; hasMore: boolean }> => {
  if (!sportId) {
    return { options: [], hasMore: false };
  }

  let stringStatus = '';
  if (status === '1') {
    stringStatus = 'upcoming';
  } else if (status === '2') {
    stringStatus = 'inprogress';
  } else if (status === '3') {
    stringStatus = 'finished';
  }

  try {
    const res = await fetch(
      Config.fantasyURL +
      `/tournament?sportId=${sportId}&limit=20&offset=${offset}&search=${search}&status=${stringStatus}`,
    );
    if (!res.ok) {
      throw new Error('Network response was not ok');
    }
    const data = await res.json();

    return {
      options:
        data?.result?.map((league: League) => ({
          id: league.id,
          label: league.name,
          value: league.id,
        })) || [],
      hasMore: data?.result?.length === 20,
    };
  } catch (error) {
    console.error('Error fetching leagues:', error);
    return { options: [], hasMore: false };
  }
};

const LeagueDropDown = ({
  name,
  control,
  sportId,
  placeholder,
  className,
  value,
  setValue,
  styles,
  status,
}: LeagueDropDownProps) => {
  const [inputValue, setInputValue] = useState('');
  const [debouncedSearch] = useDebounce(inputValue, 300);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const { data, fetchNextPage, isFetchingNextPage, isPending, isLoading } =
    useInfiniteQuery({
      queryKey: ['async-leagues', debouncedSearch, sportId, status],
      queryFn: async ({ pageParam = 0 }) => {
        const result = await fetchLeagues(
          pageParam,
          debouncedSearch,
          sportId,
          status,
        );
        setHasMore(result.hasMore);
        return result.options;
      },
      initialPageParam: 0,
      getNextPageParam: (lastPage, allPages) => {
        if (!hasMore) return undefined;
        return allPages.length * 20;
      },
      enabled: !!sportId && !!status,
      staleTime: 30000,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    });

  useEffect(() => {
    setOffset(0);
    setHasMore(true);
  }, [debouncedSearch]);

  const loadOptions = async (inputValue: string): Promise<Option[]> => {
    const result = await fetchLeagues(0, inputValue, sportId, status);
    return result.options;
  };

  const customStyles = {
    ...styles,
    valueContainer: (base: any) => ({
      ...base,
      padding: '2px 8px',
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected ? '#78C2A7' : provided.backgroundColor,
      color: state.isSelected ? '#FFFFFF !important' : '#000000',
      '&:hover': {
        backgroundColor: '#78C2A7',
        color: '#FFFFFF !important',
      },
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
    }),
    control: (base: any) => ({
      ...base,
      minHeight: '42px',
    }),
  };

  const Option = (props: any) => (
    <components.Option {...props}>
      <span>{props.label}</span>
      {props.isSelected && <Check size={16} className="text-white" />}
    </components.Option>
  );

  return (
    <div className="w-full">
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <AsyncSelect
            isMulti={true}
            isDisabled={!sportId}
            className={className}
            classNamePrefix="react-select"
            isLoading={isLoading || isFetchingNextPage}
            {...field}
            styles={customStyles}
            components={{ Option }}
            placeholder={placeholder}
            onInputChange={(value) => setInputValue(value)}
            loadOptions={loadOptions}
            defaultOptions={(sportId && data?.pages?.flat()) || []}
            onChange={(selectedOptions) => {
              const selectedIds = selectedOptions
                ?.map((option) => option.id)
                .join(',');
              field.onChange(selectedIds || null);
              setValue(selectedOptions as any);
            }}
            value={value}
            onMenuScrollToBottom={() => {
              if (hasMore && !isFetchingNextPage) {
                fetchNextPage();
              }
            }}
            hideSelectedOptions={false}
            isClearable={false}
          />
        )}
      />
    </div>
  );
};

export default LeagueDropDown;
