import { FiltersState } from '@/components/Competition/CricketPlayerFilter';
import type { Variant } from '@/components/UI/PlanCard';
import {
  AFLPlayerStatsResponse,
  AFLStatsResponse,
  SmartPlayStatsResponse,
  SoccerPlayerStat,
  SoccerPlayerStatsResponse,
  SoccerTeamStatsResponse,
} from '@/helpers/fetchers/stats';
import { AFLSmartPlayStatsResponse } from '@/lib/types/afl';
import {
  Table,
  SortingState,
  VisibilityState,
  OnChangeFn,
} from '@tanstack/react-table';

export type Player = {
  id: number;
  rank: number;
  name: string;
  type: string[];
  team: string;
  lastScore: number;
  total: number;
  avg: number;
  matches: number;
  selectionPercent: number;
  avgThreeGames: number;
  price: string;
  avatar: string;
};

export type Team = {
  id: number;
  coach?: string;
  teamName: string;
  teamValue: string;
  firstName?: string;
  lastName?: string;
  profileImage?: string | null;
  competitionId?: number;
  liveScore?: number;
  name?: string;
  nickName?: string;
  rank?: number;
  smartbUserId?: number;
  totalScore?: number;
  userId?: number;
  winningPrice?: number;
  myTeam?: boolean;
  winnerPercentage?: number;
};

export type SelectedPlayer = {
  name: string;
  position: string;
  score: number;
  icon?: string; // Optional icon to represent the additional badges/icons (e.g., vc, c)
  positionType: string;
};

export type TeamSpendData = {
  position: string;
  totalSpend: string;
  average: string;
};
export type seasonStatsUpcomingMatches = {
  opponent: string;
  venue: string;
  rounds: string;
  date: Date;
};

export type seasonStatsSummaryStats = {
  rd: number;
  opponent: string;
  venue: string;
  score: number;
  totalScore: number;
  avg: number;
  price: number;
  priceChange: number;
  points: number;
  teams: number;
};

export type TeamData = {
  position: number;
  teamName: string;
  matches: number;
  wins: number;
  losses: number;
  ties: number;
  noResults: number;
  netRunRate: number;
  deductions: number;
  points: number;
  profileImage?: string;
};

export type fantasyStatsState = {
  rd: number;
  opponent: string;
  run: number;
  sr: number;
  rb: number;
  wickets: number;
  maiden: number;
  dot: number;
  economy: number;
  catches: number;
  runOut: number;
  stumping: number;
  directHit: number;
  allRounder: number;
};

export type careerStatsOpponents = {
  opponent: string;
  timesPlayed: number;
  highScore: number;
  lowScore: number;
  avg: number;
};

export type careerStatsVenues = {
  venue: string;
  timesPlayed: number;
  highScore: number;
  lowScore: number;
  avg: number;
};

export type careerStatsSSL = {
  year: number;
  startingPrice: number;
  endPrice: number;
  priceChange: number;
  matchesPlayed: number;
  avgScore: number;
};

export type captainViceCaptains = {
  id: number;
  name: string;
  type: string;
  teamName: string;
  captain: number;
  viceCaptain: number;
};

export type PlayerProfileDetails = {
  id: number;
  name: string;
  type: string;
  teamName: string;
  lastScore: number;
  totalScore: number;
  avg: number;
  matches: number;
  threeGamesAvg: number;
  fiveGamesAvg: number;
  selectionPercent: number;
  price: string;
};

export type UserProfileForm = {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  dob: Date | null;
  gender: string;
  country: string;
  state: string;
  phone: string;
  phoneCode: string;
};

export type UserProfileData = {
  id: number;
  firstName: string;
  lastName: string;
  username: string;
  role: string;
  location: string | null;
  phone: string;
  residentalAddress: string | null;
  otpCode: string | null;
  optExpirationTime: string;
  isVarify: boolean;
  dob: string;
  bookMakerAccount: boolean;
  titlePrefix: string | null;
  status: string;
  hearedAbout: string | null;
  deviceToken: string | null;
  allfixtures: boolean;
  oddfixtures: boolean;
  referralCode: string;
  wpUserName: string;
  wpEmail: string;
  mailStatus: string | null;
  MediaId: number;
  phoneCountryId: number;
  gender: string;
  nickName: string;
  platFormType: string;
  isDefaultHomePage: boolean;
  isDefaultSportPage: boolean;
  isDefaultRacingPage: boolean;
  createdAt: string;
  updatedAt: string;
  categories: any[];
  address: {
    id: number;
    addressLine1: string | null;
    addressLine2: string | null;
    state: number;
    country: number;
    postCode: string | null;
    hash: string | null;
    userId: number;
    createdAt: string;
    updatedAt: string;
    Country: {
      country: string;
      countryCode: string;
      variation: string;
      country_flag: string;
    };
    State: {
      state: string;
      stateCode: string | null;
      variation: string | null;
    };
  };
  NotificationPreference: {
    id: number;
    UserId: number;
    dailyBestBet: boolean;
    tipOfTheDay: boolean;
    blackBook: boolean;
    smartBook: boolean;
    weeklyNewsLetter: boolean | null;
    tippingCompetition: boolean | null;
    sportsValue: {
      news: boolean;
      tips: boolean;
      SportId: number;
      fixtures: boolean;
      Sport: {
        id: number;
        sportName: string;
        sportTypeId: number;
      };
      dailyBestBet?: boolean;
      smartBNewsLetter?: boolean;
      weeklyNewsLetter?: boolean;
    }[];
    smartBNewsLetter: boolean;
    raceAlert: boolean;
    pushNotification: boolean | null;
    createdAt: string;
    updatedAt: string;
  };
  Media: {
    id: number;
    filePath: string;
    createdAt: string;
    updatedAt: string;
  };
  Country: {
    id: number;
    country: string;
    countryCode: string;
    country_flag: string;
    variation: string;
    updateRequired: boolean;
    phoneCode: number;
    createdAt: string;
    updatedAt: string;
  };
  SubscriptionPurchased: {
    id: number;
    startAt: string;
    expireAt: string;
    status: string;
    plateform: string;
    receiptURL: string | null;
    productId: string | null;
    purchaseToken: string | null;
    subscription_id: string | null;
    type: string;
    canceled_at: string | null;
    isFreeTier: boolean;
    iosOriginalTransactionId: string | null;
    comment: string | null;
    isAdmin: boolean;
    originalAmount: string;
    deductAmount: string;
    isCouponCode: boolean;
    couponCode: string | null;
    couponCodeUses: number;
    trialStatus: string;
    category: string;
    createdAt: string;
    updatedAt: string;
    UserId: number;
    SubscriptionPlanId: number;
    CardId: number;
    CouponCodeId: string | null;
  };
  bookMaker: string[];
  sportOrEvent: string[];
  offerings: any[];
  bookMakerOther: any[];
  sportOrEventOther: any[];
  offeringsOther: any[];
  subscription: {
    UserId: number;
    SubscriptionPlanId: number;
    startAt: string;
    expireAt: string;
    status: string;
    plateform: string;
    SubscriptionPlan: {
      id: number;
      name: string;
      amount: string;
      key: string;
    };
  };
  isPremiumUser: boolean;
  creditInfo: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    lastMonth: number;
    allTime: number;
  };
};

export type Country =
  | {
      id: number;
      country: string;
      countryCode: string;
      country_flag: string;
      variation: string;
      updateRequired: boolean;
      phoneCode: number;
      createdAt: string;
      updatedAt: string;
    }
  | undefined;

export interface Plan {
  id: number;
  amount: string;
  duration: string;
  period: string;
  currency: string;
  name: Variant;
  stripe_plan_id: string | null;
  status: string;
  featured: string[];
  key: string;
  subTitle: string | null;
  iosamount: string;
  androidamount: string;
  category: string;
  coins: number;
  gamePerWeek: number | null;
  createdAt: string;
  updatedAt: string;
  purchasedPlan?: boolean;
  purchasedStatus?: string;
}

// Define the card type
export interface CardType {
  id: number;
  cardHolderName: string;
  cardExp: string;
  last4: string;
  status: string;
  brand: string;
  is_default: number;
}

// Define the API response type
export interface GetCardsResponse {
  status: boolean;
  message: string;
  card: CardType[];
}

export type CardData = {
  card_holder_name: string;
  card_number: string;
  card_exp: string;
  cvv: string;
};

export type BuyPlanNewCardPayload = {
  plan_id: string | undefined; // if `selectedPlanData?.id` can be undefined
  plateform: string;
  card: CardData;
  category: string;
};

// Define the type for the card details
export interface CardDetails {
  card_number: string;
  card_expiry: string;
  card_holder_name: string;
  cvv: string;
  brand: string;
}

export type SubscriptionHold = {
  id: number;
  UserId: number;
  holdType: 'infinite' | 'duration'; // Add more options if applicable
  startDate: string; // ISO 8601 date format
  endDate: string | null; // Nullable
  holdStatus: 'hold' | 'active' | 'cancelled'; // Adjust based on possible statuses
};

// Define the main type for the subscription plan details
export interface SubscriptionPlanDetails {
  id: number;
  UserId: number;
  status: string;
  startAt: string;
  expireAt: string;
  SubscriptionPlanId: number;
  plateform: string;
  CardId: number;
  type: string;
  PlanName: string;
  duration: string;
  amount: string;
  subTitle: string | null;
  card: CardDetails;
  SubscriptionHold?: SubscriptionHold;
  planQueueStatus?: boolean;
}
export interface FantasyUser {
  id: number;
  userId: number;
  bonusCoins: number;
  firstName: string;
  lastName: string;
  email: string;
  customerStripeId: string | null;
  coins: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  holdCoins?: number;
  referralCode?: string;
  totalReferralCoins?: number;
  totalReferralTrack?: number;
  completedReferralTrack?: number;
}

export interface GetFantasyUserResponse {
  status: boolean;
  message: string;
  result: FantasyUser;
}

export interface BankDetails {
  id: number;
  accountHolderName: string;
  accountNumber: string;
  bankName: string;
  bsb: string;
  status: 'active' | 'inactive';
  userId: number;
  createdAt: string;
  updatedAt: string;
}

export interface FetchBankDetailsResponse {
  status: boolean;
  message: string;
  result: BankDetails;
}

export interface CouponData {
  id: number;
  code: string;
  name: string;
  description: string;
  uses: number;
  discountAmount: string;
  isPercentage: boolean;
  startAt: string;
  expireAt: string;
  UserId: number;
  currency: string;
  minPurchased: string;
  maxDiscount: string;
  status: string;
  months: number;
  extraDays: number;
  totalUses: number;
  category: string;
  createdAt: string;
  updatedAt: string;
}

export interface GetCouponCodeResponse {
  data: {
    status: boolean;
    message: string;
    data: CouponData;
    discountAmountPrize: number;
    statusCode: number;
  };
}

export interface CouponCodeDetailsProps {
  response: GetCouponCodeResponse | null;
}

type StandingTeam = {
  id: number;
  name: string;
  gender: string;
  flag: string; // Assuming this is a URL or file path as a string
};

export interface StandingsTeamDetails {
  id: number;
  rnk: number;
  Tid: number;
  win: number;
  winn: string | null;
  wot: string | null;
  Tnm: string;
  lst: string | null;
  lstn: string | null;
  lreg: string | null;
  lot: string | null;
  lap: string | null;
  drw: string | null;
  drwn: string | null;
  gf: number | null;
  ga: number | null;
  gd: number | null;
  ptsn: string | null;
  Ipr: string | null;
  pts: number;
  pld: number;
  nr: string | null;
  nrr: string | null;
  bab: string | null;
  bob: string | null;
  td: string | null;
  name: string;
  createdAt: string;
  updatedAt: string;
  tournamentId: number;
  teamId: number;
  group?: string;
  team?: StandingTeam;
}

export interface RLStandingsTeamDetails {
  teamId: number;
  win: number;
  loss: number;
  draw: number;
  points: number;
  p: number;
  dif: number;
  byes: number;
  pointsValue: number;
  against: number;
  id: number;
  name: string;
  flag: string;
  SportId: number;
}

export interface FetchStandingsTeamDetailsResponse {
  status: boolean;
  result: {
    count: number;
    result: StandingsTeamDetails[];
  };
}

export interface ScoreBoard {
  id: number;
  eventId: number;
  Tr1C1: number | null;
  Tr2C1: number | null;
  Tr1C2: number | null;
  Tr2C2: number | null;
  Tr1CW1: number | null;
  Tr2CW1: number | null;
  Tr1CW2: number | null;
  Tr2CW2: number | null;
  Tr1CD2: string | null;
  Tr1CD1: string | null;
  Tr2CD1: string | null;
  Tr2CD2: string | null;
  Tr1CO1: number | null;
  Tr2CO1: number | null;
  Tr1CO2: number | null;
  Tr2CO2: number | null;
  Tr1: number | null;
  Tr2: number | null;
  EpsL: string | null;
  Epr: number | null;
  Ecov: number | null;
  ErnInf: string | null;
  Et: number | null;
  EtTx: string | null;
  ECo: string | null;
  Ebat: number | null;
  TPa: number | null;
  TCho: number | null;
  Esd: string | null;
  Exd: string | null;
  LuUT: number | null;
  Eact: number | null;
  EO: number | null;
  EOX: number | null;
  LuC: number | null;
  isSyncInfo: number | null;
  createdAt: string;
  updatedAt: string;
  matchTime?: string | null;
  matchStatus?: string | null;
}

export interface TeamDetails {
  id: number;
  name: string;
  gender: string;
  flag: string;
}

export interface CricketCategory {
  name: string;
  SportId: number;
  id: number;
}

export interface CricketTournament {
  id: number;
  name: string;
  gender: string;
  CricketCategoryId: number;
  CricketCategory: CricketCategory;
}

export interface TeamSpendDetails {
  id: number;
  name: string;
  gender: string;
  CricketCategoryId: number;
  CricketCategory: CricketCategory;
}

export interface MatchDetails {
  id: number;
  eventName: string;
  awayTeamId: number;
  homeTeamId: number;
  startTime: string;
  endTime: string | null;
  Eid: string;
  outrights: boolean;
  status: string | null;
  winnerCode: string | null;
  round: number;
  displayName: string | null;
  matchType: string | null;
  createdAt: string;
  updatedAt: string;
  SportId: number;
  CricketTournamentId: number;
  CricketSeasonId: number | null;
  CricketStadiumId: number;
  CricketCategoryId: number | null;
  CricketCategoryTypeId: number | null;
  isSmartBookAwayTeam: number;
  isSmartBookHomeTeam: number;
  isSmartBookTournament: number;
  ScoreBoard: ScoreBoard;
  awayTeam: TeamDetails;
  homeTeam: TeamDetails;
  CricketTournament: CricketTournament;
  teamSpend: TeamSpendDetails;
}

export interface FetchUpcomingMatchDetailsResponse {
  status: boolean;
  result: {
    count: number;
    result: MatchDetails[];
  };
}

export interface FetchRoundDetailsResponse {
  status: boolean;
  completedRound: [];
  roundList: [
    {
      round: string | null;
      label: string | null;
      displayName: string | null;
      startTime: string | null;
    },
  ];
  result: {};
}

export interface FetchLeadingResponse {
  status: boolean;
  message: string;
  result?: [
    {
      userId: number;
      smartbUserId: number;
      firstName: string;
      lastName: string;
      nickName: string;
      profileImage: string | null;
      id: number;
      competitionId: number;
      name: string;
      teamValue: number;
      liveScore: number;
      totalScore: number;
      rank: number;
      winningPrice: number;
      totalCurrentSalary: number;
      winnerPercentage?: number;
    },
  ];
  currentUser?: [
    {
      userId: number;
      smartbUserId: number;
      firstName: string;
      lastName: string;
      nickName: string;
      profileImage: string | null;
      id: number;
      competitionId: number;
      name: string;
      teamValue: number;
      liveScore: number;
      totalScore: number;
      rank: number;
      winningPrice: number;
      myTeam: boolean;
      totalCurrentSalary: number;
      lastData: number;
      winnerPercentage?: number;
    },
  ];
}

export interface CricketTournamentSeason {
  id: number;
  name: string;
  sportKey: string | null;
  gender: string;
  Scd: string | null;
  Sid: string | null;
  Shi: string | null;
  Shu: string | null;
  isSync: boolean;
  tournamentStartTime: string | null;
  tournamentEndTime: string | null;
  season: string | null;
  uniqueTournamentId: string | null;
  isFeatured: boolean;
  createdAt: string;
  updatedAt: string;
  CricketCategoryId: number | null;
  SportId: number;
}

export interface TournamentRow {
  id: number;
  name: string;
  rapidSeasonId: number;
  year: string;
  startDate: string;
  endDate: string;
  fantasy_sport_salary_cap: string | null;
  createdAt: string;
  updatedAt: string;
  SportId: number;
  CricketTournamentId: number;
  CricketTournament: CricketTournamentSeason;
}

export interface TournamentSeasonResult {
  count: number;
  rows: TournamentRow[];
}

export interface FetchTournamentsSeasonResponse {
  status: boolean;
  result: TournamentSeasonResult;
}

export interface FetchRLTournamentsSeasonResponse {
  status: boolean;
  result: RLStandingsTeamDetails[];
}
export type CompetitionStatusProps =
  | 'inprogress'
  | 'upcoming'
  | 'finished'
  | 'innings break'
  | 'team-creation'
  | 'drink'
  | 'notstarted'
  | 'halftime'
  | 'Strategic Timeout'
  | 'Stumps';

export interface FetchLeagueDetailsResponse {
  status: boolean;
  result: {
    count: number;
    rows: Array<{
      isSmartBookTournament: number;
      id: number;
      uniqueTournamentId: string | null;
      name: string;
      Scd: string;
      Shi: boolean;
      Sid: string;
      season: string;
      tournamentStartTime: string;
      sportKey: string | null;
      gender: string;
      isFeatured: boolean;
      CricketCategoryId: number;
      SportId: number;
      CricketCategory: {
        id: number;
        name: string;
        Cid: string | null;
        Ccd: string;
        sportKey: string | null;
        createdAt: string;
        updatedAt: string;
        SportId: number;
      };
      CricketTournamentVariations: Array<any>; // Define a specific type if the structure is known
      uniqueTournament: any; // Define a specific type if the structure is known
    }>;
  };
}

export interface FetchTournamentSeasonDetailsResponse {
  status: boolean;
  result: {
    count: number;
    rows: Array<{
      id: number;
      name: string;
      rapidSeasonId: string | null;
      year: string;
      startDate: string;
      endDate: string;
      fantasy_sport_salary_cap: number;
      createdAt: string;
      updatedAt: string;
      SportId: number;
      CricketTournamentId: number;
      CricketTournament: {
        id: number;
        name: string;
        sportKey: string | null;
        gender: string;
        Scd: string;
        Sid: string;
        Shi: boolean;
        Shu: string | null;
        isSync: boolean;
        tournamentStartTime: string;
        tournamentEndTime: string | null;
        season: string;
        uniqueTournamentId: string | null;
        isFeatured: boolean;
        createdAt: string;
        updatedAt: string;
        CricketCategoryId: number;
        SportId: number;
      };
    }>;
  };
}

export interface PlayerScoreData {
  lastScore: number;
  lastThreeMatch: number;
  lastFiveMatch: number;
  totalScore: number;
  totalPlayed: number;
  avg: number;
  playerCurrentSalary: number;
  playerLastSalary: number;
}

export interface PlayerStateData {
  id: number;
  role: string;
  squadsId: number;
  tournamentId: number;
  playerId: number;
  name: string;
  image: string | null;
  teamName: string;
  teamId: number;
  scoreData: PlayerScoreData;
}

export interface PlayerStateApiResponse {
  status: boolean;
  message: string;
  result: PlayerStateData[];
}

export interface CaptainAverageValue {
  captainAverage: number;
  viceCaptainAverage: number;
}

export interface CaptainPlayer {
  id: number;
  name: string;
  rapidPlayerId: number;
  image: string | null;
  role: string;
  dob: string; // You can also use Date if needed
  createdAt: string; // Alternatively, Date type if you want to work with actual date objects
  updatedAt: string; // Same as above, Date type is an option
  CountryId: number | null;
  SportId: number;
  teamName: string;
  averageValue: CaptainAverageValue;
}

export interface CaptainApiResponse {
  status: boolean;
  message: string;
  result: CaptainPlayer[];
}
export interface NestedDetail {
  content: string;
  nestedContent?: NestedDetail[]; // Recursive type for hierarchical details
  images: StepImage[]; // Images associated with the step
  prefixBold?: boolean;
}

export interface StepImage {
  src: string;
  alt: string;
  className: string; // Class name for styling specific images
  style?: Record<string, any>; // Optional inline styles for images
}

export interface TableData {
  header: string[]; // Headers for the table
  body: Record<string, any>[]; // Table body containing rows as key-value pairs
}

export interface Step {
  title: string;
  subTitle?: {
    // Optional subtitle for the step
    content: string;
    bold?: boolean; // Whether the subtitle should be bold
  };
  content: string;
  table?: TableData; // Optional table data
  details?: NestedDetail[]; // Hierarchical details for the step
  images: StepImage[]; // Images associated with the step
  prefixBold?: boolean;
}

export interface Gradient {
  from: string; // Gradient start color
  to: string; // Gradient end color
}

export interface Benefit {
  icon: string; // Icon identifier (can be restricted via an enum)
  heading: string;
  description: string;
  gradient?: Gradient; // Optional gradient colors for benefit styling
}

export interface Footer {
  content: string; // Footer content
  supportEmail: string;
}

export interface WhyChoose {
  title: string; // Section title
  benefits: Benefit[]; // Array of benefits
  footer?: Footer; // Optional footer for the section
}

export interface CricketScoringData {
  battingData: TableData;
  bowlingData: TableData;
  strikeRateData: TableData;
  economyRateData: TableData;
  fieldingData: TableData;
  allRounderData: TableData;
  captainViceCaptainData: TableData;
}

export interface SportData {
  title: string; // Sport-specific title
  description: string; // Sport-specific description
  steps: Step[]; // Steps involved in the sport, including subtitles
  scoring?: CricketScoringData; // Optional scoring data for cricket
  whyChoose: WhyChoose; // "Why Choose" section for the sport
}

export interface SportsData {
  [key: string]: SportData; // Dictionary of sport data by sport type
}

export interface ReactSelectOptionType {
  value: string;
  label: string;
}

export type SportsType = 'all' | 'cricket' | 'nrl' | 'afl' | 'soccer';

export type TeamFilterState = {
  home: boolean;
  away: boolean;
};

export type LineUpPlayerCricketRole =
  | 'bet1'
  | 'Batsman'
  | 'WK-Batsman'
  | 'Batting Allrounder'
  | 'Bowling Allrounder'
  | 'Bowler'
  | 'Bowling Coach'
  | 'Fielding Coach'
  | 'Assistant coach'
  | 'Head Coach';

export type ValidateCardApiResponse = {
  status: boolean;
  message: string;
  data: {
    status: boolean;
    totalAmount: number;
    cardFee: number;
    country: string;
  };
};

export type StatsContextType = {
  playerStats: AFLPlayerStatsResponse | undefined;
  smartPlayStats: AFLSmartPlayStatsResponse | undefined;
  aflTeamStats: AFLStatsResponse | undefined;
  soccerTeamStats: SoccerTeamStatsResponse | undefined; // TODO: Define proper type for soccer team stats
  soccerPlayerStats: SoccerPlayerStatsResponse | undefined;
  soccerSmartPlayStats: SmartPlayStatsResponse | undefined;
};

export type SoccerTeamStat = {
  teamId: number;
  played: number;
  win: number;
  loss: number;
  draw: number;
  goalsFor: number;
  goalsAgainst: number;
  goalDifference: number;
  points: number;
  yellowCards: number;
  redCards: number;
  name: string;
  flag: string;
  nameCode: string | null;
};

export type SoccerStandingsResponse = {
  status: boolean;
  result: SoccerTeamStat[];
};

export type SoccerPlayer = {
  id: number;
  name: string;
  rapidPlayerId: number | null;
  image: string | null;
  dob: string | null;
  createdAt: string;
  updatedAt: string;
  CountryId: number | null;
  SportId: number;
  isCaptain: boolean;
  isViceCaptain: boolean;
};

export type SoccerTeam = {
  id: number;
  name: string;
  rapidTeamId: number | null;
  national: boolean | null;
  gender: string;
  flag: string;
  nameCode: string | null;
  createdAt: string;
  updatedAt: string;
  SportId: number;
  CountryId: number | null;
};

export type ResultItem = {
  id: number;
  role: string;
  status: string | null;
  createdAt: string;
  updatedAt: string;
  playerId: number;
  teamId: number;
  tournamentId: number;
  seasonId: number;
  player: SoccerPlayer;
  team: SoccerTeam;
  position: string;
  totalMatches: number;
  totalScore: number;
  avgScore: number;
  lastScore: number;
  lastThreeMatchScore: number;
  lastThreeMatchAvg: number;
  goals: number;
  assists: number;
  shots: number;
  shotsOnGoal: number;
  passes: number;
  passesAcc: number;
  tackles: number;
  interceptions: number;
  clearances: number;
  blocks: number;
  foulsCommited: number;
  foulsDrawn: number;
  yellowCards: number;
  redCards: number;
  minutesPlayed: number;
  saves: number;
  keyPasses: number;
  dribbleSucc: number;
  cleanSheet: number;
  appearances: number;
  offsides: number;
};

export type SoccerLineupData = {
  status: boolean;
  result: ResultItem[];
};
