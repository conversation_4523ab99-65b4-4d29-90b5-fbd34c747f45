{"name": "smartb-sports-league", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@copilotkit/react-core": "^1.8.12", "@copilotkit/react-textarea": "^1.8.12", "@copilotkit/react-ui": "^1.8.12", "@copilotkit/runtime": "^1.8.12", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@faker-js/faker": "^9.6.0", "@hookform/resolvers": "^3.9.0", "@lottiefiles/dotlottie-react": "^0.12.0", "@material-tailwind/react": "^2.1.10", "@mui/x-date-pickers": "^7.21.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@redux-devtools/extension": "^3.3.0", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.59.0", "@tanstack/react-table": "^8.20.5", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-testing-library": "^6.3.0", "eslint-plugin-unicorn": "^56.0.0", "husky": "^9.1.6", "lodash": "^4.17.21", "lucide-react": "^0.453.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "motion": "^11.13.1", "next": "14.2.14", "react": "^18.3.1", "react-countdown": "^2.3.6", "react-cropper": "^2.3.3", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.9", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-image-crop": "^11.0.7", "react-intersection-observer": "^9.13.1", "react-lottie": "^1.2.4", "react-phone-number-input": "^3.4.8", "react-select": "^5.8.1", "react-share": "^5.2.2", "react-slick": "^0.30.2", "react-toastify": "^10.0.5", "react-use": "^17.6.0", "readme-md-generator": "^1.0.0", "recharts": "^2.13.3", "sass": "^1.79.4", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "vaul": "^1.1.0", "yup": "^1.4.0", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.5", "@storybook/addon-essentials": "^8.6.4", "@storybook/addon-onboarding": "^8.6.4", "@storybook/blocks": "^8.6.4", "@storybook/experimental-addon-test": "^8.6.4", "@storybook/experimental-nextjs-vite": "^8.6.4", "@storybook/react": "^8.6.4", "@storybook/test": "^8.6.4", "@types/lodash": "^4.17.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-lottie": "^1.2.10", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "@vitest/browser": "^3.0.8", "@vitest/coverage-v8": "^3.0.8", "eslint": "^8.57.1", "eslint-config-next": "14.2.14", "eslint-plugin-react": "^7.37.3", "eslint-plugin-storybook": "^0.11.4", "eslint-plugin-unused-imports": "^4.1.4", "playwright": "^1.51.0", "postcss": "^8", "storybook": "^8.6.4", "tailwindcss": "^3.4.1", "typescript": "5.5.x", "vitest": "^3.0.8"}}