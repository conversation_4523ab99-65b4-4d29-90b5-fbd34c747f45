// SimpleSelect.tsx
import React, { useEffect, useState } from 'react';
import type { Control } from 'react-hook-form';
import { Controller } from 'react-hook-form';
import type { ValueContainerProps } from 'react-select';
import Select, { components } from 'react-select';

interface CountryOption {
  label: string;
  value: string;
}

interface SimpleSelectProps {
  control: Control<any>; // Accepts the control object from parent
  name: string; // Name of the field (e.g., 'country')
  options: CountryOption[]; // Options passed from parent
  defaultValue?: string; // Optional prop for default value
  fetchNextPage?: () => {};
  setSearch: React.Dispatch<React.SetStateAction<string>>;
  isLoading?: boolean;
}

const { ValueContainer, Placeholder } = components;

// CustomValueContainer component with appropriate typing
const CustomValueContainer: React.FC<
  ValueContainerProps<CountryOption, false>
> = ({ children, ...props }) => {
  return (
    <ValueContainer {...props}>
      {/* @ts-expect-error */}
      <Placeholder {...props} isFocused={props.selectProps.menuIsOpen}>
        {props.selectProps.placeholder}
      </Placeholder>
      {React.Children.map(children, (child) =>
        child && (child as React.ReactElement).type !== Placeholder
          ? child
          : null,
      )}
    </ValueContainer>
  );
};

const SimpleSelect: React.FC<SimpleSelectProps> = ({
  control,
  name,
  options,
  defaultValue,
  fetchNextPage,
  setSearch,
  isLoading,
}) => {
  const [selectedValue, setSelectedValue] = useState<CountryOption | null>(
    null,
  );

  useEffect(() => {
    if (defaultValue) {
      setSelectedValue(
        options.find((option) => option.value === defaultValue) || null,
      );
    }
  }, [defaultValue, options]);

  return (
    <div className="simple-select">
      <Controller
        name={name} // Now the name is dynamic
        control={control}
        rules={{ required: true }} // You can modify validation rules as per requirements
        render={({ field: { onChange, value, ref } }) => (
          <Select
            className="React"
            components={{ ValueContainer: CustomValueContainer }}
            styles={{
              container: (provided) => ({
                ...provided,
              }),
              valueContainer: (provided) => ({
                ...provided,
                overflow: 'visible',
              }),
              // @ts-expect-error

              placeholder: (provided, state) => ({
                ...provided,
                position: 'absolute',
                top:
                  state.hasValue ||
                  state.selectProps.inputValue ||
                  state.selectProps.menuIsOpen
                    ? -14
                    : 'auto',
                backgroundColor:
                  state.hasValue ||
                  state.selectProps.inputValue ||
                  state.selectProps.menuIsOpen
                    ? 'white'
                    : 'transparent',
                transition: 'top 0.2s, font-size 0.1s !important',
                fontSize:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '12px !important',
                color: state.selectProps.menuIsOpen ? '#4455c7' : '#a4a4a4',
                padding:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '0px 3px',
                paddingLeft:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '1px !important',
                marginLeft:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '7px !important',
                lineHeight:
                  (state.hasValue ||
                    state.selectProps.inputValue ||
                    state.selectProps.menuIsOpen) &&
                  '8px !important',
              }),
            }}
            onInputChange={(search) => {
              setSearch(search);
            }}
            options={options} // Options passed from props
            classNamePrefix="select"
            value={
              options?.find((option) => option.value === value) || selectedValue
            }
            onChange={(val) => onChange(val?.value)}
            ref={ref}
            onMenuScrollToBottom={() => {
              // @ts-expect-error

              fetchNextPage();
            }}
            isLoading={isLoading}
          />
        )}
      />
    </div>
  );
};

export default SimpleSelect;
