<h1 align="center">Welcome to Smart Play 👋</h1>
<p align="center">
  <img alt="Version" src="https://img.shields.io/badge/version-1.0-blue.svg?cacheSeconds=2592000" />
</p>

### ✨ [Demo](https://smartb.com.au/fantasy)

---

## 📁 Project Structure

### Root Directory

```
📁 project-root/
├── 📁 app/                 # App Router Directory
├── 📁 public/              # Static assets
├── 📁 src/                 # Source code
├── 📄 .env                 # Environment variables
├── 📄 .eslintrc.json       # ESLint configuration
├── 📄 .gitignore           # Git ignore rules
├── 📄 next.config.mjs      # Next.js configuration
├── 📄 package.json         # Project dependencies
└── 📄 tsconfig.json        # TypeScript configuration
```

### Source Directory (`src/`)

```
📁 src/
├── 📁 api/              # API routes and services
│   ├── types.ts         # API type definitions
│   └── endpoints.ts     # API endpoint configurations
│
├── 📁 app/              # App router pages and layouts
│   ├── (auth)/          # Authentication group routes
│   ├── api/             # API route handlers
│   ├── layout.tsx       # Root layout
│   └── page.tsx         # Home page
│
├── 📁 components/       # React components
│   ├── ui/              # UI components
│   ├── layouts/         # Layout components
│   └── shared/          # Shared components
│
├── 📁 hooks/            # Custom React hooks
│   ├── useAuth.ts
│   └── useForm.ts
│
├── 📁 lib/              # Utility functions and libraries
│   ├── utils.ts
│   └── constants.ts
│
├── 📁 store/            # State management (e.g., Zustand)
│   ├── index.ts
│   └── slices/
│
├── 📁 styles/           # Global styles
│   ├── globals.css
│   └── variables.css
│
└── 📁 types/            # TypeScript type definitions
    ├── common.ts
    └── models.ts
```

---

## 🔍 Key Directory Overview

### `app/` – App Router

Defines routes and layouts using [Next.js App Router](https://nextjs.org/docs/app).

- `layout.tsx` – Base layout shared by route segments.
- `page.tsx` – Main page component.
- `api/` – API route handlers.
- Supports `loading.tsx`, `error.tsx`, `not-found.tsx` for UI states.

### `components/`

Reusable UI and layout components:

- `ui/` – Basic components like buttons, modals.
- `layouts/` – High-level layout-specific components.
- `shared/` – Common components used across features.

### `hooks/`

Custom logic extracted into reusable React hooks (e.g., `useAuth`, `useForm`).

### `lib/`

Utility logic and reusable configuration:

- `utils.ts` – Helper methods.
- `constants.ts` – Static values like roles, paths, etc.

### `store/`

Centralized state using Zustand or other libraries.

### `types/`

Shared TypeScript interfaces and types for models, API, etc.

---

## 🔐 Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
NEXT_PUBLIC_API_URL=https://staging.smartb.au.sydney.digiground.com.au/api/
NEXT_PUBLIC_FANTASY_API_URL=https://staging.smartb.au.sydney.digiground.com.au/fantasy-api/fantasy
NEXT_PUBLIC_FANTASY_API_BASE_URL=https://staging.smartb.au.sydney.digiground.com.au/fantasy
NEXT_PUBLIC_MEDIA_URL=https://media.staging.smartb.au.sydney.digiground.com.au/
NEXT_PUBLIC_COUNTRY_MEDIA_URL=https://media.staging.smartb.au.sydney.digiground.com.au/
NEXT_PUBLIC_WP_BASE_URL=https://staging.smartb.au.sydney.digiground.com.au/
NEXT_PUBLIC_VERSION=v.0.1s
NEXT_PUBLIC_RELEASE=AU
NEXT_PUBLIC_SUBSCRIPTION_USER_ID="[24,31,10,23,1]"
NEXT_PUBLIC_FACEBOOK_ID=708883073941381
```

Note: Never commit the `.env` file to version control. The `.env` file is already included in `.gitignore`.

---

## ⚙️ Configuration Files

### `next.config.mjs`

```js
const nextConfig = {
  reactStrictMode: true,
  experimental: {
    typedRoutes: true,
  },
};

export default nextConfig;
```

### `tsconfig.json`

```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [{ "name": "next" }],
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

---

## 🧪 App Router Example

```tsx
// app/layout.tsx
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  );
}

// app/page.tsx
export default function HomePage() {
  return (
    <main>
      <h1>Welcome to Next.js 14</h1>
    </main>
  );
}

// app/api/hello/route.ts
import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({ message: 'Hello World' });
}
```

---

## 🚀 Install

```sh
npm install
```

## ▶️ Usage

```sh
npm run start
```
