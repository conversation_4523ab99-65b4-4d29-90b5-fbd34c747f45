import Image from 'next/image';
import React, { useState } from 'react';

import { Badge } from '@/components/UI/badge';
import { Card } from '@/components/UI/card';

import SimpleTabs from '../UI/SimpleTabs';

interface ProfileStats {
  currentRank: string;
  liveScore: number;
  projectedScore: number;
}

interface ProfileHeaderProps {
  username: string;
  stats: ProfileStats;
  isLive?: boolean;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  username = 'John08',
  stats = {
    currentRank: '20/100',
    liveScore: 500,
    projectedScore: 559,
  },
  isLive = true,
}) => {
  const [activeTab, setActiveTab] = useState<number | string>(1);
  const tabs = [
    { id: 'preview', name: 'Preview' },
    { id: 'list', name: 'List' },
  ];

  return (
    <div className="flex items-center gap-4 p-4 bg-white">
      {/* Avatar and Live Badge */}
      <div className="relative">
        <div className="w-20 border h-20 rounded-full flex items-center justify-center overflow-hidden">
          <Image
            width={80}
            height={80}
            src="/fantasy/images/userIcon.png"
            alt="user"
            unoptimized={true}
          />
        </div>
        {isLive && (
          <Badge className="absolute bottom-[-10px] right-4 bg-red-500 text-white text-xs px-2">
            LIVE
          </Badge>
        )}
      </div>

      {/* User Info and Stats */}
      <div className="flex flex-1 items-center justify-between">
        <div className="flex items-start gap-2 flex-col">
          <h2 className="text-lg font-semibold text-slate-900">{username}</h2>
          <div>
            <SimpleTabs
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              tabs={tabs}
            />
          </div>
        </div>

        {/* Stats Cards */}
        <div className="flex items-center gap-4">
          <Card className="p-3 shadow-sm">
            <div className="text-center">
              <div className="text-sm text-slate-500 mb-1">Current Rank:</div>
              <div className="font-semibold text-slate-900">
                {stats.currentRank}
              </div>
            </div>
          </Card>

          <Card className="p-3 shadow-sm">
            <div className="text-center">
              <div className="text-sm text-slate-500 mb-1">Live score:</div>
              <div className="font-semibold text-orange-500">
                {stats.liveScore}
              </div>
            </div>
          </Card>

          <Card className="p-3 shadow-sm">
            <div className="text-center">
              <div className="text-sm text-slate-500 mb-1">
                Projected score:
              </div>
              <div className="font-semibold text-slate-900">
                {stats.projectedScore}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
