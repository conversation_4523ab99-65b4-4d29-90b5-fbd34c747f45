import { StoryObj, Meta } from '@storybook/react';
import PlayerCard from './index';
import { BasePlayer } from '@/lib/types';

const meta: Meta<typeof PlayerCard> = {
  title: 'Components/PlayerCard',
  component: PlayerCard,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
  },

  decorators: [
    (Story) => (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Story />
      </div>
    ),
  ],

  argTypes: {
    eventStatus: {
      control: 'radio',
      options: ['upcoming', 'live', 'completed'],
    },
    type: {
      control: 'radio',
      options: ['empty', 'filled'],
    },
  },
};

export default meta;

type Story = StoryObj<typeof PlayerCard<BasePlayer>>;

export const EmptyPlayerCard: Story = {
  args: {
    type: 'empty',
    isActive: false,
  },
  argTypes: {
    eventStatus: {
      control: false,
    },
    type: {
      control: 'radio',
      options: ['empty'],
    },
  },
};

export const FilledPlayerCard: Story = {
  args: {
    type: 'filled',
    eventStatus: 'upcoming',
    player: {
      playerId: 1,
      id: '1',
      squadsId: 1,
      tournamentId: 1,
      name: 'John Doe',
      image: '',
      team: 'Team A',
      role: 'Batsman',
      teamName: 'Team A',
      price: 100,
      points: 100,
      selected: true,
      number: 1,
      scoreData: {
        lastScore: 100,
        totalScore: 100,
        lastThreeMatch: 100,
        lastFiveMatch: 100,
        avg: 100,
        totalPlayed: 100,
        playerCurrentSalary: 100,
        playerLastSalary: 100,
      },
      isCaptain: true,
      isViceCaptain: false,
    },
  },

  argTypes: {
    type: {
      control: 'select',
      options: ['filled'],
    },
  },
};
