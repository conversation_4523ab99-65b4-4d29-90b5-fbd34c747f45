import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useState } from 'react';
import Select, { components } from 'react-select';

import DefualtProfile from '@/assets/images/icons/coolLogo.png';
import { Badge } from '@/components/UI/badge';
import { Button } from '@/components/UI/button';
import { Card } from '@/components/UI/card';
import SimpleTabs from '@/components/UI/SimpleTabs';
import { useCompetition } from '@/helpers/context/competitionContext';
import { Config } from '@/helpers/context/config';
import { useTeam } from '@/helpers/context/createTeamContext';
import { useUserProfileContext } from '@/helpers/context/userContext';
import useScreen from '@/hooks/useScreen';
import { cn, formatNumberWithCommas, isObjectEmpty } from '@/lib/utils';

import type { CompetitionStatusProps } from '../../../types';
import type { DreamTeam, FantasyTeamResponse } from '../../../types/competitions';
import SmileIconMotion from '../motions/SmileIcon';
import { Option } from '../UI/CountryDropDown';
import { round } from 'lodash';



export interface ProfileStats {
  selectedPlayer?: string;
  remainingSalary: number;
  allLiveScore?: number;
}

interface ProfileHeaderProps {
  stats: ProfileStats;
  status?: CompetitionStatusProps;
  activeTab?: string | number;
  setActiveTab?: Dispatch<SetStateAction<string | number>>;
  readOnly?: boolean;
  dreamTeamResponse?: FantasyTeamResponse;
}

const CompetitionDetailsHeader: React.FC<ProfileHeaderProps> = ({
  stats,
  status,
  activeTab,
  readOnly,
  setActiveTab,
  dreamTeamResponse,
}) => {
  const {
    createLuckyTeam,
    createFavouriteTeam,
    createExpertTeam,
    setBudget,
    state: {
      playersByRole: { ALL, BAT, BOW, WKP },
    },
  } = useTeam();
  const searchParams = useSearchParams();
  const { eventDetailsResponse, favouriteTeamResponse, playerByRole, activeTeam, setActiveTeam } =
    useCompetition();
  const { user } = useUserProfileContext();



  const dreamTeamOptions = eventDetailsResponse?.result?.dreamTeams?.map((team) => ({
    id: team.id,
    label: team.name || '',
    value: team.name || '',
  })) || [];


  const validTeamSelection =
    ALL?.length + BAT?.length + BOW?.length + WKP?.length === 11;

  const event_id = searchParams.get('event_id');
  const tournament_id = searchParams.get('tournament_id');
  const competition_id = searchParams.get('competition_id');
  const dreamTeamId = searchParams.get('dreamTeamId');
  const [selectedDreamTeam, setSelectedDreamTeam] = useState<Option | null>(null);



  useEffect(() => {
    if (activeTeam && activeTeam.name) {
      setSelectedDreamTeam({
        id: activeTeam.id,
        label: activeTeam.name || '',
        value: activeTeam.name || '',
      });
    }
  }, [activeTeam]);

  const profilePicSrc = user?.Media?.filePath
    ? Config.mediaURL + user?.Media?.filePath
    : '';

  const isLive =
    status === 'innings break' || status === 'inprogress' || status === 'drink' || status === 'Strategic Timeout' || status === 'Stumps';

  const { width } = useScreen();

  const eventDetails = eventDetailsResponse?.result?.eventDetails;
  const totalSpend =
    status === 'upcoming' || status === 'notstarted'
      ? ((eventDetails?.SportId === 4
        ? eventDetails?.CricketSeason
        : eventDetails?.SportId === 9
          ? eventDetails?.ARSeason
          : eventDetails?.RLSeason
      )?.fantasy_sport_salary_cap ?? 0) -
      (dreamTeamResponse?.result?.totalSpendValue ?? 0)
      : (dreamTeamResponse?.result?.totalSpendValue ?? 0);

  const renderUserProfile = () => (
    <div className="flex items-center justify-center flex-col w-full">
      {(status === 'upcoming' ||
        status === 'notstarted' ||
        status === 'inprogress' ||
        status === 'finished' ||
        status === 'innings break') && (
          <div className="flex space-x-4 justify-start items-center w-full">
            {/* <div className="relative">
              <Image
                src={profilePicSrc || DefualtProfile}
                height={65}
                width={65}
                style={{
                  width: '65px',
                  height: '65px',
                  objectFit: 'cover',
                }}
                alt="profile"
                className="rounded-full"
                unoptimized={true}
              />
              {isLive && (
                <Badge className="bg-negative-300 text-white rounded absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                  LIVE
                </Badge>
              )}
              </div> */}
            <div className="md:max-w-[650px] w-full flex items-center space-y-2 md:space-y-0 flex-col md:flex-row justify-between space-x-4">
              {/* <div className="text-[22.4px] font-semibold text-black-100">
                {user?.nickName}
              </div> */}
              {!readOnly && (
                <>
                  <SimpleTabs
                    tabs={[
                      { id: 1, name: 'Preview' },
                      { id: 2, name: 'List' },
                    ]}
                    className="w-full"
                    activeTab={activeTab!}
                    setActiveTab={setActiveTab!}
                    variant="default"
                  />
                  <div className='dream-team-select w-full'

                  >
                    <Select
                      className="min-w-[360px] md:min-w-[300px] w-full -ml-2"
                      onChange={(e) => {
                        setSelectedDreamTeam(e as Option);
                        const team: DreamTeam = {
                          id: e?.id as number || 0,
                          name: e?.label,
                        }
                        setActiveTeam(team);
                      }}
                      options={dreamTeamOptions}
                      classNamePrefix="select"
                      placeholder="Select Team"
                      isSearchable={true}
                      value={selectedDreamTeam}
                      styles={{
                        control: (base) => ({
                          ...base,
                          border: 'none',
                          boxShadow: 'none',
                          backgroundColor: 'transparent',
                          borderRadius: '10px',
                          marginLeft: '0px !important',

                        }),
                        menu: (base) => ({
                          ...base,
                          borderRadius: '10px',
                        }),
                        menuList: (base) => ({
                          ...base,
                          borderRadius: '5px',
                          border: "none",
                          boxShadow: "none",
                          scrollbarWidth: "none",
                          "&::-webkit-scrollbar": {
                            display: "none"
                          }
                        }),
                      }}
                    />
                  </div>
                </>

              )}

            </div>
          </div>
        )}
    </div>
  );

  const renderTeamCreationSection = () => (
    <div className="flex items-start flex-col w-full">
      {status === 'team-creation' && (
        <div>
          <div>
            <h2 className="text-[16px] font-semibold mb-[3px]">
              {' '}
              {dreamTeamId ? 'Update Team' : 'Create Team'}
            </h2>
            <p className="text-[12px] text-black-700 leading-[15px] mb-3">
              Choose your{' '}
              <span className="font-bold text-bt-primery-500">11 players</span>{' '}
              and submit your team to enter the competition.
            </p>
          </div>
          <div className="flex space-x-2 justify-start items-center w-full flex-wrap gap-y-2 md:flex-nowrap">
            {favouriteTeamResponse?.result?.favoriteTeam &&
              !isObjectEmpty(favouriteTeamResponse?.result?.favoriteTeam) && (
                <Button
                  size={'sm'}
                  className="lg:text-[16px] text-[12px]"
                  onClick={() => {
                    setBudget(
                      eventDetailsResponse?.result?.eventDetails?.CricketSeason
                        ?.fantasy_sport_salary_cap ?? 0,
                    );
                    createFavouriteTeam({
                      favoriteTeam: favouriteTeamResponse?.result?.favoriteTeam,
                      playersByRole: playerByRole,
                    });
                  }}
                >
                  Favourites
                </Button>
              )}

            {favouriteTeamResponse?.result?.expertFavorite &&
              !isObjectEmpty(favouriteTeamResponse?.result?.expertFavorite) && (
                <Button
                  size={'sm'}
                  className="lg:text-[16px] text-[12px]"
                  onClick={() => {
                    setBudget(
                      eventDetailsResponse?.result?.eventDetails?.CricketSeason
                        ?.fantasy_sport_salary_cap ?? 0,
                    );
                    createExpertTeam(
                      favouriteTeamResponse?.result?.expertFavorite
                        ?.DreamTeamPlayers,
                      playerByRole,
                    );
                  }}
                >
                  Expert Favourites
                </Button>
              )}

            <Button
              size={'sm'}
              className="lg:text-[16px] text-[12px]"
              onClick={() => {
                if (playerByRole) {
                  setBudget(
                    eventDetailsResponse?.result?.eventDetails?.CricketSeason
                      ?.fantasy_sport_salary_cap ?? 0,
                  );
                  createLuckyTeam({
                    playersByRole: playerByRole,
                    competitionId: competition_id,
                    eventId: event_id,
                    eventName:
                      eventDetailsResponse?.result?.eventDetails?.eventName!,
                    sportId: '4',
                    tournamentId: tournament_id,
                    name: `team ${eventDetailsResponse?.result?.dreamTeams?.length}`,
                  });
                }
              }}
            >
              <SmileIconMotion />
              <span>Feeling Lucky</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );

  const renderStatsCards = () => (
    <div className="w-full">
      <div className="flex items-end gap-4 justify-end w-full h-full">
        {isLive && (
          <Card className="p-3 shadow-sm w-full bg-dark-card-gradient text-white">
            <div className="text-center">
              <div className="text-sm text-slate-500 mb-1">Current Rank:</div>
              <div className="font-semibold text-slate-900">
                {dreamTeamResponse?.result?.currentRank}/
                {dreamTeamResponse?.result?.totalRank}
              </div>
            </div>
          </Card>
        )}

        {isLive && (
          <Card className="p-3 shadow-sm w-full bg-live-score-gradient border-[#FC4714]">
            <div className="text-center">
              <div className="text-sm text-slate-500 mb-1 text-[#FDA289]">
                Live Score
              </div>
              <div className="font-semibold text-slate-900 text-[#FC4714]">
                {dreamTeamResponse?.result?.totalLivePoints ?? 0}
              </div>
            </div>
          </Card>
        )}

        {(status === 'finished' ||
          status === 'upcoming' ||
          status === 'notstarted') && (
            <Card className="p-3 shadow-sm w-full bg-dark-card-gradient text-white hidden lg:block">
              <div className="text-center">
                <div className="text-xs text-[#BFCCD8] mb-1">
                  {status === 'upcoming' || status === 'notstarted'
                    ? 'Remaining Salary'
                    : 'Team Value'}
                </div>
                <div className="font-semibold text-slate-900">
                  ${formatNumberWithCommas(totalSpend)}
                </div>
              </div>
            </Card>
          )}

        {status === 'finished' && (
          <Card className="p-3 shadow-sm w-full bg-dark-card-gradient text-white">
            <div className="text-center">
              <div className="text-sm text-slate-500 mb-1">Total Score:</div>
              <div className="font-semibold text-slate-900">
                {dreamTeamResponse?.result?.totalLivePoints ?? 0}
              </div>
            </div>
          </Card>
        )}

        {status === 'finished' && (
          <Card className="p-3 shadow-sm w-full bg-dark-card-gradient text-white">
            <div className="text-center">
              <div className="text-sm text-slate-500 mb-1">Rank:</div>
              <div className="font-semibold text-slate-900">
                {dreamTeamResponse?.result?.currentRank ?? 0} (
                {dreamTeamResponse?.result?.name})
              </div>
            </div>
          </Card>
        )}

        {status === 'team-creation' && (
          <>
            <Card className="p-3 shadow-sm w-full bg-dark-card-gradient text-white hidden md:block">
              <div className="text-center">
                <div className="text-xs text-[#BFCCD8] mb-1">
                  Remaining Salary:
                </div>
                <div className="font-semibold text-slate-900">
                  ${formatNumberWithCommas(stats?.remainingSalary)}
                </div>
              </div>
            </Card>
            <Card
              className={cn(
                'p-3 shadow-sm w-full text-white hidden md:block',
                validTeamSelection
                  ? 'bg-[#D1E7DF] border border-[#B4DBCD]'
                  : 'bg-dark-card-gradient',
              )}
            >
              <div className="text-center">
                <div
                  className={cn(
                    'text-xs text-[#BFCCD8]  mb-1',
                    validTeamSelection ? 'text-black-700' : 'text-slate-500',
                  )}
                >
                  <span className={cn(
                    'text-xs   mb-1',
                    validTeamSelection ? 'text-black-700' : 'text-[#BFCCD8]',
                  )}>Selected Player:</span>
                </div>
                <div
                  className={cn(
                    'font-semibold',
                    validTeamSelection ? 'text-[#1C9A6C]' : 'text-slate-900',
                  )}
                >
                  {stats.selectedPlayer}
                </div>
              </div>
            </Card>
          </>
        )}
      </div>
    </div>
  );

  return (
    <div className={`flex items-center`}>
      <div className="grid grid-cols-1 md:grid-cols-2 w-full my-2 gap-8">
        {status === 'team-creation'
          ? renderTeamCreationSection()
          : renderUserProfile()}
        {renderStatsCards()}
      </div>
    </div>
  );
};

export default CompetitionDetailsHeader;
