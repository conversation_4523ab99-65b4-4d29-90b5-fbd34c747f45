import React, { createRef, useEffect, useState } from 'react';

import {
  InfoIcon,
  VerificationCheckedIcon,
  VerificationCrossIcon,
  VerificationDeleteIcon,
  VerifyDocIcon,
} from '@/components/images';
import { Button } from '@/components/UI/button';
import CustomDialog from '@/components/UI/CustomDialog';
import PreLoader from '@/components/UI/PreLoader';
import axiosInstance from '@/helpers/axios/axiosInstance';
import { setApiMessage } from '@/helpers/commonFunctions';
import { URLS } from '@/helpers/constants/urls';
import { Config } from '@/helpers/context/config';
import useScreen from '@/hooks/useScreen';
import { generateUniqueId } from '@/lib/utils';

interface VerificationItem {
  id: number;
  name: string;
  photoID: 'Yes' | 'No';
  pointsWorth: number;
  imageName: { fileName: string; originalName: string }[];
  type: string;
}

const IdentityVerification = ({
  verificationDetails,
  setVerificationStatus,
}: any) => {
  const { width } = useScreen();
  const [requirementScore, setRequirementScore] = useState(0);
  const [isPhotoID, setIsPhotoID] = useState(false);
  const [scoreError, setScoreError] = useState('');
  const [photoIdError, setPhotoIdError] = useState('');
  const [verificationList, setVerificationList] = useState<VerificationItem[]>(
    [],
  );
  const [loading, setLoading] = useState(false);

  const [toggleModal, setToggleModal] = useState(false);
  const [removeImgId, setRemoveImgId] = useState<number>();
  const [removeImgIndexId, setRemoveImgIndexId] = useState<number>();
  const [isProgressToggle, setIsProgressToggle] = useState(false);

  const requiredScore = 100;

  const verificationData: VerificationItem[] = [
    {
      id: 1,
      name: 'Foreign Passport',
      photoID: 'Yes',
      pointsWorth: 70,
      imageName: [],
      type: 'foreignPassport',
    },
    {
      id: 2,
      name: 'Australian Passport',
      photoID: 'Yes',
      pointsWorth: 70,
      imageName: [],
      type: 'ausPassport',
    },
    {
      id: 3,
      name: 'Australian Driver Licence/Learner’s Permit',
      photoID: 'Yes',
      pointsWorth: 70,
      imageName: [],
      type: 'ausDrivingLic',
    },
    {
      id: 4,
      name: 'Government employee ID (Australian Federal/State/Territory)',
      photoID: 'Yes',
      pointsWorth: 40,
      imageName: [],
      type: 'govEmpId',
    },
    {
      id: 5,
      name: 'Defence Force Identity Card (with photo or signature)',
      photoID: 'Yes',
      pointsWorth: 40,
      imageName: [],
      type: 'defForceId',
    },
    {
      id: 6,
      name: 'Department of Veterans Affairs (DVA) card',
      photoID: 'No',
      pointsWorth: 40,
      imageName: [],
      type: 'dvaCard',
    },
    {
      id: 7,
      name: 'Centrelink card (with reference number)',
      photoID: 'No',
      pointsWorth: 40,
      imageName: [],
      type: 'centrlinkCard',
    },
    {
      id: 8,
      name: 'Medicare card',
      photoID: 'No',
      pointsWorth: 25,
      imageName: [],
      type: 'medicareCardt',
    },
    {
      id: 9,
      name: 'Taxation assessment notice',
      photoID: 'No',
      pointsWorth: 25,
      imageName: [],
      type: 'TaxAssessNotice',
    },
    {
      id: 10,
      name: 'Utility Bill - electricity, gas, telephone - Current address (less than 12 months old)',
      photoID: 'No',
      pointsWorth: 25,
      imageName: [],
      type: 'utiliyBill',
    },
    {
      id: 11,
      name: '',
      photoID: 'No',
      pointsWorth: 0,
      imageName: [],
      type: 'utiliyBill',
    },
  ];

  useEffect(() => {
    setVerificationList(verificationData);
    if (verificationDetails === '') {
      getVerificationData();
    } else {
      // update score and photo id and status
      setRequirementScore(verificationDetails?.requirementScore);
      setIsPhotoID(
        verificationDetails?.isPhotoIdSupplied === 1 ||
          verificationDetails?.verificationStatus === 'verified',
      );

      // Parse identificationDocuments if it's a string
      let identificationDocuments =
        verificationDetails?.identificationDocuments;
      if (typeof identificationDocuments === 'string') {
        try {
          identificationDocuments = JSON.parse(identificationDocuments);
        } catch (error) {
          console.error(error);
          identificationDocuments = [];
        }
      }

      // Set updated data in verification list
      const newVerificationArray = verificationData?.map((item) => {
        return {
          ...item,
          imageName: [
            ...(identificationDocuments?.filter(
              (i: any) => i?.type === item?.type,
            ) ?? []),
          ],
        };
      });

      setVerificationList(newVerificationArray);
    }
  }, [verificationDetails]);

  // fetch verification document data
  const getVerificationData = async () => {
    setLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        Config.baseURL + URLS.VERIFICATION_URL,
      );
      if (status === 200 || status === 201) {
        const verifyData = data?.result;

        if (verifyData) {
          // update score and photo id and status
          setRequirementScore(verifyData?.requirementScore);
          setIsPhotoID(
            verifyData?.isPhotoIdSupplied === 1 ||
              verifyData?.verificationStatus === 'verified',
          );
          setVerificationStatus(verifyData?.verificationStatus);

          // Parse identificationDocuments if it's a string
          let identificationDocuments = verifyData?.identificationDocuments;
          if (typeof identificationDocuments === 'string') {
            try {
              identificationDocuments = JSON.parse(identificationDocuments);
            } catch (error) {
              identificationDocuments = [];
            }
          }

          // Set updated data in verification list
          const newVerificationArray = verificationData?.map((item) => {
            return {
              ...item,
              imageName: [
                ...(identificationDocuments?.filter(
                  (i: any) => i?.type === item?.type,
                ) ?? []),
              ],
            };
          });
          setVerificationList(newVerificationArray);
        }
        setLoading(false);
      }
      setLoading(false);
    } catch (err) {
      console.error(err);
      setLoading(false);
    }
  };

  const fileInput = verificationList?.map(() => createRef<HTMLInputElement>());

  const handleUploadFile = (id: number, files: any) => {
    uploadImage(files, id);
  };

  // FILE UPLOAD
  const uploadImage = async (file: any, id: number) => {
    const body = new FormData();
    const url = Config.baseURL + URLS.MEDIA_UPLOAD_URL;

    setLoading(true);
    for (const element of file) {
      body.append('image', element);
    }

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    try {
      const { status, data } = await axiosInstance.post(url, body, config);
      if (status === 200 || status === 201) {
        setLoading(false);
        if (data?.success) {
          setApiMessage('success', data?.msg);
        } else {
          setApiMessage('error', data?.msg);
        }
        // added uploaded original file name and view path
        const filterList = verificationList.find((item) => item?.id === id);
        if (filterList) {
          filterList.imageName.push({
            originalName: file[0]?.name, // Name of the uploaded file
            fileName: data?.image?.filePath, // Path returned by the server
          });
        }

        // update score and photo id
        const getScore = verificationList
          ?.filter((item) => item?.imageName?.length !== 0)
          ?.map((obj) => obj?.pointsWorth);
        // Update `requirementScore` based on the uploaded item's `pointsWorth`
        const totalScore = getScore?.reduce((acc, current) => acc + current, 0);

        setRequirementScore(totalScore);

        const getIsPhotoId = verificationList
          ?.filter((item) => item?.imageName?.length !== 0)
          ?.filter((obj) => obj?.photoID === 'Yes');
        // Update `isPhotoID` based on the uploaded item's `photoID` value
        const isPhotoIDValue = getIsPhotoId?.length !== 0;

        setIsPhotoID(isPhotoIDValue);
        checkValidation(totalScore, isPhotoIDValue);
      }
    } catch (err) {
      setLoading(false);
      // @ts-expect-error
      setApiMessage('error', err?.response?.data?.msg);
    }
  };

  // view uploaded file
  const openPreview = (urls: string) => {
    const url = Config?.mediaURL + urls;

    window.open(url, '_blank');
  };

  // handle delete file modal
  const handleRemoveToggle = (removeId: number, imageIndex: number) => {
    setToggleModal(true);
    setRemoveImgId(removeId);
    setRemoveImgIndexId(imageIndex);
  };

  // delete file
  const handleRemoveDoc = () => {
    const filterRemoveList = verificationList?.map((obj) => {
      if (obj?.id === removeImgId) {
        const updatedImageNames = obj.imageName
          ? obj.imageName.filter((_, index) => index !== removeImgIndexId)
          : [];
        if (fileInput[obj?.id]?.current) {
          // @ts-expect-error
          fileInput[obj?.id].current.value = '';
        }
        return {
          ...obj,
          imageName: updatedImageNames,
        };
      } else {
        return obj;
      }
    });

    // update score and photo id
    const getScore = filterRemoveList
      ?.filter((item) => item?.imageName?.length !== 0)
      ?.map((item) => item?.pointsWorth || 0);

    // Update `requirementScore` based on the uploaded item's `pointsWorth`
    const totalScore =
      getScore?.reduce((acc, current) => acc + current, 0) || 0;

    setRequirementScore(totalScore);

    // Determine if Photo ID is supplied
    const getIsPhotoId = filterRemoveList
      ?.filter((item) => item?.imageName?.length !== 0)
      ?.filter((item) => item?.photoID === 'Yes');
    // Update `isPhotoID` based on the uploaded item's `photoID` value
    const isPhotoIDValue = getIsPhotoId?.length > 0;

    setIsPhotoID(isPhotoIDValue);

    setVerificationList(filterRemoveList);
    setToggleModal(false);
  };

  const checkValidation = (score: number, photoID: boolean) => {
    if (score <= requiredScore) {
      setScoreError('Score of 100 or more is required.');
    } else {
      setScoreError('');
    }

    if (!photoID) {
      setPhotoIdError(
        'At least one identification document with a photo is required.',
      );
    } else {
      setPhotoIdError('');
    }
  };
  // submit verification upload data and check validation
  const handleSubmitVerification = async () => {
    checkValidation(requirementScore, isPhotoID);
    if (requirementScore >= requiredScore && isPhotoID) {
      setLoading(true);
      // manage payload data
      const newArray = verificationList?.flatMap((item) =>
        item?.imageName?.map((obj) => ({ ...obj, type: item?.type })),
      );
      try {
        const sendData = {
          requirementScore: requirementScore,
          isPhotoIdSupplied: isPhotoID ? 1 : 0,
          identificationDocuments: newArray,
        };
        const { status, data } = await axiosInstance.post(
          Config.baseURL + URLS.UPDATE_VERIFICATION_DOCUMENT,
          sendData,
        );
        if (status === 200 || status === 201) {
          setLoading(false);
          setApiMessage('success', data?.message);
          setIsProgressToggle(true);
          getVerificationData();
        } else {
          setApiMessage('error', data?.data);
        }
      } catch (err) {
        setLoading(false);
        // @ts-expect-error
        setApiMessage('error', err?.response?.data?.msg);
      }
    } else {
      setApiMessage(
        'error',
        'Requirements missing. Upload all required identification documents.',
      );
    }
  };

  return (
    <>
      {loading && <PreLoader />}
      <div className="flex items-start">
        <div>
          <InfoIcon />
        </div>
        <div className="ml-[9px] mb-[21px]">
          <p className="text-base leading-5 text-black-100 font-semibold mb-[13px]">
            Identification requirements
          </p>
          <p className="text-sm leading-[18px] text-black-100 mb-[18px]">
            A minimum of 100 points of identification must be provided and you
            must provide at least one document containing photo identification.
            Ensure to provide both sides of the document if applicable.
          </p>
          <p className="text-sm leading-[18px] text-black-100">
            SmartB protects your privacy by automatically destroying
            identification documents once your event is finished and payout has
            been confirmed
          </p>
        </div>
      </div>
      <div className="bg-black-300 p-[18px] rounded-lg mb-[18px]">
        <div className="ml-[81px] max-1600:ml-[18px] max-767:ml-0 max-767:mr-6 flex max-767:justify-between">
          <div className="mr-[466px] max-1300:mr-[300px] max-1120:mr-[200px] max-767:mr-0">
            <p
              className={`text-base leading-5 font-semibold w-max ${scoreError ? 'mb-[24px] max-767:mb-[4px]' : 'mb-[9px]'} ${requirementScore >= requiredScore ? 'text-success-100' : 'text-black-100'}`}
            >
              ID Requirement Score:
            </p>
            {scoreError && (
              <h6 className="text-xs leading-[15px] text-negative-200 hidden max-767:block max-767:mb-[9px]">
                {scoreError}
              </h6>
            )}
            <p
              className={`text-base leading-5 font-semibold w-max ${isPhotoID ? 'text-success-100' : 'text-black-100'}`}
            >
              Photo ID:
            </p>
            {photoIdError && (
              <h6 className="text-xs leading-[15px] text-negative-200 hidden max-767:block max-767:mb-[9px]">
                {photoIdError}
              </h6>
            )}
          </div>
          <div>
            <div className={`${scoreError ? 'max-767:mb-6' : 'mb-[9px]'}`}>
              <div className="flex">
                {requirementScore >= requiredScore ? (
                  <VerificationCheckedIcon />
                ) : (
                  <VerificationCrossIcon />
                )}
                <p
                  className={`text-base leading-5 ml-2 ${requirementScore >= requiredScore ? 'text-success-100' : 'text-black-100'}`}
                >
                  {requirementScore}/{requiredScore}
                </p>
              </div>
              {scoreError && (
                <h6 className="text-xs leading-[15px] text-negative-200 mb-[9px] max-767:hidden">
                  {scoreError}
                </h6>
              )}
            </div>
            <div>
              <div className="flex items-center">
                {isPhotoID ? (
                  <VerificationCheckedIcon />
                ) : (
                  <VerificationCrossIcon />
                )}
                <p
                  className={`text-base leading-5 ml-2 ${isPhotoID ? 'text-success-100' : 'text-black-100'}`}
                >
                  {isPhotoID ? 'Yes' : 'No'}
                </p>
              </div>
              {photoIdError && (
                <h6 className="text-xs leading-[15px] text-negative-200 max-767:hidden">
                  {photoIdError}
                </h6>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="max-767:-mx-3">
        <table
          width="100%"
          className="shadow-[0px_1px_3px_rgba(0,0,0,0.16)] rounded-lg max-767:rounded-none overflow-hidden"
        >
          <thead>
            <tr className="bg-primary-200 text-white text-base leading-5 font-semibold">
              <td className="py-3 px-[27px]">Document Type</td>
              <td className="py-3 text-center w-[150px] max-639:w-[100px]">
                Photo ID
              </td>
              <td className="py-3 text-center w-[130px] max-639:w-[80px]">
                Points
              </td>
            </tr>
          </thead>
          <tbody>
            {verificationList
              ?.filter((item) => item?.id !== 11)
              ?.map((obj) => (
                <>
                  <tr
                    key={obj?.name}
                    className={`border-b-black-300 text-base leading-5 ${width <= 768 ? 'border-b-0' : 'border-b'}`}
                  >
                    <td
                      className={`pl-[27px] pt-[15px] pb-3 max-767:p-3 ${obj?.imageName?.length !== 0 ? 'text-success-100' : ''}`}
                    >
                      <h6 className="pb-[9px] font-medium">{obj?.name}</h6>

                      <div>
                        <button
                          className="bg-secondary-100 text-white text-sm leading-[17px] rounded-lg py-[6px] px-3"
                          onClick={() => {
                            fileInput[obj?.id]?.current?.click();
                          }}
                        >
                          Choose
                        </button>
                        <input
                          ref={fileInput[obj?.id]}
                          accept="image/*,application/pdf"
                          type="file"
                          style={{ display: 'none' }}
                          id={`upload-${obj?.id}`}
                          multiple
                          onChange={(e) =>
                            handleUploadFile(obj?.id, e.target.files)
                          }
                        />
                        {width > 767 && (
                          <>
                            {obj?.imageName &&
                              obj?.imageName?.length !== 0 &&
                              obj?.imageName?.map((img, index) => (
                                <div
                                  key={generateUniqueId()}
                                  className="mt-[10px]"
                                >
                                  <div className="flex items-center cursor-pointer">
                                    <div>
                                      <VerifyDocIcon />
                                    </div>
                                    <button
                                      className="pl-[6px] pr-3 hover:underline"
                                      onClick={() => openPreview(img?.fileName)}
                                    >
                                      {img?.originalName}
                                    </button>
                                    <div>
                                      <VerificationDeleteIcon
                                        className="cursor-pointer"
                                        onClick={() =>
                                          handleRemoveToggle(obj?.id, index)
                                        }
                                      />
                                    </div>
                                  </div>
                                </div>
                              ))}
                          </>
                        )}
                      </div>
                    </td>
                    <td className="text-center">
                      <h6
                        className={`${obj?.imageName?.length !== 0 ? 'text-success-100' : ''}`}
                      >
                        {obj?.photoID}
                      </h6>
                    </td>
                    <td className="text-center">
                      <h6
                        className={` ${
                          obj?.imageName?.length !== 0 ? 'text-success-100' : ''
                        }`}
                      >
                        {obj?.pointsWorth}
                      </h6>
                    </td>
                  </tr>
                  {width <= 767 && (
                    <tr className="border-b border-b-black-300 text-base leading-5">
                      <td
                        colSpan={3}
                        className={`max-767:pt-0 max-767:p-3 ${obj?.imageName?.length !== 0 ? 'text-success-100' : ''}`}
                      >
                        {obj?.imageName &&
                          obj?.imageName?.length !== 0 &&
                          obj?.imageName?.map((img, index) => (
                            <div key={generateUniqueId()}>
                              <div className="flex items-center justify-between cursor-pointer">
                                <div className="flex items-center">
                                  <div>
                                    <VerifyDocIcon />
                                  </div>
                                  <button
                                    className="pl-[6px] pr-3 hover:underline"
                                    onClick={() => openPreview(img?.fileName)}
                                  >
                                    {img?.originalName}
                                  </button>
                                </div>
                                <div>
                                  <VerificationDeleteIcon
                                    className="cursor-pointer"
                                    onClick={() =>
                                      handleRemoveToggle(obj?.id, index)
                                    }
                                  />
                                </div>
                              </div>
                            </div>
                          ))}
                      </td>
                    </tr>
                  )}
                </>
              ))}
          </tbody>
        </table>
      </div>

      <div className="sticky z-10 bottom-0 p-[11px] bg-black-300 mt-5 max-767:-mx-3">
        <div className="flex justify-center items-center">
          <Button
            onClick={() => handleSubmitVerification()}
            type="button"
            className="app-button blue-button h-45"
          >
            Submit Verification
          </Button>
        </div>
      </div>

      {/* cancel Modal code */}

      <CustomDialog
        isOpen={toggleModal}
        onClose={() => setToggleModal(false)}
        maxWidth={680}
        className="cancel-modal"
        outerClickClose={true}
      >
        <div className="text-center">
          <p className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-medium font-inter text-black-100 mb-[18px] max-799:mb-[12px]">
            Are you sure you want to delete this document?
          </p>
          <p className="text-base max-799:text-[12px] leading-5 max-799:leading-[17px] font-inter text-black-100 mb-[33px] max-799:mb-[23px]">
            Doing so may lower your identification score and require
            re-verification.
          </p>
          <div className="mb-[18px] max-799:mb-3">
            <Button className="w-full" onClick={() => handleRemoveDoc()}>
              Delete
            </Button>
          </div>
          <div>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setToggleModal(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      </CustomDialog>

      {/* Progress dialog */}
      <CustomDialog
        isOpen={isProgressToggle}
        onClose={() => setIsProgressToggle(false)}
        title="Verification in process"
        maxWidth={570}
        className="verification-required-modal"
        outerClickClose={true}
      >
        <div className="">
          <p className="text-[22.4px] max-799:text-[14px] leading-[27px] max-799:leading-[17px] font-semibold font-inter text-black-100 mb-[23px] max-799:mb-[13px]">
            We are reviewing your ID details
          </p>
          <p className="text-[16px] leading-[22.4px] font-inter font-normal text-black-100 mb-[37px]">
            Your ID details will be processed within 48 to 72 hours. You will
            receive an email notification as soon as the process is complete.
          </p>
          <div className="mb-[18px]">
            <Button
              className="w-full"
              onClick={() => setIsProgressToggle(false)}
            >
              Close
            </Button>
          </div>
        </div>
      </CustomDialog>
    </>
  );
};

export default IdentityVerification;
