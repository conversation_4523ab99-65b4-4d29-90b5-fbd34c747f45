import React from 'react';

const GreyhoundRacing = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="8.383"
      viewBox="0 0 22 8.383"
    >
      <path
        id="Path_12117"
        data-name="Path 12117"
        d="M21.693,4.947a1.9,1.9,0,0,1-.883-.022,4.286,4.286,0,0,0-.827-.007,4.525,4.525,0,0,0-1-.9A9.085,9.085,0,0,1,17,3.865c-.023-.021-.172-.4-.238-.542s.449-.868.681-1.02c.43-.283,1.375.156,1.969.256.855.145,1.31.251,1.556.086,0,0,.2-.147-.195-.187-.148-.014.4-.045.508-.088.086-.036.365-.292.34-.38-.013-.044-.06-.111-.3-.19,0,0-.816-.38-.906-.456,0,0-.36-.473-.425-.584A2.043,2.043,0,0,0,18.854.371c-.139-.255-.662-.491-.9-.3.195.131.212.238.195.347a4.844,4.844,0,0,0-1.056,0c-.449.057-1.313.349-1.369.34a7.537,7.537,0,0,0-1.884.032,15.7,15.7,0,0,0-1.89.716,20.193,20.193,0,0,1-3.084.46,11.847,11.847,0,0,0-1.971.479c-.691-.233-1.224-.038-2.466.462h0A4.093,4.093,0,0,1,2.4,3.277,19.9,19.9,0,0,1,.12,2.416,6.309,6.309,0,0,0,2.537,3.55a6.05,6.05,0,0,0,2.3-.389,3.454,3.454,0,0,1,1-.145A4.669,4.669,0,0,0,4.819,4.322a4.864,4.864,0,0,1-2.227.4c-.088.026-.555.461-.936.726a4.876,4.876,0,0,1-.888.486c-.477-.08-1.02.166-.639.462a3.016,3.016,0,0,0,.828.064,16.383,16.383,0,0,0,1.9-.924s.346-.022.787-.021a7.114,7.114,0,0,1-1.319.681C2,6.323,1.336,7.7.876,7.811c-.164.039-.711.237-.6.433.212.292,1.015.048,1.126-.06S2.9,6.822,2.9,6.822s3.272-.869,4.126-1.9c.389-.05,1.025-.336,1.9-.542,1.054-.25,3.6.747,4.759.636a4.728,4.728,0,0,0,1.547-.409,9.582,9.582,0,0,0,1.181,1.167A15.863,15.863,0,0,0,18.4,5.653c.266-.012.943-.126,1.378-.195-.084.027.506.56.656.6.382.1.531-.017.553-.125.054-.277-.355-.241-.636-.541a1.611,1.611,0,0,1,.577.045.8.8,0,0,0,.711.051c.635-.26.282-.636.048-.544m-3.761-.165a4.139,4.139,0,0,1,.691-.215,3.712,3.712,0,0,1,.634.395,2.947,2.947,0,0,1-1.325-.18"
        transform="translate(-0.001 0)"
        fill="#003764"
      />
    </svg>
  );
};

export default GreyhoundRacing;
