import * as React from 'react';
import type { FieldValues, UseControllerProps } from 'react-hook-form';
import { useController } from 'react-hook-form';
import type { SingleValue } from 'react-select';
import Select from 'react-select';

export interface CommonOption {
  label: string;
  value: string;
}

export interface CommonSelectProps {
  placeholder: string;
  name?: string;
  control?: any;
  options: CommonOption[];
  disaplayValue: CommonOption | undefined; // Optional standalone value prop
  setDisplayValue?: React.Dispatch<
    React.SetStateAction<CommonOption | undefined>
  >;
  value?: CommonOption;
  onChange?: (value: string) => void; // Optional standalone onChange prop
}

export function CommonSelect({
  placeholder,
  control,
  name,
  options,
  disaplayValue,
  onChange: standaloneOnChange,
  setDisplayValue,
}: Readonly<CommonSelectProps>) {
  // Only call useController if control and name are provided
  const controllerProps =
    control && name
      ? { field: { onChange: (val: any) => {} }, fieldState: { error: null } }
      : useController({
          name: name || '',
          control,
        });

  const handleChange = (selectedOption: SingleValue<CommonOption>) => {
    // Update the value for react-hook-form controller if control is provided
    if (control && controllerProps.field) {
      controllerProps.field.onChange(selectedOption?.value ?? '');
    }

    // Update standalone onChange if provided
    if (standaloneOnChange) {
      standaloneOnChange(selectedOption?.value ?? '');
    }

    // Update display value if setDisplayValue is provided
    if (setDisplayValue) {
      setDisplayValue(selectedOption ?? undefined);
    }
  };

  return (
    <div>
      <Select
        value={disaplayValue || null}
        onChange={handleChange}
        options={options}
        placeholder={placeholder}
        className="React details-select"
        classNamePrefix="select"
        styles={{
          container: (provided) => ({
            ...provided,
          }),
          valueContainer: (provided) => ({
            ...provided,
            overflow: 'visible',
          }),
          // @ts-expect-error
          placeholder: (provided, state) => ({
            ...provided,
            position: 'absolute',
            top:
              state.hasValue ||
              state.selectProps.inputValue ||
              state.selectProps.menuIsOpen
                ? -14
                : 'auto',
            backgroundColor:
              state.hasValue ||
              state.selectProps.inputValue ||
              state.selectProps.menuIsOpen
                ? 'white'
                : 'transparent',
            transition: 'top 0.2s, font-size 0.1s !important',
            fontSize:
              (state.hasValue ||
                state.selectProps.inputValue ||
                state.selectProps.menuIsOpen) &&
              '12px !important',
            color: state.selectProps.menuIsOpen ? '#4455c7' : '#a4a4a4',
            padding:
              (state.hasValue ||
                state.selectProps.inputValue ||
                state.selectProps.menuIsOpen) &&
              '0px 3px',
            paddingLeft:
              (state.hasValue ||
                state.selectProps.inputValue ||
                state.selectProps.menuIsOpen) &&
              '1px !important',
            marginLeft:
              (state.hasValue ||
                state.selectProps.inputValue ||
                state.selectProps.menuIsOpen) &&
              '7px !important',
            lineHeight:
              (state.hasValue ||
                state.selectProps.inputValue ||
                state.selectProps.menuIsOpen) &&
              '8px !important',
          }),
        }}
      />
      {controllerProps.fieldState.error && (
        <span className="text-red-500 text-sm mt-2">
          {controllerProps.fieldState.error.message}
        </span>
      )}
    </div>
  );
}
